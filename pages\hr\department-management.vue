<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="goBack">
        <text class="ri-arrow-left-line"></text>
      </view>
      <text class="page-title">部门管理</text>
      <view class="header-actions">
        <button type="button" class="action-button" @click="showAddDepartment">
          <text class="ri-add-line"></text>
        </button>
      </view>
    </view>
    
    <!-- 部门统计卡片 -->
    <view class="stats-card">
      <view class="stats-item">
        <text class="stats-value">{{departments.length}}</text>
        <text class="stats-label">部门总数</text>
      </view>
      <view class="stats-item">
        <text class="stats-value">{{totalEmployees}}</text>
        <text class="stats-label">员工总数</text>
      </view>
      <view class="stats-item">
        <text class="stats-value">{{avgDeptSize}}</text>
        <text class="stats-label">平均部门规模</text>
      </view>
    </view>
    
    <!-- 部门列表 -->
    <view class="department-list">
      <view 
        class="department-item" 
        v-for="(dept, index) in departments" 
        :key="index"
        @click="viewDepartmentDetail(dept.id)"
      >
        <view class="department-header">
          <view class="department-icon" :style="{backgroundColor: dept.color}">
            <text :class="dept.icon"></text>
          </view>
          <view class="department-main">
            <text class="department-name">{{dept.name}}</text>
            <text class="department-manager">{{dept.manager}}</text>
          </view>
          <view class="department-count">
            <text class="count-value">{{dept.employeeCount}}</text>
            <text class="count-label">人</text>
          </view>
        </view>
        
        <view class="department-body">
          <view class="info-row">
            <view class="info-item">
              <text class="info-label">部门编号</text>
              <text class="info-value">{{dept.code}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">创建日期</text>
              <text class="info-value">{{dept.createDate}}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item">
              <text class="info-label">上级部门</text>
              <text class="info-value">{{dept.parentDepartment || '-'}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">部门状态</text>
              <text :class="['info-value', 'status-' + dept.status]">{{dept.status === 'active' ? '正常' : '已停用'}}</text>
            </view>
          </view>
        </view>
        
        <view class="department-footer">
          <view class="action-buttons">
            <view class="action-btn" @click.stop="editDepartment(dept)">
              <text class="ri-edit-line"></text>
              <text>编辑</text>
            </view>
            <view class="action-btn" @click.stop="manageDepartmentMembers(dept.id)">
              <text class="ri-team-line"></text>
              <text>成员</text>
            </view>
            <view class="action-btn" v-if="dept.status === 'active'" @click.stop="toggleDepartmentStatus(dept, 'inactive')">
              <text class="ri-pause-circle-line"></text>
              <text>停用</text>
            </view>
            <view class="action-btn" v-else @click.stop="toggleDepartmentStatus(dept, 'active')">
              <text class="ri-play-circle-line"></text>
              <text>启用</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 添加部门弹窗 -->
    <view class="popup-mask" v-if="showPopup" @click="closePopup"></view>
    <view class="popup-container" v-if="showPopup">
      <view class="popup-header">
        <text class="popup-title">{{isEditing ? '编辑部门' : '添加部门'}}</text>
        <text class="popup-close" @click="closePopup">×</text>
      </view>
      
      <view class="popup-body">
        <view class="form-group">
          <text class="form-label required">部门名称</text>
          <input 
            type="text" 
            class="form-input" 
            v-model="formData.name" 
            placeholder="请输入部门名称"
            :class="{ 'error': errors.name }"
          />
          <text v-if="errors.name" class="error-message">{{errors.name}}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label required">部门编号</text>
          <input 
            type="text" 
            class="form-input" 
            v-model="formData.code" 
            placeholder="请输入部门编号"
            :class="{ 'error': errors.code }"
          />
          <text v-if="errors.code" class="error-message">{{errors.code}}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label">上级部门</text>
          <picker 
            mode="selector" 
            :range="parentDepartments" 
            range-key="name"
            @change="onParentDepartmentChange"
            class="form-picker"
          >
            <view class="picker-value">
              <text v-if="formData.parentDepartment">{{formData.parentDepartment}}</text>
              <text v-else class="placeholder">请选择上级部门（可选）</text>
              <text class="ri-arrow-down-s-line picker-icon"></text>
            </view>
          </picker>
        </view>
        
        <view class="form-group">
          <text class="form-label required">部门负责人</text>
          <picker 
            mode="selector" 
            :range="managers" 
            range-key="name"
            @change="onManagerChange"
            class="form-picker"
          >
            <view class="picker-value">
              <text v-if="formData.manager">{{formData.manager}}</text>
              <text v-else class="placeholder">请选择部门负责人</text>
              <text class="ri-arrow-down-s-line picker-icon"></text>
            </view>
          </picker>
          <text v-if="errors.manager" class="error-message">{{errors.manager}}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label">部门描述</text>
          <textarea 
            class="form-textarea" 
            v-model="formData.description" 
            placeholder="请输入部门描述"
          ></textarea>
        </view>
      </view>
      
      <view class="popup-footer">
        <button class="btn-cancel" @click="closePopup">取消</button>
        <button class="btn-submit" @click="saveDepartment">保存</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      departments: [
        {
          id: '1',
          name: '销售部',
          code: 'DEPT-001',
          manager: '王经理',
          createDate: '2020-01-10',
          employeeCount: 20,
          parentDepartment: '',
          status: 'active',
          icon: 'ri-briefcase-4-line',
          color: '#4a6fff',
          description: '负责公司产品销售和客户关系管理'
        },
        {
          id: '2',
          name: '技术部',
          code: 'DEPT-002',
          manager: '张经理',
          createDate: '2020-01-10',
          employeeCount: 15,
          parentDepartment: '',
          status: 'active',
          icon: 'ri-terminal-box-line',
          color: '#00c48c',
          description: '负责公司产品开发和技术支持'
        },
        {
          id: '3',
          name: '市场部',
          code: 'DEPT-003',
          manager: '李经理',
          createDate: '2020-02-15',
          employeeCount: 10,
          parentDepartment: '',
          status: 'active',
          icon: 'ri-line-chart-line',
          color: '#ff9500',
          description: '负责公司品牌推广和市场活动策划'
        },
        {
          id: '4',
          name: '财务部',
          code: 'DEPT-004',
          manager: '赵经理',
          createDate: '2020-01-10',
          employeeCount: 8,
          parentDepartment: '',
          status: 'active',
          icon: 'ri-money-dollar-circle-line',
          color: '#ff4d4f',
          description: '负责公司财务管理和成本控制'
        },
        {
          id: '5',
          name: '人事部',
          code: 'DEPT-005',
          manager: '刘经理',
          createDate: '2020-01-20',
          employeeCount: 6,
          parentDepartment: '',
          status: 'active',
          icon: 'ri-user-settings-line',
          color: '#7870ff',
          description: '负责公司人员招聘、培训和人力资源管理'
        },
        {
          id: '6',
          name: '行政部',
          code: 'DEPT-006',
          manager: '陈经理',
          createDate: '2020-03-05',
          employeeCount: 5,
          parentDepartment: '',
          status: 'inactive',
          icon: 'ri-building-line',
          color: '#909399',
          description: '负责公司日常行政事务和后勤支持'
        }
      ],
      totalEmployees: 64,
      showPopup: false,
      isEditing: false,
      formData: {
        id: '',
        name: '',
        code: '',
        manager: '',
        parentDepartment: '',
        description: '',
        status: 'active'
      },
      errors: {},
      managers: [
        { id: '1', name: '王经理' },
        { id: '2', name: '张经理' },
        { id: '3', name: '李经理' },
        { id: '4', name: '赵经理' },
        { id: '5', name: '刘经理' },
        { id: '6', name: '陈经理' }
      ],
      parentDepartments: []
    }
  },
  computed: {
    avgDeptSize() {
      const activeDepts = this.departments.filter(dept => dept.status === 'active').length;
      return activeDepts ? Math.round(this.totalEmployees / activeDepts) : 0;
    }
  },
  created() {
    this.updateParentDepartments();
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    updateParentDepartments() {
      // 构建上级部门选择列表
      this.parentDepartments = [
        { id: '', name: '无上级部门' },
        ...this.departments.map(dept => ({
          id: dept.id,
          name: dept.name
        }))
      ];
    },
    viewDepartmentDetail(id) {
      // 跳转到部门详情页面
      uni.navigateTo({
        url: `/pages/hr/department-detail?id=${id}`
      });
    },
    showAddDepartment() {
      this.isEditing = false;
      this.formData = {
        id: '',
        name: '',
        code: '',
        manager: '',
        parentDepartment: '',
        description: '',
        status: 'active'
      };
      this.errors = {};
      this.showPopup = true;
    },
    editDepartment(dept) {
      this.isEditing = true;
      this.formData = {
        id: dept.id,
        name: dept.name,
        code: dept.code,
        manager: dept.manager,
        parentDepartment: dept.parentDepartment,
        description: dept.description,
        status: dept.status
      };
      this.errors = {};
      this.showPopup = true;
    },
    closePopup() {
      this.showPopup = false;
    },
    onParentDepartmentChange(e) {
      const index = e.detail.value;
      this.formData.parentDepartment = this.parentDepartments[index].name;
    },
    onManagerChange(e) {
      const index = e.detail.value;
      this.formData.manager = this.managers[index].name;
    },
    validateForm() {
      this.errors = {};
      let isValid = true;
      
      if (!this.formData.name) {
        this.errors.name = '请输入部门名称';
        isValid = false;
      }
      
      if (!this.formData.code) {
        this.errors.code = '请输入部门编号';
        isValid = false;
      }
      
      if (!this.formData.manager) {
        this.errors.manager = '请选择部门负责人';
        isValid = false;
      }
      
      return isValid;
    },
    saveDepartment() {
      if (this.validateForm()) {
        if (this.isEditing) {
          // 编辑现有部门
          const index = this.departments.findIndex(dept => dept.id === this.formData.id);
          if (index !== -1) {
            // 保留原有的一些属性
            const originalDept = this.departments[index];
            this.departments[index] = {
              ...originalDept,
              name: this.formData.name,
              code: this.formData.code,
              manager: this.formData.manager,
              parentDepartment: this.formData.parentDepartment,
              description: this.formData.description
            };
          }
        } else {
          // 添加新部门
          const newDept = {
            id: String(this.departments.length + 1),
            name: this.formData.name,
            code: this.formData.code,
            manager: this.formData.manager,
            parentDepartment: this.formData.parentDepartment,
            description: this.formData.description,
            createDate: new Date().toISOString().substr(0, 10),
            employeeCount: 0,
            status: 'active',
            icon: 'ri-building-line',
            color: this.getRandomColor()
          };
          
          this.departments.push(newDept);
        }
        
        // 更新上级部门选择列表
        this.updateParentDepartments();
        
        // 关闭弹窗并显示成功提示
        this.closePopup();
        uni.showToast({
          title: this.isEditing ? '部门更新成功' : '部门添加成功',
          icon: 'success'
        });
      }
    },
    manageDepartmentMembers(id) {
      // 跳转到部门成员管理页面
      uni.navigateTo({
        url: `/pages/hr/department-members?id=${id}`
      });
    },
    toggleDepartmentStatus(dept, newStatus) {
      // 切换部门状态
      const index = this.departments.findIndex(d => d.id === dept.id);
      if (index !== -1) {
        this.departments[index].status = newStatus;
        
        // 显示状态变更成功提示
        uni.showToast({
          title: newStatus === 'active' ? '部门已启用' : '部门已停用',
          icon: 'success'
        });
      }
    },
    getRandomColor() {
      // 返回随机颜色
      const colors = ['#4a6fff', '#00c48c', '#ff9500', '#ff4d4f', '#7870ff', '#722ed1', '#13c2c2', '#52c41a'];
      return colors[Math.floor(Math.random() * colors.length)];
    }
  }
};
</script>

<style>
.container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 30rpx;
}

.page-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  position: relative;
  border-bottom: 1rpx solid #eaeaea;
}

.back-button {
  font-size: 40rpx;
  color: #333;
  padding: 10rpx;
}

.page-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
}

.action-button {
  background: none;
  border: none;
  font-size: 40rpx;
  color: #666;
  padding: 10rpx;
}

.stats-card {
  margin: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  border-radius: 20rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-value {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #999;
}

.department-list {
  padding: 0 30rpx;
}

.department-item {
  margin-bottom: in;
  border-radius: 20rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 20rpx;
}

.department-header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eaeaea;
}

.department-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
  color: #ffffff;
  font-size: 40rpx;
}

.department-main {
  flex: 1;
}

.department-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.department-manager {
  font-size: 26rpx;
  color: #666;
}

.department-count {
  text-align: center;
}

.count-value {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  margin-right: 5rpx;
}

.count-label {
  font-size: 24rpx;
  color: #999;
}

.department-body {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eaeaea;
}

.info-row {
  display: flex;
  margin-bottom: 15rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  flex: 1;
}

.info-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 5rpx;
  display: block;
}

.info-value {
  font-size: 28rpx;
  color: #333;
}

.status-active {
  color: #52c41a;
}

.status-inactive {
  color: #ff4d4f;
}

.department-footer {
  padding: 15rpx 30rpx;
  background-color: #f9fafc;
}

.action-buttons {
  display: flex;
  justify-content: space-around;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx 0;
  font-size: 24rpx;
  color: #666;
}

.action-btn text:first-child {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

/* 弹窗样式 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.popup-container {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  background-color: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  z-index: 1001;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eaeaea;
}

.popup-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
}

.popup-close {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.popup-body {
  max-height: 70vh;
  overflow-y: auto;
  padding: 20rpx 0;
}

.form-group {
  padding: 15rpx 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.required:after {
  content: ' *';
  color: #ff4d4f;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #dcdfe6;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
}

.form-input.error {
  border-color: #ff4d4f;
}

.form-textarea {
  width: 100%;
  height: 180rpx;
  border: 1rpx solid #dcdfe6;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
}

.error-message {
  display: block;
  font-size: 24rpx;
  color: #ff4d4f;
  margin-top: 5rpx;
}

.form-picker {
  width: 100%;
}

.picker-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  border: 1rpx solid #dcdfe6;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
}

.placeholder {
  color: #999;
}

.picker-icon {
  font-size: 32rpx;
  color: #999;
}

.popup-footer {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eaeaea;
}

.btn-cancel {
  width: 48%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  color: #666;
  background-color: #f5f7fa;
  border: 1rpx solid #dcdfe6;
  font-size: 28rpx;
}

.btn-submit {
  width: 48%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  color: #ffffff;
  background-color: #4a6fff;
  font-size: 28rpx;
}
</style> 
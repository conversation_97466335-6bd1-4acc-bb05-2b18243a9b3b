<template>
	<view class="action-sheet" v-show="visible">
		<view class="action-sheet-mask" @tap="handleClose"></view>
		<view class="action-sheet-container">
			<view class="action-sheet-header" v-if="title || subtitle">
				<text class="action-sheet-title">{{ title }}</text>
				<text class="action-sheet-subtitle" v-if="subtitle">{{ subtitle }}</text>
			</view>
			<view class="action-sheet-body">
				<slot></slot>
			</view>
			<view class="action-sheet-footer">
				<view class="action-sheet-cancel" @tap="handleClose">
					<text>{{ cancelText }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'ActionSheet',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		title: {
			type: String,
			default: ''
		},
		subtitle: {
			type: String,
			default: ''
		},
		cancelText: {
			type: String,
			default: '取消'
		}
	},
	methods: {
		handleClose() {
			this.$emit('close')
		}
	}
}
</script>

<style lang="scss">
.action-sheet {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 999;
	
	&-mask {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.4);
		z-index: 1;
		animation: fade-in 0.3s ease;
	}
	
	&-container {
		position: absolute;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #ffffff;
		border-radius: 24rpx 24rpx 0 0;
		z-index: 2;
		overflow: hidden;
		animation: slide-up 0.3s ease;
		box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
	}
	
	&-header {
		padding: 24rpx 30rpx;
		border-bottom: 1px solid #f5f5f5;
	}
	
	&-title {
		font-size: 32rpx;
		font-weight: 500;
		color: #333333;
		line-height: 1.4;
	}
	
	&-subtitle {
		display: block;
		font-size: 26rpx;
		color: #666666;
		margin-top: 8rpx;
		line-height: 1.4;
	}
	
	&-body {
		max-height: 60vh;
		overflow-y: auto;
		padding: 10rpx 0;
	}
	
	&-footer {
		padding: 16rpx 0;
		border-top: 1px solid #f5f5f5;
	}
	
	&-cancel {
		padding: 24rpx 0;
		text-align: center;
		font-size: 32rpx;
		color: #333333;
		font-weight: 500;
		
		&:active {
			background-color: #f5f5f5;
		}
	}
}

@keyframes slide-up {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style> 
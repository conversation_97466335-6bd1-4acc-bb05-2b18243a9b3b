(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-sales-opportunity-detail"],{"17c4":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.getDictionaryPageDetail=e.getDictionaryPage=void 0;var i=a("c475");e.getDictionaryPage=function(t){return(0,i.request)({url:"/api/DataDictionary/page",method:"POST",data:t})};e.getDictionaryPageDetail=function(t){return(0,i.request)({url:"/api/DataDictionary/pageDetail",method:"POST",data:t})}},2634:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
e.default=function(){return t};var t={},a=Object.prototype,n=a.hasOwnProperty,o=Object.defineProperty||function(t,e,a){t[e]=a.value},r="function"==typeof Symbol?Symbol:{},s=r.iterator||"@@iterator",c=r.asyncIterator||"@@asyncIterator",u=r.toStringTag||"@@toStringTag";function l(t,e,a){return Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(z){l=function(t,e,a){return t[e]=a}}function d(t,e,a,i){var n=e&&e.prototype instanceof p?e:p,r=Object.create(n.prototype),s=new T(i||[]);return o(r,"_invoke",{value:C(t,a,s)}),r}function f(t,e,a){try{return{type:"normal",arg:t.call(e,a)}}catch(z){return{type:"throw",arg:z}}}t.wrap=d;var v={};function p(){}function g(){}function m(){}var y={};l(y,s,(function(){return this}));var h=Object.getPrototypeOf,b=h&&h(h(P([])));b&&b!==a&&n.call(b,s)&&(y=b);var w=m.prototype=p.prototype=Object.create(y);function x(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function _(t,e){var a;o(this,"_invoke",{value:function(o,r){function s(){return new e((function(a,s){(function a(o,r,s,c){var u=f(t[o],t,r);if("throw"!==u.type){var l=u.arg,d=l.value;return d&&"object"==(0,i.default)(d)&&n.call(d,"__await")?e.resolve(d.__await).then((function(t){a("next",t,s,c)}),(function(t){a("throw",t,s,c)})):e.resolve(d).then((function(t){l.value=t,s(l)}),(function(t){return a("throw",t,s,c)}))}c(u.arg)})(o,r,a,s)}))}return a=a?a.then(s,s):s()}})}function C(t,e,a){var i="suspendedStart";return function(n,o){if("executing"===i)throw new Error("Generator is already running");if("completed"===i){if("throw"===n)throw o;return j()}for(a.method=n,a.arg=o;;){var r=a.delegate;if(r){var s=k(r,a);if(s){if(s===v)continue;return s}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if("suspendedStart"===i)throw i="completed",a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);i="executing";var c=f(t,e,a);if("normal"===c.type){if(i=a.done?"completed":"suspendedYield",c.arg===v)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(i="completed",a.method="throw",a.arg=c.arg)}}}function k(t,e){var a=e.method,i=t.iterator[a];if(void 0===i)return e.delegate=null,"throw"===a&&t.iterator["return"]&&(e.method="return",e.arg=void 0,k(t,e),"throw"===e.method)||"return"!==a&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+a+"' method")),v;var n=f(i,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,v;var o=n.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,v):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,v)}function D(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(D,this),this.reset(!0)}function P(t){if(t){var e=t[s];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function e(){for(;++a<t.length;)if(n.call(t,a))return e.value=t[a],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:j}}function j(){return{value:void 0,done:!0}}return g.prototype=m,o(w,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=l(m,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,l(t,u,"GeneratorFunction")),t.prototype=Object.create(w),t},t.awrap=function(t){return{__await:t}},x(_.prototype),l(_.prototype,c,(function(){return this})),t.AsyncIterator=_,t.async=function(e,a,i,n,o){void 0===o&&(o=Promise);var r=new _(d(e,a,i,n),o);return t.isGeneratorFunction(a)?r:r.next().then((function(t){return t.done?t.value:r.next()}))},x(w),l(w,u,"Generator"),l(w,s,(function(){return this})),l(w,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),a=[];for(var i in e)a.push(i);return a.reverse(),function t(){for(;a.length;){var i=a.pop();if(i in e)return t.value=i,t.done=!1,t}return t.done=!0,t}},t.values=P,T.prototype={constructor:T,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(O),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function a(a,i){return r.type="throw",r.arg=t,e.next=a,i&&(e.method="next",e.arg=void 0),!!i}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],r=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var s=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var r=o?o.completion:{};return r.type=t,r.arg=e,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(r)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.finallyLoc===t)return this.complete(a.completion,a.afterLoc),O(a),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.tryLoc===t){var i=a.completion;if("throw"===i.type){var n=i.arg;O(a)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,a){return this.delegate={iterator:P(t),resultName:e,nextLoc:a},"next"===this.method&&(this.arg=void 0),v}},t},a("6a54"),a("01a2"),a("e39c"),a("bf0f"),a("844d"),a("18f7"),a("de6c"),a("3872e"),a("4e9b"),a("114e"),a("c240"),a("926e"),a("7a76"),a("c9b5"),a("aa9c"),a("2797"),a("8a8d"),a("dc69"),a("f7a5");var i=function(t){return t&&t.__esModule?t:{default:t}}(a("fcf3"))},"2fdc":function(t,e,a){"use strict";function i(t,e,a,i,n,o,r){try{var s=t[o](r),c=s.value}catch(u){return void a(u)}s.done?e(c):Promise.resolve(c).then(i,n)}a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return function(){var e=this,a=arguments;return new Promise((function(n,o){var r=t.apply(e,a);function s(t){i(r,n,o,s,c,"next",t)}function c(t){i(r,n,o,s,c,"throw",t)}s(void 0)}))}},a("bf0f")},"6bbf":function(t,e,a){"use strict";a.r(e);var i=a("eff3"),n=a("7d90");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("7042");var r=a("828b"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"285ee3f5",null,!1,i["a"],void 0);e["default"]=s.exports},7042:function(t,e,a){"use strict";var i=a("d54d"),n=a.n(i);n.a},"7d90":function(t,e,a){"use strict";a.r(e);var i=a("f377"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},c475:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.request=e.getTenantInfo=void 0;var n=i(a("9b1b"));a("bf0f"),a("4626"),a("5ac7");var o=null;e.getTenantInfo=function(t){return new Promise((function(e,a){uni.request({url:"/api/Tenants/getTenantsByLogin",method:"POST",data:t,header:{"Content-Type":"application/json"},success:function(t){200===t.statusCode?(o=t.data,e(t.data)):a(t.data)},fail:function(t){a(t)}})}))};e.request=function(t){return t.url.includes("/login")&&o&&(t.header=(0,n.default)((0,n.default)({},t.header),{},{__tenant:o[0].id})),new Promise((function(e,a){uni.request({url:t.url,method:t.method||"GET",data:t.data||{},header:(0,n.default)({Authorization:"Bearer "+uni.getStorageSync("token"),"Content-Type":"application/json"},t.header),success:function(t){200===t.statusCode?e(t.data):a(t.data)},fail:function(t){a(t)}})}))}},c780:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return s.apply(this,arguments)},a("8f71"),a("bf0f");var n=i(a("2634")),o=i(a("2fdc")),r=a("17c4");function s(){return s=(0,o.default)((0,n.default)().mark((function t(e){var a,i,o,s,c,u,l,d,f=arguments;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=f.length>1&&void 0!==f[1]?f[1]:{},i=a.pageIndex,o=void 0===i?1:i,s=a.pageSize,c=void 0===s?100:s,t.prev=2,t.next=5,(0,r.getDictionaryPage)({pageIndex:o,pageSize:c,filter:e});case 5:if(l=t.sent,null!==l&&void 0!==l&&null!==(u=l.items)&&void 0!==u&&u.length){t.next=8;break}return t.abrupt("return",[]);case 8:return t.next=10,(0,r.getDictionaryPageDetail)({pageIndex:o,pageSize:c,dataDictionaryId:l.items[0].id});case 10:return d=t.sent,t.abrupt("return",d.items.filter((function(t){return t.isEnabled})));case 14:throw t.prev=14,t.t0=t["catch"](2),console.error("Error fetching select options:",t.t0),t.t0;case 18:case"end":return t.stop()}}),t,null,[[2,14]])}))),s.apply(this,arguments)}},d54d:function(t,e,a){var i=a("e751");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("0f01fef6",i,!0,{sourceMap:!1,shadowMode:!1})},d8b2:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.getBusinessList=e.getBusinessDetail=e.getAllCompanyList=e.deleteBusiness=e.UpdateBusiness=e.AddNewBusiness=void 0;var i=a("c475");e.getBusinessList=function(t){return(0,i.request)({url:"/api/crm/business/getList",method:"POST",data:t})};e.AddNewBusiness=function(t){return(0,i.request)({url:"/api/crm/business/create",method:"POST",data:t})};e.deleteBusiness=function(t){return(0,i.request)({url:"/api/crm/business/delete?id=".concat(t),method:"POST"})};e.getBusinessDetail=function(t){return(0,i.request)({url:"/api/crm/business/getBusinessById?id=".concat(t),method:"GET"})};e.UpdateBusiness=function(t,e){return(0,i.request)({url:"/api/crm/business/update?id=".concat(t),method:"POST",data:e})};e.getAllCompanyList=function(){return(0,i.request)({url:"/api/crm/business/getAllCompanys",method:"GET"})}},e751:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'.page-header[data-v-285ee3f5]{display:flex;align-items:center;justify-content:space-between;padding:var(--spacing-md) var(--spacing-lg);border-bottom:%?1?% solid var(--border-color);background-color:#fff;position:-webkit-sticky;position:sticky;top:0;z-index:10}.page-title[data-v-285ee3f5]{font-size:%?32?%;font-weight:700;color:var(--text-primary)}.back-button[data-v-285ee3f5]{color:var(--text-secondary);display:flex;align-items:center}.header-actions[data-v-285ee3f5]{display:flex;gap:var(--spacing-sm)}.action-button[data-v-285ee3f5]{width:%?72?%;height:%?72?%;display:flex;align-items:center;justify-content:center;border-radius:var(--radius-full);color:var(--text-secondary);background-color:var(--light-color);border:%?1?% solid var(--border-color)}.opportunity-header[data-v-285ee3f5]{background-color:#fff;padding:var(--spacing-lg);border-bottom:%?1?% solid var(--border-color)}.opportunity-title[data-v-285ee3f5]{font-size:%?40?%;font-weight:700;margin-bottom:var(--spacing-xs);color:var(--text-primary)}.opportunity-company[data-v-285ee3f5]{font-size:%?28?%;color:var(--text-secondary);margin-bottom:var(--spacing-md);display:flex;align-items:center}.prev-icon[data-v-285ee3f5]{margin-right:%?8?%}.opportunity-meta[data-v-285ee3f5]{display:flex;flex-wrap:wrap;gap:var(--spacing-sm);margin-bottom:var(--spacing-md)}.meta-item[data-v-285ee3f5]{display:flex;align-items:center;font-size:%?28?%;color:var(--text-secondary);margin-right:%?20?%}.opportunity-stage[data-v-285ee3f5]{display:inline-block;padding:%?8?% %?24?%;border-radius:var(--radius-full);font-size:%?28?%;font-weight:500;margin-bottom:var(--spacing-md)}.opportunity-stage.stage-Survey[data-v-285ee3f5]{background-color:#e0e7ff;color:#4f46e5}.opportunity-stage.stage-Proposal[data-v-285ee3f5]{background-color:#fef3c7;color:#d97706}.opportunity-stage.stage-Quote[data-v-285ee3f5]{background-color:#dbeafe;color:#2563eb}.opportunity-stage.stage-Negotiate[data-v-285ee3f5]{background-color:#fee2e2;color:#dc2626}.opportunity-stage.stage-Win[data-v-285ee3f5]{background-color:#d1fae5;color:#059669}.opportunity-stage.stage-Lose[data-v-285ee3f5], .opportunity-stage.stage-Cancel[data-v-285ee3f5], .opportunity-stage.stage-default[data-v-285ee3f5]{background-color:#e5e7eb;color:#6b7280}.stage-progress[data-v-285ee3f5]{width:100%;height:%?12?%;background-color:var(--border-color);border-radius:var(--radius-full);margin-bottom:var(--spacing-sm);overflow:hidden}.progress-bar[data-v-285ee3f5]{height:100%;background-color:var(--primary-color);border-radius:var(--radius-full)}.stage-labels[data-v-285ee3f5]{display:flex;justify-content:space-between;font-size:%?24?%;color:var(--text-secondary)}.info-section[data-v-285ee3f5]{background-color:#fff;margin:var(--spacing-md) 0;padding:var(--spacing-lg);border-top:%?1?% solid var(--border-color);border-bottom:%?1?% solid var(--border-color)}.section-title[data-v-285ee3f5]{font-size:%?32?%;font-weight:600;margin-bottom:var(--spacing-md);color:var(--text-primary);display:flex;align-items:center;justify-content:space-between}.section-title .action[data-v-285ee3f5]{font-size:%?28?%;font-weight:400;color:var(--primary-color)}.detail-list[data-v-285ee3f5]{display:grid;grid-template-columns:%?200?% 1fr;row-gap:var(--spacing-sm)}.detail-label[data-v-285ee3f5]{font-size:%?28?%;color:var(--text-secondary)}.detail-value[data-v-285ee3f5]{font-size:%?28?%;color:var(--text-primary);font-weight:500}.requirements-text[data-v-285ee3f5]{font-size:%?28?%;line-height:1.5;color:var(--text-secondary)}.activity-item[data-v-285ee3f5]{display:flex;margin-bottom:var(--spacing-md);position:relative}.activity-item[data-v-285ee3f5]:not(:last-child)::before{content:"";position:absolute;top:%?48?%;left:%?24?%;width:%?2?%;height:calc(100% + var(--spacing-md));background-color:var(--border-color);z-index:1}.activity-icon[data-v-285ee3f5]{width:%?48?%;height:%?48?%;border-radius:var(--radius-full);background-color:var(--primary-light);color:var(--primary-color);display:flex;align-items:center;justify-content:center;margin-right:var(--spacing-md);position:relative;z-index:2;flex-shrink:0}.activity-icon.call[data-v-285ee3f5]{background-color:#fef3c7;color:#d97706}.activity-icon.note[data-v-285ee3f5]{background-color:#e0e7ff;color:#4f46e5}.activity-icon.email[data-v-285ee3f5]{background-color:#dbeafe;color:#2563eb}.activity-content[data-v-285ee3f5]{flex:1}.activity-title[data-v-285ee3f5]{font-size:%?28?%;font-weight:500;margin-bottom:%?4?%;color:var(--text-primary)}.activity-meta[data-v-285ee3f5]{font-size:%?24?%;color:var(--text-secondary);margin-bottom:%?16?%}.activity-description[data-v-285ee3f5]{font-size:%?28?%;color:var(--text-secondary);background-color:var(--light-color);padding:var(--spacing-sm);border-radius:var(--radius-md);margin-top:var(--spacing-xs)}.contact-item[data-v-285ee3f5]{display:flex;align-items:center;padding:var(--spacing-sm) 0;border-bottom:%?1?% solid var(--border-color)}.contact-item[data-v-285ee3f5]:last-child{border-bottom:none}.contact-avatar[data-v-285ee3f5]{width:%?80?%;height:%?80?%;border-radius:var(--radius-full);background-color:var(--primary-light);color:var(--primary-color);display:flex;align-items:center;justify-content:center;margin-right:var(--spacing-md);font-weight:700;flex-shrink:0}.contact-info[data-v-285ee3f5]{flex:1}.contact-name[data-v-285ee3f5]{font-size:%?28?%;font-weight:500;color:var(--text-primary)}.contact-role[data-v-285ee3f5]{font-size:%?24?%;color:var(--text-secondary)}.contact-actions[data-v-285ee3f5]{display:flex;gap:var(--spacing-sm)}.contact-action[data-v-285ee3f5]{width:%?64?%;height:%?64?%;border-radius:var(--radius-full);display:flex;align-items:center;justify-content:center;color:var(--text-secondary);background-color:var(--light-color)}.action-bar[data-v-285ee3f5]{position:fixed;bottom:0;left:0;right:0;background-color:#fff;padding:var(--spacing-md);display:flex;justify-content:space-between;gap:var(--spacing-md);border-top:%?1?% solid var(--border-color);z-index:10}.action-bar .btn[data-v-285ee3f5]{flex:1;display:flex;align-items:center;justify-content:center;gap:var(--spacing-xs)}.action-bar .btn uni-text[data-v-285ee3f5]:first-child{margin-right:%?8?%}.notes-container[data-v-285ee3f5]{margin-bottom:%?180?%}.communication-container[data-v-285ee3f5]{margin-bottom:%?180?%}.communication-timeline[data-v-285ee3f5]{width:100%}.communication-item[data-v-285ee3f5]{display:flex;align-items:flex-start;padding:var(--spacing-md) 0;border-bottom:%?1?% solid var(--border-color);transition:background-color .2s}.communication-item[data-v-285ee3f5]:last-child{border-bottom:none}.communication-icon[data-v-285ee3f5]{width:%?48?%;height:%?48?%;border-radius:var(--radius-full);background-color:var(--primary-light);color:var(--primary-color);display:flex;align-items:center;justify-content:center;margin-right:var(--spacing-md);position:relative;z-index:2;flex-shrink:0}.communication-icon.call[data-v-285ee3f5]{background-color:#dbeafe;color:#2563eb}.communication-icon.meeting[data-v-285ee3f5]{background-color:#e0e7ff;color:#4f46e5}.communication-icon.email[data-v-285ee3f5]{background-color:#fef3c7;color:#d97706}.communication-icon.visit[data-v-285ee3f5]{background-color:#d1fae5;color:#059669}.communication-content[data-v-285ee3f5]{flex:1}.communication-header[data-v-285ee3f5]{display:flex;justify-content:space-between;align-items:baseline;margin-bottom:%?4?%}.communication-title[data-v-285ee3f5]{font-size:%?28?%;font-weight:500;color:var(--text-primary)}.communication-type[data-v-285ee3f5]{font-size:%?24?%;color:var(--text-secondary);background-color:var(--light-color);padding:%?2?% %?12?%;border-radius:var(--radius-full)}.communication-meta[data-v-285ee3f5]{font-size:%?24?%;color:var(--text-secondary);margin-bottom:%?12?%}.communication-description[data-v-285ee3f5]{font-size:%?28?%;color:var(--text-secondary);background-color:var(--light-color);padding:var(--spacing-sm);border-radius:var(--radius-md);margin-bottom:var(--spacing-sm);line-height:1.5}.tag-container[data-v-285ee3f5]{display:flex;flex-wrap:wrap;gap:var(--spacing-xs)}.tag[data-v-285ee3f5]{padding:%?4?% %?12?%;border-radius:var(--radius-full);background-color:var(--primary-light);color:var(--primary-color);font-size:%?22?%}.empty-state[data-v-285ee3f5]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:var(--spacing-lg);border:%?1?% solid var(--border-color);border-radius:var(--radius-md);margin-top:var(--spacing-md)}.empty-text[data-v-285ee3f5]{font-size:%?28?%;color:var(--text-secondary);margin-bottom:var(--spacing-md)}.empty-action[data-v-285ee3f5]{padding:var(--spacing-sm) var(--spacing-md);border-radius:var(--radius-full);background-color:var(--primary-color);color:#fff;font-size:%?28?%;font-weight:500}.item-hover[data-v-285ee3f5]{background-color:var(--primary-light)}',""]),t.exports=e},eff3:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={svgIcon:a("8a0f").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"container"},[a("v-uni-view",{staticClass:"page-header"},[a("v-uni-navigator",{staticClass:"back-button",attrs:{url:"./opportunity-list","open-type":"navigateBack"}},[a("svg-icon",{attrs:{name:"arrow-left",type:"svg",size:"40"}})],1),a("v-uni-view",{staticClass:"page-title"},[t._v("商机详情")]),a("v-uni-view",{staticClass:"header-actions"},[a("v-uni-view",{staticClass:"action-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showMoreOptions.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"more",type:"svg",size:"24"}})],1)],1)],1),a("v-uni-view",{staticClass:"opportunity-header"},[a("v-uni-view",{staticClass:"opportunity-title"},[t._v(t._s(t.opportunityData.name))]),a("v-uni-view",{staticClass:"opportunity-company"},[a("svg-icon",{staticClass:"prev-icon",attrs:{name:"building",type:"svg",size:"24"}}),a("v-uni-text",[t._v(t._s(t.opportunityData.customName))])],1),a("v-uni-view",{staticClass:"opportunity-meta"},[a("v-uni-view",{staticClass:"meta-item"},[a("svg-icon",{staticClass:"prev-icon",attrs:{name:"money",type:"svg",size:"24"}}),a("v-uni-text",[t._v(t._s(t._f("price")(t.opportunityData.expectedTransAmount)))])],1),a("v-uni-view",{staticClass:"meta-item"},[a("svg-icon",{staticClass:"prev-icon",attrs:{name:"calendar",type:"svg",size:"24"}}),a("v-uni-text",[t._v("预计成单: "+t._s(t._f("formatDateFilter")(t.opportunityData.expectedCompleteDate)))])],1)],1),t.opportunityData.businessProcessId?a("v-uni-view",{staticClass:"opportunity-stage",class:null===t.opportunityData.businessProcess?"stage-default":"stage-"+t.opportunityData.businessProcess.code},[t._v(t._s(t.opportunityData.businessProcess.displayText))]):t._e(),a("v-uni-view",{staticClass:"stage-progress"},[a("v-uni-view",{staticClass:"progress-bar",style:{width:t.opportunityData.expectedTransProbability}})],1),a("v-uni-view",{staticClass:"stage-labels"},t._l(t.stageOptions,(function(e){return a("v-uni-text",{key:e.id},[t._v(t._s(e.displayText))])})),1)],1),a("v-uni-view",{staticClass:"info-section"},[a("v-uni-view",{staticClass:"section-title"},[a("v-uni-text",[t._v("商机信息")]),a("v-uni-text",{staticClass:"action",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.editOpportunity.apply(void 0,arguments)}}},[t._v("编辑")])],1),a("v-uni-view",{staticClass:"detail-list"},[a("v-uni-view",{staticClass:"detail-label"},[t._v("商机来源")]),a("v-uni-view",{staticClass:"detail-value"},[t._v(t._s(t._f("formatEmptyFilter")(t.opportunityData.businessSource&&t.opportunityData.businessSource.displayText)))]),a("v-uni-view",{staticClass:"detail-label"},[t._v("商机类型")]),a("v-uni-view",{staticClass:"detail-value"},[t._v(t._s(t._f("formatEmptyFilter")(t.opportunityData.businessType&&t.opportunityData.businessType.displayText)))]),a("v-uni-view",{staticClass:"detail-label"},[t._v("优先级")]),a("v-uni-view",{staticClass:"detail-value"},[t._v(t._s(t._f("formatEmptyFilter")(t.opportunityData.businessPriority&&t.opportunityData.businessPriority.displayText)))]),a("v-uni-view",{staticClass:"detail-label"},[t._v("成交概率")]),a("v-uni-view",{staticClass:"detail-value"},[t._v(t._s(t._f("formatEmptyFilter")(t.opportunityData.expectedTransProbability)))]),a("v-uni-view",{staticClass:"detail-label"},[t._v("负责人")]),a("v-uni-view",{staticClass:"detail-value"},[t._v(t._s(t.opportunityData.owner))]),a("v-uni-view",{staticClass:"detail-label"},[t._v("创建时间")]),a("v-uni-view",{staticClass:"detail-value"},[t._v(t._s(t._f("formatDateFilter")(t.opportunityData.creationTime)))]),a("v-uni-view",{staticClass:"detail-label"},[t._v("最近更新")]),a("v-uni-view",{staticClass:"detail-value"},[t._v(t._s(t._f("formatDateFilter")(t.opportunityData.lastModificationTime)))])],1)],1),a("v-uni-view",{staticClass:"info-section"},[a("v-uni-view",{staticClass:"section-title"},[a("v-uni-text",[t._v("商机描述")])],1),a("v-uni-text",{staticClass:"requirements-text"},[t._v(t._s(t.opportunityData.description))])],1),t.opportunityData.contacts&&t.opportunityData.contacts.length?a("v-uni-view",{staticClass:"info-section"},[a("v-uni-view",{staticClass:"section-title"},[a("v-uni-text",[t._v("联系人")])],1),a("v-uni-view",{staticClass:"contact-list"},t._l(t.opportunityData.contacts,(function(e,i){return a("v-uni-view",{key:i,staticClass:"contact-item"},[a("v-uni-view",{staticClass:"contact-avatar"},[t._v(t._s(e.contact.name.charAt(0)))]),a("v-uni-view",{staticClass:"contact-info"},[a("v-uni-view",{staticClass:"contact-name"},[t._v(t._s(e.contact.name))]),a("v-uni-view",{staticClass:"contact-role"},[t._v(t._s(e.contact.position))])],1),a("v-uni-view",{staticClass:"contact-actions"},[a("v-uni-view",{staticClass:"contact-action",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.callContact(e.contact.telephone)}}},[a("svg-icon",{attrs:{name:"phone",type:"svg",size:"16"}})],1)],1)],1)})),1)],1):t._e(),a("v-uni-view",{staticClass:"action-bar"},[a("v-uni-button",{staticClass:"btn btn-primary",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.editOpportunity.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"edit",type:"svg",color:"#FFFFFF",size:"20"}}),a("v-uni-text",[t._v("编辑商机")])],1)],1)],1)},o=[]},f377:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("c223");var n=i(a("2634")),o=i(a("2fdc")),r=a("d8b2"),s=i(a("c780")),c={data:function(){return{opportunityId:"",stageOptions:[],opportunityData:{businessProcess:{},businessSource:{},businessType:{},businessPriority:{}},communicationRecords:[{id:"1001",type:"call",subject:"产品功能确认电话",creator:"李销售",time:"2023-10-20 15:30",content:"与张经理通话30分钟，讨论了产品功能细节和交付时间。客户关心数据安全问题，需要准备详细的技术方案。",tags:["跟进中","重要"]},{id:"1002",type:"meeting",subject:"需求沟通会议",creator:"李销售",time:"2023-10-15 10:00",content:"与客户IT团队进行了详细的需求沟通，明确了系统架构和主要功能模块。客户对交付时间表有明确要求，需在12月前完成初版。",tags:["需求确认"]},{id:"1003",type:"email",subject:"方案文档发送",creator:"王产品经理",time:"2023-10-10 09:30",content:"向客户发送了完整的解决方案文档，包含技术架构、实施计划和报价。",tags:["方案阶段"]}]}},methods:{loadOpportunityData:function(t){var e=this;(0,r.getBusinessDetail)(t).then((function(t){Object.assign(e.opportunityData,t)}))},loadDictionaryOptions:function(){var t=this;return(0,o.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,s.default)("BusinessProcess");case 3:t.stageOptions=e.sent,t.stageOptions.pop(),t.stageOptions.pop(),e.next=11;break;case 8:e.prev=8,e.t0=e["catch"](0),t.$message.error("加载字典数据失败");case 11:case"end":return e.stop()}}),e,null,[[0,8]])})))()},showMoreOptions:function(){var t=this;uni.showActionSheet({itemList:["删除商机"],success:function(e){switch(e.tapIndex){case 0:t.confirmDeleteOpportunity();break}}})},confirmDeleteOpportunity:function(){var t=this;uni.showModal({title:"删除商机",content:"确定要删除此商机吗？删除后无法恢复！",confirmColor:"#f56c6c",success:function(e){e.confirm&&(0,r.deleteBusiness)(t.opportunityId).then((function(t){uni.showToast({title:"删除成功",icon:"success"}),uni.navigateBack()}))}})},editOpportunity:function(){uni.navigateTo({url:"./opportunity-edit?id=".concat(this.opportunityId)})},addContact:function(){uni.showToast({title:"添加联系人功能开发中",icon:"none"})},callContact:function(t){t&&uni.makePhoneCall({phoneNumber:t,fail:function(){uni.showToast({title:"拨号取消",icon:"none"})}})},emailContact:function(t){t&&uni.showToast({title:"邮件功能开发中",icon:"none"})},shareOpportunity:function(){uni.showActionSheet({itemList:["分享给同事","导出PDF","发送邮件"],success:function(t){uni.showToast({title:"分享功能开发中",icon:"none"})}})},addActivity:function(){uni.navigateTo({url:"/pages/actions/action-create?type=activity&relatedId=".concat(this.opportunityId,"&relatedType=opportunity&relatedName=").concat(encodeURIComponent(this.opportunityData.title))})},arrangeActivity:function(){uni.navigateTo({url:"/pages/actions/action-create?type=activity&relatedId=".concat(this.opportunityId,"&relatedType=opportunity&relatedName=").concat(encodeURIComponent(this.opportunityData.title))})},addCommunication:function(){uni.navigateTo({url:"/pages/interactions/interaction-create?type=communication&relatedId=".concat(this.opportunityId,"&relatedType=opportunity&relatedName=").concat(encodeURIComponent(this.opportunityData.title))})},viewCommunicationDetail:function(t){uni.navigateTo({url:"/pages/interactions/interaction-detail?id=".concat(t)})},loadCommunicationRecords:function(){console.log("加载商机相关的沟通记录")},getTypeName:function(t){return{call:"电话",meeting:"会议",email:"邮件",visit:"拜访",message:"消息"}[t]||"未知"}},onLoad:function(t){t.id&&(this.opportunityId=t.id,this.loadDictionaryOptions(),this.loadOpportunityData(this.opportunityId))}};e.default=c}}]);
<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <navigator class="back-button" open-type="navigateBack">
        <text class="iconfont icon-arrow-left"></text>
      </navigator>
      <view class="page-title">客户分组</view>
      <view class="header-actions">
        <button class="action-button" @tap="showAddGroupModal">
          <text class="iconfont icon-add"></text>
        </button>
      </view>
    </view>

    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <view class="search-icon">
          <text class="iconfont icon-search"></text>
        </view>
        <input type="text" class="search-input" v-model="searchKeyword" placeholder="搜索分组名称" />
      </view>
    </view>

    <!-- 分组列表 -->
    <view class="groups-list">
      <!-- 空状态提示 -->
      <view class="empty-state" v-if="filteredGroups.length === 0">
        <view class="empty-icon">
          <text class="iconfont icon-folder"></text>
        </view>
        <view class="empty-title">暂无客户分组</view>
        <view class="empty-description">您还没有创建任何客户分组，点击右上角的加号创建新分组。</view>
        <button class="btn btn-primary" @tap="showAddGroupModal">
          <text class="iconfont icon-add"></text> 创建分组
        </button>
      </view>
      
      <!-- 分组卡片列表 -->
      <view 
        class="group-card" 
        v-for="(group, index) in filteredGroups" 
        :key="index"
      >
        <view class="group-header">
          <view class="group-name">
            <view class="group-color" :style="{ backgroundColor: group.color }"></view>
            <text>{{ group.name }}</text>
            <text class="system-tag" v-if="group.isSystem">系统</text>
          </view>
          <view class="group-count">{{ group.count }}个客户</view>
        </view>
        <view class="group-content">
          <view class="group-description">{{ group.description }}</view>
          <view class="group-meta">
            <view>创建于 {{ group.createDate }}</view>
            <view>最近更新 {{ group.updateDate }}</view>
          </view>
        </view>
        <view class="group-actions">
          <view class="group-action" @tap="viewGroup(group)">
            <text class="iconfont icon-eye"></text> 查看
          </view>
          <view class="group-action" @tap="editGroup(group)" v-if="!group.isSystem">
            <text class="iconfont icon-edit"></text> 编辑
          </view>
          <view class="group-action" @tap="confirmDeleteGroup(group)" v-if="!group.isSystem">
            <text class="iconfont icon-delete"></text> 删除
          </view>
        </view>
      </view>
    </view>

    <!-- 浮动添加按钮 -->
    <view class="fab" @tap="showAddGroupModal">
      <text class="iconfont icon-add"></text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      searchKeyword: '',
      
      // 客户分组数据
      groups: [
        {
          id: 1,
          name: '所有客户',
          color: '#4a6fff',
          count: 112,
          description: '系统默认分组，包含所有客户',
          isSystem: true,
          createDate: '2023-01-01',
          updateDate: '2023-01-01'
        },
        {
          id: 2,
          name: '新增客户',
          color: '#00c16e',
          count: 28,
          description: '最近30天新增的客户',
          isSystem: true,
          createDate: '2023-01-01',
          updateDate: '2023-10-20'
        },
        {
          id: 3,
          name: '重点关注',
          color: '#f59e0b',
          count: 15,
          description: '需要重点关注和跟进的客户',
          isSystem: false,
          createDate: '2023-08-15',
          updateDate: '2023-10-18'
        },
        {
          id: 4,
          name: '上海区域',
          color: '#3b82f6',
          count: 32,
          description: '上海区域的所有客户',
          isSystem: false,
          createDate: '2023-09-05',
          updateDate: '2023-10-10'
        },
        {
          id: 5,
          name: '年度目标',
          color: '#ec4899',
          count: 24,
          description: '今年的销售目标客户',
          isSystem: false,
          createDate: '2023-01-10',
          updateDate: '2023-10-15'
        }
      ]
    }
  },
  computed: {
    // 根据搜索关键词过滤分组
    filteredGroups() {
      if (!this.searchKeyword) return this.groups
      const keyword = this.searchKeyword.toLowerCase()
      return this.groups.filter(group => 
        group.name.toLowerCase().includes(keyword) || 
        group.description.toLowerCase().includes(keyword)
      )
    }
  },
  methods: {
    // 显示添加分组弹窗
    showAddGroupModal() {
      uni.navigateTo({
        url: '/pages/customers/customer-group-create'
      })
    },
    
    // 查看分组详情
    viewGroup(group) {
      uni.navigateTo({
        url: `/pages/customers/customer-list?groupId=${group.id}&groupName=${group.name}`
      })
    },
    
    // 编辑分组
    editGroup(group) {
      if (group.isSystem) {
        uni.showToast({
          title: '系统分组不可编辑',
          icon: 'none'
        })
        return
      }
      
      uni.navigateTo({
        url: `/pages/customers/customer-group-edit?id=${group.id}`
      })
    },
    
    // 确认删除分组
    confirmDeleteGroup(group) {
      if (group.isSystem) {
        uni.showToast({
          title: '系统分组不可删除',
          icon: 'none'
        })
        return
      }
      
      uni.showModal({
        title: '删除分组',
        content: `确定要删除"${group.name}"分组吗？分组内的客户不会被删除。`,
        success: (res) => {
          if (res.confirm) {
            this.deleteGroup(group.id)
          }
        }
      })
    },
    
    // 删除分组
    deleteGroup(groupId) {
      const index = this.groups.findIndex(group => group.id === groupId)
      if (index !== -1) {
        this.groups.splice(index, 1)
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
      }
    }
  },
  onLoad() {
    // 页面加载逻辑，实际应用中可能会从服务器获取分组数据
  }
}
</script>

<style>
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1rpx solid var(--border-color);
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.page-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
}

.back-button {
  color: var(--text-secondary);
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.action-button {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  color: var(--text-secondary);
  background-color: var(--light-color);
  border: 1rpx solid var(--border-color);
  padding: 0;
  line-height: 1;
}

/* 搜索栏样式 */
.search-container {
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: #ffffff;
  border-bottom: 1rpx solid var(--border-color);
}

.search-box {
  display: flex;
  align-items: center;
  background-color: var(--light-color);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-md);
  padding: 0 var(--spacing-sm);
  overflow: hidden;
}

.search-icon {
  color: var(--text-secondary);
  padding: var(--spacing-xs);
}

.search-input {
  flex: 1;
  border: none;
  padding: var(--spacing-sm);
  background-color: transparent;
  color: var(--text-primary);
  font-size: 28rpx;
}

/* 分组列表样式 */
.groups-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  padding: 32rpx;
  padding-bottom: 160rpx; /* 添加底部填充，确保内容不被底部导航栏遮挡 */
}

.group-card {
  background-color: #ffffff;
  border-radius: var(--radius-md);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  margin-bottom: var(--spacing-sm);
  overflow: hidden;
  border: 1rpx solid var(--border-color);
}

.group-header {
  padding: var(--spacing-md);
  border-bottom: 1rpx solid var(--border-color-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group-name {
  font-size: 30rpx;
  font-weight: 500;
  color: var(--text-primary);
  display: flex;
  align-items: center;
}

.group-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  margin-right: var(--spacing-xs);
}

.group-count {
  font-size: 26rpx;
  color: var(--text-secondary);
  background-color: var(--light-color);
  border-radius: var(--radius-full);
  padding: 4rpx 16rpx;
}

.group-content {
  padding: var(--spacing-md);
}

.group-description {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

.group-meta {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: var(--text-tertiary);
}

.group-actions {
  display: flex;
  border-top: 1rpx solid var(--border-color);
}

.group-action {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 0;
  color: var(--text-secondary);
  font-size: 26rpx;
}

.group-action text {
  margin-right: 8rpx;
}

.group-action:not(:last-child) {
  border-right: 1rpx solid var(--border-color);
}

/* 系统分组标识 */
.system-tag {
  font-size: 22rpx;
  background-color: #f3f4f6;
  color: #6b7280;
  padding: 2rpx 16rpx;
  border-radius: var(--radius-full);
  margin-left: var(--spacing-xs);
}

/* 浮动操作按钮 */
.fab {
  position: fixed;
  bottom: var(--spacing-lg);
  right: var(--spacing-lg);
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  font-size: 48rpx;
}

/* 空状态样式 */
.empty-state {
  padding: var(--spacing-xl) var(--spacing-lg);
  text-align: center;
}

.empty-icon {
  font-size: 96rpx;
  color: var(--border-color);
  margin-bottom: var(--spacing-md);
}

.empty-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.empty-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}
</style> 
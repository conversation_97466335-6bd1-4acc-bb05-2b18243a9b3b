<template>
  <view class="custom-tab-bar">
    <view 
      v-for="(item, index) in tabList" 
      :key="index" 
      class="tab-item" 
      :class="{ active: current === index }"
      @click="handleTabClick(item, index)"
    >
      <svg-icon :name="current === index ? item.selectedIconPath : item.iconPath" type="svg" :size="24" :color="current === index ? activeColor : color"></svg-icon>
      <text class="tab-text" :class="{ 'active-text': current === index }">{{ item.text }}</text>
    </view>
    
    <!-- 更多菜单弹出层 -->
    <view class="more-menu" v-if="showMoreMenu">
      <view class="menu-overlay" @click="closeMoreMenu"></view>
      <view class="menu-content">
        <view class="menu-header">
          <text class="menu-title">更多功能</text>
          <view class="menu-close" @click="closeMoreMenu">
            <svg-icon name="close" type="svg" :size="32" color="#666"></svg-icon>
          </view>
        </view>
        <view class="menu-list">
          <view 
            v-for="(menuItem, menuIndex) in moreMenuList" 
            :key="menuIndex"
            class="menu-item"
            @click="navigateToPage(menuItem.pagePath)"
          >
            <svg-icon :name="menuItem.iconPath" type="svg" :size="24" color="#333333"></svg-icon>
            <text class="menu-item-text">{{ menuItem.text }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CustomTabBar',
  data() {
    return {
      current: 0,
      color: '#333333',
      activeColor: '#007AFF',
      showMoreMenu: false,
      tabList: [
        {
          pagePath: '/pages/dashboard/main-dashboard',
          text: '首页',
          iconPath: 'dashboard',
          selectedIconPath: 'dashboard'
        },
        {
          pagePath: '/pages/customers/customer-list',
          text: '客户',
          iconPath: 'customer',
          selectedIconPath: 'customer'
        },
        {
          pagePath: '/pages/sales/opportunity-list',
          text: '销售',
          iconPath: 'sales',
          selectedIconPath: 'sales'
        },
        // {
        //   pagePath: '/pages/actions/action-list',
        //   text: '计划',
        //   iconPath: 'calendar',
        //   selectedIconPath: 'calendar'
        // },
        {
          type: 'more',
          text: '更多',
          iconPath: 'more',
          selectedIconPath: 'more'
        },
        {
          pagePath: '/pages/settings/profile',
          text: '我的',
          iconPath: 'user',
          selectedIconPath: 'user'
        }
      ],
      moreMenuList: [
        {
          pagePath: '/pages/marketing/leads',
          text: '线索',
          iconPath: 'lead'
        },
        {
          pagePath: '/pages/interactions/interaction-list',
          text: '沟通',
          iconPath: 'communication'
        },
        {
          pagePath: '/pages/sales/quotation-list',
          text: '报价',
          iconPath: 'quotation'
        },
        {
          pagePath: '/pages/contracts/contract-list',
          text: '合同',
          iconPath: 'contract'
        },
        {
          pagePath: '/pages/contracts/invoice-list',
          text: '发票',
          iconPath: 'file-text'
        },
        {
          pagePath: '/pages/contracts/payment-list',
          text: '收款',
          iconPath: 'money'
        },
        {
          pagePath: '/pages/reports/report-list',
          text: '报表',
          iconPath: 'report'
        }
      ]
    }
  },
  created() {
    this.updateCurrentTab();
  },
  onLoad() {
    // 在组件加载时设置初始状态
    this.updateCurrentTab();
  },
  onShow() {
    // 在页面显示时主动更新当前选项卡
    setTimeout(() => {
      this.updateCurrentTab();
    }, 100); // 延迟一点执行，确保路由已更新
  },
  methods: {
    updateCurrentTab() {
      try {
        const pages = getCurrentPages();
        const page = pages[pages.length - 1];
        if (!page || !page.route) return;
        
        const route = page.route;
        console.log('当前路由:', route);
        // 根据路由路径确定选中的选项卡
        if (route.includes('/pages/dashboard/')) {
          this.current = 0; // 首页
        } else if (route.includes('/pages/customers/')) {
          this.current = 1; // 客户
        } else if (route.includes('/pages/sales/')) {
          this.current = 2; // 销售
        } else if (route.includes('/pages/actions/')) {
          this.current = 3; // 计划
        } else if (route.includes('/pages/settings/')) {
          this.current = 5; // 我的
        }
      } catch (e) {
        console.error('更新Tab出错:', e);
      }
    },
    handleTabClick(item, index) {
      if (item.type === 'more') {
        this.toggleMoreMenu();
        this.current = index;
      } else {
        this.switchTab(item.pagePath, index);
      }
    },
    switchTab(path, index) {
      if (this.current !== index) {
        this.current = index;
        uni.switchTab({
          url: path
        });
      }
    },
    toggleMoreMenu() {
      this.showMoreMenu = !this.showMoreMenu;
    },
    closeMoreMenu() {
      this.showMoreMenu = false;
    },
    navigateToPage(path) {
      // 检查路径是否是主tab页面
      const isTabPage = this.tabList.some(item => item.pagePath === path);
      
      if (isTabPage) {
        uni.switchTab({
          url: path
        });
        
        // 更新当前选中的tab
        const index = this.tabList.findIndex(item => item.pagePath === path);
        if (index !== -1) {
          this.current = index;
        }
      } else {
        uni.navigateTo({
          url: path
        });
      }
      
      this.closeMoreMenu();
    }
  },
  // 监听页面路由变化
  watch: {
    $route: {
      handler() {
        this.updateCurrentTab();
      },
      immediate: true
    }
  }
}
</script>

<style>
.custom-tab-bar {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: #FFFFFF;
  box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.1);
  height: 100rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  padding: 10rpx 0;
}

.tab-text {
  font-size: 22rpx;
  color: #333333;
  margin-top: 4rpx;
}

.active-text {
  color: #007AFF;
}

/* 更多菜单样式 */
.more-menu {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
}

.menu-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.menu-content {
  position: absolute;
  bottom: 100rpx;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  overflow: hidden;
  animation: slideUp 0.3s ease;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.menu-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.menu-close {
  padding: 10rpx;
}

.menu-list {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
}

.menu-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
}

.menu-item-text {
  font-size: 24rpx;
  color: #333;
  margin-top: 10rpx;
  text-align: center;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
</style> 
<template>
  <view class="auth-container">
    <view class="auth-header">
      <view class="logo">CRM</view>
      <text class="welcome-text">找回密码</text>
      <text class="sub-text">请输入您的手机号以重置密码</text>
    </view>
    
    <view class="auth-form">
      <view class="step-indicator">
        <view class="step" :class="{ 'active': currentStep >= 1 }">
          <view class="step-number">1</view>
          <text class="step-text">身份验证</text>
        </view>
        <view class="step-line"></view>
        <view class="step" :class="{ 'active': currentStep >= 2 }">
          <view class="step-number">2</view>
          <text class="step-text">重置密码</text>
        </view>
        <view class="step-line"></view>
        <view class="step" :class="{ 'active': currentStep >= 3 }">
          <view class="step-number">3</view>
          <text class="step-text">完成</text>
        </view>
      </view>
      
      <!-- 步骤1：手机号验证 -->
      <view v-if="currentStep === 1">
        <view class="form-group">
          <text class="form-label">手机号</text>
          <input type="number" class="form-input" placeholder="请输入手机号" v-model="formData.phone" maxlength="11" />
        </view>
        
        <view class="form-group">
          <text class="form-label">验证码</text>
          <view class="code-input-group">
            <input type="number" class="form-input" placeholder="请输入验证码" v-model="formData.code" maxlength="6" />
            <button class="code-btn" :disabled="isCountdownActive" @tap="getVerificationCode">
              {{ countdownText }}
            </button>
          </view>
        </view>
        
        <button class="btn btn-primary btn-full" :disabled="!canProceedToStep2" @tap="goToStep2">下一步</button>
      </view>
      
      <!-- 步骤2：重置密码 -->
      <view v-if="currentStep === 2">
        <view class="form-group">
          <text class="form-label">新密码</text>
          <view class="password-input-group">
            <input :type="passwordVisible ? 'text' : 'password'" class="form-input" placeholder="请设置8-20位密码" v-model="formData.password" />
            <text class="password-toggle" @tap="togglePasswordVisibility">
              <text v-if="passwordVisible" class="ri-eye-off-line"></text>
              <text v-else class="ri-eye-line"></text>
            </text>
          </view>
        </view>
        
        <view class="form-group">
          <text class="form-label">确认密码</text>
          <view class="password-input-group">
            <input :type="confirmPasswordVisible ? 'text' : 'password'" class="form-input" placeholder="请再次输入密码" v-model="formData.confirmPassword" />
            <text class="password-toggle" @tap="toggleConfirmPasswordVisibility">
              <text v-if="confirmPasswordVisible" class="ri-eye-off-line"></text>
              <text v-else class="ri-eye-line"></text>
            </text>
          </view>
        </view>
        
        <view class="password-rules">
          <text class="password-rule-title">密码规则:</text>
          <text class="password-rule-item">• 包含大小写字母、数字</text>
          <text class="password-rule-item">• 长度在8-20个字符之间</text>
          <text class="password-rule-item">• 不包含连续重复字符</text>
        </view>
        
        <button class="btn btn-primary btn-full" :disabled="!canProceedToStep3" @tap="goToStep3">重置密码</button>
      </view>
      
      <!-- 步骤3：完成 -->
      <view v-if="currentStep === 3" class="success-container">
        <view class="success-icon">
          <text class="ri-check-line"></text>
        </view>
        <text class="success-title">密码重置成功</text>
        <text class="success-message">您已成功重置密码，请使用新密码登录</text>
        <button class="btn btn-primary btn-full" @tap="goToLogin">返回登录</button>
      </view>
    </view>
    
    <view class="auth-footer" v-if="currentStep < 3">
      <text class="auth-footer-text">
        记起密码了？ 
      </text>
      <navigator url="/pages/auth/login" class="auth-footer-link">返回登录</navigator>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentStep: 1,
      passwordVisible: false,
      confirmPasswordVisible: false,
      countdown: 0,
      formData: {
        phone: '',
        code: '',
        password: '',
        confirmPassword: ''
      }
    }
  },
  computed: {
    isCountdownActive() {
      return this.countdown > 0
    },
    countdownText() {
      return this.isCountdownActive ? `${this.countdown}秒后重新获取` : '获取验证码'
    },
    canProceedToStep2() {
      return this.formData.phone && 
             this.formData.phone.length === 11 && 
             this.formData.code && 
             this.formData.code.length === 6
    },
    canProceedToStep3() {
      return this.formData.password && 
             this.formData.password.length >= 8 && 
             this.formData.confirmPassword &&
             this.formData.password === this.formData.confirmPassword
    }
  },
  methods: {
    togglePasswordVisibility() {
      this.passwordVisible = !this.passwordVisible
    },
    toggleConfirmPasswordVisibility() {
      this.confirmPasswordVisible = !this.confirmPasswordVisible
    },
    getVerificationCode() {
      if (!this.formData.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return
      }
      
      if (this.formData.phone.length !== 11) {
        uni.showToast({
          title: '请输入有效的手机号',
          icon: 'none'
        })
        return
      }
      
      // 开始倒计时
      this.countdown = 60
      this.countdownTimer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(this.countdownTimer)
        }
      }, 1000)
      
      // 这里应该调用发送验证码的接口
      uni.showToast({
        title: '验证码已发送',
        icon: 'success'
      })
    },
    goToStep2() {
      if (!this.canProceedToStep2) {
        return
      }
      
      // 实际项目中这里应该验证验证码是否正确
      uni.showLoading({
        title: '验证中...'
      })
      
      setTimeout(() => {
        uni.hideLoading()
        this.currentStep = 2
      }, 1000)
    },
    goToStep3() {
      if (!this.canProceedToStep3) {
        return
      }
      
      if (this.formData.password !== this.formData.confirmPassword) {
        uni.showToast({
          title: '两次输入的密码不一致',
          icon: 'none'
        })
        return
      }
      
      // 实际项目中这里应该调用重置密码的接口
      uni.showLoading({
        title: '重置中...'
      })
      
      setTimeout(() => {
        uni.hideLoading()
        this.currentStep = 3
      }, 1500)
    },
    goToLogin() {
      uni.redirectTo({
        url: '/pages/auth/login'
      })
    }
  },
  beforeDestroy() {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
    }
  }
}
</script>

<style>
.auth-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 30rpx;
  background-color: #ffffff;
}

.auth-header {
  text-align: center;
  margin: 40rpx 0;
}

.logo {
  font-size: 80rpx;
  font-weight: bold;
  color: #3a86ff;
  margin-bottom: 20rpx;
}

.welcome-text {
  font-size: 48rpx;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}

.sub-text {
  font-size: 32rpx;
  color: #666666;
  display: block;
}

.step-indicator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 40rpx 0;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  background-color: #e0e0e0;
  color: #666666;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.step.active .step-number {
  background-color: #3a86ff;
  color: #ffffff;
}

.step-text {
  font-size: 24rpx;
  color: #666666;
}

.step.active .step-text {
  color: #3a86ff;
  font-weight: 500;
}

.step-line {
  height: 2rpx;
  background-color: #e0e0e0;
  flex: 1;
  margin: 0 10rpx;
}

.auth-form {
  margin-top: 20rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  margin-bottom: 10rpx;
  color: #333333;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 90rpx;
  padding: 0 30rpx;
  border: 1px solid #dddddd;
  border-radius: 12rpx;
  font-size: 32rpx;
  box-sizing: border-box;
}

.code-input-group {
  display: flex;
  align-items: center;
}

.code-input-group .form-input {
  flex: 1;
}

.code-btn {
  width: 220rpx;
  height: 90rpx;
  margin-left: 20rpx;
  background-color: #e6f0ff;
  color: #3a86ff;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.code-btn[disabled] {
  background-color: #f0f0f0;
  color: #999999;
}

.password-input-group {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #666666;
}

.password-rules {
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
}

.password-rule-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}

.password-rule-item {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 5rpx;
  display: block;
}

.success-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 60rpx;
}

.success-icon {
  width: 120rpx;
  height: 120rpx;
  background-color: #e6f0ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.success-icon text {
  font-size: 60rpx;
  color: #3a86ff;
}

.success-title {
  font-size: 40rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.success-message {
  font-size: 32rpx;
  color: #666666;
  text-align: center;
  margin-bottom: 50rpx;
}

.btn {
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.btn-primary {
  background-color: #3a86ff;
  color: #ffffff;
}

.btn-primary[disabled] {
  background-color: #a6c8ff;
}

.btn-full {
  width: 100%;
}

.auth-footer {
  text-align: center;
  margin-top: auto;
  padding: 40rpx 0;
  display: flex;
  justify-content: center;
}

.auth-footer-text {
  color: #666666;
  font-size: 28rpx;
}

.auth-footer-link {
  color: #3a86ff;
  font-size: 28rpx;
  font-weight: 500;
  margin-left: 10rpx;
}
</style> 
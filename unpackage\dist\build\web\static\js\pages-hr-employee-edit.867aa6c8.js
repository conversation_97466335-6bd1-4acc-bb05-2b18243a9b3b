(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-hr-employee-edit"],{"07e0":function(t,a,e){var i=e("c86c");a=i(!1),a.push([t.i,'.container[data-v-277ca04e]{background-color:#f5f7fa;min-height:100vh}.page-header[data-v-277ca04e]{display:flex;align-items:center;padding:%?20?% %?30?%;background-color:#fff;position:relative;border-bottom:%?1?% solid #eaeaea}.back-button[data-v-277ca04e]{font-size:%?40?%;color:#333;padding:%?10?%}.page-title[data-v-277ca04e]{flex:1;text-align:center;font-size:%?36?%;font-weight:500;color:#333}.header-actions[data-v-277ca04e]{display:flex;align-items:center}.action-button[data-v-277ca04e]{background:none;border:none;font-size:%?32?%;color:#4a6fff;padding:%?10?%}.form-container[data-v-277ca04e]{padding-bottom:%?120?%;height:calc(100vh - %?100?%)}.form-section[data-v-277ca04e]{margin:%?20?% %?30?%;border-radius:%?20?%;background-color:#fff;box-shadow:0 %?2?% %?10?% rgba(0,0,0,.05);overflow:hidden}.section-header[data-v-277ca04e]{display:flex;justify-content:space-between;align-items:center;padding:%?20?% %?30?%;border-bottom:%?1?% solid #eaeaea}.section-title[data-v-277ca04e]{font-size:%?30?%;font-weight:500;color:#333}.section-hint[data-v-277ca04e]{font-size:%?24?%;color:#999}.required-hint[data-v-277ca04e]{font-size:%?24?%;color:#ff4d4f}.form-group[data-v-277ca04e]{padding:%?20?% %?30?%;border-bottom:%?1?% solid #f0f0f0}.form-group[data-v-277ca04e]:last-child{border-bottom:none}.form-label[data-v-277ca04e]{display:block;font-size:%?28?%;color:#666;margin-bottom:%?15?%}.required[data-v-277ca04e]:after{content:" *";color:#ff4d4f}.form-input[data-v-277ca04e]{width:100%;height:%?80?%;border:%?1?% solid #dcdfe6;border-radius:%?8?%;padding:0 %?20?%;font-size:%?28?%;color:#333;background-color:#fff}.form-input.error[data-v-277ca04e]{border-color:#ff4d4f}.form-textarea[data-v-277ca04e]{width:100%;height:%?180?%;border:%?1?% solid #dcdfe6;border-radius:%?8?%;padding:%?20?%;font-size:%?28?%;color:#333;background-color:#fff}.error-message[data-v-277ca04e]{display:block;font-size:%?24?%;color:#ff4d4f;margin-top:%?10?%}.avatar-upload[data-v-277ca04e]{display:flex;flex-direction:column;align-items:center;margin:%?10?% 0 %?30?%}.avatar-preview[data-v-277ca04e]{width:%?160?%;height:%?160?%;border-radius:%?80?%;overflow:hidden;background-color:#f0f2f5;display:flex;justify-content:center;align-items:center;margin-bottom:%?15?%}.avatar-preview uni-image[data-v-277ca04e]{width:100%;height:100%}.avatar-placeholder[data-v-277ca04e]{font-size:%?80?%;color:#bbbec4}.upload-hint[data-v-277ca04e]{font-size:%?24?%;color:#999}.radio-group[data-v-277ca04e]{display:flex;align-items:center}.radio-item[data-v-277ca04e]{display:flex;align-items:center;margin-right:%?60?%}.radio-button[data-v-277ca04e]{width:%?36?%;height:%?36?%;border-radius:50%;border:%?1?% solid #dcdfe6;margin-right:%?10?%;position:relative;background-color:#fff}.radio-button.active[data-v-277ca04e]{border-color:#4a6fff}.radio-button.active[data-v-277ca04e]:after{content:"";position:absolute;width:%?20?%;height:%?20?%;border-radius:50%;background-color:#4a6fff;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.radio-label[data-v-277ca04e]{font-size:%?28?%;color:#333}.form-picker[data-v-277ca04e]{width:100%}.picker-value[data-v-277ca04e]{display:flex;justify-content:space-between;align-items:center;height:%?80?%;border:%?1?% solid #dcdfe6;border-radius:%?8?%;padding:0 %?20?%;font-size:%?28?%;color:#333;background-color:#fff}.placeholder[data-v-277ca04e]{color:#999}.picker-icon[data-v-277ca04e]{font-size:%?32?%;color:#999}.history-hint[data-v-277ca04e]{display:flex;align-items:center;padding:%?30?%;background-color:#f9f9fc}.history-icon[data-v-277ca04e]{font-size:%?40?%;color:#4a6fff;margin-right:%?15?%}.history-text[data-v-277ca04e]{font-size:%?28?%;color:#666}.form-actions[data-v-277ca04e]{display:flex;justify-content:space-between;padding:%?30?%;margin-bottom:%?30?%}.btn-cancel[data-v-277ca04e]{width:48%;height:%?88?%;line-height:%?88?%;text-align:center;border-radius:%?44?%;color:#666;background-color:#f5f7fa;border:%?1?% solid #dcdfe6;font-size:%?30?%}.btn-submit[data-v-277ca04e]{width:48%;height:%?88?%;line-height:%?88?%;text-align:center;border-radius:%?44?%;color:#fff;background-color:#4a6fff;font-size:%?30?%}',""]),t.exports=a},"29f3":function(t,a,e){"use strict";e.r(a);var i=e("b101"),o=e("ad1d");for(var s in o)["default"].indexOf(s)<0&&function(t){e.d(a,t,(function(){return o[t]}))}(s);e("ed66");var r=e("828b"),n=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,"277ca04e",null,!1,i["a"],void 0);a["default"]=n.exports},3803:function(t,a,e){var i=e("07e0");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=e("967d").default;o("0393fdca",i,!0,{sourceMap:!1,shadowMode:!1})},ad1d:function(t,a,e){"use strict";e.r(a);var i=e("feff"),o=e.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){e.d(a,t,(function(){return i[t]}))}(s);a["default"]=o.a},b101:function(t,a,e){"use strict";e.d(a,"b",(function(){return i})),e.d(a,"c",(function(){return o})),e.d(a,"a",(function(){}));var i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("v-uni-view",{staticClass:"container"},[e("v-uni-view",{staticClass:"page-header"},[e("v-uni-view",{staticClass:"back-button",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.goBack.apply(void 0,arguments)}}},[e("v-uni-text",{staticClass:"ri-arrow-left-line"})],1),e("v-uni-text",{staticClass:"page-title"},[t._v("编辑员工")]),e("v-uni-view",{staticClass:"header-actions"},[e("v-uni-button",{staticClass:"action-button submit-button",attrs:{type:"button"},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.submitForm.apply(void 0,arguments)}}},[t._v("保存")])],1)],1),e("v-uni-scroll-view",{staticClass:"form-container",attrs:{"scroll-y":!0}},[e("v-uni-view",{staticClass:"form-section"},[e("v-uni-view",{staticClass:"section-header"},[e("v-uni-text",{staticClass:"section-title"},[t._v("基本信息")]),e("v-uni-text",{staticClass:"required-hint"},[t._v("* 为必填项")])],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-view",{staticClass:"avatar-upload"},[e("v-uni-view",{staticClass:"avatar-preview",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.chooseAvatar.apply(void 0,arguments)}}},[t.formData.avatar?e("v-uni-image",{attrs:{src:t.formData.avatar,mode:"aspectFill"}}):e("v-uni-text",{staticClass:"ri-user-line avatar-placeholder"})],1),e("v-uni-text",{staticClass:"upload-hint"},[t._v("点击更换头像")])],1)],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label required"},[t._v("姓名")]),e("v-uni-input",{staticClass:"form-input",class:{error:t.errors.name},attrs:{type:"text",placeholder:"请输入员工姓名"},model:{value:t.formData.name,callback:function(a){t.$set(t.formData,"name",a)},expression:"formData.name"}}),t.errors.name?e("v-uni-text",{staticClass:"error-message"},[t._v(t._s(t.errors.name))]):t._e()],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label required"},[t._v("工号")]),e("v-uni-input",{staticClass:"form-input",class:{error:t.errors.employeeId},attrs:{type:"text",placeholder:"请输入员工工号"},model:{value:t.formData.employeeId,callback:function(a){t.$set(t.formData,"employeeId",a)},expression:"formData.employeeId"}}),t.errors.employeeId?e("v-uni-text",{staticClass:"error-message"},[t._v(t._s(t.errors.employeeId))]):t._e()],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label required"},[t._v("手机号码")]),e("v-uni-input",{staticClass:"form-input",class:{error:t.errors.phone},attrs:{type:"number",placeholder:"请输入手机号码"},model:{value:t.formData.phone,callback:function(a){t.$set(t.formData,"phone",a)},expression:"formData.phone"}}),t.errors.phone?e("v-uni-text",{staticClass:"error-message"},[t._v(t._s(t.errors.phone))]):t._e()],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("邮箱")]),e("v-uni-input",{staticClass:"form-input",class:{error:t.errors.email},attrs:{type:"text",placeholder:"请输入邮箱地址"},model:{value:t.formData.email,callback:function(a){t.$set(t.formData,"email",a)},expression:"formData.email"}}),t.errors.email?e("v-uni-text",{staticClass:"error-message"},[t._v(t._s(t.errors.email))]):t._e()],1)],1),e("v-uni-view",{staticClass:"form-section"},[e("v-uni-view",{staticClass:"section-header"},[e("v-uni-text",{staticClass:"section-title"},[t._v("工作信息")])],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label required"},[t._v("部门")]),e("v-uni-picker",{staticClass:"form-picker",attrs:{mode:"selector",range:t.departments,"range-key":"name"},on:{change:function(a){arguments[0]=a=t.$handleEvent(a),t.onDepartmentChange.apply(void 0,arguments)}}},[e("v-uni-view",{staticClass:"picker-value"},[t.formData.department?e("v-uni-text",[t._v(t._s(t.formData.department))]):e("v-uni-text",{staticClass:"placeholder"},[t._v("请选择部门")]),e("v-uni-text",{staticClass:"ri-arrow-down-s-line picker-icon"})],1)],1),t.errors.department?e("v-uni-text",{staticClass:"error-message"},[t._v(t._s(t.errors.department))]):t._e()],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label required"},[t._v("职位")]),e("v-uni-input",{staticClass:"form-input",class:{error:t.errors.position},attrs:{type:"text",placeholder:"请输入职位名称"},model:{value:t.formData.position,callback:function(a){t.$set(t.formData,"position",a)},expression:"formData.position"}}),t.errors.position?e("v-uni-text",{staticClass:"error-message"},[t._v(t._s(t.errors.position))]):t._e()],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label required"},[t._v("员工状态")]),e("v-uni-view",{staticClass:"radio-group"},[e("v-uni-view",{staticClass:"radio-item",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.formData.status="active"}}},[e("v-uni-view",{staticClass:"radio-button",class:{active:"active"===t.formData.status}}),e("v-uni-text",{staticClass:"radio-label"},[t._v("在职")])],1),e("v-uni-view",{staticClass:"radio-item",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.formData.status="inactive"}}},[e("v-uni-view",{staticClass:"radio-button",class:{active:"inactive"===t.formData.status}}),e("v-uni-text",{staticClass:"radio-label"},[t._v("离职")])],1)],1)],1)],1),e("v-uni-view",{staticClass:"form-section"},[e("v-uni-view",{staticClass:"section-header"},[e("v-uni-text",{staticClass:"section-title"},[t._v("备注")])],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-textarea",{staticClass:"form-textarea",attrs:{placeholder:"添加员工相关备注信息"},model:{value:t.formData.notes,callback:function(a){t.$set(t.formData,"notes",a)},expression:"formData.notes"}})],1)],1),e("v-uni-view",{staticClass:"form-section"},[e("v-uni-view",{staticClass:"section-header"},[e("v-uni-text",{staticClass:"section-title"},[t._v("操作记录")]),e("v-uni-text",{staticClass:"section-hint"},[t._v("此次修改将创建新的操作记录")])],1),e("v-uni-view",{staticClass:"history-hint"},[e("v-uni-text",{staticClass:"history-icon ri-history-line"}),e("v-uni-text",{staticClass:"history-text"},[t._v("保存修改后，系统将自动记录本次变更")])],1)],1),e("v-uni-view",{staticClass:"form-actions"},[e("v-uni-button",{staticClass:"btn-cancel",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.goBack.apply(void 0,arguments)}}},[t._v("取消")]),e("v-uni-button",{staticClass:"btn-submit",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.submitForm.apply(void 0,arguments)}}},[t._v("保存")])],1)],1)],1)},o=[]},ed66:function(t,a,e){"use strict";var i=e("3803"),o=e.n(i);o.a},feff:function(t,a,e){"use strict";e("6a54"),Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0,e("d4b5"),e("5c47"),e("0506"),e("aa9c"),e("c223");var i={data:function(){return{id:"",formData:{name:"",employeeId:"",phone:"",email:"",department:"",position:"",status:"active",avatar:"",notes:""},errors:{},departments:[{id:"1",name:"销售部"},{id:"2",name:"技术部"},{id:"3",name:"市场部"},{id:"4",name:"财务部"},{id:"5",name:"人事部"}],originalData:null}},onLoad:function(t){t.id&&(this.id=t.id,this.fetchEmployeeData(this.id))},methods:{fetchEmployeeData:function(t){var a={id:"1",name:"张三",employeeId:"EMP001",phone:"13812345678",email:"<EMAIL>",department:"销售部",position:"销售经理",status:"active",avatar:"/static/images/avatars/avatar1.png",notes:"优秀员工，负责华东区销售业务"};this.originalData=JSON.parse(JSON.stringify(a)),this.formData=a},goBack:function(){uni.navigateBack()},chooseAvatar:function(){var t=this;uni.chooseImage({count:1,sizeType:["compressed"],sourceType:["album","camera"],success:function(a){t.formData.avatar=a.tempFilePaths[0]}})},onDepartmentChange:function(t){var a=t.detail.value;this.formData.department=this.departments[a].name},validateForm:function(){this.errors={};var t=!0;return this.formData.name||(this.errors.name="请输入员工姓名",t=!1),this.formData.employeeId||(this.errors.employeeId="请输入员工工号",t=!1),this.formData.phone?/^1\d{10}$/.test(this.formData.phone)||(this.errors.phone="请输入有效的手机号码",t=!1):(this.errors.phone="请输入手机号码",t=!1),this.formData.email&&!/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(this.formData.email)&&(this.errors.email="请输入有效的邮箱地址",t=!1),this.formData.department||(this.errors.department="请选择部门",t=!1),this.formData.position||(this.errors.position="请输入职位名称",t=!1),t},getChangedFields:function(){var t=[];if(this.originalData.name!==this.formData.name&&t.push('姓名从"'.concat(this.originalData.name,'"修改为"').concat(this.formData.name,'"')),this.originalData.position!==this.formData.position&&t.push('职位从"'.concat(this.originalData.position,'"修改为"').concat(this.formData.position,'"')),this.originalData.department!==this.formData.department&&t.push('部门从"'.concat(this.originalData.department,'"修改为"').concat(this.formData.department,'"')),this.originalData.status!==this.formData.status){var a={active:"在职",inactive:"离职"};t.push('状态从"'.concat(a[this.originalData.status],'"修改为"').concat(a[this.formData.status],'"'))}return this.originalData.phone!==this.formData.phone&&t.push('电话从"'.concat(this.originalData.phone,'"修改为"').concat(this.formData.phone,'"')),this.originalData.email!==this.formData.email&&t.push('邮箱从"'.concat(this.originalData.email,'"修改为"').concat(this.formData.email,'"')),t},submitForm:function(){if(this.validateForm()){var t=this.getChangedFields();t.length>0?(console.log("变更记录:",t),uni.showToast({title:"保存成功",icon:"success",duration:2e3,success:function(){setTimeout((function(){uni.navigateBack()}),2e3)}})):uni.showToast({title:"未检测到变更",icon:"none",duration:2e3})}else uni.showToast({title:"请填写必填项",icon:"none"})}}};a.default=i}}]);
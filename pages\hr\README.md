# 人力资源管理模块

## 模块概述

人力资源管理模块是CRM系统的重要组成部分，主要负责公司内部员工信息管理、部门组织架构、绩效考核、考勤记录、薪资管理、招聘流程和培训发展等功能。该模块旨在提高人力资源管理效率，优化人才配置，为企业发展提供人才支持。

## 功能列表

### 已完成功能

- **员工管理**：基本信息录入、查询、编辑和详情展示
- **员工绩效**：绩效评估数据展示和评分
- **部门管理**：部门组织架构展示和管理
- **员工详情**：包含个人信息、工作信息、技能证书等

### 开发中功能

- **考勤记录**：员工出勤记录管理和统计
- **薪资管理**：员工薪资发放和查询
- **招聘管理**：招聘流程跟踪和人才库管理
- **培训发展**：员工培训计划和发展路径
- **HR分析**：人力资源数据分析和报表

## 页面说明

- `hr-index.vue`: HR模块首页，展示概览数据和功能入口
- `icon-demo.vue`: 图标展示页面，用于开发参考
- `employee-list.vue`: 员工列表页面
- `employee-detail.vue`: 员工详情页面
- `employee-edit.vue`: 员工编辑页面
- `employee-create.vue`: 新增员工页面
- `employee-performance.vue`: 员工绩效页面
- `department-management.vue`: 部门管理页面

## 技术实现

- 使用Vue.js + uni-app框架开发
- 响应式布局，适配多种移动设备
- SVG图标资源位于`/static/icons/`目录
- 本地存储和模拟数据用于开发测试

## 使用说明

1. 进入HR模块首页可查看概览数据
2. 通过功能图标进入各个功能模块
3. 员工管理支持查询、筛选、详情查看和编辑
4. 绩效页面展示员工KPI完成情况和技能评分
5. 部门管理页面可查看组织架构

## 后续计划

- 完善考勤管理功能
- 开发薪资管理模块
- 实现招聘管理功能
- 增加培训发展追踪
- 优化数据分析和报表展示
import { request }  from '@/utils/request';

// 获取商机列表
export const getBusinessList = (data) => {
  return request({
    url: '/api/crm/business/getList',
    method: 'POST',
    data: data
  });
};
// 新增商机
export const AddNewBusiness = (data) => {
  return request({
    url: '/api/crm/business/create',
    method: 'POST',
    data: data
  });
};
// 删除商机
export const deleteBusiness = (id) => {
  return request({
    url: `/api/crm/business/delete?id=${id}`,
    method: 'POST',
  });
};
// 获取商机详情
export const getBusinessDetail = (id) => {
  return request({
    url: `/api/crm/business/getBusinessById?id=${id}`,
    method: 'GET',
  });
};
// 编辑商机
export const UpdateBusiness = (id, data) => {
  return request({
    url: `/api/crm/business/update?id=${id}`,
    method: 'POST',
    data: data
  });
};
// 获取所有公司列表
export const getAllCompanyList = () => {
  return request({
    url: '/api/crm/business/getAllCompanys',
    method: 'GET',
  });
};
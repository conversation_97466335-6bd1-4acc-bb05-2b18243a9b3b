<template>
  <view class="contract-create-page">
    <!-- 头部 -->
    <view class="header">
      <view class="left" @click="goBack">
        <svg-icon name="back" type="svg" size="32" color="#FFFFFF"></svg-icon>
      </view>
      <view class="title">新建合同</view>
      <view class="right"></view>
    </view>

    <!-- 步骤指示器 -->
    <view class="steps-indicator">
      <view
        v-for="(step, index) in steps"
        :key="index"
        class="step"
        :class="{
          active: currentStep === index + 1,
          completed: currentStep > index + 1,
        }"
        @click="goToStep(index + 1)"
      >
        <view class="step-number">{{ index + 1 }}</view>
        <view class="step-name">{{ step }}</view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="content-container">
      <!-- 步骤1: 基本信息 -->
      <view class="step-container" v-if="currentStep === 1">
        <!-- 必填项 -->
        <view class="form-section">
          <view class="section-title">
            <svg-icon
              name="info"
              type="svg"
              size="32"
              color="#3a86ff"
            ></svg-icon>
            <text>基本信息</text>
          </view>
          <view class="form-group">
            <view class="form-item">
              <text class="label required">合同名称</text>
              <input
                type="text"
                v-model="formData.title"
                placeholder="请输入合同名称"
              />
            </view>
            <view class="form-item">
              <text class="label required">客户名称</text>
              <view class="selector" @click="selectCustomer">
                <text v-if="formData.customer">{{
                  formData.customer.name
                }}</text>
                <text v-else class="placeholder">请选择客户</text>
                <svg-icon
                  name="right"
                  type="svg"
                  size="28"
                  color="#999999"
                ></svg-icon>
              </view>
            </view>
            <view class="form-item">
              <text class="label required">合同类型</text>
              <picker
                mode="selector"
                :range="contractTypes"
                range-key="displayText"
                @change="onContractTypeChange"
              >
                <view class="picker-view">
                  <text v-if="formData.contractType">{{
                    formData.contractType
                  }}</text>
                  <text v-else class="placeholder">请选择合同类型</text>
                  <svg-icon
                    name="right"
                    type="svg"
                    size="28"
                    color="#999999"
                  ></svg-icon>
                </view>
              </picker>
            </view>
            <view class="form-item">
              <text class="label required">签约日期</text>
              <picker
                mode="date"
                :value="formData.signingDate"
                @change="onSigningDateChange"
              >
                <view class="picker-view">
                  <text v-if="formData.signingDate">{{
                    formData.signingDate
                  }}</text>
                  <text v-else class="placeholder">请选择签约日期</text>
                  <svg-icon
                    name="calendar"
                    type="svg"
                    size="28"
                    color="#999999"
                  ></svg-icon>
                </view>
              </picker>
            </view>
          </view>
        </view>

        <!-- 可折叠的次要信息 -->
        <view class="form-section">
          <view class="collapsible-section">
            <view class="section-header" @click="toggleMoreBasicInfo">
              <text>更多信息</text>
              <svg-icon
                :name="showMoreBasicInfo ? 'up' : 'down'"
                type="svg"
                size="28"
                color="#999999"
              ></svg-icon>
            </view>
            <view class="section-content" v-if="showMoreBasicInfo">
              <view class="form-item">
                <text class="label">公司</text>
                <picker
                  mode="selector"
                  :range="companies"
                  range-key="displayName"
                  @change="onCompanyChange"
                >
                  <view class="picker-view">
                    <text v-if="formData.company">{{ formData.company }}</text>
                    <text v-else class="placeholder">请选择公司</text>
                    <svg-icon
                      name="right"
                      type="svg"
                      size="28"
                      color="#999999"
                    ></svg-icon>
                  </view>
                </picker>
              </view>
              <view class="form-item">
                <text class="label">负责人</text>
                <picker
                  mode="selector"
                  :range="owners"
                  range-key="name"
                  @change="onOwnerChange"
                >
                  <view class="picker-view">
                    <text v-if="formData.owner">{{ formData.owner }}</text>
                    <text v-else class="placeholder">请选择负责人</text>
                    <svg-icon
                      name="right"
                      type="svg"
                      size="28"
                      color="#999999"
                    ></svg-icon>
                  </view>
                </picker>
              </view>
              <view class="form-item">
                <text class="label">关联协议</text>
                <view class="selector" @click="selectAgreement">
                  <text v-if="formData.agreement">{{
                    formData.agreement.name
                  }}</text>
                  <text v-else class="placeholder">请选择关联协议</text>
                  <svg-icon
                    name="right"
                    type="svg"
                    size="28"
                    color="#999999"
                  ></svg-icon>
                </view>
              </view>
              <view class="form-item">
                <text class="label">关联商机</text>
                <view class="selector" @click="selectOpportunity">
                  <text v-if="formData.businessName">{{
                    formData.businessName
                  }}</text>
                  <text v-else class="placeholder">请选择关联商机</text>
                  <svg-icon
                    name="right"
                    type="svg"
                    size="28"
                    color="#999999"
                  ></svg-icon>
                </view>
              </view>
              <view class="form-item">
                <text class="label">到期日期</text>
                <picker
                  mode="date"
                  :value="formData.expirationDate"
                  @change="onExpirationDateChange"
                >
                  <view class="picker-view">
                    <text v-if="formData.expirationDate">{{
                      formData.expirationDate
                    }}</text>
                    <text v-else class="placeholder">请选择到期日期</text>
                    <svg-icon
                      name="calendar"
                      type="svg"
                      size="28"
                      color="#999999"
                    ></svg-icon>
                  </view>
                </picker>
              </view>
            </view>
          </view>
        </view>

        <!-- 底部导航按钮 -->
        <view class="step-buttons">
          <button class="btn btn-primary" @click="nextStep">下一步</button>
          <button class="btn btn-outline" @click="saveDraft">保存草稿</button>
          <button class="btn btn-outline cancel" @click="cancel">取消</button>
        </view>
      </view>

      <!-- 步骤2: 现在是产品信息 -->
      <view class="step-container" v-if="currentStep === 2">
        <view class="form-section">
          <view class="section-title">
            <svg-icon
              name="product"
              type="svg"
              size="32"
              color="#3a86ff"
            ></svg-icon>
            <text>产品信息</text>
          </view>

          <!-- 产品卡片列表 -->
          <view class="product-card">
            <view class="product-items">
              <view
                v-for="(item, index) in formData.items"
                :key="index"
                class="product-item"
              >
                <view class="product-header">
                  <text class="product-title">产品 {{ index + 1 }}</text>
                  <view class="product-actions">
                    <view
                      @click="toggleProductDetails(index)"
                      class="expand-button"
                    >
                      <svg-icon
                        :name="item.expanded ? 'up' : 'down'"
                        type="svg"
                        size="24"
                        color="#999999"
                      ></svg-icon>
                    </view>
                    <view @click="deleteItem(index)" class="delete-button">
                      <svg-icon
                        name="delete"
                        type="svg"
                        size="24"
                        color="#ff4d4f"
                      ></svg-icon>
                    </view>
                  </view>
                </view>

                <view class="product-content">
                  <view class="product-row">
                    <text class="label">产品名称</text>
                    <view class="selector" @click="selectProduct(index)">
                      <text v-if="item.productName">{{
                        item.productName
                      }}</text>
                      <text v-else class="placeholder">请选择产品</text>
                      <svg-icon
                        name="right"
                        type="svg"
                        size="28"
                        color="#999999"
                      ></svg-icon>
                    </view>
                  </view>

                  <view class="product-row">
                    <text class="label">数量</text>
                    <input
                      type="number"
                      v-model="item.quantity"
                      placeholder="请输入数量"
                      @input="calculateItemAmount(index)"
                    />
                  </view>

                  <view class="product-row">
                    <text class="label">单价</text>
                    <input
                      type="number"
                      v-model="item.unitPrice"
                      placeholder="请输入单价"
                      @input="calculateItemAmount(index)"
                    />
                  </view>

                  <view class="product-row">
                    <text class="label">金额</text>
                    <text class="amount">¥{{ item.amount || "0.00" }}</text>
                  </view>

                  <!-- 展开的详细信息 -->
                  <view class="product-details" v-if="item.expanded">
                    <view class="product-row">
                      <text class="label">产品描述</text>
                      <text class="description">{{
                        item.description || "暂无描述"
                      }}</text>
                    </view>

                    <view class="product-row">
                      <text class="label">产品单位</text>
                      <text>{{ item.unit || "个" }}</text>
                    </view>

                    <view class="product-row">
                      <text class="label">税率</text>
                      <text>{{
                        item.taxRate ? item.taxRate.name : "暂无"
                      }}</text>
                    </view>

                    <view class="product-row">
                      <text class="label">未税单价</text>
                      <text>¥{{ item.netUnitPrice || "0.00" }}</text>
                    </view>

                    <view class="product-row">
                      <text class="label">含税单价</text>
                      <text>¥{{ item.taxedUnitPrice || "0.00" }}</text>
                    </view>

                    <view class="product-row">
                      <text class="label">未税金额</text>
                      <text>¥{{ item.netAmount || "0.00" }}</text>
                    </view>

                    <view class="product-row">
                      <text class="label">含税金额</text>
                      <text>¥{{ item.taxedAmount || "0.00" }}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>

            <view class="add-product" @click="addItem">
              <svg-icon
                name="add"
                type="svg"
                size="28"
                color="#3a86ff"
              ></svg-icon>
              <text>添加产品</text>
            </view>

            <view class="total-amount">
              <text>合计金额: ¥{{ totalAmount }}</text>
            </view>
          </view>
        </view>

        <!-- 底部导航按钮 -->
        <view class="step-buttons">
          <button class="btn btn-secondary" @click="prevStep">上一步</button>
          <button class="btn btn-primary" @click="nextStep">下一步</button>
          <button class="btn btn-outline" @click="saveDraft">保存草稿</button>
          <button class="btn btn-outline" @click="cancel">取消</button>
        </view>
      </view>

      <!-- 步骤3: 现在是财务信息 -->
      <view class="step-container" v-if="currentStep === 3">
        <!-- 必填项 -->
        <view class="form-section">
          <view class="section-title">
            <svg-icon
              name="money"
              type="svg"
              size="32"
              color="#3a86ff"
            ></svg-icon>
            <text>财务信息</text>
          </view>
          <view class="form-group">
            <view class="form-item">
              <text class="label required">合同金额(含税)</text>
              <input
                type="digit"
                v-model="formData.taxedAmount"
                placeholder="请输入合同金额"
                @input="calculateNetAmount"
                :disabled="formData.useProductTotal"
              />
              <view
                class="checkbox-container"
                style="margin-top: 10rpx; display: flex; align-items: center"
              >
                <checkbox
                  :checked="formData.useProductTotal"
                  @tap="toggleUseProductTotal"
                  style="transform: scale(0.8)"
                />
                <text style="font-size: 24rpx; margin-left: 10rpx; color: #666"
                  >使用产品总价</text
                >
              </view>
            </view>
            <view class="form-item">
              <text class="label">合同税率</text>
              <picker
                mode="selector"
                :range="taxRates"
                range-key="name"
                @change="onTaxRateChange"
              >
                <view class="picker-view">
                  <text v-if="formData.taxRate">{{
                    formData.taxRate.name
                  }}</text>
                  <text v-else class="placeholder">请选择税率</text>
                  <svg-icon
                    name="right"
                    type="svg"
                    size="28"
                    color="#999999"
                  ></svg-icon>
                </view>
              </picker>
            </view>
            <view class="form-item">
              <text class="label">未税金额</text>
              <input
                type="digit"
                v-model="formData.netAmount"
                placeholder="自动计算"
                disabled
              />
            </view>
          </view>
        </view>

        <!-- 可折叠的次要信息 -->
        <view class="form-section">
          <view class="collapsible-section">
            <view class="section-header" @click="toggleMoreFinancialInfo">
              <text>更多财务信息</text>
              <svg-icon
                :name="showMoreFinancialInfo ? 'up' : 'down'"
                type="svg"
                size="28"
                color="#999999"
              ></svg-icon>
            </view>
            <view class="section-content" v-if="showMoreFinancialInfo">
              <view class="form-item">
                <text class="label">资金币种</text>
                <picker
                  mode="selector"
                  :range="currencies"
                  range-key="displayText"
                  @change="onCurrencyChange"
                >
                  <view class="picker-view">
                    <text v-if="formData.currency">{{
                      formData.currency
                    }}</text>
                    <text v-else class="placeholder">请选择币种</text>
                    <svg-icon
                      name="right"
                      type="svg"
                      size="28"
                      color="#999999"
                    ></svg-icon>
                  </view>
                </picker>
              </view>
              <view class="form-item">
                <text class="label">付款比例</text>
                <picker
                  mode="selector"
                  :range="paymentRatios"
                  range-key="name"
                  @change="onPaymentRatioChange"
                >
                  <view class="picker-view">
                    <text v-if="formData.paymentRatio">{{
                      formData.paymentRatio.name
                    }}</text>
                    <text v-else class="placeholder">请选择付款比例</text>
                    <svg-icon
                      name="right"
                      type="svg"
                      size="28"
                      color="#999999"
                    ></svg-icon>
                  </view>
                </picker>
              </view>
            </view>
          </view>
        </view>

        <!-- 添加应收款数据卡片 -->
        <view class="form-section" v-if="formData.payments.length > 0">
          <view class="section-title">
            <svg-icon
              name="money"
              type="svg"
              size="32"
              color="#3a86ff"
            ></svg-icon>
            <text>应收款计划</text>
          </view>
          <view class="payment-plans">
            <view
              v-for="(payment, index) in formData.payments"
              :key="index"
              class="payment-plan-item"
            >
              <view class="payment-plan-header">
                <text class="stage">{{ payment.stage }}</text>
                <text class="ratio">{{ payment.details }}</text>
              </view>
              <view class="payment-plan-amount">
                <text class="amount-label">应收金额：</text>
                <text class="amount-value">¥{{ payment.amount }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 日期信息折叠区 -->
        <view class="form-section">
          <view class="collapsible-section">
            <view class="section-header" @click="toggleDateInfo">
              <text>日期信息</text>
              <svg-icon
                :name="showDateInfo ? 'up' : 'down'"
                type="svg"
                size="28"
                color="#999999"
              ></svg-icon>
            </view>
            <view class="section-content" v-if="showDateInfo">
              <view class="form-item">
                <text class="label">计划完工日期</text>
                <picker
                  mode="date"
                  :value="formData.plannedCompletionDate"
                  @change="onPlannedCompletionDateChange"
                >
                  <view class="picker-view">
                    <text v-if="formData.plannedCompletionDate">{{
                      formData.plannedCompletionDate
                    }}</text>
                    <text v-else class="placeholder">请选择计划完工日期</text>
                    <svg-icon
                      name="calendar"
                      type="svg"
                      size="28"
                      color="#999999"
                    ></svg-icon>
                  </view>
                </picker>
              </view>
              <view class="form-item">
                <text class="label">计划验收日期</text>
                <picker
                  mode="date"
                  :value="formData.plannedAcceptanceDate"
                  @change="onPlannedAcceptanceDateChange"
                >
                  <view class="picker-view">
                    <text v-if="formData.plannedAcceptanceDate">{{
                      formData.plannedAcceptanceDate
                    }}</text>
                    <text v-else class="placeholder">请选择计划验收日期</text>
                    <svg-icon
                      name="calendar"
                      type="svg"
                      size="28"
                      color="#999999"
                    ></svg-icon>
                  </view>
                </picker>
              </view>
              <view class="form-item">
                <text class="label">维保限计划到期日期</text>
                <picker
                  mode="date"
                  :value="formData.warrantyExpirationDate"
                  @change="onWarrantyExpirationDateChange"
                >
                  <view class="picker-view">
                    <text v-if="formData.warrantyExpirationDate">{{
                      formData.warrantyExpirationDate
                    }}</text>
                    <text v-else class="placeholder">请选择维保到期日期</text>
                    <svg-icon
                      name="calendar"
                      type="svg"
                      size="28"
                      color="#999999"
                    ></svg-icon>
                  </view>
                </picker>
              </view>
            </view>
          </view>
        </view>

        <!-- 底部导航按钮 -->
        <view class="step-buttons">
          <button class="btn btn-secondary" @click="prevStep">上一步</button>
          <button class="btn btn-primary" @click="nextStep">下一步</button>
          <button class="btn btn-outline" @click="saveDraft">保存草稿</button>
          <button class="btn btn-outline" @click="cancel">取消</button>
        </view>
      </view>

      <!-- 步骤4: 回收款项和附件 -->
      <view class="step-container" v-if="currentStep === 4">
        <!-- 回收款项 -->
        <view class="form-section">
          <view class="collapsible-section">
            <view class="section-header" @click="togglePaymentInfo">
              <text>回收款项</text>
              <svg-icon
                :name="showPaymentInfo ? 'up' : 'down'"
                type="svg"
                size="28"
                color="#999999"
              ></svg-icon>
            </view>
            <view class="section-content" v-if="showPaymentInfo">
              <view class="payment-list">
                <view class="payment-header">
                  <text class="col stage">收款阶段</text>
                  <text class="col amount">应收金额</text>
                  <text class="col date">计划日期</text>
                  <text class="col action">操作</text>
                </view>

                <view
                  v-for="(payment, index) in formData.payments"
                  :key="index"
                  class="payment-item"
                >
                  <!-- 常规显示行 -->
                  <view class="payment-row">
                    <input
                      class="col stage"
                      type="text"
                      v-model="payment.stage"
                      placeholder="收款阶段"
                    />
                    <input
                      class="col amount"
                      type="digit"
                      v-model="payment.amount"
                      placeholder="金额"
                    />
                    <picker
                      mode="date"
                      :value="payment.plannedDate"
                      @change="(e) => onPaymentDateChange(e, index)"
                      class="col date"
                    >
                      <view class="picker-view">
                        <text v-if="payment.plannedDate">{{
                          payment.plannedDate
                        }}</text>
                        <text v-else class="placeholder">选择日期</text>
                      </view>
                    </picker>
                    <view class="col action">
                      <view
                        @click="togglePaymentDetails(index)"
                        class="expand-button"
                      >
                        <svg-icon
                          :name="payment.expanded ? 'up' : 'down'"
                          type="svg"
                          size="24"
                          color="#999999"
                        ></svg-icon>
                      </view>
                      <view @click="deletePayment(index)">
                        <svg-icon
                          name="delete"
                          type="svg"
                          size="24"
                          color="#ff4d4f"
                        ></svg-icon>
                      </view>
                    </view>
                  </view>

                  <!-- 展开的详细信息 -->
                  <view class="payment-details" v-if="payment.expanded">
                    <view class="form-item">
                      <text class="label">计划收款明细</text>
                      <textarea
                        v-model="payment.details"
                        placeholder="请输入收款明细"
                      />
                    </view>
                    <view class="form-item">
                      <text class="label">开票金额</text>
                      <input
                        type="digit"
                        v-model="payment.invoiceAmount"
                        placeholder="请输入开票金额"
                      />
                    </view>
                    <view class="form-item">
                      <text class="label">实际收款金额</text>
                      <input
                        type="digit"
                        v-model="payment.actualAmount"
                        placeholder="请输入实际收款金额"
                      />
                    </view>
                    <view class="form-item">
                      <text class="label">备注</text>
                      <textarea
                        v-model="payment.remarks"
                        placeholder="请输入备注"
                      />
                    </view>
                  </view>
                </view>

                <view class="add-payment" @click="addPayment">
                  <svg-icon
                    name="add"
                    type="svg"
                    size="28"
                    color="#3a86ff"
                  ></svg-icon>
                  <text>添加收款计划</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 附件与备注 -->
        <view class="form-section">
          <view class="section-title">
            <svg-icon
              name="file"
              type="svg"
              size="32"
              color="#3a86ff"
            ></svg-icon>
            <text>附件与备注</text>
          </view>
          <view class="form-group">
            <view class="form-item">
              <text class="label">合同附件</text>
              <view class="file-uploader" @click="uploadContractFile">
                <svg-icon
                  name="upload"
                  type="svg"
                  size="32"
                  color="#999999"
                ></svg-icon>
                <text>点击上传附件</text>
              </view>
              <view
                class="file-list"
                v-if="formData.attachments && formData.attachments.length > 0"
              >
                <view
                  v-for="(file, index) in formData.attachments"
                  :key="index"
                  class="file-item"
                >
                  <text class="file-name">{{ file.name }}</text>
                  <text class="file-delete" @click="deleteFile(index)"
                    >删除</text
                  >
                </view>
              </view>
            </view>
            <view class="form-item">
              <text class="label">备注</text>
              <textarea
                v-model="formData.remarks"
                placeholder="请输入备注信息"
              />
            </view>
          </view>
        </view>

        <!-- 在第四步添加状态选择 -->
        <view class="form-section" v-if="currentStep === 4">
          <view class="section-title">
            <svg-icon
              name="status"
              type="svg"
              size="32"
              color="#3a86ff"
            ></svg-icon>
            <text>合同状态</text>
          </view>
          <view class="form-group">
            <view class="form-item">
              <text class="label">状态</text>
              <picker
                mode="selector"
                :range="contractStatus"
                range-key="name"
                @change="onStatusChange"
              >
                <view class="picker-view">
                  <text v-if="formData.status">{{ formData.status.name }}</text>
                  <text v-else class="placeholder">请选择状态</text>
                  <svg-icon
                    name="right"
                    type="svg"
                    size="28"
                    color="#999999"
                  ></svg-icon>
                </view>
              </picker>
            </view>
          </view>
        </view>

        <!-- 底部导航按钮 -->
        <view class="step-buttons" v-if="currentStep === 4">
          <button class="btn btn-secondary" @click="prevStep">上一步</button>
          <button class="btn btn-primary" @click="submit">审批</button>
          <button class="btn btn-outline" @click="saveDraft">存草稿</button>
          <button class="btn btn-outline" @click="cancel">取消</button>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import SvgIcon from "@/components/svg-icon.vue";
import getSelectOptions from "@/utils/dictionary";
import {
  getCompanyList,
  getUserList,
  getOpportunityList,
} from "@/api/contact.api";

export default {
  components: {
    SvgIcon,
  },
  data() {
    return {
      // 步骤控制
      currentStep: 1,
      steps: ["基本信息", "产品信息", "财务信息", "款项/附件"],
      opportunities: [],

      // 折叠面板控制
      showMoreBasicInfo: false,
      showMoreFinancialInfo: false,
      showDateInfo: false,
      showPaymentInfo: false,

      // 表单数据
      formData: {
        // 基本信息
        title: "", // 合同名称
        company: null, // 公司
        customer: null, // 客户
        contractType: null, // 合同类型
        agreement: null, // 关联协议
        opportunity: null, // 关联商机
        owner: null, // 负责人
        signingDate: "", // 签约日期
        expirationDate: "", // 到期日期

        // 财务信息
        currency: {},
        currencyId: null,
        taxRate: null, // 合同税率
        taxedAmount: "", // 合同金额(含税)
        netAmount: "", // 未税金额
        paymentRatio: null,
        status: null, // 状态
        useProductTotal: true, // 使用产品总价作为合同金额

        // 日期信息
        plannedCompletionDate: "", // 计划完工日期
        plannedAcceptanceDate: "", // 计划验收日期
        warrantyExpirationDate: "", // 维保到期日期

        // 商机名称和id
        businessName: "",
        businessId: "",

        // 产品信息
        items: [
          {
            productName: "",
            description: "",
            unit: "",
            quantity: "",
            unitPrice: "",
            taxRate: null,
            netUnitPrice: "",
            taxedUnitPrice: "",
            netAmount: "",
            taxedAmount: "",
            amount: "0.00",
            expanded: false, // 是否展开详情
          },
        ],

        // 回收款项
        payments: [],

        // 附件与备注
        attachments: [],
        remarks: "",
      },

      // 选项数据
      companies: [],
      contractTypes: [],
      owners: [],
      currencies: [],
      currencyId: null,
      taxRates: [
        { id: 1, name: "13%", value: 0.13 },
        { id: 2, name: "6%", value: 0.06 },
        { id: 3, name: "3%", value: 0.03 },
        { id: 4, name: "0%", value: 0 },
      ],
      productTaxRates: [
        { id: 1, name: "13%", value: 0.13 },
        { id: 2, name: "6%", value: 0.06 },
        { id: 3, name: "3%", value: 0.03 },
        { id: 4, name: "0%", value: 0 },
      ],
      contractStatus: [
        { id: 1, name: "草稿" },
        { id: 2, name: "审批中" },
        { id: 3, name: "已生效" },
        { id: 4, name: "已完成" },
        { id: 5, name: "已取消" },
      ],
      paymentRatios: [
        { id: 1, name: "50%-50%", ratios: [50, 50] },
        { id: 2, name: "30%-70%", ratios: [30, 70] },
        { id: 3, name: "40%-30%-30%", ratios: [40, 30, 30] },
        { id: 4, name: "30%-30%-30%-10%", ratios: [30, 30, 30, 10] },
      ],
    };
  },
  computed: {
    totalAmount() {
      let total = 0;
      this.formData.items.forEach((item) => {
        total += parseFloat(item.amount || 0);
      });
      return total.toFixed(2);
    },
  },
  watch: {
    // 监听totalAmount变化，如果开启了使用产品总价，则自动更新财务信息
    totalAmount: {
      handler(newVal) {
        if (this.formData.useProductTotal && newVal) {
          this.formData.taxedAmount = newVal;
          this.calculateNetAmount();
        }
      },
      immediate: true,
    },
  },
  onLoad(options) {
    // 检查并加载草稿
    this.loadDraft();
    this.loadDictionaryOptions();
    this.loadBusinessData();

    // 如果有关联的报价单ID，自动加载报价单
    if (options.quotationId) {
      this.loadQuotationData(options.quotationId);
    }

    // 初始化合同状态为草稿
    this.formData.status = this.contractStatus[0];
  },
  onShow() {
    // 检查是否有从customer-select页面选择的客户
    const selectedCustomer = uni.getStorageSync("selected_customer");
    if (selectedCustomer) {
      this.formData.customer = selectedCustomer;
      // 清除存储，避免下次进入页面时仍然使用此数据
      uni.removeStorageSync("selected_customer");
    }

    // 检查是否有从product-select页面选择的产品
    const selectedProduct = uni.getStorageSync("selected_product");
    const productIndex = uni.getStorageSync("current_product_index");

    if (selectedProduct && productIndex !== "" && productIndex !== undefined) {
      // 更新产品信息
      this.$set(this.formData.items, productIndex, {
        ...this.formData.items[productIndex],
        productId: selectedProduct.id,
        productName: selectedProduct.name,
        unit: selectedProduct.unit,
        unitPrice: selectedProduct.price,
        taxRate: selectedProduct.taxRate || this.productTaxRates[0],
        description: selectedProduct.description || "",
        // 保持原有的数量
        quantity: this.formData.items[productIndex].quantity || "1",
        // 重新计算金额
        expanded: false,
      });

      // 计算金额
      this.calculateItemAmount(productIndex);

      // 清除存储
      uni.removeStorageSync("selected_product");
      uni.removeStorageSync("current_product_index");
    }
  },
  methods: {
    // ==== 步骤控制 ====
    goToStep(step) {
      // 只允许切换到已完成或当前步骤
      if (step <= this.currentStep) {
        this.currentStep = step;
      }
    },

    // 获取数据字典
    async loadDictionaryOptions() {
      try {
        this.contractTypes = await getSelectOptions("ContractType");
        this.currencies = await getSelectOptions("CapitalType");
        this.companies = await getCompanyList();

        getUserList({ pageIndex: 1, pageSize: 9999 }).then((res) => {
          if (res.items && res.items.length > 0) {
            this.owners = res?.items?.filter((user) => user.isActive);
          }
        });
      } catch (error) {
        this.$message.error("加载字典数据失败");
      }
    },

    nextStep() {
      if (this.validateCurrentStep()) {
        this.currentStep += 1;
      }
    },

    prevStep() {
      this.currentStep -= 1;
    },

    // ==== 折叠面板控制 ====
    toggleMoreBasicInfo() {
      this.showMoreBasicInfo = !this.showMoreBasicInfo;
    },

    toggleMoreFinancialInfo() {
      this.showMoreFinancialInfo = !this.showMoreFinancialInfo;
    },

    toggleDateInfo() {
      this.showDateInfo = !this.showDateInfo;
    },

    togglePaymentInfo() {
      this.showPaymentInfo = !this.showPaymentInfo;
    },

    // ==== 基础操作 ====
    goBack() {
      uni.navigateBack();
    },

    // ==== 选择器事件 ====
    selectCustomer() {
      uni.navigateTo({
        url: "/pages/sales/customer-select",
        events: {
          updateSelectedCustomer: this.updateSelectedCustomer,
          updateCurrency: (currency, currencyId) => {
            this.formData.currency = currency;
            this.formData.currencyId = currencyId;
          },
        },
      });
    },

    selectAgreement() {
      uni.navigateTo({
        url: "/pages/contracts/agreement-select",
        events: {
          agreementSelected: (agreement) => {
            this.formData.agreement = agreement;
            this.formData.businessName = agreement.businessName;
            this.formData.businessId = agreement.businessId;
            if (agreement.businessName && agreement.businessId) {
              const business = this.opportunities.find(
                (item) => item.id === agreement.businessId
              );
              console.log("business", business);
            }
          },
        },
      });
    },

    selectOpportunity() {
      uni.navigateTo({
        url: "/pages/sales/opportunity-select",
        events: {
          opportunitySelected: (opportunity) => {
            this.formData.businessName = opportunity.name;
            this.formData.businessId = opportunity.id;
          },
        },
      });
    },

    selectQuotation() {
      uni.navigateTo({
        url: "/pages/sales/quotation-select",
        events: {
          // 选择报价单后的回调
          quotationSelected: (quotation) => {
            this.formData.quotation = quotation;
            this.loadQuotationData(quotation.id);
          },
        },
      });
    },

    // ==== 日期选择器事件 ====
    onSigningDateChange(e) {
      this.formData.signingDate = e.detail.value;
    },

    onExpirationDateChange(e) {
      this.formData.expirationDate = e.detail.value;
    },

    onPlannedCompletionDateChange(e) {
      this.formData.plannedCompletionDate = e.detail.value;
    },

    onPlannedAcceptanceDateChange(e) {
      this.formData.plannedAcceptanceDate = e.detail.value;
    },

    onWarrantyExpirationDateChange(e) {
      this.formData.warrantyExpirationDate = e.detail.value;
    },

    // ==== 下拉选择器事件 ====
    onContractTypeChange(e) {
      const index = e.detail.value;

      this.formData.contractType = this.contractTypes[index].displayText;
    },

    onCompanyChange(e) {
      const index = e.detail.value;
      this.formData.company = this.companies[index].displayName;
    },

    onOwnerChange(e) {
      const index = e.detail.value;
      this.formData.owner = this.owners[index].name;
    },

    onCurrencyChange(e) {
      const index = e.detail.value;
      console.log("onCurrencyChange", this.currencies[index]);
      this.formData.currency = this.currencies[index].displayText;
    },

    onTaxRateChange(e) {
      const index = e.detail.value;
      this.formData.taxRate = this.taxRates[index];
      this.calculateNetAmount();
    },

    onStatusChange(e) {
      const index = e.detail.value;
      this.formData.status = this.contractStatus[index];
    },

    // ==== 财务计算 ====
    calculateNetAmount() {
      if (!this.formData.taxedAmount) {
        this.formData.netAmount = "";
        return;
      }

      const taxedAmount = parseFloat(this.formData.taxedAmount || 0);
      const taxRate = this.formData.taxRate ? this.formData.taxRate.value : 0;

      // 计算未税金额 = 含税金额 / (1 + 税率)
      this.formData.netAmount = (taxedAmount / (1 + taxRate)).toFixed(2);
    },

    // ==== 产品相关方法 ====
    toggleProductDetails(index) {
      this.$set(
        this.formData.items[index],
        "expanded",
        !this.formData.items[index].expanded
      );
    },

    onProductTaxRateChange(e, index) {
      const taxRateIndex = e.detail.value;
      this.$set(
        this.formData.items[index],
        "taxRate",
        this.productTaxRates[taxRateIndex]
      );
      this.calculateItemAmounts(index);
    },

    // 选择产品
    selectProduct(index) {
      // 保存当前产品索引
      uni.setStorageSync("current_product_index", index);

      // 导航到产品选择页面
      uni.navigateTo({
        url: "/pages/products/product-select",
        events: {
          selectProduct: (product) => {
            console.log("fdfee", product);
            this.formData.items[index].productName = product.name;
            this.formData.items[index].productId = product.id;
          },
        },
      });
    },

    calculateFromNetPrice(index) {
      const item = this.formData.items[index];
      const taxRate = item.taxRate ? item.taxRate.value : 0;

      // 从未税单价计算含税单价
      if (item.netUnitPrice) {
        item.taxedUnitPrice = (
          parseFloat(item.netUnitPrice) *
          (1 + taxRate)
        ).toFixed(2);
        // 更新基本单价以保持一致
        item.unitPrice = item.taxedUnitPrice;
      }

      this.calculateItemAmounts(index);
    },

    calculateFromTaxedPrice(index) {
      const item = this.formData.items[index];
      const taxRate = item.taxRate ? item.taxRate.value : 0;

      // 从含税单价计算未税单价
      if (item.taxedUnitPrice) {
        item.netUnitPrice = (
          parseFloat(item.taxedUnitPrice) /
          (1 + taxRate)
        ).toFixed(2);
        // 更新基本单价以保持一致
        item.unitPrice = item.taxedUnitPrice;
      }

      this.calculateItemAmounts(index);
    },

    calculateItemAmount(index) {
      const item = this.formData.items[index];
      const unitPrice = parseFloat(item.unitPrice || 0);
      const quantity = parseFloat(item.quantity || 0);

      // 更新含税单价
      item.taxedUnitPrice = unitPrice.toFixed(2);

      // 更新未税单价
      const taxRate = item.taxRate ? item.taxRate.value : 0;
      item.netUnitPrice = (unitPrice / (1 + taxRate)).toFixed(2);

      // 更新金额
      item.amount = (unitPrice * quantity).toFixed(2);
      item.taxedAmount = item.amount;
      item.netAmount = (parseFloat(item.amount) / (1 + taxRate)).toFixed(2);
    },

    calculateItemAmounts(index) {
      const item = this.formData.items[index];
      const quantity = parseFloat(item.quantity || 0);
      const netUnitPrice = parseFloat(item.netUnitPrice || 0);
      const taxRate = item.taxRate ? item.taxRate.value : 0;

      // 计算未税金额和含税金额
      item.netAmount = (netUnitPrice * quantity).toFixed(2);
      item.taxedAmount = (parseFloat(item.netAmount) * (1 + taxRate)).toFixed(
        2
      );

      // 更新显示金额
      item.amount = item.taxedAmount;
    },

    addItem() {
      this.formData.items.push({
        productName: "",
        description: "",
        unit: "",
        quantity: "",
        unitPrice: "",
        taxRate: null,
        netUnitPrice: "",
        taxedUnitPrice: "",
        netAmount: "",
        taxedAmount: "",
        amount: "0.00",
        expanded: false,
      });
    },

    deleteItem(index) {
      if (this.formData.items.length > 1) {
        this.formData.items.splice(index, 1);
      } else {
        uni.showToast({
          title: "至少保留一个产品项",
          icon: "none",
        });
      }
    },

    // ==== 收款计划相关方法 ====
    togglePaymentDetails(index) {
      this.$set(
        this.formData.payments[index],
        "expanded",
        !this.formData.payments[index].expanded
      );
    },

    onPaymentDateChange(e, index) {
      this.$set(this.formData.payments[index], "plannedDate", e.detail.value);
    },

    addPayment() {
      this.formData.payments.push({
        stage: "",
        amount: "",
        plannedDate: "",
        details: "",
        invoiceAmount: "",
        actualAmount: "",
        remarks: "",
        expanded: false,
      });
    },

    deletePayment(index) {
      if (this.formData.payments.length > 1) {
        this.formData.payments.splice(index, 1);
      } else {
        uni.showToast({
          title: "至少保留一个收款计划",
          icon: "none",
        });
      }
    },

    // ==== 附件相关方法 ====
    uploadContractFile() {
      uni.chooseFile({
        count: 1,
        success: (res) => {
          // 处理选择的文件
          const file = res.tempFiles[0];
          this.formData.attachments.push({
            name: file.name,
            path: file.path,
            size: file.size,
          });
        },
      });
    },

    deleteFile(index) {
      this.formData.attachments.splice(index, 1);
    },

    // ==== 客户选择回调 ====
    updateSelectedCustomer(customer) {
      if (customer && customer.id) {
        this.formData.customer = customer;
      }
    },

    // ==== 数据加载 ====
    loadQuotationData(quotationId) {
      // 模拟加载报价单数据
      setTimeout(() => {
        // 这里应该是从API获取数据
        const quotationData = {
          id: quotationId,
          title: "示例报价单",
          items: [
            {
              productName: "产品A",
              unit: "台",
              description: "高性能服务器",
              unitPrice: "10000.00",
              quantity: "2",
              taxRate: { id: 1, name: "13%", value: 0.13 },
              amount: "20000.00",
            },
            {
              productName: "服务B",
              unit: "项",
              description: "年度维保服务",
              unitPrice: "5000.00",
              quantity: "1",
              taxRate: { id: 2, name: "6%", value: 0.06 },
              amount: "5000.00",
            },
          ],
        };

        this.formData.quotation = {
          id: quotationData.id,
          title: quotationData.title,
        };

        // 填充合同项目
        this.formData.items = quotationData.items.map((item) => {
          // 计算含税和未税价格
          const taxRate = item.taxRate ? item.taxRate.value : 0;
          const taxedUnitPrice = parseFloat(item.unitPrice);
          const netUnitPrice = taxedUnitPrice / (1 + taxRate);
          const taxedAmount = parseFloat(item.amount);
          const netAmount = taxedAmount / (1 + taxRate);

          return {
            productName: item.productName,
            description: item.description,
            unit: item.unit,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            taxRate: item.taxRate,
            netUnitPrice: netUnitPrice.toFixed(2),
            taxedUnitPrice: taxedUnitPrice.toFixed(2),
            netAmount: netAmount.toFixed(2),
            taxedAmount: taxedAmount.toFixed(2),
            amount: item.amount,
            expanded: false,
          };
        });
      }, 500);
    },

    // ==== 表单验证 ====
    validateCurrentStep() {
      // 根据当前步骤验证表单
      switch (this.currentStep) {
        case 1: // 基本信息验证
          if (!this.formData.title) {
            uni.showToast({
              title: "请输入合同名称",
              icon: "none",
            });
            return false;
          }

          if (!this.formData.customer) {
            uni.showToast({
              title: "请选择客户",
              icon: "none",
            });
            return false;
          }

          if (!this.formData.contractType) {
            uni.showToast({
              title: "请选择合同类型",
              icon: "none",
            });
            return false;
          }

          if (!this.formData.signingDate) {
            uni.showToast({
              title: "请选择签约日期",
              icon: "none",
            });
            return false;
          }

          return true;

        case 2: // 产品信息验证 - 不强制要求
          return true;

        case 3: // 财务信息验证
          if (!this.formData.taxedAmount) {
            uni.showToast({
              title: "请输入合同金额",
              icon: "none",
            });
            return false;
          }

          return true;

        default:
          return true;
      }
    },

    validateForm() {
      // 依次验证各个步骤的表单
      const step1Valid = this.validateCurrentStep(1);
      if (!step1Valid) {
        this.currentStep = 1;
        return false;
      }

      const step2Valid = this.validateCurrentStep(2);
      if (!step2Valid) {
        this.currentStep = 2;
        return false;
      }

      const step3Valid = this.validateCurrentStep(3);
      if (!step3Valid) {
        this.currentStep = 3;
        return false;
      }

      return true;
    },

    // ==== 表单提交操作 ====
    saveDraft() {
      try {
        // 保存草稿到本地存储
        uni.setStorageSync("contract_draft", {
          formData: this.formData,
          currentStep: this.currentStep,
          savedAt: new Date().toISOString(),
        });

        uni.showToast({
          title: "草稿保存成功",
          icon: "success",
        });

        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      } catch (e) {
        uni.showToast({
          title: "保存失败",
          icon: "none",
        });
      }
    },

    cancel() {
      uni.showModal({
        title: "提示",
        content: "确定要取消编辑吗？未保存的内容将丢失",
        success: (res) => {
          if (res.confirm) {
            uni.navigateBack();
          }
        },
      });
    },

    submit() {
      if (!this.validateForm()) {
        return;
      }

      uni.showLoading({
        title: "提交中...",
      });

      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: "提交成功",
          icon: "success",
        });

        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }, 1000);
    },

    // 切换是否使用产品总价
    toggleUseProductTotal() {
      this.formData.useProductTotal = !this.formData.useProductTotal;
      if (this.formData.useProductTotal) {
        this.formData.taxedAmount = this.totalAmount;
        this.calculateNetAmount();
      }
    },

    // 修改付款比例选择器事件
    onPaymentRatioChange(e) {
      const index = e.detail.value;
      const selectedRatio = this.paymentRatios[index];
      this.formData.paymentRatio = selectedRatio;

      // 生成对应的付款计划
      this.generatePaymentPlans(selectedRatio.ratios);
    },

    // 生成付款计划
    generatePaymentPlans(ratios) {
      const totalAmount = parseFloat(this.formData.taxedAmount || 0);

      // 生成新的付款计划
      this.formData.payments = ratios.map((ratio, index) => {
        const amount = (totalAmount * (ratio / 100)).toFixed(2);
        return {
          stage: `第${index + 1}期`,
          amount: amount,
          plannedDate: "",
          details: `付款比例${ratio}%`,
          invoiceAmount: "",
          actualAmount: "",
          remarks: "",
          expanded: false,
        };
      });
    },

    // 加载草稿
    loadDraft() {
      try {
        const draft = uni.getStorageSync("contract_draft");
        if (draft) {
          // 显示确认对话框
          uni.showModal({
            title: "提示",
            content: "检测到未完成的合同草稿，是否继续编辑？",
            success: (res) => {
              if (res.confirm) {
                // 恢复表单数据
                this.formData = draft.formData;
                this.currentStep = draft.currentStep;

                // 清除草稿
                uni.removeStorageSync("contract_draft");

                uni.showToast({
                  title: "已恢复草稿",
                  icon: "success",
                });
              } else {
                // 用户选择不恢复，清除草稿
                uni.removeStorageSync("contract_draft");
              }
            },
          });
        }
      } catch (e) {
        console.error("加载草稿失败:", e);
      }
    },

    // 加载商机数据
    async loadBusinessData() {
      const businessProcessList = await getSelectOptions("BusinessProcess");
      const target = businessProcessList.find((item) => item.code === "Win");
      await getOpportunityList({
        pageIndex: 1,
        pageSize: 10,
        filter: { hasContract: false, businessProcessId: target.id },
      }).then((res) => {
        this.opportunities = res;
      });
    },
  },
};
</script>

<style lang="scss">
.contract-create-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 90rpx;
    background-color: #3a86ff;
    color: #fff;
    padding: 0 30rpx;
    position: sticky;
    top: 0;
    z-index: 100;

    .left {
      display: flex;
      align-items: center;

      text {
        margin-left: 10rpx;
        font-size: 30rpx;
      }
    }

    .title {
      font-size: 34rpx;
      font-weight: bold;
    }

    .right {
      width: 60rpx;
    }
  }

  /* 步骤导航样式 */
  .steps-indicator {
    display: flex;
    background-color: #fff;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #eee;
    position: sticky;
    top: 90rpx;
    z-index: 99;

    .step {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;

      &:not(:last-child)::after {
        content: "";
        position: absolute;
        right: -10rpx;
        top: 30rpx;
        width: 20rpx;
        height: 2rpx;
        background-color: #ddd;
      }

      &.active .step-number {
        background-color: #3a86ff;
        color: #fff;
      }

      &.active .step-name {
        color: #3a86ff;
        font-weight: 500;
      }

      &.completed .step-number {
        background-color: #52c41a;
        color: #fff;
      }
    }

    .step-number {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background-color: #f0f0f0;
      color: #999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 26rpx;
      margin-bottom: 10rpx;
    }

    .step-name {
      font-size: 24rpx;
      color: #666;
    }
  }

  .content-container {
    flex: 1;
    padding-bottom: 30rpx;
    box-sizing: border-box;
  }

  /* 表单部分通用样式 */
  .form-section {
    background-color: #fff;
    margin: 20rpx 20rpx 0;
    border-radius: 12rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);

    .section-title {
      display: flex;
      align-items: center;
      padding: 24rpx 30rpx;
      border-bottom: 1rpx solid #eee;

      text {
        font-size: 30rpx;
        font-weight: bold;
        color: #333;
        margin-left: 12rpx;
      }
    }

    .form-group {
      padding: 0 30rpx;
    }

    .form-item {
      padding: 24rpx 0;
      border-bottom: 1rpx solid #eee;

      &:last-child {
        border-bottom: none;
      }

      .label {
        display: block;
        font-size: 28rpx;
        color: #666;
        margin-bottom: 12rpx;

        &.required:after {
          content: "*";
          color: #ff4d4f;
          margin-left: 6rpx;
        }
      }

      input {
        width: 100%;
        height: 88rpx;
        font-size: 28rpx;
        color: #333;
        background-color: #f9f9f9;
        border-radius: 8rpx;
        padding: 0 20rpx;
        box-sizing: border-box;
        border: 1rpx solid #eee;
      }

      textarea {
        width: 100%;
        height: 200rpx;
        padding: 20rpx;
        border: 1rpx solid #eee;
        border-radius: 8rpx;
        font-size: 28rpx;
        background-color: #f9f9f9;
        box-sizing: border-box;
      }

      .selector,
      .picker-view {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 88rpx;
        font-size: 28rpx;
        color: #333;
        background-color: #f9f9f9;
        border-radius: 8rpx;
        padding: 0 20rpx;
        box-sizing: border-box;
        border: 1rpx solid #eee;

        .placeholder {
          color: #999;
        }
      }
    }

    /* 并排表单项 */
    .form-item-row {
      display: flex;
      justify-content: space-between;
      gap: 20rpx;
      padding: 24rpx 0;
      border-bottom: 1rpx solid #eee;

      &:last-child {
        border-bottom: none;
      }

      .form-item {
        flex: 1;
        padding: 0;
        border-bottom: none;
      }

      .half {
        flex: 0 0 48%;
      }
    }

    /* 可折叠区域样式 */
    .collapsible-section {
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24rpx 30rpx;
        font-size: 28rpx;
        font-weight: 500;
        color: #333;
        border-bottom: 1rpx solid #eee;
      }

      .section-content {
        padding: 0 30rpx;
      }
    }
  }

  /* 产品列表样式 */
  .products-list {
    padding: 0 30rpx;

    .product-header {
      display: flex;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #eee;
      font-size: 26rpx;
      color: #666;
      font-weight: 500;
    }

    .product-row {
      display: flex;
      padding: 15rpx 0;
      border-bottom: 1rpx solid #eee;

      input {
        height: 70rpx;
        font-size: 26rpx;
        background-color: #f9f9f9;
        border-radius: 6rpx;
        padding: 0 10rpx;
        margin: 5rpx;
        box-sizing: border-box;
        border: 1rpx solid #eee;
      }

      .expand-button {
        margin-right: 10rpx;
      }
    }

    .product-details {
      background-color: #f9f9f9;
      padding: 20rpx;
      border-radius: 8rpx;
      margin-bottom: 20rpx;
    }

    .col {
      display: flex;
      align-items: center;
      padding: 0 5rpx;
    }

    .name {
      flex: 3;
    }

    .quantity,
    .price,
    .amount {
      flex: 2;
    }

    .action {
      flex: 1;
      display: flex;
      justify-content: center;
    }

    .add-product {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 24rpx 0;
      margin-top: 20rpx;
      color: #3a86ff;
      background-color: #f0f7ff;
      border-radius: 8rpx;
      border: 1rpx dashed #a0cfff;
    }

    .total-amount {
      display: flex;
      justify-content: flex-end;
      padding: 30rpx 0;
      font-size: 32rpx;
      font-weight: bold;
      color: #ff6b18;
    }
  }

  /* 收款列表样式 */
  .payment-list {
    .payment-header {
      display: flex;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #eee;
      font-size: 26rpx;
      color: #666;
      font-weight: 500;
    }

    .payment-row {
      display: flex;
      padding: 15rpx 0;
      border-bottom: 1rpx solid #eee;

      input,
      .picker-view {
        height: 70rpx;
        font-size: 26rpx;
        background-color: #f9f9f9;
        border-radius: 6rpx;
        padding: 0 10rpx;
        margin: 5rpx;
        box-sizing: border-box;
        border: 1rpx solid #eee;

        .placeholder {
          color: #999;
          font-size: 24rpx;
        }
      }

      .expand-button {
        margin-right: 10rpx;
      }
    }

    .payment-details {
      background-color: #f9f9f9;
      padding: 20rpx;
      border-radius: 8rpx;
      margin-bottom: 20rpx;
    }

    .stage {
      flex: 3;
    }

    .amount,
    .date {
      flex: 2;
    }

    .action {
      flex: 1;
      display: flex;
      justify-content: center;
    }

    .add-payment {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 24rpx 0;
      margin-top: 20rpx;
      color: #3a86ff;
      background-color: #f0f7ff;
      border-radius: 8rpx;
      border: 1rpx dashed #a0cfff;
    }
  }

  /* 文件上传区域样式 */
  .file-uploader {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;
    background-color: #f9f9f9;
    border: 1rpx dashed #ddd;
    border-radius: 8rpx;

    text {
      margin-top: 20rpx;
      color: #666;
      font-size: 28rpx;
    }
  }

  .file-list {
    margin-top: 20rpx;

    .file-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx;
      background-color: #f0f7ff;
      border-radius: 8rpx;
      margin-bottom: 10rpx;

      .file-name {
        color: #333;
        font-size: 28rpx;
      }

      .file-delete {
        color: #ff4d4f;
        font-size: 26rpx;
      }
    }
  }

  /* 底部按钮样式 */
  .step-buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20rpx;
    padding: 30rpx 20rpx;
    margin-top: 20rpx;
    background-color: #fff;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

    .btn {
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      border-radius: 8rpx;
      font-size: 28rpx;
      padding: 0;
      margin: 0;

      &.btn-primary {
        background-color: #3a86ff;
        color: #fff;
      }

      &.btn-secondary {
        background-color: #e6f0ff;
        color: #3a86ff;
      }

      &.btn-outline {
        background-color: #fff;
        color: #666;
        border: 1rpx solid #ddd;
      }
    }
  }

  /* 产品卡片样式 */
  .product-card {
    padding: 0 20rpx;

    .product-items {
      margin-bottom: 20rpx;
    }

    .product-item {
      background-color: #fff;
      border-radius: 12rpx;
      overflow: hidden;
      margin-bottom: 20rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
      border: 1rpx solid #eee;
    }

    .product-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx;
      background-color: #f9f9f9;
      border-bottom: 1rpx solid #eee;

      .product-title {
        font-size: 28rpx;
        font-weight: 500;
        color: #333;
      }

      .product-actions {
        display: flex;

        .expand-button {
          margin-right: 20rpx;
        }
      }
    }

    .product-content {
      padding: 0 20rpx;
    }

    .product-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #eee;

      &:last-child {
        border-bottom: none;
      }

      .label {
        font-size: 28rpx;
        color: #666;
        width: 180rpx;
      }

      input {
        flex: 1;
        height: 70rpx;
        font-size: 28rpx;
        background-color: #f9f9f9;
        border-radius: 6rpx;
        padding: 0 20rpx;
        box-sizing: border-box;
        border: 1rpx solid #eee;
      }

      .amount {
        color: #ff6b18;
        font-weight: 500;
      }

      .description {
        flex: 1;
        color: #666;
        font-size: 26rpx;
        padding: 0 10rpx;
        text-align: right;
      }

      .selector {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 70rpx;
        font-size: 28rpx;
        color: #333;
        background-color: #f9f9f9;
        border-radius: 6rpx;
        padding: 0 20rpx;
        box-sizing: border-box;
        border: 1rpx solid #eee;

        .placeholder {
          color: #999;
        }
      }
    }

    .product-details {
      background-color: #f9f9f9;
      border-radius: 8rpx;
      margin: 10rpx 0 20rpx;
      padding: 10rpx;
    }

    .add-product {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 24rpx 0;
      margin-top: 20rpx;
      color: #3a86ff;
      background-color: #f0f7ff;
      border-radius: 8rpx;
      border: 1rpx dashed #a0cfff;
    }

    .total-amount {
      display: flex;
      justify-content: flex-end;
      padding: 30rpx 0 10rpx;
      font-size: 32rpx;
      font-weight: bold;
      color: #ff6b18;
    }
  }

  /* 应收款计划卡片样式 */
  .payment-plans {
    padding: 20rpx;

    .payment-plan-item {
      background-color: #f9f9f9;
      border-radius: 12rpx;
      padding: 20rpx;
      margin-bottom: 20rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .payment-plan-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10rpx;

        .stage {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
        }

        .ratio {
          font-size: 26rpx;
          color: #666;
        }
      }

      .payment-plan-amount {
        display: flex;
        align-items: baseline;

        .amount-label {
          font-size: 26rpx;
          color: #666;
        }

        .amount-value {
          font-size: 32rpx;
          color: #ff6b18;
          font-weight: 500;
          margin-left: 10rpx;
        }
      }
    }
  }
}
</style>

@font-face {
  font-family: "iconfont";
  src: url('data:font/woff2;charset=utf-8;base64,BASE64_ENCODED_FONT') format('woff2');
}

/* 修改为本地路径 */
@font-face {
  font-family: "iconfont";
  src: url('https://at.alicdn.com/t/font_3345075_xxx.woff2') format('woff2'),
       url('https://at.alicdn.com/t/font_3345075_xxx.woff') format('woff'),
       url('https://at.alicdn.com/t/font_3345075_xxx.ttf') format('ttf');
  /*src: url('/static/fonts/iconfont.woff2') format('woff2'),*/
  /*     url('/static/fonts/iconfont.woff') format('woff'),*/
  /*     url('/static/fonts/iconfont.ttf') format('truetype');*/
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 用户相关 */
.icon-user:before {
  content: "\e602";
}

.icon-user-star:before {
  content: "\e603";
}

.icon-team:before {
  content: "\e604";
}

/* 联系方式 */
.icon-mail:before {
  content: "\e605";
}

.icon-phone:before {
  content: "\e606";
}

.icon-message:before {
  content: "\e607";
}

/* 组织机构 */
.icon-building:before {
  content: "\e608";
}

/* 通知相关 */
.icon-notification:before {
  content: "\e609";
}

.icon-bell:before {
  content: "\e610";
}

/* 主题相关 */
.icon-moon:before {
  content: "\e611";
}

.icon-sun:before {
  content: "\e612";
}

/* 安全相关 */
.icon-lock:before {
  content: "\e613";
}

.icon-unlock:before {
  content: "\e614";
}

.icon-key:before {
  content: "\e615";
}

/* 视图相关 */
.icon-eye:before {
  content: "\e616";
}

.icon-eye-off:before {
  content: "\e617";
}

/* 导航相关 */
.icon-right:before {
  content: "\e618";
}

.icon-left:before {
  content: "\e619";
}

.icon-up:before {
  content: "\e620";
}

.icon-down:before {
  content: "\e621";
}

/* 操作相关 */
.icon-edit:before {
  content: "\e622";
}

.icon-delete:before {
  content: "\e623";
}

.icon-add:before {
  content: "\e624";
}

.icon-check:before {
  content: "\e625";
}

.icon-close:before {
  content: "\e626";
}

.icon-plus:before {
  content: "\e627";
}

.icon-minus:before {
  content: "\e628";
}

/* 信息相关 */
.icon-info:before {
  content: "\e629";
}

.icon-warning:before {
  content: "\e630";
}

.icon-error:before {
  content: "\e631";
}

.icon-help:before {
  content: "\e632";
}

/* 功能相关 */
.icon-search:before {
  content: "\e633";
}

.icon-filter:before {
  content: "\e634";
}

.icon-sort:before {
  content: "\e635";
}

.icon-calendar:before {
  content: "\e636";
}

.icon-camera:before {
  content: "\e637";
}

.icon-translate:before {
  content: "\e638";
}

/* 设置相关 */
.icon-settings:before {
  content: "\e639";
}

.icon-more:before {
  content: "\e640";
} 
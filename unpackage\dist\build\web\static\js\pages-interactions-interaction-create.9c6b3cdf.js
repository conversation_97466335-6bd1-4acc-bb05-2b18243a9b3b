(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-interactions-interaction-create"],{2634:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
e.default=function(){return t};var t={},a=Object.prototype,n=a.hasOwnProperty,i=Object.defineProperty||function(t,e,a){t[e]=a.value},o="function"==typeof Symbol?Symbol:{},s=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function d(t,e,a){return Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{d({},"")}catch(E){d=function(t,e,a){return t[e]=a}}function l(t,e,a,r){var n=e&&e.prototype instanceof v?e:v,o=Object.create(n.prototype),s=new L(r||[]);return i(o,"_invoke",{value:T(t,a,s)}),o}function f(t,e,a){try{return{type:"normal",arg:t.call(e,a)}}catch(E){return{type:"throw",arg:E}}}t.wrap=l;var p={};function v(){}function h(){}function m(){}var g={};d(g,s,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(O([])));y&&y!==a&&n.call(y,s)&&(g=y);var w=m.prototype=v.prototype=Object.create(g);function x(t){["next","throw","return"].forEach((function(e){d(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){var a;i(this,"_invoke",{value:function(i,o){function s(){return new e((function(a,s){(function a(i,o,s,c){var u=f(t[i],t,o);if("throw"!==u.type){var d=u.arg,l=d.value;return l&&"object"==(0,r.default)(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){a("next",t,s,c)}),(function(t){a("throw",t,s,c)})):e.resolve(l).then((function(t){d.value=t,s(d)}),(function(t){return a("throw",t,s,c)}))}c(u.arg)})(i,o,a,s)}))}return a=a?a.then(s,s):s()}})}function T(t,e,a){var r="suspendedStart";return function(n,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===n)throw i;return I()}for(a.method=n,a.arg=i;;){var o=a.delegate;if(o){var s=k(o,a);if(s){if(s===p)continue;return s}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if("suspendedStart"===r)throw r="completed",a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);r="executing";var c=f(t,e,a);if("normal"===c.type){if(r=a.done?"completed":"suspendedYield",c.arg===p)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(r="completed",a.method="throw",a.arg=c.arg)}}}function k(t,e){var a=e.method,r=t.iterator[a];if(void 0===r)return e.delegate=null,"throw"===a&&t.iterator["return"]&&(e.method="return",e.arg=void 0,k(t,e),"throw"===e.method)||"return"!==a&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+a+"' method")),p;var n=f(r,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,p;var i=n.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,p):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function D(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function _(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function L(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(D,this),this.reset(!0)}function O(t){if(t){var e=t[s];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,r=function e(){for(;++a<t.length;)if(n.call(t,a))return e.value=t[a],e.done=!1,e;return e.value=void 0,e.done=!0,e};return r.next=r}}return{next:I}}function I(){return{value:void 0,done:!0}}return h.prototype=m,i(w,"constructor",{value:m,configurable:!0}),i(m,"constructor",{value:h,configurable:!0}),h.displayName=d(m,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,d(t,u,"GeneratorFunction")),t.prototype=Object.create(w),t},t.awrap=function(t){return{__await:t}},x(C.prototype),d(C.prototype,c,(function(){return this})),t.AsyncIterator=C,t.async=function(e,a,r,n,i){void 0===i&&(i=Promise);var o=new C(l(e,a,r,n),i);return t.isGeneratorFunction(a)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},x(w),d(w,u,"Generator"),d(w,s,(function(){return this})),d(w,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),a=[];for(var r in e)a.push(r);return a.reverse(),function t(){for(;a.length;){var r=a.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},t.values=O,L.prototype={constructor:L,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(_),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function a(a,r){return o.type="throw",o.arg=t,e.next=a,r&&(e.method="next",e.arg=void 0),!!r}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],o=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(s&&c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var a=this.tryEntries.length-1;a>=0;--a){var r=this.tryEntries[a];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,p):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.finallyLoc===t)return this.complete(a.completion,a.afterLoc),_(a),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.tryLoc===t){var r=a.completion;if("throw"===r.type){var n=r.arg;_(a)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,a){return this.delegate={iterator:O(t),resultName:e,nextLoc:a},"next"===this.method&&(this.arg=void 0),p}},t},a("6a54"),a("01a2"),a("e39c"),a("bf0f"),a("844d"),a("18f7"),a("de6c"),a("3872e"),a("4e9b"),a("114e"),a("c240"),a("926e"),a("7a76"),a("c9b5"),a("aa9c"),a("2797"),a("8a8d"),a("dc69"),a("f7a5");var r=function(t){return t&&t.__esModule?t:{default:t}}(a("fcf3"))},2753:function(t,e,a){"use strict";a.r(e);var r=a("b445"),n=a("619c");for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);a("a304");var o=a("828b"),s=Object(o["a"])(n["default"],r["b"],r["c"],!1,null,"663b6524",null,!1,r["a"],void 0);e["default"]=s.exports},"2fdc":function(t,e,a){"use strict";function r(t,e,a,r,n,i,o){try{var s=t[i](o),c=s.value}catch(u){return void a(u)}s.done?e(c):Promise.resolve(c).then(r,n)}a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return function(){var e=this,a=arguments;return new Promise((function(n,i){var o=t.apply(e,a);function s(t){r(o,n,i,s,c,"next",t)}function c(t){r(o,n,i,s,c,"throw",t)}s(void 0)}))}},a("bf0f")},"619c":function(t,e,a){"use strict";a.r(e);var r=a("9b75"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);e["default"]=n.a},"7c55":function(t,e,a){var r=a("c86c");e=r(!1),e.push([t.i,'.page[data-v-663b6524]{display:flex;flex-direction:column;height:100vh;background-color:#f5f7fa}.page-header[data-v-663b6524]{display:flex;align-items:center;justify-content:space-between;padding:%?30?% %?40?%;border-bottom:%?1?% solid #e0e0e0;background-color:#fff;position:-webkit-sticky;position:sticky;top:0;z-index:10}.back-button[data-v-663b6524]{padding:%?10?%}.page-title[data-v-663b6524]{font-size:%?36?%;font-weight:700;color:#333}.header-spacer[data-v-663b6524]{width:%?44?%}.page-container[data-v-663b6524]{flex:1;padding:%?30?%}.form-section[data-v-663b6524]{background-color:#fff;border-radius:%?16?%;padding:%?30?%;margin-bottom:%?30?%;box-shadow:0 %?2?% %?10?% rgba(0,0,0,.05)}.section-header[data-v-663b6524]{display:flex;align-items:center;margin-bottom:%?20?%}.section-title[data-v-663b6524]{font-size:%?30?%;font-weight:700;margin-left:%?10?%;color:#333}.section-content[data-v-663b6524]{padding:%?10?%}.type-selector[data-v-663b6524]{display:grid;grid-template-columns:repeat(2,1fr);gap:%?20?%}.type-option[data-v-663b6524]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?30?% 0;border-radius:%?12?%;border:%?1?% solid #e0e0e0;transition:all .3s}.type-option.selected[data-v-663b6524]{border-color:#3370ff;background-color:#f0f7ff}.type-option uni-text[data-v-663b6524]{margin-top:%?16?%;font-size:%?28?%;color:#666}.type-option.selected uni-text[data-v-663b6524]{color:#3370ff;font-weight:500}.form-group[data-v-663b6524]{margin-bottom:%?30?%}.form-label[data-v-663b6524]{display:block;font-size:%?28?%;color:#666;margin-bottom:%?16?%}.form-label.required[data-v-663b6524]:after{content:"*";color:#f5222d;margin-left:%?8?%}.form-control[data-v-663b6524]{width:100%;height:%?80?%;padding:0 %?20?%;font-size:%?28?%;color:#333;border:%?1?% solid #d9d9d9;border-radius:%?8?%;box-sizing:border-box}uni-textarea.form-control[data-v-663b6524]{height:%?240?%;padding:%?20?%}.uni-input[data-v-663b6524]{position:relative;height:%?80?%;padding:0 %?20?%;line-height:%?80?%;border:%?1?% solid #d9d9d9;border-radius:%?8?%}.input-icon[data-v-663b6524]{position:absolute;right:%?20?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#999}.next-steps-list[data-v-663b6524]{display:flex;flex-direction:column;gap:%?20?%;margin-bottom:%?20?%}.next-step-item[data-v-663b6524]{display:flex;align-items:center;gap:%?20?%}.step-actions[data-v-663b6524]{display:flex;align-items:center}.step-action[data-v-663b6524]{color:#999;padding:%?10?%}.add-next-step[data-v-663b6524]{display:flex;align-items:center;padding:%?20?%;border:%?1?% dashed #d9d9d9;border-radius:%?8?%;color:#3370ff}.add-next-step uni-text[data-v-663b6524]{margin-left:%?10?%}.tag-list[data-v-663b6524]{display:flex;flex-wrap:wrap;gap:%?16?%;margin-bottom:%?20?%}.tag-item[data-v-663b6524]{display:flex;align-items:center;padding:%?8?% %?20?%;background-color:#f5f5f5;color:#666;border-radius:%?100?%;font-size:%?24?%}.tag-close[data-v-663b6524]{margin-left:%?8?%;color:#999}.add-tag[data-v-663b6524]{margin-top:%?20?%}.bottom-spacer[data-v-663b6524]{height:%?120?%}.action-bar[data-v-663b6524]{position:fixed;bottom:0;left:0;right:0;padding:%?20?% %?30?%;background-color:#fff;box-shadow:0 %?-2?% %?10?% rgba(0,0,0,.05);display:flex;gap:%?30?%}.btn[data-v-663b6524]{flex:1;height:%?88?%;border-radius:%?8?%;display:flex;align-items:center;justify-content:center;font-size:%?30?%}.btn-outline[data-v-663b6524]{border:%?1?% solid #d9d9d9;color:#666}.btn-primary[data-v-663b6524]{background-color:#3370ff;color:#fff}',""]),t.exports=e},"9b75":function(t,e,a){"use strict";a("6a54");var r=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=r(a("2634")),i=r(a("2fdc"));a("795c"),a("c223"),a("bf0f"),a("2797"),a("01a2"),a("e39c");var o=r(a("8a0f")),s=a("f8c5"),c={components:{SvgIcon:o.default},data:function(){var t=new Date;return{formData:{recordType:"follow-up",type:"call",subject:"",date:this.formatDate(t),time:this.formatTime(t),relatedObject:"",relatedId:"",relatedName:"",relatedType:5,responsibleUserId:"",subRelatedType:"Accounts",title:"",sysUserId:"",description:"",contacts:"",content:"",nextSteps:[""],tags:[],ownerName:""},userList:[],newTag:"",recordTypes:[],typeOptions:[],typeIndex:0,relatedTypes:[],relatedTypeIndex:0,startDate:this.formatDate(t)}},methods:{navBack:function(){uni.navigateBack()},loadData:function(){var t=this;(0,s.getOrganizeUsers)().then((function(e){t.userList=e.items,t.setResponsibleUserId()}))},formatDate:function(t){var e=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0"),r=String(t.getDate()).padStart(2,"0");return"".concat(e,"-").concat(a,"-").concat(r)},onOwnerChange:function(t){this.formData.ownerName=this.userList[t.detail.value].name,this.formData.responsibleUserId=this.userList[t.detail.value].id},formatTime:function(t){var e=String(t.getHours()).padStart(2,"0"),a=String(t.getMinutes()).padStart(2,"0");return"".concat(e,":").concat(a)},onDateChange:function(t){this.formData.date=t.detail.value},onTimeChange:function(t){this.formData.time=t.detail.value},setResponsibleUserId:function(){var t=this,e=uni.getStorageSync("userInfo");this.formData.responsibleUserId=e.id,this.formData.sysUserId=e.id,this.formData.responsibleUserId&&this.userList.forEach((function(e){e.id===t.formData.responsibleUserId&&(t.formData.ownerName=e.name)}))},saveRecord:function(){var t={relatedId:this.formData.relatedId,relatedName:this.formData.relatedName,relatedType:this.formData.relatedType,responsibleUserId:this.formData.responsibleUserId,subRelatedType:this.formData.subRelatedType,title:this.formData.title},e="<p>".concat(this.formData.description,"</p>"),a={relatedId:this.formData.sysUserId,newDate:"".concat(this.formData.date," ").concat(this.formData.time)};(0,s.TmsTaskCreate)(t).then((function(t){(0,s.TmsTaskTimeEnd)(t.id,a.newDate).then((function(t){})),(0,s.updateTaskDescription)(t.id,{description:e}),uni.navigateBack()}))}},onLoad:function(t){this.formData.relatedId=t.relatedId,this.formData.relatedName=t.relatedName},onShow:function(){var t=this;return(0,i.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.loadData();case 2:case"end":return e.stop()}}),e)})))()}};e.default=c},a304:function(t,e,a){"use strict";var r=a("c9eb"),n=a.n(r);n.a},b445:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){return r}));var r={svgIcon:a("8a0f").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"page"},[a("v-uni-view",{staticClass:"page-header"},[a("v-uni-view",{staticClass:"back-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.navBack.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"arrow-left",type:"svg",size:"24"}})],1),a("v-uni-text",{staticClass:"page-title"},[t._v("快速创建任务")]),a("v-uni-view",{staticClass:"header-spacer"})],1),a("v-uni-scroll-view",{staticClass:"page-container",attrs:{"scroll-y":!0}},[a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-header"},[a("svg-icon",{attrs:{name:"file-list",type:"svg",size:"20"}})],1),a("v-uni-view",{staticClass:"section-content"},[a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label required"},[t._v("任务名称")]),a("v-uni-input",{staticClass:"form-control",attrs:{type:"text",placeholder:"请输入任务名称"},model:{value:t.formData.title,callback:function(e){t.$set(t.formData,"title",e)},expression:"formData.title"}})],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("负责人")]),a("v-uni-view",{staticClass:"date-picker"},[a("v-uni-picker",{attrs:{mode:"selector",range:t.userList,placeholder:"请选择负责人","range-key":"name"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onOwnerChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"uni-input"},[t._v(t._s(t.formData.ownerName)),a("v-uni-view",{staticClass:"input-icon"})],1)],1)],1)],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label required"},[t._v("日期")]),a("v-uni-view",{staticClass:"date-picker"},[a("v-uni-picker",{attrs:{mode:"date",value:t.formData.date,start:t.startDate},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onDateChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"uni-input"},[t._v(t._s(t.formData.date)),a("v-uni-view",{staticClass:"input-icon"},[a("svg-icon",{attrs:{name:"calendar",type:"svg",size:"20"}})],1)],1)],1)],1)],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("时间")]),a("v-uni-view",{staticClass:"date-picker"},[a("v-uni-picker",{attrs:{mode:"time",value:t.formData.time},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onTimeChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"uni-input"},[t._v(t._s(t.formData.time)),a("v-uni-view",{staticClass:"input-icon"},[a("svg-icon",{attrs:{name:"clock",type:"svg",size:"20"}})],1)],1)],1)],1)],1)],1)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-header"},[a("svg-icon",{attrs:{name:"align-left",type:"svg",size:"20"}}),a("v-uni-text",{staticClass:"section-title"},[t._v("详情描述")])],1),a("v-uni-view",{staticClass:"section-content"},[a("v-uni-view",{staticClass:"form-group"},[a("v-uni-textarea",{staticClass:"form-control",attrs:{placeholder:"请输入详情描述..."},model:{value:t.formData.description,callback:function(e){t.$set(t.formData,"description",e)},expression:"formData.description"}})],1)],1)],1),a("v-uni-view",{staticClass:"bottom-spacer"})],1),a("v-uni-view",{staticClass:"action-bar"},[a("v-uni-button",{staticClass:"btn btn-outline",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.navBack.apply(void 0,arguments)}}},[t._v("取消")]),a("v-uni-button",{staticClass:"btn btn-primary",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveRecord.apply(void 0,arguments)}}},[t._v("保存")])],1)],1)},i=[]},c475:function(t,e,a){"use strict";a("6a54");var r=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.request=e.getTenantInfo=void 0;var n=r(a("9b1b"));a("bf0f"),a("4626"),a("5ac7");var i=null;e.getTenantInfo=function(t){return new Promise((function(e,a){uni.request({url:"/api/Tenants/getTenantsByLogin",method:"POST",data:t,header:{"Content-Type":"application/json"},success:function(t){200===t.statusCode?(i=t.data,e(t.data)):a(t.data)},fail:function(t){a(t)}})}))};e.request=function(t){return t.url.includes("/login")&&i&&(t.header=(0,n.default)((0,n.default)({},t.header),{},{__tenant:i[0].id})),new Promise((function(e,a){uni.request({url:t.url,method:t.method||"GET",data:t.data||{},header:(0,n.default)({Authorization:"Bearer "+uni.getStorageSync("token"),"Content-Type":"application/json"},t.header),success:function(t){200===t.statusCode?e(t.data):a(t.data)},fail:function(t){a(t)}})}))}},c9eb:function(t,e,a){var r=a("7c55");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var n=a("967d").default;n("85681c20",r,!0,{sourceMap:!1,shadowMode:!1})},f8c5:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.updateTaskDescription=e.updateCustomerById=e.getTaskDetail=e.getOwnerList=e.getOrganizeUsers=e.getCustomerList=e.getCustomerDetail=e.getCustomerById=e.deleteCustomer=e.createCustomer=e.TmsTaskTimeEnd=e.TmsTaskCreate=void 0,a("c223");var r=a("c475");e.getCustomerList=function(t){return(0,r.request)({url:"/api/crm/custom/getList",method:"POST",data:t})};e.getCustomerDetail=function(t){return(0,r.request)({url:"/api/crm/custom/getCustomById?id="+t,method:"GET"})};e.createCustomer=function(t){return(0,r.request)({url:"/api/crm/custom/create",method:"POST",data:t})};e.getCustomerById=function(t){return(0,r.request)({url:"/api/crm/custom/getCustomById?id="+t,method:"GET"})};e.updateCustomerById=function(t,e){return(0,r.request)({url:"/api/crm/custom/update?id="+t,method:"POST",data:e})};e.getOwnerList=function(t){return(0,r.request)({url:"/api/Users/<USER>",method:"POST",data:t})};e.deleteCustomer=function(t){return(0,r.request)({url:"/api/crm/custom/delete?id="+t,method:"POST"})};e.getOrganizeUsers=function(){return(0,r.request)({url:"/api/tms/taskTemplate/getOrganizeUsers",method:"GET"})};e.TmsTaskCreate=function(t){return(0,r.request)({url:"/api/tms/taskItem/create",method:"POST",data:t})};e.TmsTaskTimeEnd=function(t,e){return(0,r.request)({url:"/api/tms/taskItem/editPlanDoneDate?id=".concat(t,"&newDate=").concat(e),method:"POST"})};e.updateTaskDescription=function(t,e){return(0,r.request)({url:"/api/tms/taskItem/updateDesc?id="+t,method:"POST",data:e})};e.getTaskDetail=function(t){return(0,r.request)({url:"/api/tms/taskItem/getList",method:"POST",data:t})}}}]);
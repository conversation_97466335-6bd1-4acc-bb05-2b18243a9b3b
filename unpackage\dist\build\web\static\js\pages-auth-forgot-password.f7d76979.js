(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-auth-forgot-password"],{"465b":function(t,a,e){"use strict";e.r(a);var o=e("ea5b"),s=e("5e93");for(var i in s)["default"].indexOf(i)<0&&function(t){e.d(a,t,(function(){return s[t]}))}(i);e("e8a1");var n=e("828b"),r=Object(n["a"])(s["default"],o["b"],o["c"],!1,null,"9bb29dc6",null,!1,o["a"],void 0);a["default"]=r.exports},"5e93":function(t,a,e){"use strict";e.r(a);var o=e("6089"),s=e.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){e.d(a,t,(function(){return o[t]}))}(i);a["default"]=s.a},6089:function(t,a,e){"use strict";e("6a54"),Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var o={data:function(){return{currentStep:1,passwordVisible:!1,confirmPasswordVisible:!1,countdown:0,formData:{phone:"",code:"",password:"",confirmPassword:""}}},computed:{isCountdownActive:function(){return this.countdown>0},countdownText:function(){return this.isCountdownActive?"".concat(this.countdown,"秒后重新获取"):"获取验证码"},canProceedToStep2:function(){return this.formData.phone&&11===this.formData.phone.length&&this.formData.code&&6===this.formData.code.length},canProceedToStep3:function(){return this.formData.password&&this.formData.password.length>=8&&this.formData.confirmPassword&&this.formData.password===this.formData.confirmPassword}},methods:{togglePasswordVisibility:function(){this.passwordVisible=!this.passwordVisible},toggleConfirmPasswordVisibility:function(){this.confirmPasswordVisible=!this.confirmPasswordVisible},getVerificationCode:function(){var t=this;this.formData.phone?11===this.formData.phone.length?(this.countdown=60,this.countdownTimer=setInterval((function(){t.countdown--,t.countdown<=0&&clearInterval(t.countdownTimer)}),1e3),uni.showToast({title:"验证码已发送",icon:"success"})):uni.showToast({title:"请输入有效的手机号",icon:"none"}):uni.showToast({title:"请输入手机号",icon:"none"})},goToStep2:function(){var t=this;this.canProceedToStep2&&(uni.showLoading({title:"验证中..."}),setTimeout((function(){uni.hideLoading(),t.currentStep=2}),1e3))},goToStep3:function(){var t=this;this.canProceedToStep3&&(this.formData.password===this.formData.confirmPassword?(uni.showLoading({title:"重置中..."}),setTimeout((function(){uni.hideLoading(),t.currentStep=3}),1500)):uni.showToast({title:"两次输入的密码不一致",icon:"none"}))},goToLogin:function(){uni.redirectTo({url:"/pages/auth/login"})}},beforeDestroy:function(){this.countdownTimer&&clearInterval(this.countdownTimer)}};a.default=o},"88ec":function(t,a,e){var o=e("c86c");a=o(!1),a.push([t.i,".auth-container[data-v-9bb29dc6]{min-height:100vh;display:flex;flex-direction:column;padding:%?30?%;background-color:#fff}.auth-header[data-v-9bb29dc6]{text-align:center;margin:%?40?% 0}.logo[data-v-9bb29dc6]{font-size:%?80?%;font-weight:700;color:#3a86ff;margin-bottom:%?20?%}.welcome-text[data-v-9bb29dc6]{font-size:%?48?%;color:#333;margin-bottom:%?10?%;display:block}.sub-text[data-v-9bb29dc6]{font-size:%?32?%;color:#666;display:block}.step-indicator[data-v-9bb29dc6]{display:flex;justify-content:space-between;align-items:center;margin:%?40?% 0}.step[data-v-9bb29dc6]{display:flex;flex-direction:column;align-items:center}.step-number[data-v-9bb29dc6]{width:%?60?%;height:%?60?%;background-color:#e0e0e0;color:#666;border-radius:50%;display:flex;align-items:center;justify-content:center;font-weight:700;margin-bottom:%?10?%}.step.active .step-number[data-v-9bb29dc6]{background-color:#3a86ff;color:#fff}.step-text[data-v-9bb29dc6]{font-size:%?24?%;color:#666}.step.active .step-text[data-v-9bb29dc6]{color:#3a86ff;font-weight:500}.step-line[data-v-9bb29dc6]{height:%?2?%;background-color:#e0e0e0;flex:1;margin:0 %?10?%}.auth-form[data-v-9bb29dc6]{margin-top:%?20?%}.form-group[data-v-9bb29dc6]{margin-bottom:%?30?%}.form-label[data-v-9bb29dc6]{display:block;margin-bottom:%?10?%;color:#333;font-weight:500}.form-input[data-v-9bb29dc6]{width:100%;height:%?90?%;padding:0 %?30?%;border:1px solid #ddd;border-radius:%?12?%;font-size:%?32?%;box-sizing:border-box}.code-input-group[data-v-9bb29dc6]{display:flex;align-items:center}.code-input-group .form-input[data-v-9bb29dc6]{flex:1}.code-btn[data-v-9bb29dc6]{width:%?220?%;height:%?90?%;margin-left:%?20?%;background-color:#e6f0ff;color:#3a86ff;border:none;border-radius:%?12?%;font-size:%?28?%;display:flex;align-items:center;justify-content:center}.code-btn[disabled][data-v-9bb29dc6]{background-color:#f0f0f0;color:#999}.password-input-group[data-v-9bb29dc6]{position:relative}.password-toggle[data-v-9bb29dc6]{position:absolute;right:%?30?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#666}.password-rules[data-v-9bb29dc6]{background-color:#f8f8f8;padding:%?20?%;border-radius:%?12?%;margin-bottom:%?30?%}.password-rule-title[data-v-9bb29dc6]{font-size:%?28?%;color:#333;margin-bottom:%?10?%;display:block}.password-rule-item[data-v-9bb29dc6]{font-size:%?24?%;color:#666;margin-bottom:%?5?%;display:block}.success-container[data-v-9bb29dc6]{display:flex;flex-direction:column;align-items:center;margin-top:%?60?%}.success-icon[data-v-9bb29dc6]{width:%?120?%;height:%?120?%;background-color:#e6f0ff;border-radius:50%;display:flex;align-items:center;justify-content:center;margin-bottom:%?30?%}.success-icon uni-text[data-v-9bb29dc6]{font-size:%?60?%;color:#3a86ff}.success-title[data-v-9bb29dc6]{font-size:%?40?%;color:#333;font-weight:700;margin-bottom:%?20?%}.success-message[data-v-9bb29dc6]{font-size:%?32?%;color:#666;text-align:center;margin-bottom:%?50?%}.btn[data-v-9bb29dc6]{height:%?90?%;display:flex;align-items:center;justify-content:center;border-radius:%?12?%;font-size:%?32?%;font-weight:500}.btn-primary[data-v-9bb29dc6]{background-color:#3a86ff;color:#fff}.btn-primary[disabled][data-v-9bb29dc6]{background-color:#a6c8ff}.btn-full[data-v-9bb29dc6]{width:100%}.auth-footer[data-v-9bb29dc6]{text-align:center;margin-top:auto;padding:%?40?% 0;display:flex;justify-content:center}.auth-footer-text[data-v-9bb29dc6]{color:#666;font-size:%?28?%}.auth-footer-link[data-v-9bb29dc6]{color:#3a86ff;font-size:%?28?%;font-weight:500;margin-left:%?10?%}",""]),t.exports=a},dcfb:function(t,a,e){var o=e("88ec");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var s=e("967d").default;s("1cdea0fa",o,!0,{sourceMap:!1,shadowMode:!1})},e8a1:function(t,a,e){"use strict";var o=e("dcfb"),s=e.n(o);s.a},ea5b:function(t,a,e){"use strict";e.d(a,"b",(function(){return o})),e.d(a,"c",(function(){return s})),e.d(a,"a",(function(){}));var o=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("v-uni-view",{staticClass:"auth-container"},[e("v-uni-view",{staticClass:"auth-header"},[e("v-uni-view",{staticClass:"logo"},[t._v("CRM")]),e("v-uni-text",{staticClass:"welcome-text"},[t._v("找回密码")]),e("v-uni-text",{staticClass:"sub-text"},[t._v("请输入您的手机号以重置密码")])],1),e("v-uni-view",{staticClass:"auth-form"},[e("v-uni-view",{staticClass:"step-indicator"},[e("v-uni-view",{staticClass:"step",class:{active:t.currentStep>=1}},[e("v-uni-view",{staticClass:"step-number"},[t._v("1")]),e("v-uni-text",{staticClass:"step-text"},[t._v("身份验证")])],1),e("v-uni-view",{staticClass:"step-line"}),e("v-uni-view",{staticClass:"step",class:{active:t.currentStep>=2}},[e("v-uni-view",{staticClass:"step-number"},[t._v("2")]),e("v-uni-text",{staticClass:"step-text"},[t._v("重置密码")])],1),e("v-uni-view",{staticClass:"step-line"}),e("v-uni-view",{staticClass:"step",class:{active:t.currentStep>=3}},[e("v-uni-view",{staticClass:"step-number"},[t._v("3")]),e("v-uni-text",{staticClass:"step-text"},[t._v("完成")])],1)],1),1===t.currentStep?e("v-uni-view",[e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("手机号")]),e("v-uni-input",{staticClass:"form-input",attrs:{type:"number",placeholder:"请输入手机号",maxlength:"11"},model:{value:t.formData.phone,callback:function(a){t.$set(t.formData,"phone",a)},expression:"formData.phone"}})],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("验证码")]),e("v-uni-view",{staticClass:"code-input-group"},[e("v-uni-input",{staticClass:"form-input",attrs:{type:"number",placeholder:"请输入验证码",maxlength:"6"},model:{value:t.formData.code,callback:function(a){t.$set(t.formData,"code",a)},expression:"formData.code"}}),e("v-uni-button",{staticClass:"code-btn",attrs:{disabled:t.isCountdownActive},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.getVerificationCode.apply(void 0,arguments)}}},[t._v(t._s(t.countdownText))])],1)],1),e("v-uni-button",{staticClass:"btn btn-primary btn-full",attrs:{disabled:!t.canProceedToStep2},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.goToStep2.apply(void 0,arguments)}}},[t._v("下一步")])],1):t._e(),2===t.currentStep?e("v-uni-view",[e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("新密码")]),e("v-uni-view",{staticClass:"password-input-group"},["checkbox"===(t.passwordVisible?"text":"password")?e("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请设置8-20位密码",type:"checkbox"},model:{value:t.formData.password,callback:function(a){t.$set(t.formData,"password",a)},expression:"formData.password"}}):"radio"===(t.passwordVisible?"text":"password")?e("input",{directives:[{name:"model",rawName:"v-model",value:t.formData.password,expression:"formData.password"}],staticClass:"form-input",attrs:{placeholder:"请设置8-20位密码",type:"radio"},domProps:{checked:t._q(t.formData.password,null)},on:{change:function(a){return t.$set(t.formData,"password",null)}}}):e("input",{directives:[{name:"model",rawName:"v-model",value:t.formData.password,expression:"formData.password"}],staticClass:"form-input",attrs:{placeholder:"请设置8-20位密码",type:t.passwordVisible?"text":"password"},domProps:{value:t.formData.password},on:{input:function(a){a.target.composing||t.$set(t.formData,"password",a.target.value)}}}),e("v-uni-text",{staticClass:"password-toggle",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.togglePasswordVisibility.apply(void 0,arguments)}}},[t.passwordVisible?e("v-uni-text",{staticClass:"ri-eye-off-line"}):e("v-uni-text",{staticClass:"ri-eye-line"})],1)],1)],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("确认密码")]),e("v-uni-view",{staticClass:"password-input-group"},["checkbox"===(t.confirmPasswordVisible?"text":"password")?e("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请再次输入密码",type:"checkbox"},model:{value:t.formData.confirmPassword,callback:function(a){t.$set(t.formData,"confirmPassword",a)},expression:"formData.confirmPassword"}}):"radio"===(t.confirmPasswordVisible?"text":"password")?e("input",{directives:[{name:"model",rawName:"v-model",value:t.formData.confirmPassword,expression:"formData.confirmPassword"}],staticClass:"form-input",attrs:{placeholder:"请再次输入密码",type:"radio"},domProps:{checked:t._q(t.formData.confirmPassword,null)},on:{change:function(a){return t.$set(t.formData,"confirmPassword",null)}}}):e("input",{directives:[{name:"model",rawName:"v-model",value:t.formData.confirmPassword,expression:"formData.confirmPassword"}],staticClass:"form-input",attrs:{placeholder:"请再次输入密码",type:t.confirmPasswordVisible?"text":"password"},domProps:{value:t.formData.confirmPassword},on:{input:function(a){a.target.composing||t.$set(t.formData,"confirmPassword",a.target.value)}}}),e("v-uni-text",{staticClass:"password-toggle",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.toggleConfirmPasswordVisibility.apply(void 0,arguments)}}},[t.confirmPasswordVisible?e("v-uni-text",{staticClass:"ri-eye-off-line"}):e("v-uni-text",{staticClass:"ri-eye-line"})],1)],1)],1),e("v-uni-view",{staticClass:"password-rules"},[e("v-uni-text",{staticClass:"password-rule-title"},[t._v("密码规则:")]),e("v-uni-text",{staticClass:"password-rule-item"},[t._v("• 包含大小写字母、数字")]),e("v-uni-text",{staticClass:"password-rule-item"},[t._v("• 长度在8-20个字符之间")]),e("v-uni-text",{staticClass:"password-rule-item"},[t._v("• 不包含连续重复字符")])],1),e("v-uni-button",{staticClass:"btn btn-primary btn-full",attrs:{disabled:!t.canProceedToStep3},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.goToStep3.apply(void 0,arguments)}}},[t._v("重置密码")])],1):t._e(),3===t.currentStep?e("v-uni-view",{staticClass:"success-container"},[e("v-uni-view",{staticClass:"success-icon"},[e("v-uni-text",{staticClass:"ri-check-line"})],1),e("v-uni-text",{staticClass:"success-title"},[t._v("密码重置成功")]),e("v-uni-text",{staticClass:"success-message"},[t._v("您已成功重置密码，请使用新密码登录")]),e("v-uni-button",{staticClass:"btn btn-primary btn-full",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.goToLogin.apply(void 0,arguments)}}},[t._v("返回登录")])],1):t._e()],1),t.currentStep<3?e("v-uni-view",{staticClass:"auth-footer"},[e("v-uni-text",{staticClass:"auth-footer-text"},[t._v("记起密码了？")]),e("v-uni-navigator",{staticClass:"auth-footer-link",attrs:{url:"/pages/auth/login"}},[t._v("返回登录")])],1):t._e()],1)},s=[]}}]);
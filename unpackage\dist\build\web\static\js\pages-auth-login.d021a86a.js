(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-auth-login"],{"0873":function(t,e,n){"use strict";n.r(e);var r=n("e91c"),o=n.n(r);for(var a in r)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(a);e["default"]=o.a},2634:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
e.default=function(){return t};var t={},n=Object.prototype,o=n.hasOwnProperty,a=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},s=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function l(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(P){l=function(t,e,n){return t[e]=n}}function f(t,e,n,r){var o=e&&e.prototype instanceof p?e:p,i=Object.create(o.prototype),s=new j(r||[]);return a(i,"_invoke",{value:k(t,n,s)}),i}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(P){return{type:"throw",arg:P}}}t.wrap=f;var h={};function p(){}function v(){}function g(){}var m={};l(m,s,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(F([])));y&&y!==n&&o.call(y,s)&&(m=y);var w=g.prototype=p.prototype=Object.create(m);function x(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function _(t,e){var n;a(this,"_invoke",{value:function(a,i){function s(){return new e((function(n,s){(function n(a,i,s,c){var u=d(t[a],t,i);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==(0,r.default)(f)&&o.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,s,c)}),(function(t){n("throw",t,s,c)})):e.resolve(f).then((function(t){l.value=t,s(l)}),(function(t){return n("throw",t,s,c)}))}c(u.arg)})(a,i,n,s)}))}return n=n?n.then(s,s):s()}})}function k(t,e,n){var r="suspendedStart";return function(o,a){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw a;return O()}for(n.method=o,n.arg=a;;){var i=n.delegate;if(i){var s=L(i,n);if(s){if(s===h)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=d(t,e,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===h)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}function L(t,e){var n=e.method,r=t.iterator[n];if(void 0===r)return e.delegate=null,"throw"===n&&t.iterator["return"]&&(e.method="return",e.arg=void 0,L(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),h;var o=d(r,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,h;var a=o.arg;return a?a.done?(e[t.resultName]=a.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,h):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function j(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function F(t){if(t){var e=t[s];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,r=function e(){for(;++n<t.length;)if(o.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return r.next=r}}return{next:O}}function O(){return{value:void 0,done:!0}}return v.prototype=g,a(w,"constructor",{value:g,configurable:!0}),a(g,"constructor",{value:v,configurable:!0}),v.displayName=l(g,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,l(t,u,"GeneratorFunction")),t.prototype=Object.create(w),t},t.awrap=function(t){return{__await:t}},x(_.prototype),l(_.prototype,c,(function(){return this})),t.AsyncIterator=_,t.async=function(e,n,r,o,a){void 0===a&&(a=Promise);var i=new _(f(e,n,r,o),a);return t.isGeneratorFunction(n)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},x(w),l(w,u,"Generator"),l(w,s,(function(){return this})),l(w,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},t.values=F,j.prototype={constructor:j,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!t)for(var e in this)"t"===e.charAt(0)&&o.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(n,r){return i.type="throw",i.arg=t,e.next=n,r&&(e.method="next",e.arg=void 0),!!r}for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r],i=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var s=o.call(a,"catchLoc"),c=o.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,h):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),C(n),h}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;C(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:F(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),h}},t},n("6a54"),n("01a2"),n("e39c"),n("bf0f"),n("844d"),n("18f7"),n("de6c"),n("3872e"),n("4e9b"),n("114e"),n("c240"),n("926e"),n("7a76"),n("c9b5"),n("aa9c"),n("2797"),n("8a8d"),n("dc69"),n("f7a5");var r=function(t){return t&&t.__esModule?t:{default:t}}(n("fcf3"))},"2fdc":function(t,e,n){"use strict";function r(t,e,n,r,o,a,i){try{var s=t[a](i),c=s.value}catch(u){return void n(u)}s.done?e(c):Promise.resolve(c).then(r,o)}n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return function(){var e=this,n=arguments;return new Promise((function(o,a){var i=t.apply(e,n);function s(t){r(i,o,a,s,c,"next",t)}function c(t){r(i,o,a,s,c,"throw",t)}s(void 0)}))}},n("bf0f")},"578c":function(t,e,n){var r=n("e677");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var o=n("967d").default;o("3de29ea4",r,!0,{sourceMap:!1,shadowMode:!1})},"6c9a":function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){}));var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"auth-container"},[n("v-uni-view",{staticClass:"auth-header"},[n("v-uni-view",{staticClass:"logo"},[t._v("CRM")]),n("v-uni-text",{staticClass:"welcome-text"},[t._v("欢迎回来")]),n("v-uni-text",{staticClass:"sub-text"},[t._v("请登录您的账号以继续")])],1),n("v-uni-view",{staticClass:"auth-form"},[n("v-uni-view",{staticClass:"form-group"},[n("v-uni-text",{staticClass:"form-label"},[t._v("账号")]),n("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入账号"},model:{value:t.loginForm.name,callback:function(e){t.$set(t.loginForm,"name",e)},expression:"loginForm.name"}})],1),n("v-uni-view",{staticClass:"form-group"},[n("v-uni-text",{staticClass:"form-label"},[t._v("密码")]),n("v-uni-view",{staticClass:"password-input-group"},["checkbox"===(t.passwordVisible?"text":"password")?n("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入密码",type:"checkbox"},model:{value:t.loginForm.password,callback:function(e){t.$set(t.loginForm,"password",e)},expression:"loginForm.password"}}):"radio"===(t.passwordVisible?"text":"password")?n("input",{directives:[{name:"model",rawName:"v-model",value:t.loginForm.password,expression:"loginForm.password"}],staticClass:"form-input",attrs:{placeholder:"请输入密码",type:"radio"},domProps:{checked:t._q(t.loginForm.password,null)},on:{change:function(e){return t.$set(t.loginForm,"password",null)}}}):n("input",{directives:[{name:"model",rawName:"v-model",value:t.loginForm.password,expression:"loginForm.password"}],staticClass:"form-input",attrs:{placeholder:"请输入密码",type:t.passwordVisible?"text":"password"},domProps:{value:t.loginForm.password},on:{input:function(e){e.target.composing||t.$set(t.loginForm,"password",e.target.value)}}}),n("v-uni-text",{staticClass:"password-toggle",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.togglePasswordVisibility.apply(void 0,arguments)}}},[t.passwordVisible?n("v-uni-text",{staticClass:"ri-eye-off-line"}):n("v-uni-text",{staticClass:"ri-eye-line"})],1)],1)],1),n("v-uni-view",{staticClass:"auth-options"},[n("v-uni-view",{staticClass:"remember-me"},[n("v-uni-checkbox",{attrs:{checked:t.rememberMe,color:"#3a86ff"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toggleRememberMe.apply(void 0,arguments)}}}),n("v-uni-text",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toggleRememberMe.apply(void 0,arguments)}}},[t._v("记住我")])],1),n("v-uni-navigator",{staticClass:"forgot-password",attrs:{url:"/pages/auth/forgot-password"}},[t._v("忘记密码？")])],1),n("v-uni-button",{staticClass:"btn btn-primary btn-full",attrs:{disabled:!t.canLogin},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleLogin.apply(void 0,arguments)}}},[t._v("登录")])],1)],1)},o=[]},ae01:function(t,e,n){"use strict";n.r(e);var r=n("6c9a"),o=n("0873");for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);n("fbbc");var i=n("828b"),s=Object(i["a"])(o["default"],r["b"],r["c"],!1,null,"3d9b8f4a",null,!1,r["a"],void 0);e["default"]=s.exports},c475:function(t,e,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.request=e.getTenantInfo=void 0;var o=r(n("9b1b"));n("bf0f"),n("4626"),n("5ac7");var a=null;e.getTenantInfo=function(t){return new Promise((function(e,n){uni.request({url:"/api/Tenants/getTenantsByLogin",method:"POST",data:t,header:{"Content-Type":"application/json"},success:function(t){200===t.statusCode?(a=t.data,e(t.data)):n(t.data)},fail:function(t){n(t)}})}))};e.request=function(t){return t.url.includes("/login")&&a&&(t.header=(0,o.default)((0,o.default)({},t.header),{},{__tenant:a[0].id})),new Promise((function(e,n){uni.request({url:t.url,method:t.method||"GET",data:t.data||{},header:(0,o.default)({Authorization:"Bearer "+uni.getStorageSync("token"),"Content-Type":"application/json"},t.header),success:function(t){200===t.statusCode?e(t.data):n(t.data)},fail:function(t){n(t)}})}))}},ca41:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.login=void 0;var r=n("c475");e.login=function(t){return(0,r.request)({url:"/api/app/account/login",method:"POST",data:t})}},e677:function(t,e,n){var r=n("c86c");e=r(!1),e.push([t.i,".auth-container[data-v-3d9b8f4a]{min-height:100vh;display:flex;flex-direction:column;padding:%?30?%;background-color:#fff}.auth-header[data-v-3d9b8f4a]{text-align:center;margin:%?60?% 0}.logo[data-v-3d9b8f4a]{font-size:%?80?%;font-weight:700;color:#3a86ff;margin-bottom:%?20?%}.welcome-text[data-v-3d9b8f4a]{font-size:%?48?%;color:#333;margin-bottom:%?10?%;display:block}.sub-text[data-v-3d9b8f4a]{font-size:%?32?%;color:#666;display:block}.auth-form[data-v-3d9b8f4a]{margin-top:%?20?%}.form-group[data-v-3d9b8f4a]{margin-bottom:%?30?%}.form-label[data-v-3d9b8f4a]{display:block;margin-bottom:%?10?%;color:#333;font-weight:500}.form-input[data-v-3d9b8f4a]{width:100%;height:%?90?%;padding:0 %?30?%;border:1px solid #ddd;border-radius:%?12?%;font-size:%?32?%;box-sizing:border-box}.password-input-group[data-v-3d9b8f4a]{position:relative}.password-toggle[data-v-3d9b8f4a]{position:absolute;right:%?30?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#666}.auth-options[data-v-3d9b8f4a]{display:flex;justify-content:space-between;align-items:center;margin-bottom:%?60?%}.remember-me[data-v-3d9b8f4a]{display:flex;align-items:center}.remember-me uni-text[data-v-3d9b8f4a]{margin-left:%?10?%;font-size:%?28?%;color:#666}.forgot-password[data-v-3d9b8f4a]{font-size:%?28?%;color:#3a86ff}.btn[data-v-3d9b8f4a]{height:%?90?%;display:flex;align-items:center;justify-content:center;border-radius:%?12?%;font-size:%?32?%;font-weight:500}.btn-primary[data-v-3d9b8f4a]{background-color:#3a86ff;color:#fff}.btn-primary[disabled][data-v-3d9b8f4a]{background-color:#a6c8ff}.btn-full[data-v-3d9b8f4a]{width:100%}.or-divider[data-v-3d9b8f4a]{display:flex;align-items:center;margin:%?40?% 0}.or-divider .line[data-v-3d9b8f4a]{flex:1;height:1px;background-color:#eee}.or-divider uni-text[data-v-3d9b8f4a]{margin:0 %?20?%;color:#999;font-size:%?28?%}.other-login-methods[data-v-3d9b8f4a]{display:flex;justify-content:center;margin-bottom:%?40?%}.login-method[data-v-3d9b8f4a]{display:flex;flex-direction:column;align-items:center;margin:0 %?30?%}.login-icon[data-v-3d9b8f4a]{width:%?90?%;height:%?90?%;border-radius:50%;display:flex;align-items:center;justify-content:center;margin-bottom:%?15?%}.login-icon uni-text[data-v-3d9b8f4a]{font-size:%?40?%;color:#fff}.wechat-icon[data-v-3d9b8f4a]{background-color:#07c160}.sms-icon[data-v-3d9b8f4a]{background-color:#ff9500}.login-method uni-text[data-v-3d9b8f4a]{font-size:%?24?%;color:#666}.auth-footer[data-v-3d9b8f4a]{text-align:center;margin-top:auto;padding:%?40?% 0;display:flex;justify-content:center}.auth-footer-text[data-v-3d9b8f4a]{color:#666;font-size:%?28?%}.auth-footer-link[data-v-3d9b8f4a]{color:#3a86ff;font-size:%?28?%;font-weight:500;margin-left:%?10?%}",""]),t.exports=e},e91c:function(t,e,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=r(n("2634")),a=r(n("2fdc")),i=n("c475"),s=n("ca41"),c={data:function(){return{loginForm:{name:"",password:""},passwordVisible:!1,rememberMe:!1,isLoading:!1}},computed:{canLogin:function(){return this.loginForm.name&&this.loginForm.password}},methods:{login:function(){var t=this;return(0,a.default)((0,o.default)().mark((function e(){var n;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,i.getTenantInfo)({emailPhoneNumber:t.loginForm.name,password:t.loginForm.password});case 3:return e.next=5,(0,s.login)(t.loginForm);case 5:n=e.sent,uni.switchTab({url:"/pages/dashboard/main-dashboard"}),uni.setStorageSync("token",n.token),uni.setStorageSync("userInfo",n),e.next=14;break;case 11:e.prev=11,e.t0=e["catch"](0),console.error("登录失败",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})))()},togglePasswordVisibility:function(){this.passwordVisible=!this.passwordVisible},toggleRememberMe:function(){this.rememberMe=!this.rememberMe},handleLogin:function(){this.login()},loginWithWechat:function(){uni.showToast({title:"微信登录功能开发中",icon:"none"})},loginWithSms:function(){uni.showToast({title:"短信登录功能开发中",icon:"none"})}}};e.default=c},fbbc:function(t,e,n){"use strict";var r=n("578c"),o=n.n(r);o.a}}]);
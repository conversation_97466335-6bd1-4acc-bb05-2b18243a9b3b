<template>
  <view class="container" :style="cssVars">
    <!-- 页面顶部Tabs -->
    <scroll-view class="tabs-container" scroll-x>
      <view
        class="tab"
        v-for="tab in tabs"
        :key="tab.id"
        :class="{ active: activeTab === tab.id }"
        @tap="switchTab(tab.id)"
      >
        {{ tab.displayText }}
      </view>
    </scroll-view>
    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <view class="search-icon">
          <svg-icon name="search" type="svg" size="32"></svg-icon>
        </view>
        <input
          type="text"
          class="search-input"
          v-model="searchKeyword"
          placeholder="搜索客户名称、地区等"
          @input="loadCustomers(true)"
        />
      </view>
    </view>
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <button class="filter-button" @tap="showFilterPanel">
        <svg-icon name="filter" type="svg" size="28"></svg-icon>
        <text>筛选</text>
      </button>
      <button class="sort-button" @tap="showSortOptions">
        <svg-icon name="sort" type="svg" size="28"></svg-icon>
        <text>排序</text>
      </button>
    </view>
    <!-- 客户列表 -->
    <view class="customers-list">
      <!-- 加载中状态 -->
       <view class="empty-state" v-show="isLoading">
        <view class="loading-icon">
          <svg-icon name="loading" type="svg" size="64"></svg-icon>
        </view>
        <view class="empty-title">正在加载</view>
        <view class="empty-description">正在获取客户数据，请稍候...</view>
      </view>
      <!-- 加载错误状态 -->
       <view class="empty-state" v-show="loadError">
        <view class="empty-icon">
          <svg-icon name="error" type="svg" size="64"></svg-icon>
        </view>
        <view class="empty-title">加载失败</view>
        <view class="empty-description">获取客户数据失败，请检查网络连接后重试。</view>
        <button class="btn btn-primary" @tap="loadCustomers">
          <svg-icon name="refresh" type="svg" size="32"></svg-icon> 重新加载
        </button>
      </view>
      <!-- 空状态提示 -->
       <view class="empty-state" v-show="customers.length === 0">
        <view class="empty-icon">
          <svg-icon name="customer" type="svg" size="96"></svg-icon>
        </view>
        <view class="empty-title">暂无客户</view>
        <view class="empty-description">您还没有创建任何客户，点击右下角的加号创建新客户。</view>
        <button class="btn btn-primary" @tap="navigateToCreate">
          <svg-icon name="add" type="svg" size="32"></svg-icon> 创建客户
        </button>
      </view>
      <!-- 客户卡片列表 -->
      <view v-if="!isLoading">
        <view
          class="customer-card"
          v-for="(customer) in customers"
          :key="customer.id"
          :data-status="customer.customLevel === null ? 'default' : customer.customLevel.code"
        >
          <view class="card-overlay" @tap="viewCustomer(customer)"></view>
          <view class="customer-header">
            <view class="customer-info">
              <view class="customer-name" @tap="viewCustomer(customer)">{{customer.name}}</view>
              <view class="customer-tags">
                <text
                  v-if="customer.customLevelName"
                  class="tag"
                  :class="customer.customLevel === null ? 'tag-default' : 'tag-' + customer.customLevel.code"
                >
                  {{ customer.customLevelName }}
                </text>
                <text v-if="customer.industry" class="tag tag-industry">{{ customer.industry }}</text>
              </view>
            </view>
            <view class="customer-value">
              {{ customer.registeredCapital | price }}W
            </view>
          </view>
          <view class="customer-content">
            <view class="customer-details">
              <view class="detail-item">
                <view class="detail-label">创建日期:</view>
                <view class="detail-value">
                  {{ customer.creationTime | formatDateFilter }}
                </view>
              </view>
              <view class="detail-item">
                <view class="detail-label">负责人:</view>
                <view class="detail-value">{{ customer.owner }}</view>
              </view>
              <view class="detail-item">
                <view class="detail-label">最近联系:</view>
                <view class="detail-value">
                  {{ customer.lastContact | formatDateFilter }}
                </view>
              </view>
              <view class="detail-item">
                <view class="detail-label">地区:</view>
                <view class="detail-value">{{ customer.province }} {{ customer.city }}</view>
              </view>
              <view class="detail-item">
                <view class="detail-label">联系人:</view>
                <view class="detail-value">{{ customer.contacts.length }}位联系人</view>
              </view>
              <view class="detail-item">
                <view class="detail-label">合作项目:</view>
                <view class="detail-value">{{ customer.projects || 0 }}个项目</view>
              </view>
              <view class="detail-item">
                <view class="detail-label">在跟商机:</view>
                <view
                    class="detail-value"
                    :class="{
                  'detail-value-highlight': hasActiveOpportunities(customer),
                }"
                >
                  {{ customer.opportunities || 0 }}个商机
                </view>
              </view>
              <view class="detail-item">
                <view class="detail-label">预计成交:</view>
                <view
                    class="detail-value"
                    :class="{ 'detail-value-urgent': isUrgent(customer) }"
                >
                  {{ getNextStepDate(customer) }}
                </view>
              </view>
            </view>
            <view class="customer-summary">
              <view class="summary-item">
                <view class="summary-icon">
                  <svg-icon name="opportunity" type="svg" size="24"></svg-icon>
                </view>
                <view
                    class="summary-value"
                    :class="getImportanceClass(customer)"
                >{{ getPotentialValue(customer.customLevelName) }}</view>
              </view>
              <view class="summary-item">
                <view class="summary-icon">
                  <svg-icon name="history" type="svg" size="24"></svg-icon>
                </view>
                <view class="summary-value">{{ customer.followUps || 0 }}次跟进</view>
              </view>
              <view class="summary-item">
                <view class="summary-icon">
                  <svg-icon name="time" type="svg" size="24"></svg-icon>
                </view>
                <view class="summary-value">{{getLastContactDays(customer)}}</view>
              </view>
            </view>
          </view>
          <view class="customer-footer">
            <view class="customer-action" @tap="addFollowUp(customer)">
              <svg-icon name="add" type="svg" size="28"></svg-icon> 添加跟进
            </view>
            <view class="customer-action" @tap="addOpportunity(customer)">
              <svg-icon name="opportunity" type="svg" size="28"></svg-icon>
              添加商机
            </view>
            <view class="customer-action" @tap="editCustomer(customer)">
              <svg-icon name="edit" type="svg" size="28"></svg-icon> 编辑
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 浮动添加按钮 -->
    <view class="fab" @tap="navigateToCreate">
      <svg-icon name="add" type="svg" size="60" color="#FFFFFF"></svg-icon>
    </view>

    <!-- 筛选面板 -->
    <view class="modal-filter" v-if="showFilter">
      <view class="modal-mask" @tap="hideFilterPanel"></view>
      <view class="modal-dialog">
        <view class="modal-header">
          <view class="modal-title">筛选条件</view>
          <view class="modal-close" @tap="hideFilterPanel">
            <svg-icon name="close" type="svg" size="32"></svg-icon>
          </view>
        </view>
        <scroll-view class="modal-content" scroll-y>
          <!-- 筛选表单内容 -->
          <!-- <view class="filter-group">
            <text class="filter-label">客户类型</text>
            <view class="checkbox-grid">
              <view
                v-for="(type, index) in customerTypes"
                :key="index"
                :class="[
                  'status-option',
                  { active: filterOptions.types.includes(type.value) },
                ]"
                @tap="toggleCustomerType(type.value)"
              >
                {{ type.name }}
              </view>
            </view>
          </view> -->
          <view class="filter-group">
            <text class="filter-label">国家</text>
            <view class="picker-wrapper" @tap="showCountryPicker">
              <view class="picker-value">
                {{ filterOptions.country || "请选择国家" }}
              </view>
              <view class="picker-arrow">
                <svg-icon name="arrow-down" type="svg" size="24"></svg-icon>
              </view>
            </view>
          </view>
          <view class="filter-group" v-if="filterOptions.country === '中国'">
            <text class="filter-label">省份</text>
            <view class="picker-wrapper" @tap="showProvincePicker">
              <view class="picker-value">
                {{ filterOptions.province || "请选择省份" }}
              </view>
              <view class="picker-arrow">
                <svg-icon name="arrow-down" type="svg" size="24"></svg-icon>
              </view>
            </view>
          </view>
          <view class="filter-group" v-else>
            <text class="filter-label">省份</text>
            <input type="text" class="picker-input" placeholder="请输入省份"></input>
          </view>

          <view class="filter-group" v-if="filterOptions.country === '中国'">
            <text class="filter-label">城市</text>
            <view class="picker-wrapper" @tap="showCityPicker">
              <view class="picker-value">
                {{ filterOptions.city || "请选择城市" }}
              </view>
              <view class="picker-arrow">
                <svg-icon name="arrow-down" type="svg" size="24"></svg-icon>
              </view>
            </view>
          </view>
          <view class="filter-group" v-else>
            <text class="filter-label">城市</text>
            <input type="text" class="picker-input" placeholder="请输入城市"></input>
          </view>
          <view class="filter-group">
            <text class="filter-label">行业</text>
            <view class="picker-wrapper" @tap="showIndustryPicker">
              <view class="picker-value">{{filterOptions.industry || "请选择行业"}}</view>
              <view class="picker-arrow">
                <svg-icon name="arrow-down" type="svg" size="24"></svg-icon>
              </view>
            </view>
          </view>
          <view class="filter-group">
            <text class="filter-label">负责人</text>
            <view class="picker-wrapper" @tap="showOwnerPicker">
              <view class="picker-value">{{filterOptions.owner || "请选择负责人"}}</view>
              <view class="picker-arrow">
                <svg-icon name="arrow-down" type="svg" size="24"></svg-icon>
              </view>
            </view>
          </view>
          <!-- <view class="filter-group">
            <text class="filter-label">金额范围</text>
            <view class="input-group">
              <input
                type="number"
                class="form-input"
                v-model="filterOptions.minAmount"
                placeholder="最小金额"
              />
              <input
                type="number"
                class="form-input"
                v-model="filterOptions.maxAmount"
                placeholder="最大金额"
              />
            </view>
          </view> -->
        </scroll-view>
        <view class="modal-footer">
          <button class="btn btn-reset" @tap="resetFilter">重置</button>
          <button class="btn btn-confirm" @tap="applyFilter">应用</button>
        </view>
      </view>
    </view>
    <!-- 选择器弹窗 -->
    <view class="uni-picker-popup" v-if="showPickerPopup">
      <view class="picker-mask" @tap="hidePickerPopup"></view>
      <view class="picker-content">
        <view class="picker-header">
          <view class="picker-action" @tap="hidePickerPopup">取消</view>
          <view class="picker-title">{{ pickerTitle }}</view>
          <view class="picker-action confirm" @tap="confirmPickerSelection">确定</view>
        </view>
        <picker-view
          class="picker-view"
          :value="pickerIndex"
          @change="onPickerChange"
        >
          <picker-view-column>
            <view
              class="picker-item"
              v-for="(item, index) in pickerItems"
              :key="index"
            >{{ item.name || item.displayText }}</view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>
    <!-- 自定义TabBar组件 -->
    <custom-tab-bar ref="customTabBar"></custom-tab-bar>
  </view>
</template>

<script>
import SvgIcon from "@/components/svg-icon.vue";
import CustomTabBar from "@/components/CustomTabBar.vue";
import getSelectOptions from "@/utils/dictionary";
import countryList from "../../country.json";
import provinceCity from "../../provinceCity.json";
import { getCustomerList, getOwnerList } from "@/api/customer.api";


export default {
  components: {
    SvgIcon,
    CustomTabBar,
  },
  computed: {
    // CSS变量计算值
    cssVars() {
      // 将主题色转换为RGB以便在rgba()中使用
      const primaryColor = "#0057ff"; // 假设的主色值
      const primaryColorRGB = this.hexToRgb(primaryColor);
      // 其他主题色变体
      const primaryColorLight = "#3a80ff";
      const primaryColorDark = "#0046cc";
      return {
        "--primary-color-rgb": primaryColorRGB,
        "--primary-color-light": primaryColorLight,
        "--primary-color-dark": primaryColorDark,
        "--light-color-rgb": "245, 247, 250",
        "--primary-color": primaryColor,
      };
    },
  },
  data() {
    return {
      // 选项卡数据
      tabs: [],
      activeTab: undefined,
      searchKeyword: "",
      showFilter: false,
      countryList: countryList,
      provinces: provinceCity,
      cityList: [],
      ownerList: [],
      // 筛选选项
      filterOptions: {
        // types: [],
        // minAmount: "",
        // maxAmount: "",
        country: "中国",
        province: "",
        city: "",
        industry: "",
        owner: "",
      },
      // 筛选选项数据源
      customerTypes: [
        { name: "重点客户", value: "key" },
        { name: "普通客户", value: "regular" },
        { name: "潜在客户", value: "potential" },
        { name: "非活跃客户", value: "inactive" },
      ],
      industries: [],
      owners: [],
      // Picker相关数据
      showPickerPopup: false,
      pickerType: "", // 'province', 'city', 'industry', 'owner'
      pickerItems: [],
      pickerIndex: [0],
      pickerTitle: "",
      tempSelectedValue: "",
      // 客户数据
      customers: [],
      pageIndex: 1,
      pageSize: 10,
      total: 0,
      isLoading: false,
      loadError: false,
      noMore: false,
    };
  },
  methods: {
    // 将十六进制颜色转换为RGB
    hexToRgb(hex) {
      // 移除#前缀如果存在
      hex = hex.replace(/^#/, "");

      // 解析十六进制
      let bigint = parseInt(hex, 16);
      let r = (bigint >> 16) & 255;
      let g = (bigint >> 8) & 255;
      let b = bigint & 255;

      return `${r}, ${g}, ${b}`;
    },
    // 获取数据字典
    async loadDictionaryOptions() {
      try {
        this.tabs = await getSelectOptions('CustomLevel');
        this.tabs.unshift({
          displayText: '全部客户',
          id: undefined,
        })
        this.industries = await getSelectOptions('CustomIndustry');
      } catch (error) {
        this.$message.error('加载字典数据失败');
      }
    },
    getOwner(){
      let params = {
        pageIndex: 1,
        pageSize:999,
      }
      getOwnerList(params).then((res) => {
        this.ownerList = res?.items?.filter((item) => item.isActive)
      })
    },
    // 加载客户数据
    async loadCustomers(isRefresh = false) {
      if (this.isLoading && !isRefresh) return;
      this.isLoading = true;
      this.loadError = false;
      try {
        const params = {
          pageIndex: isRefresh ? 1 : this.pageIndex,
          pageSize: this.pageSize,
          filter: {
            likeString: this.searchKeyword,
            customLevelId: this.activeTab,
            ...this.filterOptions
          }
        };
        const res = await getCustomerList(params);
        if (isRefresh) {
          this.customers = [...res.items];
          this.pageIndex = 1;
        } else {
          this.customers = [...this.customers, ...res.items];
        }
        this.total = res.totalCount;
        this.noMore = this.customers.length >= this.total;
        // 如果是刷新，停止刷新动画
        if (isRefresh) {
          uni.stopPullDownRefresh();
        }
      } catch (error) {
        console.error("加载客户数据失败", error);
        this.loadError = true;
      } finally {
        this.isLoading = false;
      }
    },
    // 加载更多数据
    loadMore() {
      if (this.noMore || this.isLoading) return;
      this.pageIndex++;
      this.loadCustomers();
    },
    // 切换选项卡
    switchTab(tabValue) {
      this.activeTab = tabValue;
      this.loadCustomers(true);
    },
    // 显示筛选面板
    showFilterPanel() {
      this.showFilter = true;
      // 添加防止滚动
      document.body.style.overflow = "hidden";
    },
    // 隐藏筛选面板
    hideFilterPanel() {
      this.showFilter = false;
      // 恢复滚动
      document.body.style.overflow = "";
    },
    // 显示排序选项
    showSortOptions() {
      uni.showActionSheet({
        itemList: ["客户名称", "创建时间"],
        success: (res) => {
          const sortTypes = ["Name", "CreationTime"];
          this.filterOptions.sortProperty = sortTypes[res.tapIndex];
          this.filterOptions.sortAsc = true;
          this.loadCustomers(true);
          this.showToast(`已按${res.tapIndex === 0 ? "客户名称" : "创建时间"}排序`);
        },
      });
    },
    // 重置筛选
    resetFilter() {
      this.showFilter = false
      this.filterOptions = {
        country: "中国",
        province: "",
        city: "",
        industry: "",
        owner: "",
      };
      this.showToast("筛选条件已重置");
      this.loadCustomers(true);
    },

    // 应用筛选
    applyFilter() {
      let filterCount = 0;
      if (this.filterOptions.province) filterCount++;
      if (this.filterOptions.city) filterCount++;
      if (this.filterOptions.industry) filterCount++;
      if (this.filterOptions.owner) filterCount++;
      this.hideFilterPanel();
      if (filterCount > 0) {
        uni.showToast({
          title: `已应用${filterCount}个筛选条件`,
          icon: "success",
          duration: 2000,
        });
      } else {
        this.showToast("未设置筛选条件");
      }
      this.loadCustomers(true);
    },
    // 切换客户类型选择
    toggleCustomerType(typeValue) {
      if (this.filterOptions.types.includes(typeValue)) {
        this.filterOptions.types = this.filterOptions.types.filter(
          (t) => t !== typeValue
        );
      } else {
        this.filterOptions.types.push(typeValue);
      }
    },

    // 显示国家
    showCountryPicker() {
      this.pickerType = "country";
      this.pickerItems = this.countryList;
      this.pickerTitle = "选择国家";
      this.pickerIndex = [this.countryList.indexOf(this.filterOptions.country)];
      this.tempSelectedValue = this.countryList[0];
      this.showPickerPopup = true;
    },
    // 显示省份选择器
    showProvincePicker() {
      this.pickerType = "province";
      this.pickerItems = this.provinces.province;
      this.pickerTitle = "选择省份";
      this.pickerIndex = [
        this.provinces.province.indexOf(this.filterOptions.province),
      ];
      this.tempSelectedValue = this.provinces.province[0];
      this.showPickerPopup = true;
    },

    // 显示城市选择器
    showCityPicker() {
      this.pickerType = "city";
      this.pickerItems = this.cityList;
      this.pickerTitle = "选择城市";
      this.pickerIndex = [this.cityList.indexOf(this.filterOptions.city)];
      this.tempSelectedValue = this.cityList[0];
      this.showPickerPopup = true;
    },

    // 显示行业选择器
    showIndustryPicker() {
      this.pickerType = "industry";
      this.pickerItems = this.industries;
      this.pickerTitle = "选择行业";
      // 设置当前选中的索引
      const industryIndex = this.filterOptions.industry ? this.industries.findIndex(item => item.displayText === this.filterOptions.industry) : 0;
      this.pickerIndex = [industryIndex >= 0 ? industryIndex : 0];
      this.tempSelectedValue = this.industries[this.pickerIndex[0]];
      this.showPickerPopup = true;
    },
    // 显示负责人选择器
    showOwnerPicker() {
      this.pickerType = "owner";
      this.pickerItems = this.ownerList;
      this.pickerTitle = "选择负责人";
      // 设置当前选中的索引
      const ownerIndex = this.filterOptions.owner ? this.ownerList.findIndex(item => item.name === this.filterOptions.owner) : 0;
      this.pickerIndex = [ownerIndex >= 0 ? ownerIndex : 0];
      this.tempSelectedValue = this.ownerList[this.pickerIndex[0]];
      this.showPickerPopup = true;
    },
    // 隐藏选择器
    hidePickerPopup() {
      this.showPickerPopup = false;
      this.tempSelectedValue = ""; // 清空临时值
    },

    // 确认选择器选择
    confirmPickerSelection() {
      if (this.pickerType === "province") {
        this.filterOptions.province = this.tempSelectedValue.name;
        this.filterOptions.city = "";
        this.cityList = this.provinces.city.filter((item) => {
          return item.ProID === this.tempSelectedValue.ProID;
        });
      } else if (this.pickerType === "city") {
        this.filterOptions.city = this.tempSelectedValue.name;
      } else if (this.pickerType === "industry") {
        this.filterOptions.industry = this.tempSelectedValue.displayText;
      } else if (this.pickerType === "owner") {   
        this.filterOptions.owner = this.tempSelectedValue.name;
      } else if (this.pickerType === "country") {
        this.filterOptions.country = this.tempSelectedValue.name;
      }
      this.hidePickerPopup();
      this.showToast(`已选择${this.tempSelectedValue}`);
    },
    // 选择器变化事件
    onPickerChange(e) {
      const index = e.detail.value[0];
      if (index >= 0 && index < this.pickerItems.length) {
        this.tempSelectedValue = this.pickerItems[index];
      }
    },
    // 导航到创建客户页面
    navigateToCreate() {
      uni.navigateTo({
        url: "/pages/customers/customer-create",
      });
    },
    viewCustomer(customer) {
      uni.navigateTo({
        url: `/pages/customers/customer-detail?id=${customer.id}`,
      });
    },
    editCustomer(customer) {
      uni.navigateTo({
        url: `/pages/customers/customer-edit?id=${customer.id}`,
      });
    },
    // 获取主要联系人
    getPrimaryContact(customer) {
      return customer.contacts.find((contact) => contact.isPrimary);
    },
    // viewContactDetails(contact) {
    //   uni.navigateTo({
    //     url: `/pages/customers/contact-detail?id=${contact.id}`,
    //   });
    // },
    addFollowUp(customer) {
      uni.navigateTo({
        url: `/pages/interactions/interaction-create?type=follow-up&relatedId=${customer.id}&relatedType=customer&relatedName=
        ${encodeURIComponent(customer.name)}`,
      });
    },
    addOpportunity(customer) {
      uni.navigateTo({
        url: `/pages/sales/opportunity-create?customerId=${customer.id}&customerName=${encodeURIComponent(customer.name)}`,
      });
    },
    // 获取预计成交日期
    getNextStepDate(customer) {
      // 这里需要根据实际业务逻辑来实现
      // 这里只是一个示例，实际应用中需要根据客户状态和业务逻辑来计算预计成交日期
      return "2024-03-15"; // 临时返回值，实际应用中需要根据实际情况计算
    },

    // 判断是否为紧急客户
    isUrgent(customer) {
      // 这里需要根据实际业务逻辑来实现
      // 这里只是一个示例，实际应用中需要根据客户状态和业务逻辑来判断是否为紧急客户
      return false; // 临时返回值，实际应用中需要根据实际情况判断
    },

    // 获取重要性类
    getImportanceClass(customer) {
      // 这里需要根据实际业务逻辑来实现
      // 这里只是一个示例，实际应用中需要根据客户状态和业务逻辑来判断重要性
      return "high"; // 临时返回值，实际应用中需要根据实际情况判断
    },
    // 获取潜在价值
    getPotentialValue(level) {
      return level || "未知";
    },
    // 获取最后联系天数
    getLastContactDays(customer) {
      if (!customer.lastContact) return "暂无联系";
      // 计算天数差异
      const lastDate = new Date(customer.lastContact);
      const today = new Date();
      const diffTime = Math.abs(today - lastDate);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      if (diffDays === 0) return "今天";
      if (diffDays === 1) return "昨天";
      return `${diffDays}天前`;
    },
    // 判断是否有活跃商机
    hasActiveOpportunities(customer) {
      // 这里需要根据实际业务逻辑来实现
      // 这里只是一个示例，实际应用中需要根据客户状态和业务逻辑来判断是否有活跃商机
      return customer.opportunities && customer.opportunities > 0;
    },
    // Toast提示
    showToast(message) {
      uni.showToast({
        title: message,
        icon: "none",
      });
    },
  },
  onShow() {
    this.customers = [];
    uni.pageScrollTo({
        scrollTop: 0, // 滚动到顶部
        duration: 300, // 滚动动画时长
      });
    this.loadCustomers(true);
    // 设置TabBar选中项
    if (typeof this.$refs.customTabBar !== "undefined") {
      this.$refs.customTabBar.current = 1;
    } else {
      // 如果refs还没准备好，尝试延迟设置
      setTimeout(() => {
        if (typeof this.$refs.customTabBar !== "undefined") {
          this.$refs.customTabBar.current = 1;
        }
      }, 300);
    }
  },
  onLoad() {
    this.loadDictionaryOptions();
    this.getOwner();
    // 初始化加载客户列表
    this.loadCustomers(true);
  },
  onPullDownRefresh() {
    this.loadCustomers(true);
  },
  onReachBottom() {
    this.loadMore();
  },

};
</script>

<style lang="scss">
.container {
  background-color: #f8fafc;
  min-height: 100vh;
}

.tabs-container {
  display: flex;
  white-space: nowrap;
  background-color: #ffffff;
  border-bottom: 1rpx solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.tab {
  display: inline-block;
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--text-secondary);
  font-size: 28rpx;
  position: relative;
  transition: all 0.2s ease;
}

.tab.active {
  color: var(--primary-color);
  font-weight: 600;
}

.tab.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: var(--spacing-lg);
  right: var(--spacing-lg);
  height: 4rpx;
  background-color: var(--primary-color);
  border-radius: var(--radius-full);
}

/* 搜索栏样式 */
.search-container {
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: #ffffff;
  border-bottom: 1rpx solid var(--border-color);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
}

.search-box {
  display: flex;
  align-items: center;
  background-color: var(--light-color);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 0 var(--spacing-sm);
  overflow: hidden;
  box-shadow: inset 0 2rpx 5rpx rgba(0, 0, 0, 0.03);
}

.search-icon {
  color: var(--text-secondary);
  padding: var(--spacing-xs);
}

.search-input {
  flex: 1;
  border: none;
  padding: var(--spacing-sm);
  background-color: transparent;
  color: var(--text-primary);
  font-size: 28rpx;
}

/* 筛选栏样式 */
.filter-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: #ffffff;
  border-bottom: 1rpx solid var(--border-color);
}

.filter-button,
.sort-button {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: var(--text-secondary);
  padding: 16rpx 24rpx;
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--light-color);
  transition: all 0.2s ease;
}

.filter-button:active,
.sort-button:active {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  border-color: var(--primary-color-light);
}

.filter-button text,
.sort-button text {
  margin-left: 8rpx;
}

/* 客户列表和卡片样式 */
.customers-list {
  padding: var(--spacing-md) 0;
  padding-bottom: calc(var(--spacing-xl) * 4); /* 增加底部填充空间 */
}

.customer-card {
  background-color: #ffffff;
  border-radius: var(--radius-md);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  margin-bottom: var(--spacing-md);
  overflow: hidden;
  border: 1rpx solid var(--border-color);
  position: relative;
  transition: all 0.3s ease;
}

.customer-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 客户状态标识条 */
.customer-card::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background-color: var(--border-color);
  z-index: 2;
}
.customer-card[data-status="A"]::before {
  background-color: #f59e0b;
}
.customer-card[data-status="B"]::before {
  background-color: #0ea5e9;
}
.customer-card[data-status="C"]::before {
  background-color: #3b82f6;
}
.customer-card[data-status="default"]::before {
  background-color: #9ca3af;
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.customer-header,
.customer-content,
.customer-footer {
  position: relative;
  z-index: 2;
}

/* 确保所有可点击元素在overlay之上 */
.customer-name,
.contact-actions,
.customer-action {
  position: relative;
  z-index: 3;
}

.customer-header {
  padding: var(--spacing-md);
  border-bottom: 1rpx solid var(--border-color-light);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: linear-gradient(
    to right,
    rgba(249, 250, 251, 0.5),
    rgba(255, 255, 255, 0.8)
  );
}

.customer-info {
  flex: 1;
}

.customer-name {
  font-size: 32rpx;
  font-weight: 600;
  margin: 0 0 12rpx 0;
  color: var(--text-primary);
  cursor: pointer;
  position: relative;
  display: inline-block;
  padding: 4rpx 0;
}

.customer-name:hover,
.customer-name:active {
  color: var(--primary-color);
}

.customer-name:active:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background-color: var(--primary-color);
}

.customer-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: var(--spacing-xs);
}

.tag {
  padding: 4rpx 16rpx;
  border-radius: var(--radius-full);
  font-size: 22rpx;
  font-weight: 500;
  &.tag-A {
    background-color: #fef3c7;
    color: #d97706;
  }
  &.tag-B {
    background-color: #e0f2fe;
    color: #0284c7;
  }
  &.tag-C {
    background-color: #dbeafe;
    color: #2563eb;
  }
  &.tag-default {
    background-color: #9ca3af;
    color: #000;
  }
  &.tag-industry {
    background-color: #f3f4f6;
    color: #4b5563;
  }
}

.customer-value {
  font-weight: 700;
  color: var(--primary-color);
  font-size: 32rpx;
  background-color: rgba(var(--primary-color-rgb), 0.1);
  padding: 8rpx 16rpx;
  border-radius: var(--radius-md);
  text-align: right;
}

.customer-content {
  padding: var(--spacing-md);
}

.customer-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
  background-color: rgba(var(--light-color-rgb), 0.3);
  padding: var(--spacing-md) var(--spacing-sm);
  border-radius: var(--radius-md);
}

.detail-item {
  min-width: 0;
  display: flex;
  align-items: baseline;
  margin-bottom: var(--spacing-xs);
}

.detail-label {
  font-size: 24rpx;
  color: var(--text-tertiary);
  margin-right: 8rpx;
  white-space: nowrap;
}

.detail-value {
  font-size: 26rpx;
  color: var(--text-secondary);
  font-weight: 500;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 联系人数量突出显示 */
.detail-item:last-child .detail-value {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  padding: 2rpx 12rpx;
  border-radius: var(--radius-full);
  display: inline-block;
  font-weight: 600;
  font-size: 24rpx;
}

/* 紧急客户样式 */
.detail-value-urgent {
  color: #ef4444 !important;
  font-weight: 700 !important;
}

/* 高亮显示样式 */
.detail-value-highlight {
  color: #3b82f6 !important;
  font-weight: 600 !important;
}

/* 添加底部信息栏 */
.customer-summary {
  margin-top: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: rgba(var(--light-color-rgb), 0.5);
  border-radius: var(--radius-md);
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
}

.summary-item {
  display: flex;
  align-items: center;
}

.summary-icon {
  margin-right: 6rpx;
  color: var(--text-tertiary);
}

.summary-value {
  color: var(--text-primary);
  font-weight: 500;
}

.summary-value.high {
  color: #10b981;
}

.summary-value.medium {
  color: #f59e0b;
}

.summary-value.low {
  color: #6b7280;
}

.customer-footer {
  display: flex;
  border-top: 1rpx solid var(--border-color);
  background: linear-gradient(to bottom, #ffffff, #f9fafb);
}

.customer-action {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 0;
  color: var(--text-secondary);
  font-size: 26rpx;
  transition: all 0.2s ease;
}

.customer-action:active {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
}

.customer-action:not(:last-child) {
  border-right: 1rpx solid var(--border-color-light);
}

/* 浮动操作按钮 */
.fab {
  position: fixed;
  bottom: calc(128rpx + var(--spacing-xl)); /* 调整底部位置，避开TabBar */
  right: var(--spacing-xl);
  width: 110rpx; /* 减小尺寸 */
  height: 110rpx; /* 减小尺寸 */
  border-radius: 50%;
  background: linear-gradient(135deg, #0a6bff, #0057ff);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6),
    0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  z-index: 100;
  transition: all 0.3s ease;
  border: 4rpx solid rgba(255, 255, 255, 0.7);
  animation: pulse 2s infinite; /* 添加脉动动画 */
}

.fab:active {
  transform: scale(0.95);
  box-shadow: 0 5rpx 10rpx rgba(0, 87, 255, 0.5),
    0 3rpx 3rpx rgba(0, 87, 255, 0.3);
  animation: none; /* 点击时停止动画 */
}

/* 添加脉动动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6),
      0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 15rpx 25rpx rgba(0, 87, 255, 0.7),
      0 8rpx 10rpx rgba(0, 87, 255, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6),
      0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  }
}

/* 筛选面板样式完善 */
.modal-filter {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1001;
}

.modal-mask {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-dialog {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideUp 0.3s ease;
  box-shadow: 0 -8rpx 24rpx rgba(0, 0, 0, 0.12);
}

.modal-header {
  padding: 24rpx;
  border-bottom: 1rpx solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: var(--text-secondary);
}

.modal-content {
  padding: 24rpx;
  max-height: 60vh;
  overflow-y: auto;
  overflow-x: hidden; /* 防止水平滚动 */
  width: 100%;
  box-sizing: border-box;
}

.filter-group {
  margin-bottom: 32rpx;
  width: 100%;
  box-sizing: border-box;
}

.filter-label {
  display: block;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-secondary);
}

.checkbox-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.status-option {
  padding: 12rpx 20rpx; /* 减小内边距 */
  border-radius: 100rpx;
  font-size: 26rpx;
  background-color: #f5f7fa;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  border: 1rpx solid transparent;
  box-sizing: border-box;
}

.status-option.active {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  font-weight: 500;
  border: 1rpx solid var(--primary-color);
}

.status-option:active {
  transform: scale(0.95);
}

.picker-wrapper {
  padding: 20rpx 24rpx;
  background-color: #f5f7fa;
  border-radius: 12rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  border: 1rpx solid #e0e5ed;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
}

.picker-wrapper:active {
  background-color: #edf2fa;
}

.picker-value {
  font-size: 28rpx;
  color: var(--text-primary);
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 10rpx;
}

.picker-arrow {
  color: var(--text-tertiary);
  flex-shrink: 0;
}

.input-group {
  display: flex;
  align-items: center;
  gap: 16rpx;
  width: 100%;
  box-sizing: border-box;
}

.input-group text {
  color: var(--text-secondary);
}

.form-input {
  flex: 1;
  padding: 20rpx 24rpx;
  background-color: #f5f7fa;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: var(--text-primary);
  border: 1rpx solid #e0e5ed;
  width: 0; /* 让flex布局自动计算宽度 */
  min-width: 0; /* 防止内容超出 */
  box-sizing: border-box;
  height: 80rpx; /* 确保高度与其他选择器控件一致 */
}

/* 确保输入框获取焦点时有明显反馈 */
.form-input:focus {
  border-color: var(--primary-color);
  background-color: #ffffff;
  outline: none;
}

.modal-footer {
  padding: 24rpx;
  display: flex;
  border-top: 1rpx solid var(--border-color-light);
  background-color: #f9fafb;
}

.btn {
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  flex: 1;
  text-align: center;
}

.btn-reset {
  background-color: #f5f7fa;
  color: var(--text-secondary);
  margin-right: 16rpx;
  border: 1rpx solid #e0e5ed;
}

.btn-reset:active {
  background-color: #e5e7eb;
}

.btn-confirm {
  background-color: var(--primary-color);
  color: #ffffff;
  font-weight: 500;
}

.btn-confirm:active {
  background-color: var(--primary-color-dark);
}

/* 选择器弹窗样式完善 */
.uni-picker-popup {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1002;
}

.picker-mask {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  animation: fadeIn 0.3s ease;
}

.picker-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  animation: slideUp 0.3s ease;
  box-shadow: 0 -8rpx 24rpx rgba(0, 0, 0, 0.12);
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.picker-action {
  font-size: 28rpx;
  color: var(--text-secondary);
  padding: 8rpx 16rpx;
}

.picker-action.confirm {
  color: var(--primary-color);
  font-weight: 500;
}

.picker-action:active {
  opacity: 0.7;
}

.picker-title {
  font-size: 30rpx;
  font-weight: 500;
  color: var(--text-primary);
}

.picker-view {
  height: 400rpx;
  width: 100%;
}

.picker-item {
  line-height: 100rpx;
  text-align: center;
  font-size: 28rpx;
  color: var(--text-primary);
}

/* 空状态样式 */
.empty-state {
  padding: var(--spacing-xl) var(--spacing-lg);
  text-align: center;
  background-color: #ffffff;
  border-radius: var(--radius-lg);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  margin: var(--spacing-lg) 0;
}

.empty-icon,
.loading-icon {
  margin-bottom: var(--spacing-md);
  color: var(--border-color);
  display: inline-block;
}

.loading-icon {
  animation: rotating 2s linear infinite;
  color: var(--primary-color);
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.empty-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: 1.5;
}

/* 增加底部间距，避免内容被底部操作栏遮挡 */
.page-container {
  padding-bottom: calc(128rpx + var(--spacing-md) * 2 + var(--spacing-lg));
}
.picker-input {
  width: 100%;
  padding: 20rpx 24rpx;
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: #f5f7fa;
  font-size: 28rpx;
  color: var(--text-primary);
}
</style>

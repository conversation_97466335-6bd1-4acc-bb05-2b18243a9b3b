<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <navigator url="./customer-detail" open-type="navigateBack" class="back-button">
        <svg-icon name="arrow-left" type="svg" size="32"></svg-icon>
      </navigator>
      <view class="page-title">添加跟进记录</view>
      <button class="save-button" :disabled="!formData.content" @tap="saveFollowUp">保存</button>
    </view>

    <view class="page-container">
      <view class="form-section">
        <view class="section-header">
          <svg-icon name="history" type="svg" size="28"></svg-icon>
          <text class="section-title">跟进信息</text>
        </view>
        <view class="section-content">
          <view class="form-group">
            <text class="form-label required">跟进类型</text>
            <view class="follow-up-types">
              <view 
                v-for="(type, index) in followUpTypes" 
                :key="index"
                class="type-option" 
                :class="{ selected: formData.type === type.value }"
                @tap="formData.type = type.value"
              >
                <svg-icon :name="type.icon" type="svg" size="32"></svg-icon>
                <text>{{ type.label }}</text>
              </view>
            </view>
          </view>
          
          <view class="form-group">
            <text class="form-label required">跟进内容</text>
            <textarea 
              class="form-control" 
              placeholder="请输入跟进内容..." 
              v-model="formData.content"
            ></textarea>
          </view>
          
          <view class="form-group">
            <text class="form-label">下一步计划</text>
            <view class="next-steps">
              <view 
                v-for="(step, index) in nextSteps" 
                :key="index"
                class="next-step" 
                :class="{ selected: formData.selectedSteps.includes(step) }"
                @tap="toggleNextStep(step)"
              >
                {{ step }}
              </view>
            </view>
          </view>
          
          <view class="form-group">
            <text class="form-label">跟进时间</text>
            <view class="date-picker">
              <picker 
                mode="datetime" 
                :value="formData.followUpTime"
                @change="onDateTimeChange"
              >
                <view class="form-control picker-value">
                  {{ formData.followUpTime || '请选择跟进时间' }}
                </view>
              </picker>
              <svg-icon name="calendar" type="svg" size="28" class="picker-icon"></svg-icon>
            </view>
          </view>
        </view>
      </view>
      
      <view class="form-section">
        <view class="section-header">
          <svg-icon name="attachment" type="svg" size="28"></svg-icon>
          <text class="section-title">附件</text>
        </view>
        <view class="section-content">
          <view class="attachment-upload" @tap="chooseFile">
            <svg-icon name="upload" type="svg" size="32"></svg-icon>
            <text>点击上传附件</text>
          </view>
          
          <view class="attachment-list" v-if="formData.attachments.length > 0">
            <view 
              class="attachment-item" 
              v-for="(attachment, index) in formData.attachments" 
              :key="index"
            >
              <view class="attachment-info">
                <view class="attachment-icon">
                  <svg-icon :name="getFileIconName(attachment.name)" type="svg" size="32"></svg-icon>
                </view>
                <view>
                  <view class="attachment-name">{{ attachment.name }}</view>
                  <view class="attachment-size">{{ formatFileSize(attachment.size) }}</view>
                </view>
              </view>
              <view class="attachment-delete" @tap="deleteAttachment(index)">
                <svg-icon name="delete" type="svg" size="28"></svg-icon>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import SvgIcon from '@/components/svg-icon.vue';

export default {
  components: {
    SvgIcon
  },
  data() {
    return {
      customerId: '',
      formData: {
        type: 'call',
        content: '',
        selectedSteps: ['安排面谈'],
        followUpTime: this.formatDate(new Date()),
        attachments: [
          {
            name: '产品方案.pdf',
            size: 2621440, // 2.5MB
            path: '/temp/product_plan.pdf'
          },
          {
            name: '会议照片.jpg',
            size: 1258291, // 1.2MB
            path: '/temp/meeting_photo.jpg'
          }
        ]
      },
      
      // 跟进类型选项
      followUpTypes: [
        { label: '电话沟通', value: 'call', icon: 'call' },
        { label: '客户拜访', value: 'visit', icon: 'team' },
        { label: '邮件往来', value: 'email', icon: 'email' },
        { label: '其他', value: 'other', icon: 'more' }
      ],
      
      // 下一步计划选项
      nextSteps: ['安排面谈', '发送方案', '电话跟进', '等待反馈', '制定报价', '签订合同']
    }
  },
  methods: {
    // 获取文件图标名称
    getFileIconName(filename) {
      const ext = filename.split('.').pop().toLowerCase()
      
      if (['jpg', 'jpeg', 'png', 'gif'].includes(ext)) {
        return 'image'
      } else if (['pdf'].includes(ext)) {
        return 'file-pdf'
      } else if (['doc', 'docx'].includes(ext)) {
        return 'file-word'
      } else if (['xls', 'xlsx'].includes(ext)) {
        return 'file-excel'
      } else if (['ppt', 'pptx'].includes(ext)) {
        return 'file-ppt'
      } else {
        return 'file'
      }
    },
    
    // 切换下一步计划
    toggleNextStep(step) {
      const index = this.formData.selectedSteps.indexOf(step)
      if (index > -1) {
        this.formData.selectedSteps.splice(index, 1)
      } else {
        this.formData.selectedSteps.push(step)
      }
    },
    
    // 日期时间选择器变更
    onDateTimeChange(e) {
      this.formData.followUpTime = e.detail.value
    },
    
    // 选择文件
    chooseFile() {
      uni.chooseFile({
        count: 1,
        success: (res) => {
          // 处理选中的文件
          const tempFile = res.tempFiles[0]
          this.formData.attachments.push({
            name: tempFile.name || `附件${this.formData.attachments.length + 1}`,
            size: tempFile.size,
            path: tempFile.path
          })
        },
        fail: (err) => {
          if (err.errMsg !== 'chooseFile:fail cancel') {
            uni.showToast({
              title: '选择文件失败',
              icon: 'none'
            })
          }
        }
      })
    },
    
    // 删除附件
    deleteAttachment(index) {
      this.formData.attachments.splice(index, 1)
    },
    
    // 格式化文件大小
    formatFileSize(size) {
      if (size < 1024) {
        return size + 'B'
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(1) + 'KB'
      } else if (size < 1024 * 1024 * 1024) {
        return (size / 1024 / 1024).toFixed(1) + 'MB'
      } else {
        return (size / 1024 / 1024 / 1024).toFixed(1) + 'GB'
      }
    },
    
    // 格式化日期时间
    formatDate(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      const hour = date.getHours().toString().padStart(2, '0')
      const minute = date.getMinutes().toString().padStart(2, '0')
      
      return `${year}-${month}-${day} ${hour}:${minute}`
    },
    
    // 保存跟进记录
    saveFollowUp() {
      if (!this.formData.content) {
        uni.showToast({
          title: '请填写跟进内容',
          icon: 'none'
        })
        return
      }
      
      uni.showLoading({
        title: '保存中...'
      })
      
      // 构建跟进记录数据
      const followUpData = {
        customerId: this.customerId,
        type: this.formData.type,
        content: this.formData.content,
        nextSteps: this.formData.selectedSteps,
        followUpTime: this.formData.followUpTime,
        // 处理附件数据...
      }
      
      // 这里添加实际的API调用
      console.log('保存跟进记录', followUpData)
      
      // 模拟保存成功
      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '保存成功',
          icon: 'success',
          success: () => {
            setTimeout(() => {
              uni.navigateBack()
            }, 1500)
          }
        })
      }, 1000)
    }
  },
  onLoad(options) {
    if (options.id) {
      this.customerId = options.id
    }
  }
}
</script>

<style>
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1rpx solid var(--border-color);
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.page-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
}

.back-button {
  color: var(--text-secondary);
  display: flex;
  align-items: center;
}

.save-button {
  color: var(--primary-color);
  font-weight: 500;
  font-size: 28rpx;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: transparent;
  border: none;
  line-height: 1;
}

.save-button:disabled {
  color: var(--text-tertiary);
  opacity: 0.5;
}

.page-container {
  padding: var(--spacing-md) var(--spacing-lg);
}

/* 表单样式 */
.form-section {
  background-color: white;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-md);
  overflow: hidden;
  border: 1rpx solid var(--border-color);
}

.section-header {
  padding: var(--spacing-md);
  border-bottom: 1rpx solid var(--border-color-light);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-secondary);
}

.section-header text:first-child {
  color: var(--primary-color);
}

.section-content {
  padding: var(--spacing-md);
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.required::after {
  content: "*";
  color: var(--danger-color);
  margin-left: var(--spacing-xs);
}

.form-control {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 28rpx;
  color: var(--text-primary);
  background-color: white;
}

/* 跟进类型选择 */
.follow-up-types {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.type-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-md);
}

.type-option.selected {
  border-color: var(--primary-color);
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.type-option text:first-child {
  font-size: 32rpx;
}

/* 文本域样式 */
textarea.form-control {
  min-height: 200rpx;
  width: 100%;
}

/* 日期选择器 */
.date-picker {
  position: relative;
}

.picker-value {
  padding-right: var(--spacing-xl);
}

.picker-icon {
  position: absolute;
  right: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
  pointer-events: none;
}

/* 下一步计划 */
.next-steps {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-sm);
}

.next-step {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--light-color);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-full);
  font-size: 24rpx;
  color: var(--text-secondary);
}

.next-step.selected {
  background-color: var(--primary-light);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

/* 附件上传 */
.attachment-upload {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1rpx dashed var(--border-color);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-md);
  color: var(--text-tertiary);
}

.attachment-upload text {
  font-size: 28rpx;
  color: var(--text-secondary);
}

/* 已上传附件列表 */
.attachment-list {
  margin-top: var(--spacing-sm);
}

.attachment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm);
  background-color: var(--light-color);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-xs);
}

.attachment-item:last-child {
  margin-bottom: 0;
}

.attachment-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.attachment-icon {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  border-radius: var(--radius-md);
  color: var(--text-secondary);
}

.attachment-name {
  font-size: 28rpx;
  color: var(--text-primary);
}

.attachment-size {
  font-size: 24rpx;
  color: var(--text-tertiary);
}

.attachment-delete {
  color: var(--text-tertiary);
  padding: var(--spacing-xs);
  border-radius: var(--radius-full);
}
</style> 
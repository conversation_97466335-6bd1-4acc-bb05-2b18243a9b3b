<template>
  <view class="page-layout">
    <slot></slot>
    <custom-tab-bar v-if="showTabBar"></custom-tab-bar>
  </view>
</template>

<script>
import CustomTabBar from '../custom-tab-bar/index.vue';

export default {
  components: {
    CustomTabBar
  },
  props: {
    showTabBar: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {};
  },
  created() {
    // 存储tabBar实例在全局变量中，以便页面间共享
    const app = getApp();
    if (app && app.globalData) {
      app.globalData.customTabBar = this.$refs.customTabBar;
    }
  }
};
</script>

<style>
.page-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
}
</style> 
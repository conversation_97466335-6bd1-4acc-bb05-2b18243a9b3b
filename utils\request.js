// 租户信息缓存
let tenantInfo = null;
/**
 * 获取租户信息
 * @param {Object} credentials - 包含账号密码的对象
 */
export const getTenantInfo = (credentials) => {
  return new Promise((resolve, reject) => {
    uni.request({
      url: '/api/Tenants/getTenantsByLogin',
      method: 'POST',
      data: credentials,
      header: {
        'Content-Type': 'application/json'
      },
      success: (res) => {
        if (res.statusCode === 200) {
          tenantInfo = res.data;
          resolve(res.data);
        } else {
          reject(res.data);
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};
/**
 * 接口请求
 */
export const request = (options) => {
  // 如果请求是登录接口且存在租户信息，自动添加租户ID头
  if (options.url.includes('/login') && tenantInfo) {
    options.header = {
      ...options.header,
      '__tenant': tenantInfo[0].id
    };
  }
  return new Promise((resolve, reject) => {
    uni.request({
      url: options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Authorization': 'Bearer ' + uni.getStorageSync('token'), // 从缓存获取 token
        'Content-Type': 'application/json',
        ...options.header
      },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data); // 返回接口数据
        } else {
          reject(res.data); // 返回错误信息
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};
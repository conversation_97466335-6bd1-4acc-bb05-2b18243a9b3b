<template>
  <view class="page">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="handleBack">
        <text class="ri-arrow-left-line"></text>
      </view>
      <text class="page-title">添加沟通记录</text>
      <view class="header-actions">
        <!-- 额外的操作按钮 -->
      </view>
    </view>
    
    <!-- 表单内容区域 -->
    <scroll-view scroll-y class="form-container">
      <!-- 沟通类型 -->
      <view class="form-section">
        <view class="section-title">
          <text class="ri-information-line"></text>
          <text>沟通类型</text>
        </view>
        <view class="type-options">
          <view 
            v-for="(type, index) in communicationTypes" 
            :key="index"
            class="type-option"
            :class="{ selected: formData.type === type.value }"
            @click="formData.type = type.value"
          >
            <radio :checked="formData.type === type.value" :value="type.value" color="#3a86ff" style="display:none" />
            <view :class="['type-icon', `icon-${type.value}`]">
              <text :class="type.icon"></text>
            </view>
            <view class="type-label">{{ type.label }}</view>
          </view>
        </view>
      </view>
      
      <!-- 关联信息 -->
      <view class="form-section">
        <view class="section-title">
          <text class="ri-link-m"></text>
          <text>关联信息</text>
        </view>
        <view class="form-group">
          <view class="associated-item" v-for="(item, index) in formData.associatedItems" :key="index">
            <view class="associated-name">{{ item.name }}</view>
            <view class="associated-type">{{ item.type }}</view>
            <view class="remove-associated" @click="removeAssociatedItem(index)">
              <text class="ri-close-line"></text>
            </view>
          </view>
          
          <button class="select-associated" @click="showAssociateSelector = true">
            <text class="ri-add-line"></text>
            <text>关联更多</text>
          </button>
        </view>
      </view>
      
      <!-- 沟通基本信息 -->
      <view class="form-section">
        <view class="section-title">
          <text class="ri-file-list-line"></text>
          <text>沟通基本信息</text>
        </view>
        <view class="form-group">
          <view class="form-row">
            <view class="form-label required">主题</view>
            <view class="form-control">
              <input class="form-input" placeholder="请输入沟通主题" v-model="formData.subject" />
            </view>
          </view>
        </view>
        
        <view class="form-group">
          <view class="form-row">
            <view class="form-label">参与人</view>
            <view class="form-control">
              <view class="associated-item" v-for="(person, index) in formData.participants" :key="index">
                <view class="associated-name">{{ person.name }}</view>
                <view class="associated-type">{{ person.type }}</view>
                <view class="remove-associated" @click="removeParticipant(index)">
                  <text class="ri-close-line"></text>
                </view>
              </view>
              <button class="select-associated" @click="showParticipantSelector = true">
                <text class="ri-add-line"></text>
                <text>添加参与人</text>
              </button>
            </view>
          </view>
        </view>
        
        <view class="form-group">
          <view class="form-row">
            <view class="form-label">时间</view>
            <view class="form-control">
              <picker mode="date" :value="formData.date" @change="onDateChange">
                <view class="uni-input form-input">{{ formData.date || '请选择日期' }}</view>
              </picker>
              <picker mode="time" :value="formData.time" @change="onTimeChange">
                <view class="uni-input form-input" style="margin-top: 10rpx;">{{ formData.time || '请选择时间' }}</view>
              </picker>
            </view>
          </view>
        </view>
        
        <view class="form-group">
          <view class="form-row">
            <view class="form-label">位置</view>
            <view class="form-control">
              <input class="form-input" placeholder="可选" v-model="formData.location" />
            </view>
          </view>
        </view>
        
        <view class="form-group">
          <view class="form-row">
            <view class="form-label required">负责人</view>
            <view class="form-control">
              <picker @change="onOwnerChange" :value="ownerIndex" :range="owners" range-key="name">
                <view class="uni-input form-input">{{ owners[ownerIndex].name }}</view>
              </picker>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 沟通内容 -->
      <view class="form-section">
        <view class="section-title">
          <text class="ri-chat-4-line"></text>
          <text>沟通内容</text>
        </view>
        <view class="form-group">
          <textarea class="form-textarea" placeholder="请输入详细的沟通内容..." v-model="formData.content"></textarea>
          <view class="form-note">详细记录能帮助团队更好地了解客户需求和进展</view>
        </view>
      </view>
      
      <!-- 标签 -->
      <view class="form-section">
        <view class="section-title">
          <text class="ri-price-tag-3-line"></text>
          <text>标签</text>
        </view>
        <view class="tag-selector">
          <view 
            v-for="(tag, index) in tags" 
            :key="index"
            class="tag"
            :class="{ selected: tag.selected }"
            @click="toggleTag(index)"
          >
            {{ tag.name }}
          </view>
          <view class="tag-input" @click="showAddTagInput">
            <text class="ri-add-line"></text>
            <text>新建标签</text>
          </view>
        </view>
      </view>
      
      <!-- 附件 -->
      <view class="form-section">
        <view class="section-title">
          <text class="ri-attachment-2"></text>
          <text>附件</text>
        </view>
        <button class="select-associated" @click="uploadAttachment">
          <text class="ri-upload-2-line"></text>
          <text>上传附件</text>
        </button>
        <view class="attachment-list" v-if="formData.attachments.length > 0">
          <view class="attachment-item" v-for="(file, index) in formData.attachments" :key="index">
            <text class="ri-file-line"></text>
            <text class="attachment-name">{{ file.name }}</text>
            <view class="remove-attachment" @click="removeAttachment(index)">
              <text class="ri-close-line"></text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="bottom-spacer"></view>
    </scroll-view>
    
    <!-- 底部操作栏 -->
    <view class="action-bar">
      <button class="btn btn-outline" @click="handleCancel">取消</button>
      <button class="btn btn-primary" @click="handleSave">保存</button>
    </view>
    
    <!-- 参与人选择弹窗 -->
    <uni-popup ref="participantPopup" type="bottom">
      <view class="popup-content">
        <view class="popup-header">
          <text>选择参与人</text>
          <view class="popup-close" @click="showParticipantSelector = false">
            <text class="ri-close-line"></text>
          </view>
        </view>
        <view class="popup-search">
          <text class="ri-search-line"></text>
          <input type="text" placeholder="搜索联系人" v-model="searchParticipant" />
        </view>
        <scroll-view scroll-y class="popup-list">
          <checkbox-group @change="onParticipantSelect">
            <label class="participant-item" v-for="(item, index) in filteredParticipants" :key="index">
              <checkbox :value="item.id" :checked="selectedParticipants.includes(item.id)" />
              <view class="participant-info">
                <view class="participant-name">{{ item.name }}</view>
                <view class="participant-type">{{ item.type }}</view>
              </view>
            </label>
          </checkbox-group>
        </scroll-view>
        <view class="popup-footer">
          <button class="btn btn-primary" @click="confirmParticipants">确定</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        type: 'call',
        subject: '',
        date: this.formatDate(new Date()),
        time: this.formatTime(new Date()),
        location: '',
        owner: '李销售',
        content: '',
        participants: [
          { id: '1', name: '张经理', type: '客户联系人' }
        ],
        associatedItems: [
          { id: '1', name: '北京科技有限公司', type: '客户' },
          { id: '2', name: '云数据分析平台项目', type: '商机' }
        ],
        attachments: []
      },
      communicationTypes: [
        { value: 'call', label: '电话', icon: 'ri-phone-line' },
        { value: 'meeting', label: '会议', icon: 'ri-team-line' },
        { value: 'note', label: '纪要', icon: 'ri-file-list-line' },
        { value: 'email', label: '邮件', icon: 'ri-mail-line' },
        { value: 'visit', label: '客户拜访', icon: 'ri-building-line' }
      ],
      owners: [
        { id: '1', name: '李销售' },
        { id: '2', name: '王销售' },
        { id: '3', name: '张销售' }
      ],
      ownerIndex: 0,
      tags: [
        { name: '需跟进', selected: false },
        { name: '重要客户', selected: true },
        { name: '合同阶段', selected: false },
        { name: '等待回复', selected: false },
        { name: '已解决', selected: false },
        { name: '未解决', selected: false }
      ],
      showParticipantSelector: false,
      showAssociateSelector: false,
      participantList: [
        { id: '1', name: '张经理', type: '客户联系人' },
        { id: '2', name: '李总监', type: '客户联系人' },
        { id: '3', name: '王工程师', type: '客户联系人' },
        { id: '4', name: '赵技术总监', type: '客户联系人' }
      ],
      searchParticipant: '',
      selectedParticipants: ['1']
    }
  },
  computed: {
    filteredParticipants() {
      if (!this.searchParticipant) return this.participantList;
      return this.participantList.filter(item => 
        item.name.includes(this.searchParticipant) || 
        item.type.includes(this.searchParticipant)
      );
    }
  },
  watch: {
    showParticipantSelector(val) {
      if (val) {
        this.$refs.participantPopup.open();
      } else {
        this.$refs.participantPopup.close();
      }
    }
  },
  methods: {
    formatDate(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    formatTime(date) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    },
    onDateChange(e) {
      this.formData.date = e.detail.value;
    },
    onTimeChange(e) {
      this.formData.time = e.detail.value;
    },
    onOwnerChange(e) {
      this.ownerIndex = e.detail.value;
      this.formData.owner = this.owners[this.ownerIndex].name;
    },
    toggleTag(index) {
      this.tags[index].selected = !this.tags[index].selected;
    },
    showAddTagInput() {
      uni.showModal({
        title: '新建标签',
        editable: true,
        placeholderText: '请输入标签名称',
        success: (res) => {
          if (res.confirm && res.content) {
            this.tags.push({
              name: res.content,
              selected: true
            });
          }
        }
      });
    },
    uploadAttachment() {
      uni.chooseFile({
        count: 1,
        success: (res) => {
          const file = res.tempFiles[0];
          this.formData.attachments.push({
            name: file.name || '未命名文件',
            path: file.path,
            size: file.size
          });
        }
      });
    },
    removeAttachment(index) {
      this.formData.attachments.splice(index, 1);
    },
    removeAssociatedItem(index) {
      this.formData.associatedItems.splice(index, 1);
    },
    removeParticipant(index) {
      const removedId = this.formData.participants[index].id;
      this.formData.participants.splice(index, 1);
      // 从已选列表中移除
      const idIndex = this.selectedParticipants.indexOf(removedId);
      if (idIndex > -1) {
        this.selectedParticipants.splice(idIndex, 1);
      }
    },
    onParticipantSelect(e) {
      this.selectedParticipants = e.detail.value;
    },
    confirmParticipants() {
      // 清空当前参与人列表
      this.formData.participants = [];
      // 根据选中的ID添加参与人
      this.selectedParticipants.forEach(id => {
        const participant = this.participantList.find(p => p.id === id);
        if (participant) {
          this.formData.participants.push(participant);
        }
      });
      this.showParticipantSelector = false;
    },
    handleBack() {
      this.confirmCancel('确定要放弃当前编辑的内容吗？');
    },
    handleCancel() {
      this.confirmCancel('确定要放弃当前编辑的内容吗？');
    },
    confirmCancel(message) {
      uni.showModal({
        title: '提示',
        content: message,
        success: (res) => {
          if (res.confirm) {
            uni.navigateBack();
          }
        }
      });
    },
    handleSave() {
      // 表单验证
      if (!this.formData.subject) {
        uni.showToast({
          title: '请输入沟通主题',
          icon: 'none'
        });
        return;
      }
      
      // 获取选中的标签
      const selectedTags = this.tags.filter(tag => tag.selected).map(tag => tag.name);
      
      // 构建保存数据
      const saveData = {
        ...this.formData,
        tags: selectedTags,
        datetime: `${this.formData.date} ${this.formData.time}`
      };
      
      // 模拟保存
      console.log('保存数据', saveData);
      
      uni.showToast({
        title: '保存成功',
        icon: 'success',
        success: () => {
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        }
      });
    }
  }
}
</script>

<style>
.page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  height: 88rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-button {
  font-size: 40rpx;
  padding: 10rpx;
}

.page-title {
  font-size: 34rpx;
  font-weight: 500;
}

.header-actions {
  min-width: 44rpx;
}

.form-container {
  flex: 1;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  width: 100%;
}

.form-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
  width: 100%;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.section-title text:first-child {
  margin-right: 10rpx;
  font-size: 32rpx;
}

.type-options {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
  justify-content: space-around;
}

.type-option {
  width: calc(20% - 20rpx);
  min-width: 120rpx;
  max-width: 140rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  margin: 10rpx;
  border-radius: 12rpx;
  background-color: #f8f8f8;
  box-sizing: border-box;
}

.type-option.selected {
  background-color: rgba(58, 134, 255, 0.1);
}

.type-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10rpx;
  font-size: 36rpx;
  background-color: #eee;
  color: #666;
}

.type-option.selected .type-icon {
  background-color: #3a86ff;
  color: #fff;
}

.icon-call { color: #5181b8; }
.icon-meeting { color: #4caf50; }
.icon-note { color: #ff9800; }
.icon-email { color: #9c27b0; }
.icon-visit { color: #f44336; }

.type-option.selected .icon-call,
.type-option.selected .icon-meeting,
.type-option.selected .icon-note,
.type-option.selected .icon-email,
.type-option.selected .icon-visit {
  color: #fff;
}

.type-label {
  font-size: 24rpx;
}

.form-group {
  margin-bottom: 20rpx;
}

.form-row {
  display: flex;
  margin-bottom: 20rpx;
}

.form-label {
  width: 140rpx;
  font-size: 28rpx;
  padding-top: 10rpx;
}

.required:after {
  content: '*';
  color: #f44336;
  margin-left: 6rpx;
}

.form-control {
  flex: 1;
}

.form-input {
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 16rpx 20rpx;
  font-size: 28rpx;
  width: 100%;
  box-sizing: border-box;
}

.form-textarea {
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  width: 100%;
  height: 200rpx;
  box-sizing: border-box;
}

.form-note {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.associated-item {
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 16rpx 20rpx;
  margin-bottom: 20rpx;
  width: 100%;
  box-sizing: border-box;
  flex-wrap: wrap;
}

.associated-name {
  flex: 1;
  font-size: 28rpx;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.associated-type {
  font-size: 24rpx;
  background-color: #eee;
  color: #666;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin: 0 20rpx;
  white-space: nowrap;
}

.remove-associated, .remove-attachment {
  padding: 6rpx;
  font-size: 32rpx;
  color: #999;
}

.select-associated {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 16rpx 20rpx;
  font-size: 28rpx;
  width: 100%;
  color: #3a86ff;
  border: none;
}

.tag-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-top: 10rpx;
  width: 100%;
  box-sizing: border-box;
}

.tag {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 30rpx;
  background-color: #f0f0f0;
  color: #666;
  border: 1rpx solid #ddd;
  max-width: calc(50% - 20rpx);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: border-box;
}

.tag.selected {
  background-color: rgba(58, 134, 255, 0.1);
  color: #3a86ff;
  border-color: #3a86ff;
}

.tag-input {
  display: flex;
  align-items: center;
  border: 1rpx dashed #ddd;
  border-radius: 30rpx;
  padding: 4rpx 16rpx;
  color: #999;
  font-size: 24rpx;
}

.tag-input text:first-child {
  margin-right: 6rpx;
}

.attachment-list {
  margin-top: 20rpx;
}

.attachment-item {
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 16rpx 20rpx;
  margin-bottom: 10rpx;
}

.attachment-name {
  flex: 1;
  font-size: 28rpx;
  margin-left: 10rpx;
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
  border-top: 1rpx solid #eee;
  z-index: 90;
}

.btn {
  flex: 1;
  border-radius: 8rpx;
  padding: 20rpx 0;
  text-align: center;
  font-size: 30rpx;
}

.btn-outline {
  border: 1rpx solid #3a86ff;
  color: #3a86ff;
  background-color: transparent;
}

.btn-primary {
  background-color: #3a86ff;
  color: #fff;
  border: none;
}

.bottom-spacer {
  height: 180rpx;
}

.popup-content {
  background-color: #fff;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
  overflow: hidden;
  max-height: 60vh;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.popup-close {
  font-size: 40rpx;
  color: #999;
}

.popup-search {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #f5f5f5;
}

.popup-search text {
  color: #999;
  margin-right: 10rpx;
}

.popup-search input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
}

.popup-list {
  flex: 1;
  padding: 0 30rpx;
  max-height: 40vh;
}

.participant-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.participant-info {
  margin-left: 20rpx;
}

.participant-name {
  font-size: 28rpx;
}

.participant-type {
  font-size: 24rpx;
  color: #999;
}

.popup-footer {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
}

/* 确保图标正确显示 */
.ri-arrow-left-line:before { content: "\ea64"; }
.ri-information-line:before { content: "\ee80"; }
.ri-link-m:before { content: "\eeac"; }
.ri-file-list-line:before { content: "\ed95"; }
.ri-chat-4-line:before { content: "\eb6d"; }
.ri-price-tag-3-line:before { content: "\f07e"; }
.ri-attachment-2:before { content: "\ea6e"; }
.ri-add-line:before { content: "\ea12"; }
.ri-close-line:before { content: "\eb99"; }
.ri-phone-line:before { content: "\f034"; }
.ri-team-line:before { content: "\f1a7"; }
.ri-mail-line:before { content: "\eed1"; }
.ri-building-line:before { content: "\eb00"; }
.ri-upload-2-line:before { content: "\f1c9"; }
.ri-file-line:before { content: "\ed91"; }
.ri-search-line:before { content: "\f0d1"; }
.ri-more-2-fill:before { content: "\ef55"; }
</style> 
<template>
  <view class="container">
    <uni-nav-bar left-icon="left" title="HR 模块图标" @clickLeft="goBack" fixed />
    
    <view class="content">
      <view class="page-title">
        <text>人力资源模块图标</text>
      </view>
      
      <view class="icon-grid">
        <view class="icon-item" v-for="icon in icons" :key="icon.name">
          <svg-icon :name="icon.name" type="svg" size="96" class="icon-image"></svg-icon>
          <text class="icon-name">{{ icon.displayName }}</text>
          <text class="icon-desc">{{ icon.description }}</text>
        </view>
      </view>

      <view class="page-title">
        <text>其他模块图标</text>
      </view>
      
      <view class="icon-grid">
        <view class="icon-item" v-for="icon in otherIcons" :key="icon.name">
          <svg-icon :name="icon.name" type="svg" size="96" class="icon-image"></svg-icon>
          <text class="icon-name">{{ icon.displayName }}</text>
          <text class="icon-desc">{{ icon.description }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      icons: [
        {
          name: 'employee',
          displayName: '员工',
          description: '员工基本信息和管理'
        },
        {
          name: 'department',
          displayName: '部门',
          description: '部门组织架构'
        },
        {
          name: 'performance',
          displayName: '绩效',
          description: '员工绩效评估'
        },
        {
          name: 'attendance',
          displayName: '考勤',
          description: '员工出勤记录'
        },
        {
          name: 'payroll',
          displayName: '工资',
          description: '薪资发放管理'
        },
        {
          name: 'recruitment',
          displayName: '招聘',
          description: '人才招聘流程'
        },
        {
          name: 'training',
          displayName: '培训',
          description: '员工培训发展'
        },
        {
          name: 'analytics',
          displayName: '分析',
          description: '人力资源数据分析'
        },
        {
          name: 'hiring',
          displayName: '录用',
          description: '人员录用审批'
        }
      ],
      otherIcons: [
        {
          name: 'dashboard',
          displayName: '仪表盘',
          description: '系统总览'
        },
        {
          name: 'customer',
          displayName: '客户',
          description: '客户管理'
        },
        {
          name: 'contract',
          displayName: '合同',
          description: '合同管理'
        },
        {
          name: 'invoice',
          displayName: '发票',
          description: '发票管理'
        },
        {
          name: 'payment',
          displayName: '收款',
          description: '收款管理'
        },
        {
          name: 'marketing',
          displayName: '营销',
          description: '营销活动管理'
        },
        {
          name: 'lead',
          displayName: '线索',
          description: '销售线索管理'
        },
        {
          name: 'report',
          displayName: '报表',
          description: '数据报表'
        },
        {
          name: 'settings',
          displayName: '设置',
          description: '系统设置'
        }
      ]
    };
  },
  methods: {
    goBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.content {
  padding: 88rpx 30rpx 30rpx;
}

.page-title {
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 40rpx;
  padding-top: 20rpx;
}

.icon-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

.icon-item {
  width: 48%;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.icon-image {
  margin-bottom: 16rpx;
}

.icon-name {
  font-weight: 600;
  font-size: 28rpx;
  margin-bottom: 8rpx;
}

.icon-desc {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}
</style> 
<template>
  <view class="container">
    <!-- 头部导航 -->
    <view class="page-header">
      <view class="back-button" @click="navigateBack">
        <text class="ri-arrow-left-line"></text>
      </view>
      <view class="page-title">合同详情</view>
      <view class="header-actions">
        <button type="button" class="action-button" @click="showActionMenu">
          <text class="ri-more-2-fill"></text>
        </button>
      </view>
    </view>
    
    <!-- 合同内容 -->
    <scroll-view scroll-y class="contract-container">
      <!-- 合同基本信息 -->
      <view class="section-card">
        <view class="section-title">
          <text>{{ contract.title }}</text>
          <text :class="['status-badge', 'status-' + contract.status.code]">{{ contract.status.name }}</text>
        </view>
        
        <view class="info-grid">
          <view class="info-item">
            <view class="info-label">合同编号</view>
            <view class="info-value">{{ contract.code }}</view>
          </view>
          <view class="info-item">
            <view class="info-label">关联报价单</view>
            <view class="info-value">{{ contract.quoteCode }}</view>
          </view>
          <view class="info-item">
            <view class="info-label">签署日期</view>
            <view class="info-value">{{ contract.signDate }}</view>
          </view>
          <view class="info-item">
            <view class="info-label">有效期至</view>
            <view class="info-value">{{ contract.expiryDate }}</view>
          </view>
          <view class="info-item">
            <view class="info-label">合同类型</view>
            <view class="info-value">{{ contract.type }}</view>
          </view>
          <view class="info-item">
            <view class="info-label">负责人</view>
            <view class="info-value">{{ contract.owner }}</view>
          </view>
        </view>
      </view>
      
      <!-- 客户信息 -->
      <view class="section-card">
        <view class="section-title">客户信息</view>
        
        <view class="info-item">
          <view class="info-label">客户名称</view>
          <view class="info-value">{{ contract.customer.name }}</view>
        </view>
        <view class="info-item">
          <view class="info-label">联系人</view>
          <view class="info-value">{{ contract.customer.contact }} ({{ contract.customer.title }})</view>
        </view>
        <view class="info-item">
          <view class="info-label">联系电话</view>
          <view class="info-value">{{ contract.customer.phone }}</view>
        </view>
        <view class="info-item">
          <view class="info-label">电子邮箱</view>
          <view class="info-value">{{ contract.customer.email }}</view>
        </view>
        <view class="info-item">
          <view class="info-label">公司地址</view>
          <view class="info-value">{{ contract.customer.address }}</view>
        </view>
      </view>
      
      <!-- 合同金额 -->
      <view class="section-card">
        <view class="section-title">合同金额</view>
        
        <view class="amount-table">
          <view class="amount-row" v-for="(item, index) in contract.items" :key="index">
            <text class="label">{{ item.name }}</text>
            <text class="value">¥{{ formatNumber(item.amount) }}</text>
          </view>
          <view class="amount-row">
            <text class="label">增值税 ({{ contract.taxRate }}%)</text>
            <text class="value">¥{{ formatNumber(contract.tax) }}</text>
          </view>
          <view class="total-row">
            <text class="label">总计</text>
            <text class="value">¥{{ formatNumber(contract.totalAmount) }}</text>
          </view>
        </view>
      </view>
      
      <!-- 付款计划 -->
      <view class="section-card">
        <view class="section-title">付款计划</view>
        
        <view class="timeline">
          <view 
            v-for="(payment, index) in contract.paymentPlan" 
            :key="index" 
            :class="['timeline-item', payment.status]"
          >
            <view class="timeline-timestamp">{{ payment.date }}</view>
            <view class="timeline-title">{{ payment.title }}：¥{{ formatNumber(payment.amount) }} ({{ payment.percentage }}%)</view>
            <view class="timeline-desc">{{ payment.description }}</view>
          </view>
        </view>
      </view>
      
      <!-- 合同条款 -->
      <view class="section-card">
        <view class="section-title">合同条款</view>
        
        <view class="info-item">
          <view class="info-label">项目周期</view>
          <view class="info-value">{{ contract.terms.projectDuration }}</view>
        </view>
        <view class="info-item">
          <view class="info-label">质保期</view>
          <view class="info-value">{{ contract.terms.warrantyPeriod }}</view>
        </view>
        
        <view class="collapse-toggle" @click="navigateToTerms">
          <text>查看详细条款</text>
          <text class="ri-arrow-right-s-line"></text>
        </view>
      </view>
      
      <!-- 合同文档 -->
      <view class="section-card">
        <view class="section-title">相关文档</view>
        
        <view class="file-list">
          <view 
            class="file-item"
            v-for="(doc, index) in contract.documents"
            :key="index"
          >
            <text class="ri-file-pdf-line"></text>
            <text>{{ doc.name }}</text>
            <button type="button" class="download-button" @click="downloadFile(doc)">
              <text class="ri-download-line"></text>
            </button>
          </view>
        </view>
      </view>
      
      <!-- 合同历史记录 -->
      <view class="section-card">
        <view class="section-title">历史记录</view>
        
        <view class="history-summary">
          <view 
            class="history-item"
            v-for="(history, index) in contract.history.slice(0, 3)"
            :key="index"
          >
            <view class="history-date">{{ history.date }}</view>
            <view class="history-content">
              <view class="history-title">{{ history.title }}</view>
              <view class="history-user">{{ history.user }}</view>
            </view>
          </view>
        </view>
        
        <view class="collapse-toggle" @click="navigateToHistory">
          <text>查看完整历史</text>
          <text class="ri-arrow-right-s-line"></text>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部操作按钮 -->
    <view class="float-actions">
      <button type="button" class="action-btn secondary-action" @click="downloadContract">
        <text class="ri-download-line"></text>
        <text>下载合同</text>
      </button>
      <button type="button" class="action-btn primary-action" @click="navigateToEdit">
        <text class="ri-edit-line"></text>
        <text>编辑合同</text>
      </button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      contract: {
        title: '企业系统集成项目合同',
        code: 'CT-2023-09-001',
        quoteCode: 'QT-2023-09-005',
        signDate: '2023-10-25',
        expiryDate: '2024-10-24',
        type: '系统集成',
        owner: '王销售',
        status: {
          code: 'signed',
          name: '已签署'
        },
        customer: {
          name: '上海智能科技',
          contact: '张总监',
          title: '技术总监',
          phone: '13812345678',
          email: '<EMAIL>',
          address: '上海市浦东新区张江高科技园区科苑路88号'
        },
        items: [
          { name: '系统集成', amount: 320000 },
          { name: '定制开发', amount: 150000 },
          { name: '培训与支持', amount: 45000 }
        ],
        taxRate: 13,
        tax: 66950,
        totalAmount: 581950,
        paymentPlan: [
          {
            title: '首付款',
            amount: 290975,
            percentage: 50,
            date: '2023-10-25',
            description: '合同签署后7天内支付',
            status: 'done'
          },
          {
            title: '中期款',
            amount: 174585,
            percentage: 30,
            date: '预计：2023-12-25',
            description: '项目交付后7天内支付',
            status: 'pending'
          },
          {
            title: '尾款',
            amount: 116390,
            percentage: 20,
            date: '预计：2024-01-25',
            description: '项目验收通过后7天内支付',
            status: ''
          }
        ],
        terms: {
          projectDuration: '90天',
          warrantyPeriod: '12个月'
        },
        documents: [
          { name: '企业系统集成项目合同.pdf', url: '' },
          { name: '技术方案附件.pdf', url: '' }
        ],
        history: [
          {
            date: '2023-10-25',
            title: '合同签署完成',
            user: '张总监（客户）'
          },
          {
            date: '2023-10-25',
            title: '合同签署',
            user: '王经理（我方）'
          },
          {
            date: '2023-10-20',
            title: '合同审批通过',
            user: '刘总监（法务部）'
          }
        ]
      }
    }
  },
  methods: {
    navigateBack() {
      uni.navigateBack();
    },
    showActionMenu() {
      uni.showActionSheet({
        itemList: ['查看历史记录', '复制合同', '标记为已完成', '导出为PDF', '删除合同'],
        success: (res) => {
          switch(res.tapIndex) {
            case 0:
              this.navigateToHistory();
              break;
            default:
              uni.showToast({
                title: '该功能开发中...',
                icon: 'none'
              });
          }
        }
      });
    },
    navigateToEdit() {
      uni.navigateTo({
        url: `/pages/contracts/contract-edit?id=${this.contract.code}`
      });
    },
    navigateToHistory() {
      uni.navigateTo({
        url: `/pages/contracts/contract-history?id=${this.contract.code}`
      });
    },
    navigateToTerms() {
      uni.navigateTo({
        url: `/pages/contracts/contract-terms?id=${this.contract.code}`
      });
    },
    downloadContract() {
      uni.showToast({
        title: '下载合同功能开发中...',
        icon: 'none'
      });
    },
    downloadFile(file) {
      uni.showToast({
        title: '下载功能开发中...',
        icon: 'none'
      });
    },
    formatNumber(num) {
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
  }
}
</script>

<style>
/* 页面容器 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
  box-sizing: border-box;
}

/* 头部导航样式 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.back-button {
  color: #666666;
  display: flex;
  align-items: center;
  padding: 16rpx;
  margin: -16rpx;
}

.header-actions {
  display: flex;
  gap: 16rpx;
}

.action-button {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #666666;
  background-color: #f5f7fa;
  border: 2rpx solid #eaeaea;
  transition: all 0.3s ease;
}

.action-button:active {
  background-color: #e6e6e6;
}

/* 合同内容区域 */
.contract-container {
  padding: 24rpx 32rpx;
  flex: 1;
  margin-bottom: 160rpx;
  box-sizing: border-box;
}

/* 卡片通用样式 */
.section-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  border: 2rpx solid #eaeaea;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 24rpx;
  padding: 0;
  color: #333333;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
}

/* 状态标签样式 */
.status-badge {
  padding: 8rpx 24rpx;
  border-radius: 32rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-draft {
  background-color: #f3f4f6;
  color: #6b7280;
}

.status-review {
  background-color: #fff7ed;
  color: #ea580c;
}

.status-signed {
  background-color: #ecfdf5;
  color: #059669;
}

.status-completed {
  background-color: #eff6ff;
  color: #3b82f6;
}

.status-terminated {
  background-color: #fef2f2;
  color: #dc2626;
}

/* 信息网格布局 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  padding: 0;
  box-sizing: border-box;
}

.info-item {
  min-width: 0;
  padding: 0;
  box-sizing: border-box;
}

.info-label {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 8rpx;
  padding: 0;
  box-sizing: border-box;
}

.info-value {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  word-break: break-all;
  padding: 0;
  box-sizing: border-box;
}

/* 金额表格样式 */
.amount-table {
  width: 100%;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
  font-size: 28rpx;
}

.amount-table .label {
  color: #666666;
}

.amount-table .value {
  font-weight: 500;
  color: #333333;
}

.total-row {
  display: flex;
  justify-content: space-between;
  border-top: 2rpx solid #eaeaea;
  margin-top: 16rpx;
  padding-top: 16rpx;
}

.total-row .label {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.total-row .value {
  font-size: 36rpx;
  font-weight: 600;
  color: #3a86ff;
}

/* 历史记录样式 */
.history-summary {
  margin-bottom: 24rpx;
}

.history-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-date {
  flex: 0 0 140rpx;
  font-size: 24rpx;
  color: #666666;
  padding-top: 4rpx;
}

.history-content {
  flex: 1;
}

.history-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 4rpx;
}

.history-user {
  font-size: 24rpx;
  color: #666666;
}

/* 底部操作按钮 */
.float-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 24rpx;
  background-color: #ffffff;
  border-top: 2rpx solid #eaeaea;
  z-index: 100;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 24rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.primary-action {
  background: linear-gradient(135deg, #3a86ff, #0057ff);
  color: #ffffff;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(58, 134, 255, 0.3);
}

.primary-action:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(58, 134, 255, 0.2);
}

.secondary-action {
  background-color: #f5f7fa;
  color: #333333;
  border: 2rpx solid #eaeaea;
  margin-right: 24rpx;
}

.secondary-action:active {
  background-color: #e6e6e6;
}

/* 添加折叠切换按钮样式 */
.collapse-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx 0;
  color: #3a86ff;
  font-size: 28rpx;
  font-weight: 500;
}

.collapse-toggle text:last-child {
  font-size: 32rpx;
}

.timeline {
  margin-top: 12px;
  padding-left: 12px;
  border-left: 2px solid #eaeaea;
}

.timeline-item {
  position: relative;
  margin-bottom: 12px;
  padding-left: 16px;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: -6px;
  top: 0;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #007aff;
}

.timeline-item.done::before {
  background-color: #10b981;
}

.timeline-item.pending::before {
  background-color: #f59e0b;
}

.timeline-timestamp {
  font-size: 12px;
  color: #666666;
  margin-bottom: 4px;
}

.timeline-title {
  font-size: 14px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4px;
}

.timeline-desc {
  font-size: 13px;
  color: #666666;
}

.file-list {
  margin-top: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item text:first-child {
  margin-right: 8px;
  color: #666666;
}

.file-item text:nth-child(2) {
  flex: 1;
  font-size: 14px;
  color: #333333;
}

.download-button {
  color: #007aff;
  background: none;
  border: none;
  padding: 4px;
}
</style> 
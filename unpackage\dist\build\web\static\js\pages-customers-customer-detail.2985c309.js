(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-customers-customer-detail"],{"00c5":function(t,e,a){"use strict";var i=a("e5ea"),n=a.n(i);n.a},"0c63":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";.page-header[data-v-7147860a]{display:flex;align-items:center;justify-content:space-between;padding:var(--spacing-md) var(--spacing-lg);border-bottom:%?1?% solid var(--border-color);background-color:#fff;position:-webkit-sticky;position:sticky;top:0;z-index:10}.page-title[data-v-7147860a]{font-size:%?32?%;font-weight:700;color:var(--text-primary)}.back-button[data-v-7147860a]{color:var(--text-secondary);display:flex;align-items:center}.header-actions[data-v-7147860a]{display:flex;gap:var(--spacing-sm)}.action-button[data-v-7147860a]{width:%?64?%;height:%?64?%;display:flex;align-items:center;justify-content:center;color:var(--text-secondary)}.page-container[data-v-7147860a]{padding:var(--spacing-md) 0}\n/* 详情卡片样式 */.detail-card[data-v-7147860a]{background-color:#fff;border-radius:var(--radius-lg);overflow:hidden;margin-bottom:var(--spacing-md);box-shadow:var(--shadow-sm)}.detail-header[data-v-7147860a]{padding:var(--spacing-md);display:flex;justify-content:space-between;align-items:flex-start;border-bottom:%?1?% solid var(--border-color-light)}.customer-title[data-v-7147860a]{font-size:%?36?%;font-weight:700;color:var(--text-primary);margin-bottom:var(--spacing-xs)}.customer-tags[data-v-7147860a]{display:flex;flex-wrap:wrap;gap:%?8?%;margin-top:var(--spacing-xs)}.tag[data-v-7147860a]{font-size:%?22?%;padding:%?6?% %?16?%;border-radius:var(--radius-full);background-color:var(--light-color);color:var(--text-secondary)}.tag.tag-A[data-v-7147860a]{background-color:#fef3c7;color:#d97706}.tag.tag-B[data-v-7147860a]{background-color:#e0f2fe;color:#0284c7}.tag.tag-C[data-v-7147860a]{background-color:#dbeafe;color:#2563eb}.tag.tag-default[data-v-7147860a]{background-color:#9ca3af;color:#000}.tag.tag-industry[data-v-7147860a]{background-color:#f3f4f6;color:#4b5563}.tag.tag-custom[data-v-7147860a]{background-color:var(--success-light);color:var(--success-color)}.customer-value[data-v-7147860a]{font-size:%?32?%;font-weight:700;color:var(--primary-color)}.detail-content[data-v-7147860a]{padding:var(--spacing-md)}\n/* 信息区域样式 */.info-section[data-v-7147860a]{margin-bottom:var(--spacing-lg)}.info-section[data-v-7147860a]:last-child{margin-bottom:0}.section-title[data-v-7147860a]{display:flex;align-items:center;font-size:%?28?%;font-weight:600;color:var(--text-primary);margin-bottom:var(--spacing-md)}.section-title uni-text[data-v-7147860a]:first-child{margin-right:var(--spacing-xs);color:var(--primary-color)}.info-grid[data-v-7147860a]{display:grid;grid-template-columns:repeat(2,1fr);gap:var(--spacing-md) var(--spacing-lg)}.info-item[data-v-7147860a]{margin-bottom:var(--spacing-sm)}.info-item .info-label[data-v-7147860a]{font-size:%?24?%;color:var(--text-tertiary);margin-bottom:%?4?%}.info-item .info-value[data-v-7147860a]{font-size:%?28?%;color:#333;font-weight:500}\n/* 联系人列表样式 */.contact-list[data-v-7147860a]{display:flex;flex-direction:column;gap:var(--spacing-md)}.contact-item[data-v-7147860a]{display:flex;align-items:center;padding:var(--spacing-sm);background-color:var(--light-color);border-radius:var(--radius-md)}.contact-avatar[data-v-7147860a]{width:%?80?%;height:%?80?%;border-radius:var(--radius-full);background-color:var(--primary-light);display:flex;align-items:center;justify-content:center;color:var(--primary-color);margin-right:var(--spacing-sm);flex-shrink:0}.contact-info[data-v-7147860a]{flex:1}.contact-name[data-v-7147860a]{font-size:%?28?%;font-weight:500;color:var(--text-primary);margin-bottom:%?4?%}.contact-position[data-v-7147860a]{font-size:%?24?%;color:var(--text-tertiary)}.contact-actions[data-v-7147860a]{display:flex;gap:var(--spacing-sm)}.contact-action[data-v-7147860a]{width:%?64?%;height:%?64?%;border-radius:var(--radius-full);background-color:#fff;display:flex;align-items:center;justify-content:center;color:var(--primary-color);box-shadow:var(--shadow-xs)}\n/* 活动列表样式 */.activity-list[data-v-7147860a]{display:flex;flex-direction:column}.activity-item[data-v-7147860a]{display:flex;padding:var(--spacing-md);border-bottom:%?1?% solid var(--border-color-light)}.item-hover[data-v-7147860a]{background-color:var(--light-color)}.activity-item[data-v-7147860a]:last-child{border-bottom:none}.activity-icon[data-v-7147860a]{width:%?64?%;height:%?64?%;border-radius:var(--radius-full);background-color:var(--light-color);display:flex;align-items:center;justify-content:center;color:var(--primary-color);margin-right:var(--spacing-sm);flex-shrink:0}.activity-content[data-v-7147860a]{flex:1;position:relative}.activity-header[data-v-7147860a]{display:flex;justify-content:space-between;align-items:baseline;margin-bottom:var(--spacing-xs)}.activity-title[data-v-7147860a]{font-size:%?28?%;font-weight:500;color:var(--text-primary)}.activity-time[data-v-7147860a]{font-size:%?24?%;color:var(--text-tertiary);white-space:nowrap}.activity-description[data-v-7147860a]{font-size:%?26?%;color:var(--text-secondary);line-height:1.5}.activity-meta[data-v-7147860a]{display:flex;justify-content:space-between;align-items:center;margin-top:var(--spacing-xs);font-size:%?24?%;color:var(--text-tertiary)}\n/* 底部操作栏 */.bottom-actions[data-v-7147860a]{position:fixed;bottom:0;left:0;right:0;padding:var(--spacing-md) var(--spacing-lg);background-color:#fff;border-top:%?1?% solid var(--border-color);display:flex;gap:var(--spacing-md);z-index:10}.action-btn[data-v-7147860a]{flex:1;display:flex;flex-direction:column;align-items:center;justify-content:center;gap:var(--spacing-xs);height:%?128?%;background-color:var(--light-color);border:%?1?% solid var(--border-color);border-radius:var(--radius-md);color:var(--text-secondary)}.action-btn uni-text[data-v-7147860a]:first-child{font-size:%?40?%}.action-label[data-v-7147860a]{font-size:%?24?%}.action-btn.primary[data-v-7147860a]{background-color:var(--primary-color);color:#fff;border-color:var(--primary-color)}\n/* 增加底部间距，避免内容被底部操作栏遮挡 */.page-container[data-v-7147860a]{padding-bottom:calc(%?128?% + var(--spacing-md) * 2 + var(--spacing-lg))}\n/* 空列表样式 */.empty-list[data-v-7147860a]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:var(--spacing-lg) 0}.empty-icon[data-v-7147860a]{color:var(--text-tertiary);margin-bottom:var(--spacing-md)}.empty-text[data-v-7147860a]{font-size:%?28?%;color:var(--text-tertiary);margin-bottom:var(--spacing-md)}.empty-action[data-v-7147860a]{display:flex;align-items:center;gap:var(--spacing-xs);padding:var(--spacing-sm) var(--spacing-md);background-color:var(--primary-light);color:var(--primary-color);border-radius:var(--radius-md);font-size:%?26?%}.opportunity-progress[data-v-7147860a]{display:flex;flex-direction:column;align-items:flex-end;gap:%?4?%}',""]),t.exports=e},"17c4":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.getDictionaryPageDetail=e.getDictionaryPage=void 0;var i=a("c475");e.getDictionaryPage=function(t){return(0,i.request)({url:"/api/DataDictionary/page",method:"POST",data:t})};e.getDictionaryPageDetail=function(t){return(0,i.request)({url:"/api/DataDictionary/pageDetail",method:"POST",data:t})}},2634:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
e.default=function(){return t};var t={},a=Object.prototype,n=a.hasOwnProperty,o=Object.defineProperty||function(t,e,a){t[e]=a.value},r="function"==typeof Symbol?Symbol:{},s=r.iterator||"@@iterator",c=r.asyncIterator||"@@asyncIterator",u=r.toStringTag||"@@toStringTag";function l(t,e,a){return Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(L){l=function(t,e,a){return t[e]=a}}function d(t,e,a,i){var n=e&&e.prototype instanceof m?e:m,r=Object.create(n.prototype),s=new P(i||[]);return o(r,"_invoke",{value:_(t,a,s)}),r}function v(t,e,a){try{return{type:"normal",arg:t.call(e,a)}}catch(L){return{type:"throw",arg:L}}}t.wrap=d;var f={};function m(){}function p(){}function g(){}var h={};l(h,s,(function(){return this}));var y=Object.getPrototypeOf,w=y&&y(y(E([])));w&&w!==a&&n.call(w,s)&&(h=w);var b=g.prototype=m.prototype=Object.create(h);function x(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){var a;o(this,"_invoke",{value:function(o,r){function s(){return new e((function(a,s){(function a(o,r,s,c){var u=v(t[o],t,r);if("throw"!==u.type){var l=u.arg,d=l.value;return d&&"object"==(0,i.default)(d)&&n.call(d,"__await")?e.resolve(d.__await).then((function(t){a("next",t,s,c)}),(function(t){a("throw",t,s,c)})):e.resolve(d).then((function(t){l.value=t,s(l)}),(function(t){return a("throw",t,s,c)}))}c(u.arg)})(o,r,a,s)}))}return a=a?a.then(s,s):s()}})}function _(t,e,a){var i="suspendedStart";return function(n,o){if("executing"===i)throw new Error("Generator is already running");if("completed"===i){if("throw"===n)throw o;return z()}for(a.method=n,a.arg=o;;){var r=a.delegate;if(r){var s=T(r,a);if(s){if(s===f)continue;return s}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if("suspendedStart"===i)throw i="completed",a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);i="executing";var c=v(t,e,a);if("normal"===c.type){if(i=a.done?"completed":"suspendedYield",c.arg===f)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(i="completed",a.method="throw",a.arg=c.arg)}}}function T(t,e){var a=e.method,i=t.iterator[a];if(void 0===i)return e.delegate=null,"throw"===a&&t.iterator["return"]&&(e.method="return",e.arg=void 0,T(t,e),"throw"===e.method)||"return"!==a&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+a+"' method")),f;var n=v(i,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,f;var o=n.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,f):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}function D(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function k(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(D,this),this.reset(!0)}function E(t){if(t){var e=t[s];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function e(){for(;++a<t.length;)if(n.call(t,a))return e.value=t[a],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:z}}function z(){return{value:void 0,done:!0}}return p.prototype=g,o(b,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:p,configurable:!0}),p.displayName=l(g,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,l(t,u,"GeneratorFunction")),t.prototype=Object.create(b),t},t.awrap=function(t){return{__await:t}},x(C.prototype),l(C.prototype,c,(function(){return this})),t.AsyncIterator=C,t.async=function(e,a,i,n,o){void 0===o&&(o=Promise);var r=new C(d(e,a,i,n),o);return t.isGeneratorFunction(a)?r:r.next().then((function(t){return t.done?t.value:r.next()}))},x(b),l(b,u,"Generator"),l(b,s,(function(){return this})),l(b,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),a=[];for(var i in e)a.push(i);return a.reverse(),function t(){for(;a.length;){var i=a.pop();if(i in e)return t.value=i,t.done=!1,t}return t.done=!0,t}},t.values=E,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(k),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function a(a,i){return r.type="throw",r.arg=t,e.next=a,i&&(e.method="next",e.arg=void 0),!!i}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],r=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var s=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var r=o?o.completion:{};return r.type=t,r.arg=e,o?(this.method="next",this.next=o.finallyLoc,f):this.complete(r)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.finallyLoc===t)return this.complete(a.completion,a.afterLoc),k(a),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.tryLoc===t){var i=a.completion;if("throw"===i.type){var n=i.arg;k(a)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,a){return this.delegate={iterator:E(t),resultName:e,nextLoc:a},"next"===this.method&&(this.arg=void 0),f}},t},a("6a54"),a("01a2"),a("e39c"),a("bf0f"),a("844d"),a("18f7"),a("de6c"),a("3872e"),a("4e9b"),a("114e"),a("c240"),a("926e"),a("7a76"),a("c9b5"),a("aa9c"),a("2797"),a("8a8d"),a("dc69"),a("f7a5");var i=function(t){return t&&t.__esModule?t:{default:t}}(a("fcf3"))},"2fdc":function(t,e,a){"use strict";function i(t,e,a,i,n,o,r){try{var s=t[o](r),c=s.value}catch(u){return void a(u)}s.done?e(c):Promise.resolve(c).then(i,n)}a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return function(){var e=this,a=arguments;return new Promise((function(n,o){var r=t.apply(e,a);function s(t){i(r,n,o,s,c,"next",t)}function c(t){i(r,n,o,s,c,"throw",t)}s(void 0)}))}},a("bf0f")},"5cae":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("2634")),o=i(a("2fdc"));a("5c47"),a("a1c1"),a("c9b5"),a("bf0f"),a("ab80"),a("c223"),a("aa77");var r=a("f8c5"),s=i(a("c780")),c={data:function(){return{customerId:"",customerTypeList:[],relatedId:"",customerData:{},followUpData:[],opportunityData:[{id:"1",title:"企业ERP系统实施项目",description:"客户需要实施一套完整的ERP系统，覆盖采购、生产、销售、财务等模块",expectedValue:15e5,probability:75,stage:"方案确认",lastUpdateTime:"2023-10-20"},{id:"2",title:"数据中心扩容方案",description:"客户计划扩大现有数据中心规模，需要采购服务器、存储和网络设备",expectedValue:8e5,probability:60,stage:"需求分析",lastUpdateTime:"2023-10-12"}],transactionData:[{id:"1",title:"年度维护合同",time:"2023-09-15",description:"签订了价值30万的年度系统维护合同。",amount:3e5,paidAmount:3e5,status:"success",icon:"icon-check"},{id:"2",title:"系统升级项目",time:"2023-05-20",description:"完成了系统升级项目，客户表示非常满意。",amount:2e5,paidAmount:2e5,status:"success",icon:"icon-check"}]}},methods:{formatNumber:function(t){return t?t.toString().replace(/\B(?=(\d{3})+(?!\d))/g,","):"0"},loadData:function(){var t=this;return(0,o.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,s.default)("CustomType").then((function(e){t.customerTypeList=e}));case 2:case"end":return e.stop()}}),e)})))()},loadTaskData:function(){var t=this,e={pageIndex:1,pageSize:10,filter:{fuzzySeach:"",relatedId:this.relatedId,sortAsc:!0,sortProperty:"StatusSort"}};(0,r.getTaskDetail)(e).then((function(e){t.followUpData=e.items}))},showHeaderMenu:function(){var t=this;uni.showActionSheet({itemList:["删除客户"],success:function(e){0===e.tapIndex&&t.confirmDelete()}})},confirmDelete:function(){var t=this;uni.showModal({title:"确认删除",content:"确定要删除此客户吗？此操作无法撤销。",success:function(e){e.confirm&&(0,r.deleteCustomer)(t.customerId).then((function(t){uni.showToast({title:"删除成功",icon:"success"}),setTimeout((function(){uni.navigateBack()}),1500)}))}})},editCustomer:function(){uni.navigateTo({url:"./customer-edit?id=".concat(this.customerId)})},makePhoneCall:function(t){t||(t=this.getPrimaryContact().telephone),t?uni.makePhoneCall({phoneNumber:t,fail:function(){uni.showToast({title:"拨打电话失败",icon:"none"})}}):uni.showToast({title:"无电话号码",icon:"none"})},sendSMS:function(t){t?uni.sendSMS({phoneNumber:t,fail:function(){uni.showToast({title:"发送短信失败",icon:"none"})}}):uni.showToast({title:"无电话号码",icon:"none"})},sendEmail:function(t){t||(t=this.getPrimaryContact().email),t?window.location.href="mailto:".concat(t):uni.showToast({title:"无邮箱地址",icon:"none"})},createFollowUp:function(){uni.navigateTo({url:"./follow-up-create?id=".concat(this.customerId,"&name=").concat(encodeURIComponent(this.customerData.name))})},createTransaction:function(){uni.navigateTo({url:"./transaction-create?id=".concat(this.customerId,"&name=").concat(encodeURIComponent(this.customerData.name))})},createOpportunity:function(){uni.navigateTo({url:"../sales/opportunity-create?customerId=".concat(this.customerId,"&customerName=").concat(encodeURIComponent(this.customerData.name))})},loadCustomerData:function(t){this.customerId=t},getIconNameForActivity:function(t){return{call:"phone",visit:"user-check",email:"mail",proposal:"document",contract:"file-contract",payment:"money"}[t]||"history"},getPrimaryContact:function(){if(!this.customerData.contacts||0===this.customerData.contacts.length)return{name:"",phone:"",email:""};var t=this.customerData.contacts.find((function(t){return t.isPrimary}));return t||this.customerData.contacts[0].contact},goToOpportunityDetail:function(t){uni.navigateTo({url:"../sales/opportunity-detail?id=".concat(t)})},goToContractDetail:function(t){uni.navigateTo({url:"../contracts/contract-detail?id=".concat(t)})},goToFollowUpDetail:function(t){uni.navigateTo({url:"./customer-follow-up?id=".concat(t,"&mode=view")})}},onLoad:function(t){var e=this;return(0,o.default)((0,n.default)().mark((function a(){return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!t.id){a.next=8;break}return e.relatedId=t.id,e.loadCustomerData(t.id),e.loadTaskData(),a.next=6,e.loadData();case 6:return a.next=8,(0,r.getCustomerById)(t.id).then((function(t){e.customerData=t;var a=e.customerTypeList.find((function(e){if(e.id===t.customTypeId)return e}));a&&a.displayText?e.customerData.customType=a.displayText:e.customerData.customType="未知"}));case 8:case"end":return a.stop()}}),a)})))()},onShow:function(){},onPullDownRefresh:function(){setTimeout((function(){uni.stopPullDownRefresh()}),1e3)}};e.default=c},"6bae":function(t,e,a){"use strict";a.r(e);var i=a("ac08"),n=a("b610");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("00c5");var r=a("828b"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"7147860a",null,!1,i["a"],void 0);e["default"]=s.exports},ac08:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={svgIcon:a("8a0f").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"container"},[a("v-uni-view",{staticClass:"page-header"},[a("v-uni-navigator",{staticClass:"back-button",attrs:{url:"./customer-list","open-type":"navigateBack"}},[a("svg-icon",{attrs:{name:"arrow-left",type:"svg",size:"32"}})],1),a("v-uni-view",{staticClass:"page-title"},[t._v("客户详情")]),a("v-uni-view",{staticClass:"header-actions"},[a("v-uni-view",{staticClass:"action-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.editCustomer.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"edit",type:"svg",size:"28"}})],1),a("v-uni-view",{staticClass:"action-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showHeaderMenu.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"more",type:"svg",size:"28"}})],1)],1)],1),a("v-uni-view",{staticClass:"page-container"},[a("v-uni-view",{staticClass:"detail-card"},[a("v-uni-view",{staticClass:"detail-header"},[a("v-uni-view",[a("v-uni-view",{staticClass:"customer-title"},[t._v(t._s(t.customerData.name))]),a("v-uni-view",{staticClass:"customer-tags"},[t.customerData&&t.customerData.customLevel?a("v-uni-text",{staticClass:"tag",class:t.customerData.customLevel.code?"tag-"+t.customerData.customLevel.code:"tag-default"},[t._v(t._s(t.customerData.customLevel.displayText))]):t._e(),a("v-uni-text",{staticClass:"tag tag-industry"},[t._v(t._s(t.customerData.industry))]),t.customerData.customTag?a("v-uni-text",{staticClass:"tag tag-custom"},[t._v(t._s(t.customerData.customTag))]):t._e()],1)],1),a("v-uni-view",{staticClass:"customer-value"},[t._v("¥"+t._s(t.formatNumber(t.customerData.registeredCapital)))])],1),a("v-uni-view",{staticClass:"detail-content"},[a("v-uni-view",{staticClass:"info-section"},[a("v-uni-view",{staticClass:"section-title"},[a("svg-icon",{attrs:{name:"building",type:"svg",size:"28"}}),a("v-uni-text",[t._v("基本信息")])],1),a("v-uni-view",{staticClass:"info-grid"},[a("v-uni-view",{staticClass:"info-item"},[a("v-uni-view",{staticClass:"info-label"},[t._v("地区")]),a("v-uni-view",{staticClass:"info-value"},[t._v(t._s(t._f("formatEmptyFilter")(t.customerData.province+t.customerData.city)))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-view",{staticClass:"info-label"},[t._v("客户类型")]),a("v-uni-view",{staticClass:"info-value"},[t._v(t._s(t.customerData.customType))])],1)],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-view",{staticClass:"info-label"},[t._v("详细地址")]),a("v-uni-view",{staticClass:"info-value"},[t._v(t._s(t._f("formatEmptyFilter")(t.customerData.address)))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-view",{staticClass:"info-label"},[t._v("公司介绍")]),a("v-uni-view",{staticClass:"info-value"},[t._v(t._s(t._f("formatEmptyFilter")(t.customerData.introduction)))])],1)],1),a("v-uni-view",{staticClass:"info-section"},[a("v-uni-view",{staticClass:"section-title"},[a("svg-icon",{attrs:{name:"user",type:"svg",size:"28"}}),a("v-uni-text",[t._v("联系人信息")])],1),a("v-uni-view",{staticClass:"contact-list"},t._l(t.customerData.contacts,(function(e){return a("v-uni-view",{key:e.contactId,staticClass:"contact-item"},[a("v-uni-view",{staticClass:"contact-avatar"},[a("svg-icon",{attrs:{name:"user",type:"svg",size:"32"}})],1),a("v-uni-view",{staticClass:"contact-info"},[a("v-uni-view",{staticClass:"contact-name"},[t._v(t._s(e.contact.name))]),a("v-uni-view",{staticClass:"contact-position"},[t._v(t._s(e.contact.position||"暂无职位"))])],1),a("v-uni-view",{staticClass:"contact-actions"},[a("v-uni-view",{staticClass:"contact-action",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.makePhoneCall(e.contact.telephone)}}},[a("svg-icon",{attrs:{name:"phone",type:"svg",size:"28"}})],1),a("v-uni-view",{staticClass:"contact-action",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.sendSMS(e.contact.telephone)}}},[a("svg-icon",{attrs:{name:"message",type:"svg",size:"28"}})],1)],1)],1)})),1)],1),a("v-uni-view",{staticClass:"info-section"},[a("v-uni-view",{staticClass:"section-title"},[a("svg-icon",{attrs:{name:"info",type:"svg",size:"28"}}),a("v-uni-text",[t._v("附加信息")])],1),a("v-uni-view",{staticClass:"info-grid"},[a("v-uni-view",{staticClass:"info-item"},[a("v-uni-view",{staticClass:"info-label"},[t._v("创建日期")]),a("v-uni-view",{staticClass:"info-value"},[t._v(t._s(t._f("formatDateFilter")(t.customerData.creationTime)))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-view",{staticClass:"info-label"},[t._v("创建人")]),a("v-uni-view",{staticClass:"info-value"},[t._v(t._s(t.customerData.creatorName))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-view",{staticClass:"info-label"},[t._v("负责人")]),a("v-uni-view",{staticClass:"info-value"},[t._v(t._s(t.customerData.owner||"暂无"))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-view",{staticClass:"info-label"},[t._v("最近联系")]),a("v-uni-view",{staticClass:"info-value"},[t._v(t._s(t.customerData.lastContactTime||"暂无"))])],1)],1)],1)],1)],1),a("v-uni-view",{staticClass:"detail-card"},[a("v-uni-view",{staticClass:"detail-header"},[a("v-uni-view",{staticClass:"section-title",staticStyle:{"margin-bottom":"0"}},[a("svg-icon",{attrs:{name:"history",type:"svg",size:"28"}}),a("v-uni-text",[t._v("任务记录")])],1),a("v-uni-view",{staticClass:"action-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.createFollowUp.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"add",type:"svg",size:"28"}})],1)],1),a("v-uni-view",{staticClass:"detail-content"},[a("v-uni-view",{staticClass:"activity-list"},t._l(t.followUpData,(function(e,i){return a("v-uni-view",{key:i,staticClass:"activity-item",attrs:{"hover-class":"item-hover"},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.goToFollowUpDetail(e.id)}}},[a("v-uni-view",{staticClass:"activity-icon"},[a("svg-icon",{attrs:{name:t.getIconNameForActivity(e.icon),type:"svg",size:"28"}})],1),a("v-uni-view",{staticClass:"activity-content"},[a("v-uni-view",{staticClass:"activity-header"},[a("v-uni-view",{staticClass:"activity-title"},[t._v(t._s(e.title))]),a("v-uni-view",{staticClass:"activity-time"},[t._v(t._s(e.relatedName))])],1),a("v-uni-view",{staticClass:"activity-description"},[t._v(t._s(e.description))]),a("v-uni-view",{staticClass:"activity-meta"},[a("v-uni-view",[t._v(t._s(t._f("formatDateFilter")(e.creationTime)))]),a("v-uni-view",[t._v(t._s(t._f("formatDateFilter")(e.lastModificationTime)))])],1)],1)],1)})),1)],1)],1)],1),a("v-uni-view",{staticClass:"bottom-actions"},[a("v-uni-view",{staticClass:"action-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.makePhoneCall(t.getPrimaryContact().telephone)}}},[a("svg-icon",{attrs:{name:"phone",type:"svg",size:"28"}}),a("v-uni-text",{staticClass:"action-label"},[t._v("电话")])],1),a("v-uni-view",{staticClass:"action-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.sendEmail(t.getPrimaryContact().email)}}},[a("svg-icon",{attrs:{name:"mail",type:"svg",size:"28"}}),a("v-uni-text",{staticClass:"action-label"},[t._v("邮件")])],1),a("v-uni-view",{staticClass:"action-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.createFollowUp.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"history",type:"svg",size:"28"}}),a("v-uni-text",{staticClass:"action-label"},[t._v("跟进")])],1),a("v-uni-view",{staticClass:"action-btn primary",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.createOpportunity.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"add-file",type:"svg",size:"28"}}),a("v-uni-text",{staticClass:"action-label"},[t._v("创建商机")])],1)],1)],1)},o=[]},b610:function(t,e,a){"use strict";a.r(e);var i=a("5cae"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},c475:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.request=e.getTenantInfo=void 0;var n=i(a("9b1b"));a("bf0f"),a("4626"),a("5ac7");var o=null;e.getTenantInfo=function(t){return new Promise((function(e,a){uni.request({url:"/api/Tenants/getTenantsByLogin",method:"POST",data:t,header:{"Content-Type":"application/json"},success:function(t){200===t.statusCode?(o=t.data,e(t.data)):a(t.data)},fail:function(t){a(t)}})}))};e.request=function(t){return t.url.includes("/login")&&o&&(t.header=(0,n.default)((0,n.default)({},t.header),{},{__tenant:o[0].id})),new Promise((function(e,a){uni.request({url:t.url,method:t.method||"GET",data:t.data||{},header:(0,n.default)({Authorization:"Bearer "+uni.getStorageSync("token"),"Content-Type":"application/json"},t.header),success:function(t){200===t.statusCode?e(t.data):a(t.data)},fail:function(t){a(t)}})}))}},c780:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return s.apply(this,arguments)},a("8f71"),a("bf0f");var n=i(a("2634")),o=i(a("2fdc")),r=a("17c4");function s(){return s=(0,o.default)((0,n.default)().mark((function t(e){var a,i,o,s,c,u,l,d,v=arguments;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=v.length>1&&void 0!==v[1]?v[1]:{},i=a.pageIndex,o=void 0===i?1:i,s=a.pageSize,c=void 0===s?100:s,t.prev=2,t.next=5,(0,r.getDictionaryPage)({pageIndex:o,pageSize:c,filter:e});case 5:if(l=t.sent,null!==l&&void 0!==l&&null!==(u=l.items)&&void 0!==u&&u.length){t.next=8;break}return t.abrupt("return",[]);case 8:return t.next=10,(0,r.getDictionaryPageDetail)({pageIndex:o,pageSize:c,dataDictionaryId:l.items[0].id});case 10:return d=t.sent,t.abrupt("return",d.items.filter((function(t){return t.isEnabled})));case 14:throw t.prev=14,t.t0=t["catch"](2),console.error("Error fetching select options:",t.t0),t.t0;case 18:case"end":return t.stop()}}),t,null,[[2,14]])}))),s.apply(this,arguments)}},e5ea:function(t,e,a){var i=a("0c63");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("0937c6bb",i,!0,{sourceMap:!1,shadowMode:!1})},f8c5:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.updateTaskDescription=e.updateCustomerById=e.getTaskDetail=e.getOwnerList=e.getOrganizeUsers=e.getCustomerList=e.getCustomerDetail=e.getCustomerById=e.deleteCustomer=e.createCustomer=e.TmsTaskTimeEnd=e.TmsTaskCreate=void 0,a("c223");var i=a("c475");e.getCustomerList=function(t){return(0,i.request)({url:"/api/crm/custom/getList",method:"POST",data:t})};e.getCustomerDetail=function(t){return(0,i.request)({url:"/api/crm/custom/getCustomById?id="+t,method:"GET"})};e.createCustomer=function(t){return(0,i.request)({url:"/api/crm/custom/create",method:"POST",data:t})};e.getCustomerById=function(t){return(0,i.request)({url:"/api/crm/custom/getCustomById?id="+t,method:"GET"})};e.updateCustomerById=function(t,e){return(0,i.request)({url:"/api/crm/custom/update?id="+t,method:"POST",data:e})};e.getOwnerList=function(t){return(0,i.request)({url:"/api/Users/<USER>",method:"POST",data:t})};e.deleteCustomer=function(t){return(0,i.request)({url:"/api/crm/custom/delete?id="+t,method:"POST"})};e.getOrganizeUsers=function(){return(0,i.request)({url:"/api/tms/taskTemplate/getOrganizeUsers",method:"GET"})};e.TmsTaskCreate=function(t){return(0,i.request)({url:"/api/tms/taskItem/create",method:"POST",data:t})};e.TmsTaskTimeEnd=function(t,e){return(0,i.request)({url:"/api/tms/taskItem/editPlanDoneDate?id=".concat(t,"&newDate=").concat(e),method:"POST"})};e.updateTaskDescription=function(t,e){return(0,i.request)({url:"/api/tms/taskItem/updateDesc?id="+t,method:"POST",data:e})};e.getTaskDetail=function(t){return(0,i.request)({url:"/api/tms/taskItem/getList",method:"POST",data:t})}}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-hr-employee-detail"],{"0bec":function(t,e,i){"use strict";i.r(e);var a=i("5b4d"),s=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=s.a},2079:function(t,e,i){"use strict";i.r(e);var a=i("3827"),s=i("0bec");for(var n in s)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return s[t]}))}(n);i("869b");var o=i("828b"),l=Object(o["a"])(s["default"],a["b"],a["c"],!1,null,"30054025",null,!1,a["a"],void 0);e["default"]=l.exports},3827:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"container"},[i("v-uni-view",{staticClass:"page-header"},[i("v-uni-view",{staticClass:"back-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"ri-arrow-left-line"})],1),i("v-uni-text",{staticClass:"page-title"},[t._v("员工详情")]),i("v-uni-view",{staticClass:"header-actions"},[i("v-uni-button",{staticClass:"action-button",attrs:{type:"button"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.editEmployee.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"ri-edit-line"})],1)],1)],1),i("v-uni-view",{staticClass:"employee-card"},[i("v-uni-view",{staticClass:"employee-header"},[i("v-uni-view",{staticClass:"employee-avatar"},[i("v-uni-image",{attrs:{src:t.employee.avatar||"/static/images/default-avatar.png",mode:"aspectFill"}})],1),i("v-uni-view",{staticClass:"employee-main-info"},[i("v-uni-view",{staticClass:"employee-name-row"},[i("v-uni-text",{staticClass:"employee-name"},[t._v(t._s(t.employee.name))]),i("v-uni-text",{class:["employee-status","active"===t.employee.status?"status-active":"status-inactive"]},[t._v(t._s("active"===t.employee.status?"在职":"离职"))])],1),i("v-uni-text",{staticClass:"employee-position"},[t._v(t._s(t.employee.position))]),i("v-uni-text",{staticClass:"employee-department"},[t._v(t._s(t.employee.department))])],1)],1),i("v-uni-view",{staticClass:"employee-quick-contact"},[i("v-uni-view",{staticClass:"contact-action",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.callEmployee.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"ri-phone-line contact-icon"}),i("v-uni-text",{staticClass:"contact-label"},[t._v("电话")])],1),i("v-uni-view",{staticClass:"contact-action",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.messageEmployee.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"ri-message-2-line contact-icon"}),i("v-uni-text",{staticClass:"contact-label"},[t._v("短信")])],1),i("v-uni-view",{staticClass:"contact-action",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.emailEmployee.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"ri-mail-line contact-icon"}),i("v-uni-text",{staticClass:"contact-label"},[t._v("邮件")])],1)],1)],1),i("v-uni-view",{staticClass:"detail-section"},[i("v-uni-view",{staticClass:"section-header"},[i("v-uni-text",{staticClass:"section-title"},[t._v("个人信息")])],1),i("v-uni-view",{staticClass:"info-list"},[i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("工号")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.employee.employeeId))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("性别")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.employee.gender))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("出生日期")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.employee.birthday))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("身份证号")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.employee.idNumber))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("手机")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.employee.phone))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("邮箱")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.employee.email))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("居住地址")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.employee.address))])],1)],1)],1),i("v-uni-view",{staticClass:"detail-section"},[i("v-uni-view",{staticClass:"section-header"},[i("v-uni-text",{staticClass:"section-title"},[t._v("工作信息")])],1),i("v-uni-view",{staticClass:"info-list"},[i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("入职日期")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.employee.hireDate))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("部门")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.employee.department))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("职位")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.employee.position))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("直属上级")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.employee.manager))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("工作地点")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.employee.workLocation))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("员工类型")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.employee.employeeType))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("工作状态")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s("active"===t.employee.status?"在职":"离职"))])],1)],1)],1),i("v-uni-view",{staticClass:"detail-section"},[i("v-uni-view",{staticClass:"section-header"},[i("v-uni-text",{staticClass:"section-title"},[t._v("绩效概览")]),i("v-uni-text",{staticClass:"section-action",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.viewPerformance.apply(void 0,arguments)}}},[t._v("查看详情")])],1),i("v-uni-view",{staticClass:"performance-overview"},[i("v-uni-view",{staticClass:"performance-card"},[i("v-uni-text",{staticClass:"performance-value"},[t._v(t._s(t.employee.performance.sales||0))]),i("v-uni-text",{staticClass:"performance-label"},[t._v("销售业绩(元)")])],1),i("v-uni-view",{staticClass:"performance-card"},[i("v-uni-text",{staticClass:"performance-value"},[t._v(t._s(t.employee.performance.tasks||0))]),i("v-uni-text",{staticClass:"performance-label"},[t._v("完成任务数")])],1),i("v-uni-view",{staticClass:"performance-card"},[i("v-uni-text",{staticClass:"performance-value"},[t._v(t._s(t.employee.performance.customers||0))]),i("v-uni-text",{staticClass:"performance-label"},[t._v("新增客户数")])],1)],1)],1),i("v-uni-view",{staticClass:"detail-section"},[i("v-uni-view",{staticClass:"section-header"},[i("v-uni-text",{staticClass:"section-title"},[t._v("技能与证书")]),i("v-uni-text",{staticClass:"section-action",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.manageCertifications.apply(void 0,arguments)}}},[t._v("管理")])],1),i("v-uni-view",{staticClass:"tag-list"},t._l(t.employee.skills,(function(e,a){return i("v-uni-view",{key:a,staticClass:"skill-tag"},[t._v(t._s(e))])})),1),i("v-uni-view",{staticClass:"cert-list"},t._l(t.employee.certifications,(function(e,a){return i("v-uni-view",{key:a,staticClass:"cert-item"},[i("v-uni-text",{staticClass:"ri-graduation-cap-line cert-icon"}),i("v-uni-view",{staticClass:"cert-detail"},[i("v-uni-text",{staticClass:"cert-name"},[t._v(t._s(e.name))]),i("v-uni-text",{staticClass:"cert-date"},[t._v("获得时间："+t._s(e.date))])],1)],1)})),1)],1),i("v-uni-view",{staticClass:"detail-section"},[i("v-uni-view",{staticClass:"section-header"},[i("v-uni-text",{staticClass:"section-title"},[t._v("合同与薪资")]),i("v-uni-text",{staticClass:"section-action",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.viewContract.apply(void 0,arguments)}}},[t._v("查看合同")])],1),i("v-uni-view",{staticClass:"info-list"},[i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("合同开始日期")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.employee.contract.startDate))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("合同结束日期")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.employee.contract.endDate))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("合同类型")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.employee.contract.type))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("基本工资")]),i("v-uni-text",{staticClass:"info-value"},[t._v("¥"+t._s(t.formatSalary(t.employee.salary.base)))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("社保基数")]),i("v-uni-text",{staticClass:"info-value"},[t._v("¥"+t._s(t.formatSalary(t.employee.salary.socialSecurity)))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("公积金基数")]),i("v-uni-text",{staticClass:"info-value"},[t._v("¥"+t._s(t.formatSalary(t.employee.salary.housingFund)))])],1)],1)],1),i("v-uni-view",{staticClass:"detail-section"},[i("v-uni-view",{staticClass:"section-header"},[i("v-uni-text",{staticClass:"section-title"},[t._v("操作记录")]),i("v-uni-text",{staticClass:"section-action",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.viewAllRecords.apply(void 0,arguments)}}},[t._v("全部记录")])],1),i("v-uni-view",{staticClass:"timeline"},t._l(t.employee.records,(function(e,a){return i("v-uni-view",{key:a,staticClass:"timeline-item"},[i("v-uni-view",{staticClass:"timeline-dot"}),i("v-uni-view",{staticClass:"timeline-content"},[i("v-uni-text",{staticClass:"timeline-title"},[t._v(t._s(e.title))]),i("v-uni-text",{staticClass:"timeline-desc"},[t._v(t._s(e.description))]),i("v-uni-text",{staticClass:"timeline-time"},[t._v(t._s(e.time))])],1)],1)})),1)],1)],1)},s=[]},"5b4d":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={data:function(){return{id:"",employee:{id:"1",name:"张三",status:"active",employeeId:"EMP001",gender:"男",birthday:"1985-06-15",idNumber:"110101198506150011",department:"销售部",position:"销售经理",manager:"王经理",hireDate:"2020-06-15",workLocation:"北京总部",employeeType:"全职",phone:"13812345678",email:"<EMAIL>",address:"北京市朝阳区长安街1号",avatar:"/static/images/avatars/avatar1.png",performance:{sales:125e4,tasks:28,customers:15},skills:["销售谈判","CRM系统","市场分析","客户关系管理","团队领导"],certifications:[{name:"高级销售管理师",date:"2019-05-20"},{name:"客户关系管理专业认证",date:"2018-11-15"}],contract:{startDate:"2020-06-15",endDate:"2023-06-14",type:"固定期限合同"},salary:{base:12e3,socialSecurity:8e3,housingFund:8e3},records:[{title:"工资调整",description:"基本工资从10000元调整为12000元",time:"2022-01-01 09:30"},{title:"晋升",description:"职位从销售主管晋升为销售经理",time:"2021-07-15 14:20"},{title:"入职",description:"正式入职销售部，职位销售主管",time:"2020-06-15 09:00"}]}}},onLoad:function(t){t.id&&(this.id=t.id,this.fetchEmployeeData(this.id))},methods:{fetchEmployeeData:function(t){console.log("Fetching employee data for ID:",t)},formatSalary:function(t){return t.toLocaleString("zh-CN")},goBack:function(){uni.navigateBack()},editEmployee:function(){uni.navigateTo({url:"/pages/hr/employee-edit?id=".concat(this.id)})},callEmployee:function(){uni.makePhoneCall({phoneNumber:this.employee.phone,success:function(){console.log("拨打电话成功")},fail:function(t){console.error("拨打电话失败",t)}})},messageEmployee:function(){uni.showToast({title:"短信功能暂未实现",icon:"none"})},emailEmployee:function(){uni.setClipboardData({data:this.employee.email,success:function(){uni.showToast({title:"邮箱已复制",icon:"success"})}})},viewPerformance:function(){uni.navigateTo({url:"/pages/hr/employee-performance?id=".concat(this.id)})},manageCertifications:function(){uni.navigateTo({url:"/pages/hr/employee-certifications?id=".concat(this.id)})},viewContract:function(){uni.navigateTo({url:"/pages/hr/employee-contract?id=".concat(this.id)})},viewAllRecords:function(){uni.navigateTo({url:"/pages/hr/employee-records?id=".concat(this.id)})}}};e.default=a},"869b":function(t,e,i){"use strict";var a=i("a00e"),s=i.n(a);s.a},a00e:function(t,e,i){var a=i("aa32");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var s=i("967d").default;s("016878e8",a,!0,{sourceMap:!1,shadowMode:!1})},aa32:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'.container[data-v-30054025]{padding-bottom:%?50?%;background-color:#f5f7fa;min-height:100vh}.page-header[data-v-30054025]{display:flex;align-items:center;padding:%?20?% %?30?%;background-color:#fff;position:relative;border-bottom:%?1?% solid #eaeaea}.back-button[data-v-30054025]{font-size:%?40?%;color:#333;padding:%?10?%}.page-title[data-v-30054025]{flex:1;text-align:center;font-size:%?36?%;font-weight:500;color:#333}.header-actions[data-v-30054025]{display:flex;align-items:center}.action-button[data-v-30054025]{background:none;border:none;font-size:%?40?%;color:#666;padding:%?10?%}.employee-card[data-v-30054025]{margin:%?20?% %?30?%;border-radius:%?20?%;background-color:#fff;box-shadow:0 %?2?% %?10?% rgba(0,0,0,.05);overflow:hidden}.employee-header[data-v-30054025]{display:flex;padding:%?30?%;border-bottom:%?1?% solid #eaeaea}.employee-avatar[data-v-30054025]{width:%?120?%;height:%?120?%;border-radius:%?60?%;overflow:hidden;background-color:#eaeaea;margin-right:%?20?%}.employee-avatar uni-image[data-v-30054025]{width:100%;height:100%}.employee-main-info[data-v-30054025]{flex:1}.employee-name-row[data-v-30054025]{display:flex;justify-content:space-between;align-items:center;margin-bottom:%?10?%}.employee-name[data-v-30054025]{font-size:%?36?%;font-weight:500;color:#333}.employee-status[data-v-30054025]{font-size:%?24?%;padding:%?6?% %?15?%;border-radius:%?15?%}.status-active[data-v-30054025]{background-color:#e6f7ed;color:#52c41a}.status-inactive[data-v-30054025]{background-color:#fff1f0;color:#ff4d4f}.employee-position[data-v-30054025]{font-size:%?28?%;color:#666;margin-bottom:%?8?%}.employee-department[data-v-30054025]{font-size:%?26?%;color:#999}.employee-quick-contact[data-v-30054025]{display:flex;justify-content:space-around;padding:%?20?% 0}.contact-action[data-v-30054025]{display:flex;flex-direction:column;align-items:center}.contact-icon[data-v-30054025]{font-size:%?40?%;color:#4a6fff;margin-bottom:%?8?%}.contact-label[data-v-30054025]{font-size:%?24?%;color:#666}.detail-section[data-v-30054025]{margin:%?20?% %?30?%;border-radius:%?20?%;background-color:#fff;box-shadow:0 %?2?% %?10?% rgba(0,0,0,.05);overflow:hidden}.section-header[data-v-30054025]{display:flex;justify-content:space-between;align-items:center;padding:%?20?% %?30?%;border-bottom:%?1?% solid #eaeaea}.section-title[data-v-30054025]{font-size:%?30?%;font-weight:500;color:#333}.section-action[data-v-30054025]{font-size:%?26?%;color:#4a6fff}.info-list[data-v-30054025]{padding:%?15?% %?30?%}.info-item[data-v-30054025]{display:flex;justify-content:space-between;padding:%?15?% 0;border-bottom:%?1?% solid #f0f0f0}.info-item[data-v-30054025]:last-child{border-bottom:none}.info-label[data-v-30054025]{font-size:%?28?%;color:#666}.info-value[data-v-30054025]{font-size:%?28?%;color:#333}.performance-overview[data-v-30054025]{display:flex;justify-content:space-between;padding:%?20?% %?30?%}.performance-card[data-v-30054025]{flex:1;display:flex;flex-direction:column;align-items:center;padding:%?15?% 0}.performance-value[data-v-30054025]{font-size:%?32?%;font-weight:500;color:#333;margin-bottom:%?8?%}.performance-label[data-v-30054025]{font-size:%?24?%;color:#999}.tag-list[data-v-30054025]{display:flex;flex-wrap:wrap;padding:%?20?% %?30?%}.skill-tag[data-v-30054025]{background-color:#f0f2f5;color:#666;font-size:%?24?%;padding:%?8?% %?20?%;border-radius:%?30?%;margin:%?10?% %?15?% %?10?% 0}.cert-list[data-v-30054025]{padding:0 %?30?% %?20?%}.cert-item[data-v-30054025]{display:flex;align-items:center;padding:%?15?% 0;border-bottom:%?1?% solid #f0f0f0}.cert-item[data-v-30054025]:last-child{border-bottom:none}.cert-icon[data-v-30054025]{font-size:%?36?%;color:#4a6fff;margin-right:%?15?%}.cert-detail[data-v-30054025]{flex:1}.cert-name[data-v-30054025]{font-size:%?28?%;color:#333;margin-bottom:%?5?%}.cert-date[data-v-30054025]{font-size:%?24?%;color:#999}.timeline[data-v-30054025]{padding:%?20?% %?30?%}.timeline-item[data-v-30054025]{position:relative;padding-left:%?30?%;margin-bottom:%?30?%}.timeline-item[data-v-30054025]:last-child{margin-bottom:0}.timeline-dot[data-v-30054025]{position:absolute;left:0;top:%?10?%;width:%?16?%;height:%?16?%;border-radius:50%;background-color:#4a6fff}.timeline-item[data-v-30054025]:before{content:"";position:absolute;left:%?7?%;top:%?30?%;width:%?2?%;height:calc(100% + %?20?%);background-color:#e8e8e8}.timeline-item[data-v-30054025]:last-child:before{display:none}.timeline-content[data-v-30054025]{display:flex;flex-direction:column}.timeline-title[data-v-30054025]{font-size:%?28?%;font-weight:500;color:#333;margin-bottom:%?5?%}.timeline-desc[data-v-30054025]{font-size:%?26?%;color:#666;margin-bottom:%?5?%}.timeline-time[data-v-30054025]{font-size:%?24?%;color:#999}',""]),t.exports=e}}]);
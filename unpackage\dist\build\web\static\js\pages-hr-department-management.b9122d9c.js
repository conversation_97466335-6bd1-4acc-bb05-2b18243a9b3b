(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-hr-department-management"],{"30f7":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},a("7a76"),a("c9b5")},"330e":function(t,e,a){"use strict";var i=a("4eb4"),n=a.n(i);n.a},"375e":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"container"},[a("v-uni-view",{staticClass:"page-header"},[a("v-uni-view",{staticClass:"back-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-arrow-left-line"})],1),a("v-uni-text",{staticClass:"page-title"},[t._v("部门管理")]),a("v-uni-view",{staticClass:"header-actions"},[a("v-uni-button",{staticClass:"action-button",attrs:{type:"button"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showAddDepartment.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-add-line"})],1)],1)],1),a("v-uni-view",{staticClass:"stats-card"},[a("v-uni-view",{staticClass:"stats-item"},[a("v-uni-text",{staticClass:"stats-value"},[t._v(t._s(t.departments.length))]),a("v-uni-text",{staticClass:"stats-label"},[t._v("部门总数")])],1),a("v-uni-view",{staticClass:"stats-item"},[a("v-uni-text",{staticClass:"stats-value"},[t._v(t._s(t.totalEmployees))]),a("v-uni-text",{staticClass:"stats-label"},[t._v("员工总数")])],1),a("v-uni-view",{staticClass:"stats-item"},[a("v-uni-text",{staticClass:"stats-value"},[t._v(t._s(t.avgDeptSize))]),a("v-uni-text",{staticClass:"stats-label"},[t._v("平均部门规模")])],1)],1),a("v-uni-view",{staticClass:"department-list"},t._l(t.departments,(function(e,i){return a("v-uni-view",{key:i,staticClass:"department-item",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.viewDepartmentDetail(e.id)}}},[a("v-uni-view",{staticClass:"department-header"},[a("v-uni-view",{staticClass:"department-icon",style:{backgroundColor:e.color}},[a("v-uni-text",{class:e.icon})],1),a("v-uni-view",{staticClass:"department-main"},[a("v-uni-text",{staticClass:"department-name"},[t._v(t._s(e.name))]),a("v-uni-text",{staticClass:"department-manager"},[t._v(t._s(e.manager))])],1),a("v-uni-view",{staticClass:"department-count"},[a("v-uni-text",{staticClass:"count-value"},[t._v(t._s(e.employeeCount))]),a("v-uni-text",{staticClass:"count-label"},[t._v("人")])],1)],1),a("v-uni-view",{staticClass:"department-body"},[a("v-uni-view",{staticClass:"info-row"},[a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"info-label"},[t._v("部门编号")]),a("v-uni-text",{staticClass:"info-value"},[t._v(t._s(e.code))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"info-label"},[t._v("创建日期")]),a("v-uni-text",{staticClass:"info-value"},[t._v(t._s(e.createDate))])],1)],1),a("v-uni-view",{staticClass:"info-row"},[a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"info-label"},[t._v("上级部门")]),a("v-uni-text",{staticClass:"info-value"},[t._v(t._s(e.parentDepartment||"-"))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"info-label"},[t._v("部门状态")]),a("v-uni-text",{class:["info-value","status-"+e.status]},[t._v(t._s("active"===e.status?"正常":"已停用"))])],1)],1)],1),a("v-uni-view",{staticClass:"department-footer"},[a("v-uni-view",{staticClass:"action-buttons"},[a("v-uni-view",{staticClass:"action-btn",on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.editDepartment(e)}}},[a("v-uni-text",{staticClass:"ri-edit-line"}),a("v-uni-text",[t._v("编辑")])],1),a("v-uni-view",{staticClass:"action-btn",on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.manageDepartmentMembers(e.id)}}},[a("v-uni-text",{staticClass:"ri-team-line"}),a("v-uni-text",[t._v("成员")])],1),"active"===e.status?a("v-uni-view",{staticClass:"action-btn",on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.toggleDepartmentStatus(e,"inactive")}}},[a("v-uni-text",{staticClass:"ri-pause-circle-line"}),a("v-uni-text",[t._v("停用")])],1):a("v-uni-view",{staticClass:"action-btn",on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.toggleDepartmentStatus(e,"active")}}},[a("v-uni-text",{staticClass:"ri-play-circle-line"}),a("v-uni-text",[t._v("启用")])],1)],1)],1)],1)})),1),t.showPopup?a("v-uni-view",{staticClass:"popup-mask",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closePopup.apply(void 0,arguments)}}}):t._e(),t.showPopup?a("v-uni-view",{staticClass:"popup-container"},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-text",{staticClass:"popup-title"},[t._v(t._s(t.isEditing?"编辑部门":"添加部门"))]),a("v-uni-text",{staticClass:"popup-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closePopup.apply(void 0,arguments)}}},[t._v("×")])],1),a("v-uni-view",{staticClass:"popup-body"},[a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label required"},[t._v("部门名称")]),a("v-uni-input",{staticClass:"form-input",class:{error:t.errors.name},attrs:{type:"text",placeholder:"请输入部门名称"},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}}),t.errors.name?a("v-uni-text",{staticClass:"error-message"},[t._v(t._s(t.errors.name))]):t._e()],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label required"},[t._v("部门编号")]),a("v-uni-input",{staticClass:"form-input",class:{error:t.errors.code},attrs:{type:"text",placeholder:"请输入部门编号"},model:{value:t.formData.code,callback:function(e){t.$set(t.formData,"code",e)},expression:"formData.code"}}),t.errors.code?a("v-uni-text",{staticClass:"error-message"},[t._v(t._s(t.errors.code))]):t._e()],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("上级部门")]),a("v-uni-picker",{staticClass:"form-picker",attrs:{mode:"selector",range:t.parentDepartments,"range-key":"name"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onParentDepartmentChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-value"},[t.formData.parentDepartment?a("v-uni-text",[t._v(t._s(t.formData.parentDepartment))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择上级部门（可选）")]),a("v-uni-text",{staticClass:"ri-arrow-down-s-line picker-icon"})],1)],1)],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label required"},[t._v("部门负责人")]),a("v-uni-picker",{staticClass:"form-picker",attrs:{mode:"selector",range:t.managers,"range-key":"name"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onManagerChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-value"},[t.formData.manager?a("v-uni-text",[t._v(t._s(t.formData.manager))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择部门负责人")]),a("v-uni-text",{staticClass:"ri-arrow-down-s-line picker-icon"})],1)],1),t.errors.manager?a("v-uni-text",{staticClass:"error-message"},[t._v(t._s(t.errors.manager))]):t._e()],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("部门描述")]),a("v-uni-textarea",{staticClass:"form-textarea",attrs:{placeholder:"请输入部门描述"},model:{value:t.formData.description,callback:function(e){t.$set(t.formData,"description",e)},expression:"formData.description"}})],1)],1),a("v-uni-view",{staticClass:"popup-footer"},[a("v-uni-button",{staticClass:"btn-cancel",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closePopup.apply(void 0,arguments)}}},[t._v("取消")]),a("v-uni-button",{staticClass:"btn-submit",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveDepartment.apply(void 0,arguments)}}},[t._v("保存")])],1)],1):t._e()],1)},n=[]},4092:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'.container[data-v-9d426492]{background-color:#f5f7fa;min-height:100vh;padding-bottom:%?30?%}.page-header[data-v-9d426492]{display:flex;align-items:center;padding:%?20?% %?30?%;background-color:#fff;position:relative;border-bottom:%?1?% solid #eaeaea}.back-button[data-v-9d426492]{font-size:%?40?%;color:#333;padding:%?10?%}.page-title[data-v-9d426492]{flex:1;text-align:center;font-size:%?36?%;font-weight:500;color:#333}.header-actions[data-v-9d426492]{display:flex;align-items:center}.action-button[data-v-9d426492]{background:none;border:none;font-size:%?40?%;color:#666;padding:%?10?%}.stats-card[data-v-9d426492]{margin:%?20?% %?30?%;display:flex;justify-content:space-between;border-radius:%?20?%;background-color:#fff;box-shadow:0 %?2?% %?10?% rgba(0,0,0,.05);padding:%?30?%}.stats-item[data-v-9d426492]{display:flex;flex-direction:column;align-items:center}.stats-value[data-v-9d426492]{font-size:%?36?%;font-weight:500;color:#333;margin-bottom:%?8?%}.stats-label[data-v-9d426492]{font-size:%?24?%;color:#999}.department-list[data-v-9d426492]{padding:0 %?30?%}.department-item[data-v-9d426492]{margin-bottom:in;border-radius:%?20?%;background-color:#fff;box-shadow:0 %?2?% %?10?% rgba(0,0,0,.05);overflow:hidden;margin-bottom:%?20?%}.department-header[data-v-9d426492]{display:flex;align-items:center;padding:%?30?%;border-bottom:%?1?% solid #eaeaea}.department-icon[data-v-9d426492]{width:%?80?%;height:%?80?%;border-radius:50%;display:flex;justify-content:center;align-items:center;margin-right:%?20?%;color:#fff;font-size:%?40?%}.department-main[data-v-9d426492]{flex:1}.department-name[data-v-9d426492]{font-size:%?32?%;font-weight:500;color:#333;margin-bottom:%?8?%}.department-manager[data-v-9d426492]{font-size:%?26?%;color:#666}.department-count[data-v-9d426492]{text-align:center}.count-value[data-v-9d426492]{font-size:%?36?%;font-weight:500;color:#333;margin-right:%?5?%}.count-label[data-v-9d426492]{font-size:%?24?%;color:#999}.department-body[data-v-9d426492]{padding:%?20?% %?30?%;border-bottom:%?1?% solid #eaeaea}.info-row[data-v-9d426492]{display:flex;margin-bottom:%?15?%}.info-row[data-v-9d426492]:last-child{margin-bottom:0}.info-item[data-v-9d426492]{flex:1}.info-label[data-v-9d426492]{font-size:%?24?%;color:#999;margin-bottom:%?5?%;display:block}.info-value[data-v-9d426492]{font-size:%?28?%;color:#333}.status-active[data-v-9d426492]{color:#52c41a}.status-inactive[data-v-9d426492]{color:#ff4d4f}.department-footer[data-v-9d426492]{padding:%?15?% %?30?%;background-color:#f9fafc}.action-buttons[data-v-9d426492]{display:flex;justify-content:space-around}.action-btn[data-v-9d426492]{display:flex;flex-direction:column;align-items:center;padding:%?15?% 0;font-size:%?24?%;color:#666}.action-btn uni-text[data-v-9d426492]:first-child{font-size:%?40?%;margin-bottom:%?8?%}\n\n/* 弹窗样式 */.popup-mask[data-v-9d426492]{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.5);z-index:1000}.popup-container[data-v-9d426492]{position:fixed;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);width:80%;background-color:#fff;border-radius:%?20?%;overflow:hidden;z-index:1001}.popup-header[data-v-9d426492]{display:flex;justify-content:space-between;align-items:center;padding:%?30?%;border-bottom:%?1?% solid #eaeaea}.popup-title[data-v-9d426492]{font-size:%?34?%;font-weight:500;color:#333}.popup-close[data-v-9d426492]{font-size:%?40?%;color:#999;padding:%?10?%}.popup-body[data-v-9d426492]{max-height:70vh;overflow-y:auto;padding:%?20?% 0}.form-group[data-v-9d426492]{padding:%?15?% %?30?%}.form-label[data-v-9d426492]{display:block;font-size:%?28?%;color:#666;margin-bottom:%?10?%}.required[data-v-9d426492]:after{content:" *";color:#ff4d4f}.form-input[data-v-9d426492]{width:100%;height:%?80?%;border:%?1?% solid #dcdfe6;border-radius:%?8?%;padding:0 %?20?%;font-size:%?28?%;color:#333}.form-input.error[data-v-9d426492]{border-color:#ff4d4f}.form-textarea[data-v-9d426492]{width:100%;height:%?180?%;border:%?1?% solid #dcdfe6;border-radius:%?8?%;padding:%?20?%;font-size:%?28?%;color:#333}.error-message[data-v-9d426492]{display:block;font-size:%?24?%;color:#ff4d4f;margin-top:%?5?%}.form-picker[data-v-9d426492]{width:100%}.picker-value[data-v-9d426492]{display:flex;justify-content:space-between;align-items:center;height:%?80?%;border:%?1?% solid #dcdfe6;border-radius:%?8?%;padding:0 %?20?%;font-size:%?28?%;color:#333}.placeholder[data-v-9d426492]{color:#999}.picker-icon[data-v-9d426492]{font-size:%?32?%;color:#999}.popup-footer[data-v-9d426492]{display:flex;justify-content:space-between;padding:%?20?% %?30?%;border-top:%?1?% solid #eaeaea}.btn-cancel[data-v-9d426492]{width:48%;height:%?80?%;line-height:%?80?%;text-align:center;border-radius:%?8?%;color:#666;background-color:#f5f7fa;border:%?1?% solid #dcdfe6;font-size:%?28?%}.btn-submit[data-v-9d426492]{width:48%;height:%?80?%;line-height:%?80?%;text-align:center;border-radius:%?8?%;color:#fff;background-color:#4a6fff;font-size:%?28?%}',""]),t.exports=e},4733:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if(Array.isArray(t))return(0,i.default)(t)};var i=function(t){return t&&t.__esModule?t:{default:t}}(a("8d0b"))},"4eb4":function(t,e,a){var i=a("4092");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("9a891b36",i,!0,{sourceMap:!1,shadowMode:!1})},"5e6a":function(t,e,a){"use strict";a.r(e);var i=a("621e"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},"621e":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("9b1b")),o=i(a("b7c7"));a("8f71"),a("bf0f"),a("c223"),a("fd3c"),a("01a2"),a("e39c"),a("bd06"),a("aa9c");var s={data:function(){return{departments:[{id:"1",name:"销售部",code:"DEPT-001",manager:"王经理",createDate:"2020-01-10",employeeCount:20,parentDepartment:"",status:"active",icon:"ri-briefcase-4-line",color:"#4a6fff",description:"负责公司产品销售和客户关系管理"},{id:"2",name:"技术部",code:"DEPT-002",manager:"张经理",createDate:"2020-01-10",employeeCount:15,parentDepartment:"",status:"active",icon:"ri-terminal-box-line",color:"#00c48c",description:"负责公司产品开发和技术支持"},{id:"3",name:"市场部",code:"DEPT-003",manager:"李经理",createDate:"2020-02-15",employeeCount:10,parentDepartment:"",status:"active",icon:"ri-line-chart-line",color:"#ff9500",description:"负责公司品牌推广和市场活动策划"},{id:"4",name:"财务部",code:"DEPT-004",manager:"赵经理",createDate:"2020-01-10",employeeCount:8,parentDepartment:"",status:"active",icon:"ri-money-dollar-circle-line",color:"#ff4d4f",description:"负责公司财务管理和成本控制"},{id:"5",name:"人事部",code:"DEPT-005",manager:"刘经理",createDate:"2020-01-20",employeeCount:6,parentDepartment:"",status:"active",icon:"ri-user-settings-line",color:"#7870ff",description:"负责公司人员招聘、培训和人力资源管理"},{id:"6",name:"行政部",code:"DEPT-006",manager:"陈经理",createDate:"2020-03-05",employeeCount:5,parentDepartment:"",status:"inactive",icon:"ri-building-line",color:"#909399",description:"负责公司日常行政事务和后勤支持"}],totalEmployees:64,showPopup:!1,isEditing:!1,formData:{id:"",name:"",code:"",manager:"",parentDepartment:"",description:"",status:"active"},errors:{},managers:[{id:"1",name:"王经理"},{id:"2",name:"张经理"},{id:"3",name:"李经理"},{id:"4",name:"赵经理"},{id:"5",name:"刘经理"},{id:"6",name:"陈经理"}],parentDepartments:[]}},computed:{avgDeptSize:function(){var t=this.departments.filter((function(t){return"active"===t.status})).length;return t?Math.round(this.totalEmployees/t):0}},created:function(){this.updateParentDepartments()},methods:{goBack:function(){uni.navigateBack()},updateParentDepartments:function(){this.parentDepartments=[{id:"",name:"无上级部门"}].concat((0,o.default)(this.departments.map((function(t){return{id:t.id,name:t.name}}))))},viewDepartmentDetail:function(t){uni.navigateTo({url:"/pages/hr/department-detail?id=".concat(t)})},showAddDepartment:function(){this.isEditing=!1,this.formData={id:"",name:"",code:"",manager:"",parentDepartment:"",description:"",status:"active"},this.errors={},this.showPopup=!0},editDepartment:function(t){this.isEditing=!0,this.formData={id:t.id,name:t.name,code:t.code,manager:t.manager,parentDepartment:t.parentDepartment,description:t.description,status:t.status},this.errors={},this.showPopup=!0},closePopup:function(){this.showPopup=!1},onParentDepartmentChange:function(t){var e=t.detail.value;this.formData.parentDepartment=this.parentDepartments[e].name},onManagerChange:function(t){var e=t.detail.value;this.formData.manager=this.managers[e].name},validateForm:function(){this.errors={};var t=!0;return this.formData.name||(this.errors.name="请输入部门名称",t=!1),this.formData.code||(this.errors.code="请输入部门编号",t=!1),this.formData.manager||(this.errors.manager="请选择部门负责人",t=!1),t},saveDepartment:function(){var t=this;if(this.validateForm()){if(this.isEditing){var e=this.departments.findIndex((function(e){return e.id===t.formData.id}));if(-1!==e){var a=this.departments[e];this.departments[e]=(0,n.default)((0,n.default)({},a),{},{name:this.formData.name,code:this.formData.code,manager:this.formData.manager,parentDepartment:this.formData.parentDepartment,description:this.formData.description})}}else{var i={id:String(this.departments.length+1),name:this.formData.name,code:this.formData.code,manager:this.formData.manager,parentDepartment:this.formData.parentDepartment,description:this.formData.description,createDate:(new Date).toISOString().substr(0,10),employeeCount:0,status:"active",icon:"ri-building-line",color:this.getRandomColor()};this.departments.push(i)}this.updateParentDepartments(),this.closePopup(),uni.showToast({title:this.isEditing?"部门更新成功":"部门添加成功",icon:"success"})}},manageDepartmentMembers:function(t){uni.navigateTo({url:"/pages/hr/department-members?id=".concat(t)})},toggleDepartmentStatus:function(t,e){var a=this.departments.findIndex((function(e){return e.id===t.id}));-1!==a&&(this.departments[a].status=e,uni.showToast({title:"active"===e?"部门已启用":"部门已停用",icon:"success"}))},getRandomColor:function(){var t=["#4a6fff","#00c48c","#ff9500","#ff4d4f","#7870ff","#722ed1","#13c2c2","#52c41a"];return t[Math.floor(Math.random()*t.length)]}}};e.default=s},b7c7:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,i.default)(t)||(0,n.default)(t)||(0,o.default)(t)||(0,s.default)()};var i=r(a("4733")),n=r(a("d14d")),o=r(a("5d6b")),s=r(a("30f7"));function r(t){return t&&t.__esModule?t:{default:t}}},cf36:function(t,e,a){"use strict";a.r(e);var i=a("375e"),n=a("5e6a");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("330e");var s=a("828b"),r=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"9d426492",null,!1,i["a"],void 0);e["default"]=r.exports},d14d:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},a("01a2"),a("e39c"),a("bf0f"),a("844d"),a("18f7"),a("de6c"),a("08eb")}}]);
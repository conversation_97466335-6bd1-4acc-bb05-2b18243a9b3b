<template>
	<view class="container">
		<view class="header">
			<view class="back-button" @click="navigateBack">
				<svg-icon name="arrow-left" type="svg" size="24"></svg-icon>
			</view>
			<view class="title">菜单组件展示</view>
		</view>
		
		<view class="section">
			<view class="section-title">基础菜单</view>
			<view class="button" @click="openBasicMenu">
				<text>打开基础菜单</text>
			</view>
		</view>
		
		<view class="section">
			<view class="section-title">带图标菜单</view>
			<view class="button" @click="openIconMenu">
				<text>打开带图标菜单</text>
			</view>
		</view>
		
		<view class="section">
			<view class="section-title">危险操作菜单</view>
			<view class="button" @click="openDangerMenu">
				<text>打开危险操作菜单</text>
			</view>
		</view>
		
		<view class="section">
			<view class="section-title">无取消按钮菜单</view>
			<view class="button" @click="openNoCancel">
				<text>打开无取消按钮菜单</text>
			</view>
		</view>
		
		<uni-popup ref="basicPopup" type="bottom">
			<popup-menu @cancel="closePopup('basicPopup')">
				<menu-item text="选项 1" @click="handleMenuClick('选项 1')"></menu-item>
				<menu-item text="选项 2" @click="handleMenuClick('选项 2')"></menu-item>
				<menu-item text="选项 3" @click="handleMenuClick('选项 3')"></menu-item>
			</popup-menu>
		</uni-popup>
		
		<uni-popup ref="iconPopup" type="bottom">
			<popup-menu @cancel="closePopup('iconPopup')">
				<menu-item text="编辑" icon="edit" @click="handleMenuClick('编辑')"></menu-item>
				<menu-item text="分享" icon="share" iconColor="#3498db" @click="handleMenuClick('分享')"></menu-item>
				<menu-item text="收藏" icon="star" iconColor="#f1c40f" @click="handleMenuClick('收藏')"></menu-item>
			</popup-menu>
		</uni-popup>
		
		<uni-popup ref="dangerPopup" type="bottom">
			<popup-menu @cancel="closePopup('dangerPopup')">
				<menu-item text="编辑" icon="edit" @click="handleMenuClick('编辑')"></menu-item>
				<menu-item text="归档" icon="archive" @click="handleMenuClick('归档')"></menu-item>
				<menu-item text="删除" icon="delete" danger @click="handleMenuClick('删除')"></menu-item>
			</popup-menu>
		</uni-popup>
		
		<uni-popup ref="noCancelPopup" type="bottom">
			<popup-menu :showCancel="false">
				<menu-item text="选项 A" @click="handleMenuClickAndClose('选项 A', 'noCancelPopup')"></menu-item>
				<menu-item text="选项 B" @click="handleMenuClickAndClose('选项 B', 'noCancelPopup')"></menu-item>
				<menu-item text="关闭" icon="close" @click="closePopup('noCancelPopup')"></menu-item>
			</popup-menu>
		</uni-popup>
	</view>
</template>

<script>
import uniPopup from '@/components/uni-popup/uni-popup.vue'
import PopupMenu from '@/components/uni-popup/popup-menu.vue'
import MenuItem from '@/components/uni-popup/menu-item.vue'

export default {
	components: {
		uniPopup,
		PopupMenu,
		MenuItem
	},
	methods: {
		navigateBack() {
			uni.navigateBack()
		},
		openBasicMenu() {
			this.$refs.basicPopup.open()
		},
		openIconMenu() {
			this.$refs.iconPopup.open()
		},
		openDangerMenu() {
			this.$refs.dangerPopup.open()
		},
		openNoCancel() {
			this.$refs.noCancelPopup.open()
		},
		closePopup(ref) {
			this.$refs[ref].close()
		},
		handleMenuClick(option) {
			uni.showToast({
				title: `点击了: ${option}`,
				icon: 'none'
			})
		},
		handleMenuClickAndClose(option, ref) {
			this.handleMenuClick(option)
			this.closePopup(ref)
		}
	}
}
</script>

<style>
.container {
	padding: 20px;
}

.header {
	display: flex;
	align-items: center;
	margin-bottom: 30px;
}

.back-button {
	padding: 5px;
}

.title {
	font-size: 20px;
	font-weight: 600;
	margin-left: 10px;
}

.section {
	margin-bottom: 30px;
}

.section-title {
	font-size: 16px;
	font-weight: 500;
	margin-bottom: 15px;
	color: #333;
}

.button {
	background-color: #2979ff;
	padding: 12px 20px;
	border-radius: 8px;
	display: flex;
	justify-content: center;
	align-items: center;
}

.button text {
	color: #fff;
	font-size: 16px;
}
</style> 
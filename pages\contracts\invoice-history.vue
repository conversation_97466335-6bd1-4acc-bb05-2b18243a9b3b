<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="goBack">
        <text class="ri-arrow-left-line"></text>
      </view>
      <text class="page-title">发票历史</text>
      <view class="header-actions">
        <button type="button" class="action-button" @click="toggleFilter">
          <text class="ri-filter-3-line"></text>
        </button>
      </view>
    </view>
    
    <!-- 发票基本信息 -->
    <view class="invoice-info">
      <view class="invoice-header">
        <view class="invoice-number">发票编号：{{invoice.invoiceNumber}}</view>
        <view :class="['invoice-status', invoice.statusClass]">{{invoice.statusText}}</view>
      </view>
      <view class="invoice-meta">
        <view>客户：<text>{{invoice.customer}}</text></view>
        <view>金额：<text>¥{{formatMoney(invoice.amount)}}</text></view>
      </view>
    </view>
    
    <!-- 筛选栏 -->
    <scroll-view scroll-x class="filter-bar" v-if="showFilter">
      <view 
        v-for="(filter, index) in filters" 
        :key="index" 
        :class="['filter-button', currentFilter === filter.value ? 'active' : '']"
        @click="setFilter(filter.value)"
      >
        <text>{{filter.label}}</text>
      </view>
    </scroll-view>
    
    <!-- 时间线 -->
    <scroll-view scroll-y class="timeline-container">
      <view class="timeline">
        <view 
          v-for="(item, index) in filteredHistoryItems" 
          :key="index" 
          class="timeline-item"
          :data-type="item.type"
        >
          <view :class="['timeline-dot', 'dot-' + item.type]">
            <text :class="item.icon"></text>
          </view>
          <view class="timeline-content">
            <view class="timeline-header">
              <view class="timeline-title">{{item.title}}</view>
              <view class="timeline-time">{{item.time}}</view>
            </view>
            <view class="timeline-meta">
              操作人：<text>{{item.operator}}</text>
            </view>
            <view class="timeline-body">
              <text>{{item.description}}</text>
              <view class="change-list" v-if="item.changes && item.changes.length">
                <view 
                  v-for="(change, changeIndex) in item.changes" 
                  :key="changeIndex" 
                  class="change-item"
                >
                  <view class="change-field">{{change.field}}：</view>
                  <view class="change-detail">
                    <text v-if="change.oldValue" class="change-old">{{change.oldValue}}</text>
                    <text v-if="change.oldValue && change.newValue" class="change-arrow ri-arrow-right-line"></text>
                    <text v-if="change.newValue" class="change-new">{{change.newValue}}</text>
                    <text v-if="!change.oldValue && !change.newValue">{{change.description}}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view v-if="filteredHistoryItems.length === 0" class="empty-message">
          <text class="ri-file-list-3-line"></text>
          <text>没有符合条件的历史记录</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      showFilter: true,
      currentFilter: 'all',
      filters: [
        { label: '全部', value: 'all' },
        { label: '创建', value: 'create' },
        { label: '编辑', value: 'edit' },
        { label: '状态变更', value: 'status' },
        { label: '支付记录', value: 'payment' },
        { label: '备注', value: 'comment' }
      ],
      invoice: {
        id: '1',
        invoiceNumber: 'INV-2023-11-005',
        statusText: '待支付',
        statusClass: 'pending',
        customer: '上海智能科技',
        amount: 45200
      },
      historyItems: [
        {
          type: 'create',
          icon: 'ri-add-line',
          title: '创建发票',
          time: '2023-11-20 10:15',
          operator: '王经理',
          description: '系统集成项目的初始发票已创建。',
          changes: []
        },
        {
          type: 'edit',
          icon: 'ri-edit-line',
          title: '编辑发票',
          time: '2023-11-21 14:30',
          operator: '李财务',
          description: '发票信息已更新。',
          changes: [
            {
              field: '付款期限',
              oldValue: '2023-12-20',
              newValue: '2023-12-30'
            },
            {
              field: '明细条目',
              description: '添加了新条目「技术支持服务」，金额 ¥5,000.00'
            },
            {
              field: '总金额',
              oldValue: '¥39,550.00',
              newValue: '¥45,200.00'
            }
          ]
        },
        {
          type: 'status',
          icon: 'ri-refresh-line',
          title: '状态变更',
          time: '2023-11-22 09:45',
          operator: '系统',
          description: '发票已发送至客户邮箱：<EMAIL>',
          changes: [
            {
              field: '状态',
              oldValue: '草稿',
              newValue: '待支付'
            }
          ]
        },
        {
          type: 'payment',
          icon: 'ri-bank-card-line',
          title: '收到部分付款',
          time: '2023-11-29 16:20',
          operator: '李财务',
          description: '已收到客户首期付款。',
          changes: [
            {
              field: '收款金额',
              newValue: '¥20,000.00'
            },
            {
              field: '付款方式',
              description: '银行转账'
            },
            {
              field: '剩余金额',
              description: '¥25,200.00'
            }
          ]
        },
        {
          type: 'comment',
          icon: 'ri-chat-1-line',
          title: '添加备注',
          time: '2023-11-30 11:10',
          operator: '张销售',
          description: '客户表示将在下周五前支付剩余款项，请财务部门注意跟进。',
          changes: []
        }
      ]
    }
  },
  computed: {
    filteredHistoryItems() {
      if (this.currentFilter === 'all') {
        return this.historyItems;
      } else {
        return this.historyItems.filter(item => item.type === this.currentFilter);
      }
    }
  },
  onLoad(options) {
    if (options.id) {
      this.loadInvoiceHistory(options.id);
    }
  },
  methods: {
    formatMoney(amount) {
      return amount.toLocaleString('zh-CN');
    },
    goBack() {
      uni.navigateBack();
    },
    toggleFilter() {
      this.showFilter = !this.showFilter;
    },
    setFilter(filter) {
      this.currentFilter = filter;
    },
    loadInvoiceHistory(id) {
      // 实际应用中，这里应该从API获取发票历史记录
      console.log(`加载发票ID: ${id} 的历史记录`);
      // 本示例使用模拟数据，无需再次加载
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  background-color: #fff;
  z-index: 10;
}

.page-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.back-button {
  color: #666;
  font-size: 24px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #666;
  background-color: #f5f5f5;
  border: 1px solid #eee;
  padding: 0;
  margin: 0;
  font-size: 20px;
}

.invoice-info {
  padding: 16px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
}

.invoice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.invoice-number {
  font-weight: 600;
  font-size: 16px;
  color: #333;
}

.invoice-status {
  display: inline-block;
  padding: 3px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.invoice-status.pending {
  background-color: #fff5e6;
  color: #ff9a2a;
}

.invoice-meta {
  display: flex;
  justify-content: space-between;
  margin-top: 4px;
  font-size: 14px;
  color: #999;
}

.invoice-meta text {
  color: #333;
  font-weight: 500;
}

.filter-bar {
  display: flex;
  white-space: nowrap;
  padding: 16px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
}

.filter-button {
  display: inline-block;
  padding: 6px 12px;
  margin-right: 8px;
  border-radius: 20px;
  border: 1px solid #eee;
  background-color: #f5f5f5;
  color: #666;
  font-size: 14px;
  min-width: 70px;
  text-align: center;
}

.filter-button.active {
  background-color: rgba(58, 134, 255, 0.1);
  color: #3a86ff;
  border-color: #3a86ff;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.timeline-container {
  flex: 1;
  padding: 16px;
}

.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 10px;
  bottom: 0;
  left: 10px;
  width: 2px;
  background-color: #eee;
}

.timeline-item {
  position: relative;
  margin-bottom: 16px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: -30px;
  top: 10px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #fff;
  border: 2px solid #ddd;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.timeline-dot text {
  font-size: 10px;
  color: #999;
}

.timeline-content {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  border: 1px solid #eee;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.timeline-title {
  font-weight: 600;
  color: #333;
  font-size: 15px;
}

.timeline-time {
  color: #999;
  font-size: 13px;
}

.timeline-meta {
  font-size: 13px;
  color: #999;
  margin-bottom: 8px;
}

.timeline-meta text {
  color: #333;
  font-weight: 500;
}

.change-list {
  margin-top: 8px;
  border-top: 1px dashed #eee;
  padding-top: 8px;
}

.change-item {
  display: flex;
  margin-bottom: 4px;
  font-size: 14px;
  line-height: 1.5;
}

.change-field {
  flex: 0 0 90px;
  color: #999;
}

.change-detail {
  flex: 1;
}

.change-arrow {
  color: #999;
  margin: 0 4px;
}

.change-old {
  text-decoration: line-through;
  color: #f56c6c;
}

.change-new {
  color: #00b578;
}

.dot-create {
  background-color: #e6f7ee;
  border-color: #00b578;
}

.dot-create text {
  color: #00b578;
}

.dot-edit {
  background-color: #e6f4ff;
  border-color: #1890ff;
}

.dot-edit text {
  color: #1890ff;
}

.dot-status {
  background-color: #fff5e6;
  border-color: #ff9a2a;
}

.dot-status text {
  color: #ff9a2a;
}

.dot-payment {
  background-color: rgba(58, 134, 255, 0.1);
  border-color: #3a86ff;
}

.dot-payment text {
  color: #3a86ff;
}

.empty-message {
  text-align: center;
  padding: 32px 16px;
  color: #999;
  font-size: 15px;
  line-height: 1.5;
}

.empty-message text:first-child {
  font-size: 48px;
  color: #ddd;
  margin-bottom: 16px;
  display: block;
}
</style> 
<template>
  <view class="container">
    <uni-nav-bar
      left-icon="left"
      title="人力资源管理"
      right-text="图标"
      @clickLeft="goBack"
      @clickRight="goToIconDemo"
      fixed
    />

    <view class="content">
      <view class="overview-card">
        <view class="overview-header">
          <text class="overview-title">员工概览</text>
          <text class="overview-date">今日: {{ currentDate }}</text>
        </view>
        <view class="overview-stats">
          <view class="stat-item">
            <text class="stat-number">{{ stats.totalEmployees }}</text>
            <text class="stat-label">总员工</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ stats.attendanceRate }}%</text>
            <text class="stat-label">出勤率</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ stats.newEmployees }}</text>
            <text class="stat-label">本月新入职</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ stats.openPositions }}</text>
            <text class="stat-label">开放职位</text>
          </view>
        </view>
      </view>

      <view class="feature-grid">
        <view class="feature-item" v-for="(feature, index) in features" :key="index" @tap="navigateToFeature(feature.path)">
          <svg-icon :name="feature.iconName" type="svg" size="48" class="feature-icon"></svg-icon>
          <text class="feature-name">{{ feature.name }}</text>
        </view>
      </view>

      <view class="section">
        <view class="section-header">
          <text class="section-title">最近活动</text>
          <text class="section-more" @tap="navigateToActivity">更多</text>
        </view>
        <view class="activity-list">
          <view class="activity-item" v-for="(activity, index) in recentActivities" :key="index">
            <view class="activity-indicator" :style="{ backgroundColor: activity.color }"></view>
            <view class="activity-content">
              <text class="activity-title">{{ activity.title }}</text>
              <text class="activity-time">{{ activity.time }}</text>
            </view>
          </view>
        </view>
      </view>

      <view class="section">
        <view class="section-header">
          <text class="section-title">绩效排名</text>
          <text class="section-more" @tap="navigateToPerformance">更多</text>
        </view>
        <view class="performance-list">
          <view class="performance-item" v-for="(employee, index) in topPerformers" :key="index" @tap="navigateToEmployee(employee.id)">
            <text class="rank-number">{{ index + 1 }}</text>
            <image :src="employee.avatar" class="employee-avatar"></image>
            <view class="employee-info">
              <text class="employee-name">{{ employee.name }}</text>
              <text class="employee-position">{{ employee.department }} | {{ employee.position }}</text>
            </view>
            <view class="rating">
              <text class="rating-value">{{ employee.rating }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentDate: '',
      stats: {
        totalEmployees: 128,
        attendanceRate: 95.7,
        newEmployees: 5,
        openPositions: 3
      },
      features: [
        {
          name: '员工管理',
          iconName: 'employee',
          path: '/pages/hr/employee-list'
        },
        {
          name: '部门组织',
          iconName: 'department',
          path: '/pages/hr/department-management'
        },
        {
          name: '绩效考核',
          iconName: 'performance',
          path: '/pages/hr/employee-performance'
        },
        {
          name: '考勤记录',
          iconName: 'attendance',
          path: '/pages/hr/attendance-management'
        },
        {
          name: '薪资管理',
          iconName: 'payroll',
          path: '/pages/hr/payroll-management'
        },
        {
          name: '招聘管理',
          iconName: 'recruitment',
          path: '/pages/hr/recruitment-management'
        },
        {
          name: '培训发展',
          iconName: 'training',
          path: '/pages/hr/training-management'
        },
        {
          name: 'HR分析',
          iconName: 'analytics',
          path: '/pages/hr/hr-analytics'
        }
      ],
      recentActivities: [
        {
          title: '李明 已完成入职手续',
          time: '今天 10:30',
          color: '#4CD964'
        },
        {
          title: '市场部 3名员工绩效评估已完成',
          time: '今天 09:15',
          color: '#007AFF'
        },
        {
          title: '新员工培训计划已发布',
          time: '昨天 16:45',
          color: '#FF9500'
        },
        {
          title: '技术部门组织架构调整',
          time: '昨天 14:20',
          color: '#FF3B30'
        },
        {
          title: '5月份薪资已发放',
          time: '2天前',
          color: '#5856D6'
        }
      ],
      topPerformers: [
        {
          id: '1001',
          name: '张伟',
          department: '销售部',
          position: '销售经理',
          avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
          rating: '9.8'
        },
        {
          id: '1002',
          name: '王芳',
          department: '市场部',
          position: '市场总监',
          avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
          rating: '9.6'
        },
        {
          id: '1003',
          name: '李强',
          department: '技术部',
          position: '高级工程师',
          avatar: 'https://randomuser.me/api/portraits/men/59.jpg',
          rating: '9.5'
        },
        {
          id: '1004',
          name: '赵敏',
          department: '客服部',
          position: '客服主管',
          avatar: 'https://randomuser.me/api/portraits/women/17.jpg',
          rating: '9.4'
        },
        {
          id: '1005',
          name: '陈晓',
          department: '产品部',
          position: '产品经理',
          avatar: 'https://randomuser.me/api/portraits/men/4.jpg',
          rating: '9.3'
        }
      ]
    };
  },
  onLoad() {
    this.setCurrentDate();
  },
  methods: {
    setCurrentDate() {
      const date = new Date();
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      this.currentDate = `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`;
    },
    goBack() {
      uni.navigateBack();
    },
    goToIconDemo() {
      uni.navigateTo({
        url: '/pages/hr/icon-demo'
      });
    },
    navigateToFeature(path) {
      // Check if the path exists in the current pages configuration
      if (path === '/pages/hr/attendance-management' || 
          path === '/pages/hr/payroll-management' ||
          path === '/pages/hr/recruitment-management' ||
          path === '/pages/hr/training-management' ||
          path === '/pages/hr/hr-analytics') {
        uni.showToast({
          title: '功能开发中',
          icon: 'none'
        });
        return;
      }
      
      uni.navigateTo({
        url: path
      });
    },
    navigateToActivity() {
      uni.showToast({
        title: '活动记录功能开发中',
        icon: 'none'
      });
    },
    navigateToPerformance() {
      uni.navigateTo({
        url: '/pages/hr/employee-performance'
      });
    },
    navigateToEmployee(id) {
      uni.navigateTo({
        url: `/pages/hr/employee-detail?id=${id}`
      });
    }
  }
};
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.content {
  flex: 1;
  padding: 88rpx 30rpx 30rpx;
  overflow-y: auto;
}

.overview-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.overview-title {
  font-size: 32rpx;
  font-weight: 600;
}

.overview-date {
  font-size: 24rpx;
  color: #666666;
}

.overview-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #0A84FF;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666666;
}

.feature-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
  margin-bottom: 20rpx;
}

.feature-item {
  width: 25%;
  padding: 10rpx;
  box-sizing: border-box;
  margin-bottom: 16rpx;
}

.feature-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 8rpx;
  display: block;
  margin: 0 auto 8rpx;
}

.feature-name {
  font-size: 24rpx;
  color: #333333;
  text-align: center;
  display: block;
}

.section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
}

.section-more {
  font-size: 24rpx;
  color: #0A84FF;
}

.activity-list {
  border-radius: 8rpx;
  overflow: hidden;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-indicator {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 4rpx;
}

.activity-time {
  font-size: 24rpx;
  color: #999999;
}

.performance-list {
  border-radius: 8rpx;
  overflow: hidden;
}

.performance-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.performance-item:last-child {
  border-bottom: none;
}

.rank-number {
  width: 48rpx;
  height: 48rpx;
  background-color: #F2F2F7;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 16rpx;
}

.performance-item:first-child .rank-number {
  background-color: #FFD700;
  color: #FFFFFF;
}

.performance-item:nth-child(2) .rank-number {
  background-color: #C0C0C0;
  color: #FFFFFF;
}

.performance-item:nth-child(3) .rank-number {
  background-color: #CD7F32;
  color: #FFFFFF;
}

.employee-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.employee-info {
  flex: 1;
}

.employee-name {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 4rpx;
}

.employee-position {
  font-size: 24rpx;
  color: #999999;
}

.rating {
  background-color: #0A84FF;
  border-radius: 8rpx;
  padding: 6rpx 12rpx;
}

.rating-value {
  font-size: 24rpx;
  color: #FFFFFF;
  font-weight: bold;
}
</style> 
<template>
  <view class="container" :style="cssVars">
    <!-- 页面顶部Tabs -->
    <scroll-view class="tabs-container" scroll-x :show-scrollbar="false">
      <view
        v-for="(tab) in tabs"
        :key="tab.id"
        class="tab"
        :class="{ active: currentTab === tab.id }"
        @tap="changeTab(tab.id)"
      >
        {{ tab.displayText }}
      </view>
    </scroll-view>
    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <view class="search-icon">
          <svg-icon name="search" type="svg" size="32"></svg-icon>
        </view>
        <input type="text" class="search-input" v-model="searchKeyword" placeholder="搜索姓名、公司或联系方式等" @input="loadLeads(true)" />
      </view>
    </view>
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <button class="filter-button" @tap="showFilterPanel">
        <svg-icon name="filter" type="svg" size="28"></svg-icon>
        <text>筛选</text>
      </button>
      <button class="sort-button" @tap="showSortOptions">
        <svg-icon name="sort" type="svg" size="28"></svg-icon>
        <text>排序</text>
      </button>
    </view>
    <!-- 线索列表 -->
    <view class="leads-list">
      <!-- 加载中状态 -->
      <view class="empty-state" v-show="isLoading">
        <view class="loading-icon">
          <svg-icon name="loading" type="svg" size="64"></svg-icon>
        </view>
        <view class="empty-title">正在加载</view>
        <view class="empty-description">正在获取线索数据，请稍候...</view>
      </view>
      <!-- 加载错误状态 -->
      <view class="empty-state" v-show="loadError">
        <view class="empty-icon">
          <svg-icon name="error" type="svg" size="64"></svg-icon>
        </view>
        <view class="empty-title">加载失败</view>
        <view class="empty-description">获取线索数据失败，请检查网络连接后重试。</view>
        <button class="btn btn-primary" @tap="loadLeads">
          <svg-icon name="refresh" type="svg" size="32"></svg-icon> 重新加载
        </button>
      </view>
      <!-- 空状态提示 -->
      <view class="empty-state" v-show="leads.length === 0">
        <view class="empty-icon">
          <svg-icon name="lead" type="svg" size="96"></svg-icon>
        </view>
        <view class="empty-title">暂无线索</view>
        <view class="empty-description">您还没有创建任何线索，点击右下角的加号创建新线索。</view>
        <button class="btn btn-primary" @tap="navigateToCreate">
          <svg-icon name="add" type="svg" size="32"></svg-icon> 创建线索
        </button>
      </view>
      <!-- 线索卡片列表 -->
      <view v-if="!isLoading">
        <view
          class="lead-card"
          v-for="(lead) in leads"
          :key="lead.id"
          :data-status="lead.clueStatus === null ? 0 : lead.clueStatus.order"
        >
          <view class="card-overlay" @tap="viewLead(lead)"></view>
          <view class="lead-header" @tap="viewLead(lead)">
            <view class="lead-info">
              <view class="lead-name">{{ lead.name }}</view>
              <view class="lead-company">{{ lead.customName }}</view>
            </view>
            <view class="lead-tags">
              <text
                v-if="lead.clueStatusName"
                class="tag"
                :style="computedStatusTag(lead.clueStatus.order || 0)"
              >
                {{ lead.clueStatusName }}
              </text>
              <text class="tag tag-source">{{ lead.clueSourceName }}</text>
            </view>
          </view>
          <view class="lead-content">
            <view class="lead-details">
              <view class="detail-item">
                <view class="detail-label">创建日期:</view>
                <view class="detail-value">{{ lead.creationTime | formatDateFilter }}</view>
              </view>
              <view class="detail-item">
                <view class="detail-label">手机号:</view>
                <view class="detail-value">{{ lead.telephone | formatEmptyFilter }}</view>
              </view>
              <view class="detail-item">
                <view class="detail-label">邮箱:</view>
                <view class="detail-value">{{ lead.email | formatEmptyFilter }}</view>
              </view>
              <view class="detail-item">
                <view class="detail-label">职位:</view>
                <view class="detail-value">{{ lead.position | formatEmptyFilter }}</view>
              </view>
            </view>
            <view v-if="lead.remark" class="lead-description">
              {{ lead.remark }}
            </view>
          </view>
          <view class="lead-footer">
            <view class="lead-action" @tap="callLead(lead.telephone)">
              <svg-icon name="phone" type="svg" size="28"></svg-icon> 电话
            </view>
            <!--          <view class="lead-action" @tap="mailLead(lead)">
                        <svg-icon name="mail" type="svg" size="28"></svg-icon> 邮件
                      </view>-->
            <view class="lead-action" @tap="editLead(lead)">
              <svg-icon name="edit" type="svg" size="28"></svg-icon> 编辑
            </view>
<!--            <view class="lead-action" @tap="convertToCustomer(lead)">
              <svg-icon name="convert" type="svg" size="28"></svg-icon> 转客户
            </view>-->
          </view>
        </view>
      </view>
    </view>
    <!-- 浮动添加按钮 -->
    <view class="fab" @tap="navigateToCreate">
      <svg-icon name="add" type="svg" size="60" color="#FFFFFF"></svg-icon>
    </view>
    <!-- 筛选面板 -->
    <view class="modal-filter" v-if="showFilter">
      <view class="modal-mask" @tap="hideFilterPanel"></view>
      <view class="modal-dialog">
        <view class="modal-header">
          <view class="modal-title">筛选条件</view>
          <view class="modal-close" @tap="hideFilterPanel">
            <svg-icon name="close" type="svg" size="32"></svg-icon>
          </view>
        </view>
        <view class="modal-content">
          <!-- 筛选来源 -->
          <view class="filter-group">
            <text class="filter-label">来源</text>
            <view class="picker-wrapper" @tap="showSourcePicker">
              <view class="picker-value">{{ filterOptions.clueSourceName || '请选择来源' }}</view>
              <view class="picker-arrow">
                <svg-icon name="arrow-down" type="svg" size="24"></svg-icon>
              </view>
            </view>
          </view>
          <!-- 筛选时间 -->
          <view class="filter-group">
            <text class="filter-label">创建日期</text>
            <view class="date-range">
              <view class="date-picker" @tap="showStartDatePicker">
                <view class="date-value">{{ filterOptions.creationTimeStart || '开始日期' }}</view>
                <view class="date-icon">
                  <svg-icon name="calendar" type="svg" size="24"></svg-icon>
                </view>
              </view>
              <view class="date-separator">至</view>
              <view class="date-picker" @tap="showEndDatePicker">
                <view class="date-value">{{ filterOptions.creationTimeEnd || '结束日期' }}</view>
                <view class="date-icon">
                  <svg-icon name="calendar" type="svg" size="24"></svg-icon>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="modal-footer">
          <button class="btn btn-reset" @tap="resetFilter">重置</button>
          <button class="btn btn-confirm" @tap="applyFilter">应用</button>
        </view>
      </view>
    </view>

    <!-- 来源选择器弹窗 -->
    <view class="uni-picker-popup" v-if="showSourcePickerPopup">
      <view class="picker-mask" @tap="showSourcePickerPopup = false"></view>
      <view class="picker-content">
        <view class="picker-header">
          <view class="picker-action" @tap="showSourcePickerPopup = false">取消</view>
          <view class="picker-title">选择来源</view>
          <view class="picker-action confirm" @tap="confirmSourcePicker">确定</view>
        </view>
        <picker-view class="picker-view" :value="sourceIndex" @change="onSourcePickerChange">
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in sources" :key="index">{{item.displayText}}</view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>
    <!-- 日期选择器弹窗 -->
    <view class="uni-picker-popup" v-if="showDatePickerPopup">
      <view class="picker-mask" @tap="showDatePickerPopup = false"></view>
      <view class="picker-content">
        <view class="picker-header">
          <view class="picker-action" @tap="showDatePickerPopup = false">取消</view>
          <view class="picker-title">选择日期</view>
          <view class="picker-action confirm" @tap="confirmDatePicker">确定</view>
        </view>
        <picker-view class="picker-view" :value="datePickerValue" @change="onDatePickerChange">
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in years" :key="index">{{item}}年</view>
          </picker-view-column>
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in months" :key="index">{{item}}月</view>
          </picker-view-column>
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in days" :key="index">{{item}}日</view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>
    <!-- 遮罩层 -->
    <view class="backdrop" :class="{ active: showFilter }" @tap="showFilter = false"></view>
    <!-- 自定义TabBar组件 -->
    <custom-tab-bar ref="customTabBar"></custom-tab-bar>
  </view>
</template>

<script>
import SvgIcon from '@/components/svg-icon.vue'
import CustomTabBar from '@/components/CustomTabBar.vue';
import getSelectOptions from '@/utils/dictionary';
import { getClueList } from '@/api/clue.api';

export default {
  components: {
    SvgIcon,
    CustomTabBar
  },
  computed: {
    // CSS变量计算值
    cssVars() {
      // 将主题色转换为RGB以便在rgba()中使用
      const primaryColor = '#0057ff'; // 假设的主色值
      const primaryColorRGB = this.hexToRgb(primaryColor);
      // 其他主题色变体
      const primaryColorLight = '#3a80ff';
      const primaryColorDark = '#0046cc';
      return {
        '--primary-color-rgb': primaryColorRGB,
        '--primary-color-light': primaryColorLight,
        '--primary-color-dark': primaryColorDark,
        '--light-color-rgb': '245, 247, 250'
      };
    },
    computedStatusTag() {
      let backgroundArr = ['#e0f2fe', '#dbeafe', '#d1fae5', '#e5e7eb'];
      let colorArr = ['#0284c7', '#2563eb', '#059669', '#6b7280'];
      return (order) => {
        return {
          backgroundColor: backgroundArr[order],
          color: colorArr[order]
        }
      }
    }
  },
  data() {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;
    const currentDay = currentDate.getDate();
    const years = [];
    const months = [];
    const days = [];
    for (let i = currentYear - 10; i <= currentYear + 10; i++) {
      years.push(i);
    }
    for (let i = 1; i <= 12; i++) {
      months.push(i);
    }
    for (let i = 1; i <= 31; i++) {
      days.push(i);
    }
    return {
      // 线索数据
      leads: [],
      pageIndex: 1,
      pageSize: 10,
      total: 0,
      isLoading: false,
      loadError: false,
      noMore: false,
      // 线索状态选项卡数据
      tabs: [],
      currentTab: undefined,
      searchKeyword: '',
      showFilter: false,
      // 筛选选项
      filterOptions: {
        clueSourceId: '',
        clueSourceName: '',
        creationTimeStart: '',
        creationTimeEnd: '',
      },
      // 筛选选项数据源
      sources: [],
      // Picker相关状态
      showSourcePickerPopup: false,
      showDatePickerPopup: false,
      sourceIndex: [0],
      currentPickerSource: '',
      datePickerValue: [10, currentMonth - 1, currentDay - 1], // 默认当前日期
      tempDatePickerValue: [10, currentMonth - 1, currentDay - 1],
      currentDateType: '', // 'start' 或 'end'
      // 日期选择器数据
      years,
      months,
      days,
    }
  },
  methods: {
    // 将十六进制颜色转换为RGB
    hexToRgb(hex) {
      // 移除#前缀如果存在
      hex = hex.replace(/^#/, '');
      // 解析十六进制
      let bigint = parseInt(hex, 16);
      let r = (bigint >> 16) & 255;
      let g = (bigint >> 8) & 255;
      let b = bigint & 255;
      return `${r}, ${g}, ${b}`;
    },
    // 获取数据字典
    async loadDictionaryOptions() {
      try {
        this.tabs = await getSelectOptions('clueStatus');
        this.tabs.unshift({
          displayText: '全部线索',
          id: undefined,
        })
        this.sources = await getSelectOptions('clueSource');
      } catch (error) {
        this.$message.error('加载字典数据失败');
      }
    },
    // 加载线索数据
    async loadLeads(isRefresh = false) {
      if (this.isLoading && !isRefresh) return;
      this.isLoading = true;
      this.loadError = false;
      try {
        const params = {
          pageIndex: isRefresh ? 1 : this.pageIndex,
          pageSize: this.pageSize,
          filter: {
            clueStatusId: this.currentTab,
            likeString: this.searchKeyword,
            ...this.filterOptions
          }
        };
        const res = await getClueList(params);
        if (isRefresh) {
          this.leads = res.items;
          this.pageIndex = 1;
        } else {
          this.leads = [...this.leads, ...res.items];
        }
        this.total = res.totalCount;
        this.noMore = this.leads.length >= this.total;
        // 如果是刷新，停止刷新动画
        if (isRefresh) {
          uni.stopPullDownRefresh();
        }
      } catch (error) {
        console.error("加载线索数据失败", error);
        this.loadError = true;
      } finally {
        this.isLoading = false;
      }
    },
    // 加载更多数据
    loadMore() {
      if (this.noMore || this.isLoading) return;
      this.pageIndex++;
      this.loadLeads();
    },
    // 切换标签页
    changeTab(statusId) {
      this.currentTab = statusId;
      this.loadLeads(true);
    },
    // 显示排序选项
    showSortOptions() {
      uni.showActionSheet({
        itemList: [ '姓名', '创建时间'],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.filterOptions.sortProperty = 'Name';
          } else if (res.tapIndex === 1) {
            this.filterOptions.sortProperty = 'CreationTime';
          }
          this.filterOptions.sortAsc = false;
          this.loadLeads(true);
        }
      })
    },
    // 导航到创建线索页面
    navigateToCreate() {
      uni.navigateTo({
        url: '/pages/marketing/lead-create'
      })
    },
    callLead(telephone) {
      if (telephone) {
        uni.makePhoneCall({
          phoneNumber: telephone,
          fail: () => {
            uni.showToast({
              title: "拨打电话失败",
              icon: "none",
            });
          },
        });
      } else {
        uni.showToast({
          title: "无电话号码",
          icon: "none",
        });
      }
    },
    viewLead(lead) {
      uni.navigateTo({
        url: `/pages/marketing/lead-detail?id=${lead.id}`
      })
    },
    editLead(lead) {
      uni.navigateTo({
        url: `/pages/marketing/lead-edit?id=${lead.id}`
      })
    },
    convertToCustomer(lead) {
      uni.showToast({
        title: '转为客户功能开发中...',
        icon: 'none'
      })
    },
    // 显示/隐藏筛选面板
    showFilterPanel() {
      this.showFilter = true;
      document.body.style.overflow = "hidden";
    },
    hideFilterPanel() {
      this.showFilter = false;
      document.body.style.overflow = "";
    },
    // 重置筛选
    resetFilter() {
      this.hideFilterPanel();
      this.filterOptions = {
        clueSourceId: '',
        clueSourceName: '',
        creationTimeStart: '',
        creationTimeEnd: '',
      }
      uni.showToast({
        title: '筛选条件已重置',
        icon: "none",
      });
      this.loadLeads(true);
    },
    // 应用筛选
    applyFilter() {
      this.hideFilterPanel();
      this.loadLeads(true);
    },
    // 显示来源选择器
    showSourcePicker() {
      const sourceIndex = this.filterOptions.clueSourceId ? this.sources.findIndex(item => item.id === this.filterOptions.clueSourceId) : 0;
      this.sourceIndex = [sourceIndex >= 0 ? sourceIndex : 0];
      this.currentPickerSource = this.sources[this.sourceIndex[0]];
      this.showSourcePickerPopup = true;
    },
    // 来源选择变化
    onSourcePickerChange(e) {
      const index = e.detail.value[0];
      this.currentPickerSource = this.sources[index];
    },
    // 确认来源选择
    confirmSourcePicker() {
      this.filterOptions.clueSourceId = this.currentPickerSource.id;
      this.filterOptions.clueSourceName = this.currentPickerSource.displayText;
      this.showSourcePickerPopup = false;
    },
    // 显示开始日期选择器
    showStartDatePicker() {
      this.currentDateType = 'start';
      if (this.filterOptions.creationTimeStart) {
        const [year, month, day] = this.filterOptions.creationTimeStart.split('-').map(Number);
        this.datePickerValue = [
          this.years.indexOf(year), 
          this.months.indexOf(month), 
          this.days.indexOf(day)
        ];
      }
      this.tempDatePickerValue = [...this.datePickerValue];
      this.showDatePickerPopup = true;
    },
    // 显示结束日期选择器
    showEndDatePicker() {
      this.currentDateType = 'end';
      if (this.filterOptions.creationTimeEnd) {
        const [year, month, day] = this.filterOptions.creationTimeEnd.split('-').map(Number);
        this.datePickerValue = [
          this.years.indexOf(year), 
          this.months.indexOf(month), 
          this.days.indexOf(day)
        ];
      }
      this.tempDatePickerValue = [...this.datePickerValue];
      this.showDatePickerPopup = true;
    },
    // 确认日期选择
    confirmDatePicker() {
      const year = this.years[this.tempDatePickerValue[0]];
      const month = this.months[this.tempDatePickerValue[1]];
      const day = this.days[this.tempDatePickerValue[2]];
      const formattedDate = `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`;
      if (this.currentDateType === 'start') {
        this.filterOptions.creationTimeStart = formattedDate;
      } else {
        this.filterOptions.creationTimeEnd = formattedDate;
      }
      this.showDatePickerPopup = false;
    },
    // 日期选择变化
    onDatePickerChange(e) {
      this.tempDatePickerValue = e.detail.value;
    }
  },
  onLoad() {
    this.loadDictionaryOptions();
    // 初始化数据和加载线索列表
    this.loadLeads(true);
  },
  onShow() {
    this.loadLeads(true);
    // 设置TabBar选中项
    if (typeof this.$refs.customTabBar !== 'undefined') {
      this.$refs.customTabBar.current = 3;
    } else {
      // 如果refs还没准备好，尝试延迟设置
      setTimeout(() => {
        if (typeof this.$refs.customTabBar !== 'undefined') {
          this.$refs.customTabBar.current = 3;
          console.log('线索页面设置TabBar当前项为3');
        }
      }, 300);
    }
  },
  // 下拉刷新
  onPullDownRefresh() {
    this.loadLeads(true);
  },
  onReachBottom() {
    this.loadMore();
  },
}
</script>

<style lang="scss">
.container {
  background-color: #f8fafc;
  min-height: 100vh;
}

.tabs-container {
  display: flex;
  white-space: nowrap;
  background-color: #ffffff;
  border-bottom: 1rpx solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.tab {
  display: inline-block;
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--text-secondary);
  font-size: 28rpx;
  position: relative;
  transition: all 0.2s ease;
}

.tab.active {
  color: var(--primary-color);
  font-weight: 600;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: var(--spacing-lg);
  right: var(--spacing-lg);
  height: 4rpx;
  background-color: var(--primary-color);
  border-radius: var(--radius-full);
}

/* 搜索栏样式 */
.search-container {
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: #ffffff;
  border-bottom: 1rpx solid var(--border-color);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
}

.search-box {
  display: flex;
  align-items: center;
  background-color: var(--light-color);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 0 var(--spacing-sm);
  overflow: hidden;
  box-shadow: inset 0 2rpx 5rpx rgba(0, 0, 0, 0.03);
}

.search-icon {
  color: var(--text-secondary);
  padding: var(--spacing-xs);
}

.search-input {
  flex: 1;
  border: none;
  padding: var(--spacing-sm);
  background-color: transparent;
  color: var(--text-primary);
  font-size: 28rpx;
}

/* 筛选栏样式 */
.filter-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: #ffffff;
  border-bottom: 1rpx solid var(--border-color);
}

.filter-button, .sort-button {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: var(--text-secondary);
  padding: 16rpx 24rpx;
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--light-color);
  transition: all 0.2s ease;
}

.filter-button:active, .sort-button:active {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  border-color: var(--primary-color-light);
}

.filter-button text, .sort-button text {
  margin-left: 8rpx;
}

/* 线索列表和卡片样式 */
.leads-list {
  padding: var(--spacing-md) 0;
  padding-bottom: calc(var(--spacing-xl) * 1.5); /* 减少底部填充空间，原来是4倍 */
}

.lead-card {
  background-color: #ffffff;
  border-radius: var(--radius-md);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  margin-bottom: var(--spacing-md);
  overflow: hidden;
  border: 1rpx solid var(--border-color);
  position: relative;
  transition: all 0.3s ease;
  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  }
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 6rpx;
    background-color: var(--border-color);
    z-index: 2;
  }
  &[data-status="1"]::before {
    background-color: #0ea5e9;
  }
  &[data-status="2"]::before {
    background-color: #3b82f6;
  }
  &[data-status="3"]::before {
    background-color: #9ca3af;
  }
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.lead-header, .lead-content, .lead-footer {
  position: relative;
  z-index: 2;
}

/* 确保所有可点击元素在overlay之上 */
.lead-name, .lead-action {
  position: relative;
  z-index: 3;
}

.lead-header {
  padding: var(--spacing-md);
  border-bottom: 1rpx solid var(--border-color-light);
  background: linear-gradient(to right, rgba(249, 250, 251, 0.5), rgba(255, 255, 255, 0.8));
  .lead-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    .lead-name {
      font-size: 32rpx;
      font-weight: 600;
      margin: 0 0 12rpx 0;
      color: var(--text-primary);
      cursor: pointer;
      position: relative;
      display: inline-block;
      padding: 4rpx 0;
    }
    .lead-name:hover, .lead-name:active {
      color: var(--primary-color);
    }
    .lead-name:active:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 2rpx;
      background-color: var(--primary-color);
    }
    .lead-company {
      max-width: 70%;
      font-weight: 700;
      color: var(--primary-color);
      font-size: 28rpx;
      background-color: rgba(var(--primary-color-rgb), 0.1);
      padding: 8rpx 16rpx;
      border-radius: var(--radius-md);
      text-align: right;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .lead-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8rpx;
    margin-top: var(--spacing-xs);
    .tag {
      padding: 4rpx 16rpx;
      border-radius: var(--radius-full);
      font-size: 22rpx;
      font-weight: 500;
    }
    .tag-source {
      background-color: #f3f4f6;
      color: #4b5563;
    }
  }
}

.lead-content {
  padding: var(--spacing-md);
  .lead-details {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    background-color: rgba(var(--light-color-rgb), 0.3);
    padding: var(--spacing-md) var(--spacing-sm);
    border-radius: var(--radius-md);
    .detail-item {
      min-width: 0; /* 重要：防止内容溢出 */
      display: flex;
      align-items: baseline;
      margin-bottom: var(--spacing-xs);
      .detail-label {
        font-size: 24rpx;
        color: var(--text-tertiary);
        margin-right: 8rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .detail-value {
        font-size: 26rpx;
        color: var(--text-secondary);
        font-weight: 500;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .lead-description {
    margin-top: var(--spacing-md);
    font-size: 26rpx;
    color: var(--text-secondary);
    line-height: 1.5;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    background-color: rgba(var(--light-color-rgb), 0.5);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    word-break: break-all;          /* 允许单词换行（英文适用） */
  }
}

.lead-footer {
  display: flex;
  border-top: 1rpx solid var(--border-color);
  background: linear-gradient(to bottom, #ffffff, #f9fafb);
  .lead-action {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24rpx 0;
    color: var(--text-secondary);
    font-size: 26rpx;
    transition: all 0.2s ease;
  }
  .lead-action:active {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    color: var(--primary-color);
  }
  .lead-action:not(:last-child) {
    border-right: 1rpx solid var(--border-color-light);
  }
}

/* 浮动操作按钮 */
.fab {
  position: fixed;
  bottom: calc(128rpx + var(--spacing-xl)); /* 调整底部位置，避开TabBar */
  right: var(--spacing-xl);
  width: 110rpx; /* 减小尺寸 */
  height: 110rpx; /* 减小尺寸 */
  border-radius: 50%;
  background: linear-gradient(135deg, #0a6bff, #0057ff);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6), 0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  z-index: 100;
  transition: all 0.3s ease;
  border: 4rpx solid rgba(255, 255, 255, 0.7);
  animation: pulse 2s infinite; /* 添加脉动动画 */
}

.fab:active {
  transform: scale(0.95);
  box-shadow: 0 5rpx 10rpx rgba(0, 87, 255, 0.5), 0 3rpx 3rpx rgba(0, 87, 255, 0.3);
  animation: none; /* 点击时停止动画 */
}

/* 添加脉动动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6), 0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 15rpx 25rpx rgba(0, 87, 255, 0.7), 0 8rpx 10rpx rgba(0, 87, 255, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6), 0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  }
}

/* 筛选面板样式 */
.modal-filter {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1001;
}

.modal-mask {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-dialog {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.modal-header {
  padding: 24rpx;
  border-bottom: 1rpx solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: var(--text-secondary);
}

.modal-content {
  padding: 24rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 24rpx;
  border-top: 1rpx solid var(--border-color);
  display: flex;
  justify-content: space-between;
}

.filter-group {
  margin-bottom: 36rpx;
}

.filter-label {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
  display: block;
}

.checkbox-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.status-option {
  padding: 12rpx 24rpx;
  border-radius: 100rpx;
  font-size: 26rpx;
  background-color: #f5f7fa;
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.status-option.active {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  font-weight: 500;
}

.picker-wrapper {
  padding: 20rpx 24rpx;
  background-color: #f5f7fa;
  border-radius: 12rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.picker-value {
  font-size: 28rpx;
  color: var(--text-primary);
}

.date-range {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.date-picker {
  flex: 1;
  padding: 20rpx 24rpx;
  background-color: #f5f7fa;
  border-radius: 12rpx;
  position: relative;
}

.date-value {
  font-size: 28rpx;
  color: var(--text-primary);
  padding-right: 40rpx;
}

.date-icon, .picker-arrow {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

.date-separator {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.btn {
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  flex: 1;
}

.btn-reset {
  background-color: #f5f7fa;
  color: var(--text-secondary);
  margin-right: 16rpx;
}

.btn-confirm {
  background-color: var(--primary-color);
  color: #ffffff;
}

/* 选择器弹窗样式 */
.uni-picker-popup {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1002;
}

.picker-mask {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.picker-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  animation: slideUp 0.3s ease;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.picker-action {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.picker-action.confirm {
  color: var(--primary-color);
  font-weight: 500;
}

.picker-title {
  font-size: 30rpx;
  font-weight: 500;
  color: var(--text-primary);
}

.picker-view {
  height: 400rpx;
  width: 100%;
}

.picker-item {
  line-height: 100rpx;
  text-align: center;
  font-size: 28rpx;
  color: var(--text-primary);
}

/* 遮罩层 */
.backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease;
  &.active {
    opacity: 1;
    visibility: visible;
  }
}

/* 空状态样式 */
.empty-state {
  padding: var(--spacing-xl) var(--spacing-lg);
  text-align: center;
  background-color: #ffffff;
  border-radius: var(--radius-lg);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  margin: var(--spacing-lg) 0;
}

.empty-icon, .loading-icon {
  margin-bottom: var(--spacing-md);
  color: var(--border-color);
  display: inline-block;
}

.loading-icon {
  animation: rotating 2s linear infinite;
  color: var(--primary-color);
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.empty-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: 1.5;
}
</style> 
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-interactions-interaction-detail"],{"3a43":function(t,e,i){var a=i("b13d");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("a980724c",a,!0,{sourceMap:!1,shadowMode:!1})},8242:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={svgIcon:i("8a0f").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"page"},[i("v-uni-view",{staticClass:"page-header"},[i("v-uni-view",{staticClass:"back-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.navBack.apply(void 0,arguments)}}},[i("svg-icon",{attrs:{name:"arrow-left",type:"svg",size:"24"}})],1),i("v-uni-text",{staticClass:"page-title"},[t._v("沟通记录详情")]),i("v-uni-view",{staticClass:"header-actions"},[i("v-uni-view",{staticClass:"header-icon",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showMoreActions.apply(void 0,arguments)}}},[i("svg-icon",{attrs:{name:"more",type:"svg",size:"28"}})],1)],1)],1),i("v-uni-scroll-view",{staticClass:"page-container",attrs:{"scroll-y":!0}},[i("v-uni-view",{staticClass:"info-section"},[i("v-uni-view",{staticClass:"info-header"},[i("v-uni-view",{staticClass:"record-type-icon",class:t.recordData.type},[i("svg-icon",{attrs:{name:t.getTypeIconName(t.recordData),type:"svg",size:"28"}})],1),i("v-uni-view",{staticClass:"info-title"},[i("v-uni-text",{staticClass:"record-title"},[t._v(t._s(t.recordData.subject))]),i("v-uni-text",{staticClass:"record-time"},[t._v(t._s(t.recordData.date)+" "+t._s(t.recordData.time))])],1)],1)],1),t.recordData.related?i("v-uni-view",{staticClass:"info-section"},[i("v-uni-view",{staticClass:"section-header"},[i("svg-icon",{attrs:{name:"link",type:"svg",size:"20"}}),i("v-uni-text",{staticClass:"section-title"},[t._v("关联信息")])],1),i("v-uni-view",{staticClass:"section-content"},[i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("关联对象")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.recordData.related))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("关联类型")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.getRelatedTypeName(t.recordData.relatedType)))])],1)],1)],1):t._e(),t.recordData.contacts?i("v-uni-view",{staticClass:"info-section"},[i("v-uni-view",{staticClass:"section-header"},[i("svg-icon",{attrs:{name:"user",type:"svg",size:"20"}}),i("v-uni-text",{staticClass:"section-title"},[t._v("联系人")])],1),i("v-uni-view",{staticClass:"section-content"},[i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("联系人")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.recordData.contacts))])],1)],1)],1):t._e(),t.recordData.content?i("v-uni-view",{staticClass:"info-section"},[i("v-uni-view",{staticClass:"section-header"},[i("svg-icon",{attrs:{name:"align-left",type:"svg",size:"20"}}),i("v-uni-text",{staticClass:"section-title"},[t._v("详情描述")])],1),i("v-uni-view",{staticClass:"section-content"},[i("v-uni-text",{staticClass:"description-text"},[t._v(t._s(t.recordData.content))])],1)],1):t._e(),"follow-up"===t.recordData.recordType&&t.recordData.nextSteps?i("v-uni-view",{staticClass:"info-section"},[i("v-uni-view",{staticClass:"section-header"},[i("svg-icon",{attrs:{name:"check-circle",type:"svg",size:"20"}}),i("v-uni-text",{staticClass:"section-title"},[t._v("下一步计划")])],1),i("v-uni-view",{staticClass:"section-content"},[i("v-uni-view",{staticClass:"next-steps-list"},t._l(t.recordData.nextSteps,(function(e,a){return i("v-uni-view",{key:a,staticClass:"next-step-item"},[i("v-uni-view",{staticClass:"step-content"},[t._v(t._s(e))])],1)})),1)],1)],1):t._e(),t.recordData.tags&&t.recordData.tags.length?i("v-uni-view",{staticClass:"info-section"},[i("v-uni-view",{staticClass:"section-header"},[i("svg-icon",{attrs:{name:"tag",type:"svg",size:"20"}}),i("v-uni-text",{staticClass:"section-title"},[t._v("标签")])],1),i("v-uni-view",{staticClass:"section-content"},[i("v-uni-view",{staticClass:"tag-list"},t._l(t.recordData.tags,(function(e,a){return i("v-uni-view",{key:a,staticClass:"tag-item"},[i("v-uni-text",[t._v(t._s(e))])],1)})),1)],1)],1):t._e(),i("v-uni-view",{staticClass:"info-section"},[i("v-uni-view",{staticClass:"section-header"},[i("svg-icon",{attrs:{name:"history",type:"svg",size:"20"}}),i("v-uni-text",{staticClass:"section-title"},[t._v("操作记录")])],1),i("v-uni-view",{staticClass:"section-content"},[i("v-uni-view",{staticClass:"history-list"},t._l(t.recordData.history,(function(e,a){return i("v-uni-view",{key:a,staticClass:"history-item"},[i("v-uni-view",{staticClass:"history-time"},[t._v(t._s(e.time))]),i("v-uni-view",{staticClass:"history-content"},[t._v(t._s(e.content))])],1)})),1)],1)],1)],1),i("v-uni-view",{staticClass:"action-bar"},[i("v-uni-button",{staticClass:"btn btn-outline",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.editRecord.apply(void 0,arguments)}}},[t._v("编辑")]),"communication"===t.recordData.recordType?i("v-uni-button",{staticClass:"btn btn-primary",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.createFollowUp.apply(void 0,arguments)}}},[t._v("创建跟进")]):t._e()],1)],1)},o=[]},"8bcc":function(t,e,i){"use strict";i.r(e);var a=i("c5e3"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},b13d:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,".page[data-v-512b6e76]{display:flex;flex-direction:column;height:100vh;background-color:#f5f7fa}.page-header[data-v-512b6e76]{display:flex;align-items:center;justify-content:space-between;padding:%?30?% %?40?%;border-bottom:%?1?% solid #e0e0e0;background-color:#fff;position:-webkit-sticky;position:sticky;top:0;z-index:10}.back-button[data-v-512b6e76]{padding:%?10?%}.page-title[data-v-512b6e76]{font-size:%?36?%;font-weight:700;color:#333}.header-actions[data-v-512b6e76]{display:flex;gap:%?30?%}.header-icon[data-v-512b6e76]{color:#666;font-size:%?40?%;display:flex;align-items:center;justify-content:center}.page-container[data-v-512b6e76]{flex:1;padding:%?30?%}.info-section[data-v-512b6e76]{background-color:#fff;border-radius:%?16?%;padding:%?30?%;margin-bottom:%?30?%;box-shadow:0 %?2?% %?10?% rgba(0,0,0,.05)}.info-header[data-v-512b6e76]{display:flex;align-items:center;margin-bottom:%?20?%}.record-type-icon[data-v-512b6e76]{width:%?80?%;height:%?80?%;display:flex;justify-content:center;align-items:center;border-radius:50%;margin-right:%?20?%;color:#fff}.record-type-icon.call[data-v-512b6e76]{background-color:#52c41a}.record-type-icon.meeting[data-v-512b6e76]{background-color:#1890ff}.record-type-icon.email[data-v-512b6e76]{background-color:#722ed1}.record-type-icon.visit[data-v-512b6e76]{background-color:#fa8c16}.record-type-icon.other[data-v-512b6e76]{background-color:#bfbfbf}.info-title[data-v-512b6e76]{flex:1}.record-title[data-v-512b6e76]{font-size:%?32?%;font-weight:500;color:#333;display:block;margin-bottom:%?8?%}.record-time[data-v-512b6e76]{font-size:%?24?%;color:#999}.section-header[data-v-512b6e76]{display:flex;align-items:center;margin-bottom:%?20?%}.section-title[data-v-512b6e76]{font-size:%?30?%;font-weight:700;margin-left:%?10?%;color:#333}.section-content[data-v-512b6e76]{padding:%?10?%}.info-item[data-v-512b6e76]{display:flex;justify-content:space-between;align-items:center;padding:%?20?% 0;border-bottom:%?1?% solid #f0f0f0}.info-item[data-v-512b6e76]:last-child{border-bottom:none}.info-label[data-v-512b6e76]{font-size:%?28?%;color:#666}.info-value[data-v-512b6e76]{font-size:%?28?%;color:#333}.description-text[data-v-512b6e76]{font-size:%?28?%;line-height:1.6;color:#333}.next-steps-list[data-v-512b6e76]{display:flex;flex-direction:column;gap:%?20?%}.next-step-item[data-v-512b6e76]{display:flex;align-items:center;padding:%?20?%;background-color:#f5f5f5;border-radius:%?8?%}.step-content[data-v-512b6e76]{flex:1;font-size:%?28?%;color:#333}.tag-list[data-v-512b6e76]{display:flex;flex-wrap:wrap;gap:%?16?%}.tag-item[data-v-512b6e76]{padding:%?8?% %?20?%;background-color:#f5f5f5;color:#666;border-radius:%?100?%;font-size:%?24?%}.history-list[data-v-512b6e76]{display:flex;flex-direction:column;gap:%?20?%}.history-item[data-v-512b6e76]{display:flex;flex-direction:column;gap:%?8?%}.history-time[data-v-512b6e76]{font-size:%?24?%;color:#999}.history-content[data-v-512b6e76]{font-size:%?28?%;color:#333}.action-bar[data-v-512b6e76]{position:fixed;bottom:0;left:0;right:0;padding:%?20?% %?30?%;background-color:#fff;box-shadow:0 %?-2?% %?10?% rgba(0,0,0,.05);display:flex;gap:%?30?%}.btn[data-v-512b6e76]{flex:1;height:%?88?%;border-radius:%?8?%;display:flex;align-items:center;justify-content:center;font-size:%?30?%}.btn-outline[data-v-512b6e76]{border:%?1?% solid #d9d9d9;color:#666}.btn-primary[data-v-512b6e76]{background-color:#3370ff;color:#fff}",""]),t.exports=e},c5e3:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("8a0f")),o={components:{SvgIcon:n.default},data:function(){return{recordData:{id:"",recordType:"follow-up",type:"call",subject:"",date:"",time:"",related:"",relatedType:"company",contacts:"",content:"",nextSteps:[],tags:[],history:[]}}},methods:{navBack:function(){uni.navigateBack()},showMoreActions:function(){var t=this;uni.showActionSheet({itemList:["删除","分享","复制"],success:function(e){switch(e.tapIndex){case 0:t.deleteRecord();break;case 1:t.shareRecord();break;case 2:t.copyRecord();break}}})},getTypeIconName:function(t){return{call:"phone",meeting:"team",email:"mail",visit:"navigation",other:"more"}[t.type]||"more"},getRelatedTypeName:function(t){return{customer:"客户",opportunity:"商机",contract:"合同",contact:"联系人"}[t]||t},editRecord:function(){uni.navigateTo({url:"/pages/interactions/interaction-create?id=".concat(this.recordData.id)})},createFollowUp:function(){uni.navigateTo({url:"/pages/interactions/interaction-create?recordType=follow-up&relatedId=".concat(this.recordData.id,"&relatedType=communication")})},deleteRecord:function(){uni.showModal({title:"确认删除",content:"确定要删除此沟通记录吗？",success:function(t){t.confirm&&uni.showToast({title:"删除成功",icon:"success",duration:2e3,success:function(){setTimeout((function(){uni.navigateBack()}),1500)}})}})},shareRecord:function(){uni.showToast({title:"分享功能开发中",icon:"none"})},copyRecord:function(){uni.showToast({title:"复制功能开发中",icon:"none"})},loadRecordDetail:function(t){this.recordData={id:t,recordType:"follow-up",type:"call",subject:"客户需求沟通",date:"2023-10-25",time:"14:30",related:"ABC科技有限公司",relatedType:"customer",contacts:"张三",content:"与客户沟通了产品需求，客户对现有功能表示满意，但希望增加一些新功能。具体需求如下：\n1. 增加数据导出功能\n2. 优化用户界面\n3. 添加多语言支持",nextSteps:["准备产品需求文档","安排技术团队评估开发周期","下周与客户确认具体实施方案"],tags:["重要","产品需求","客户沟通"],history:[{time:"2023-10-25 14:30",content:"创建记录"},{time:"2023-10-25 15:00",content:"添加了下一步计划"}]}}},onLoad:function(t){t.id&&this.loadRecordDetail(t.id)}};e.default=o},d091:function(t,e,i){"use strict";var a=i("3a43"),n=i.n(a);n.a},e199:function(t,e,i){"use strict";i.r(e);var a=i("8242"),n=i("8bcc");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("d091");var s=i("828b"),c=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"512b6e76",null,!1,a["a"],void 0);e["default"]=c.exports}}]);
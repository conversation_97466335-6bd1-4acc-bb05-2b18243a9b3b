<template>
  <view class="content">
    <view class="loading">
      <image class="logo" src="/static/images/logo.png" mode="aspectFit"></image>
      <view class="loading-text">奥丁CRM加载中...</view>
      <view class="loading-spinner">
        <view class="spinner"></view>
      </view>
    </view>
    <view class="action-btns">
      <button class="btn btn-primary" @click="navigateTo('/pages/dashboard/main-dashboard')">
        进入应用
      </button>
      <button class="btn btn-outline" @click="navigateTo('/pages/icon-showcase')">
        查看图标
      </button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {}
  },
  onLoad() {
    // 模拟加载过程，1秒后跳转到仪表盘
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/dashboard/main-dashboard'
      });
    }, 1000);
  }
}
</script>

<style>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #FFFFFF;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.loading-text {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 40rpx;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
}

.spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #EEEEEE;
  border-top: 6rpx solid #5B8FF9;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.action-btns {
  margin-top: 40rpx;
  display: flex;
  justify-content: center;
}

.btn {
  margin: 0 10rpx;
}
</style> 
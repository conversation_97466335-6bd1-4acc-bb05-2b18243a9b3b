<template>
  <view class="container">
    <!-- 页面头部 -->
<!--    <view class="page-header">
      <view class="back-button" @click="goBack">
        <text class="ri-arrow-left-line"></text>
      </view>
      <text class="page-title">创建线索</text>
      <view class="header-actions">
        <button class="save-button" @click="saveLead">保存</button>
      </view>
    </view>-->
    <!-- 表单内容 -->
    <scroll-view scroll-y class="form-scroll">
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>
        <view class="form-group">
          <text class="form-label">姓名 <text class="required">*</text></text>
          <input 
            type="text"
            class="form-input"
            v-model="leadForm.name"
            placeholder="请输入线索姓名"
          />
        </view>
        <view class="form-group">
          <text class="form-label">公司</text>
          <input 
            type="text"
            class="form-input"
            v-model="leadForm.customName"
            placeholder="请输入公司名称"
          />
        </view>
        <view class="form-group">
          <text class="form-label">部门</text>
          <input
            type="text"
            class="form-input"
            v-model="leadForm.department"
            placeholder="请输入部门"
          />
        </view>
        <view class="form-group">
          <text class="form-label">职位</text>
          <input 
            type="text"
            class="form-input"
            v-model="leadForm.position"
            placeholder="请输入职位"
          />
        </view>
        <view class="form-group">
          <text class="form-label">线索来源 <text class="required">*</text></text>
          <picker
            @change="onSourceChange"
            :value="sourceIndex"
            :range="sources"
            range-key="displayText"
          >
            <view class="picker-input">
              {{ leadForm.clueSourceName || '请选择线索来源' }}
              <text class="ri-arrow-down-s-line"></text>
            </view>
          </picker>
        </view>
        <view class="form-group">
          <text class="form-label">线索状态</text>
          <picker
            @change="onStatusChange"
            :value="statusIndex"
            :range="statuses"
            range-key="displayText"
          >
            <view class="picker-input">
              {{ leadForm.clueStatusName || '请选择线索状态' }}
              <text class="ri-arrow-down-s-line"></text>
            </view>
          </picker>
        </view>
        <view class="form-group">
          <text class="form-label">负责人</text>
          <picker
            @change="onOwnerChange"
            :value="ownerIndex"
            :range="owners"
            range-key="name"
          >
            <view class="picker-input">
              {{ leadForm.owner || '请选择负责人' }}
              <text class="ri-arrow-down-s-line"></text>
            </view>
          </picker>
        </view>
      </view>
      <!-- 联系方式 -->
      <view class="form-section">
        <view class="section-title">联系方式</view>
        <view class="form-group">
          <text class="form-label">固定电话</text>
          <input
            type="number"
            class="form-input"
            v-model="leadForm.fixPhone"
            placeholder="请输入固定电话"
          />
        </view>
        <view class="form-group">
          <text class="form-label">手机号码</text>
          <input
            type="number"
            class="form-input"
            v-model="leadForm.telephone"
            placeholder="请输入手机号码"
          />
        </view>
        <view class="form-group">
          <text class="form-label">微信号</text>
          <input
              type="text"
              class="form-input"
              v-model="leadForm.weChat"
              placeholder="请输入微信号"
          />
        </view>
        <view class="form-group">
          <text class="form-label">电子邮箱</text>
          <input
            type="text"
            class="form-input"
            v-model="leadForm.email"
            placeholder="请输入电子邮箱"
          />
        </view>
        <view class="form-group">
          <text class="form-label">地址</text>
          <input
            type="text"
            class="form-input"
            v-model="leadForm.address"
            placeholder="请输入地址"
          />
        </view>
      </view>
      <!-- 其他信息 -->
      <view class="form-section">
        <view class="section-title">其他信息</view>
        <view class="form-group">
          <text class="form-label">公司网站</text>
          <input
              type="text"
              class="form-input"
              v-model="leadForm.website"
              placeholder="请输入公司网站"
          />
        </view>
        <view class="form-group">
          <text class="form-label">备注</text>
          <textarea 
            class="form-textarea"
            v-model="leadForm.remark"
            placeholder="请输入线索备注"
          />
        </view>
      </view>
      <!-- 保存按钮 -->
<!--      <view class="form-actions">
        <button class="btn-cancel" @click="goBack">取消</button>
        <button class="btn-save" @click="saveLead">保存</button>
      </view>-->
    </scroll-view>
    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <button class="btn btn-outline" @tap="goBack">取消</button>
      <button class="btn btn-primary" @tap="saveLead">保存</button>
    </view>
  </view>
</template>

<script>
import getSelectOptions from '@/utils/dictionary';
import { getAllOwnerList, AddNewClue } from '@/api/clue.api';

export default {
  data() {
    return {
      // 线索来源
      sources: [],
      sourceIndex: -1,
      // 线索状态
      statuses: [],
      statusIndex: 0,
      // 负责人
      owners: [],
      ownerIndex: -1,
      leadForm: {
        name: '',
        customName: '',
        department: '',
        position: '',
        clueSourceId: '',
        clueSourceName: '',
        clueStatusId: '',
        clueStatusName: '',
        fixPhone: '',
        telephone: '',
        weChat: '',
        email: '',
        address: '',
        website: '',
        remark: '',
        ownerId: '',
        owner: '',
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    // 获取数据字典
    async loadDictionaryOptions() {
      try {
        this.sources = await getSelectOptions('clueSource');
        this.statuses = await getSelectOptions('clueStatus');
        let ownerResult = await getAllOwnerList({pageIndex: 1, pageSize: 9999});
        this.owners = ownerResult.items;
      } catch (error) {
        this.$message.error('加载字典数据失败');
      }
    },
    // 线索来源选择变化
    onSourceChange(e) {
      this.sourceIndex = e.detail.value;
      this.leadForm.clueSourceId = this.sources[this.sourceIndex].id;
      this.leadForm.clueSourceName = this.sources[this.sourceIndex].displayText;
    },
    // 线索状态选择变化
    onStatusChange(e) {
      this.statusIndex = e.detail.value;
      this.leadForm.clueStatusId = this.statuses[this.statusIndex].id;
      this.leadForm.clueStatusName = this.statuses[this.statusIndex].displayText;
    },
    // 负责人选择变化
    onOwnerChange(e) {
      this.ownerIndex = e.detail.value;
      this.leadForm.ownerId = this.owners[this.ownerIndex].id;
      this.leadForm.owner = this.owners[this.ownerIndex].name;
    },
    // 保存线索
    saveLead() {
      // 表单验证
      if (!this.leadForm.name) {
        uni.showToast({
          title: '请输入线索姓名',
          icon: 'none'
        });
        return;
      }
      if (!this.leadForm.clueSourceId) {
        uni.showToast({
          title: '请选择线索来源',
          icon: 'none'
        });
        return;
      }
      uni.showLoading({
        title: '保存中...'
      });
      AddNewClue(this.leadForm).then(res => {
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        });
        uni.navigateBack();
      }).catch(error => {
        uni.showToast({
          title: error.error.message,
          icon: 'error'
        });
      }).finally(res => {
        uni.hideLoading();
      })
    }
  },
  onLoad() {
    this.loadDictionaryOptions();
  },
}
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
  padding-top: 0 !important;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  background-color: #fff;
  z-index: 10;
  .page-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
  }
  .back-button {
    color: #666;
    font-size: 24px;
  }
  .header-actions {
    display: flex;
  }
  .save-button {
    background: transparent;
    color: #3a86ff;
    font-size: 16px;
    font-weight: 500;
    border: none;
    padding: 0;
    &:after {
      border: none;
    }
  }
}

.form-scroll {
  flex: 1;
  padding-bottom: 70px;
  .form-section {
    background-color: white;
    margin-top: 12px;
    padding: 16px;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
    }
    .form-group {
      margin-bottom: 16px;
      .form-label {
        display: block;
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
      }
      .required {
        color: #f56c6c;
      }
      .form-input, .picker-input {
        width: 100%;
        height: 44px;
        padding: 0 12px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 14px;
        color: #333;
        box-sizing: border-box;
      }
      .form-textarea {
        width: 100%;
        height: 100px;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 14px;
        color: #333;
        box-sizing: border-box;
      }
      .picker-input {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
      }
    }
  }
  //.form-actions {
  //  display: flex;
  //  padding: 16px;
  //  gap: 12px;
  //  .btn-cancel, .btn-save {
  //    flex: 1;
  //    height: 44px;
  //    border-radius: 8px;
  //    display: flex;
  //    align-items: center;
  //    justify-content: center;
  //    font-size: 16px;
  //  }
  //  .btn-cancel {
  //    background-color: #f5f5f5;
  //    color: #666;
  //    border: 1px solid #ddd;
  //  }
  //  .btn-save {
  //    background-color: #3a86ff;
  //    color: white;
  //    border: none;
  //  }
  //}
}
/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-top: 1rpx solid var(--border-color);
  padding: var(--spacing-md);
  display: flex;
  gap: var(--spacing-md);
  z-index: 100;
  .btn {
    flex: 1;
  }
}
</style> 
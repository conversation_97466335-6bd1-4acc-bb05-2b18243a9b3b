<template>
  <view class="container">
    <!-- 页面头部 -->
<!--    <view class="page-header">
      <navigator open-type="navigateBack" class="back-button">
        <svg-icon name="arrow-left" type="svg" size="24"></svg-icon>
      </navigator>
      <view class="page-title">创建商机</view>
      <view class="header-actions">
        <text class="save-button" @tap="saveOpportunity">保存</text>
      </view>
    </view>-->
    <view class="tips-container">
      <view class="tips">
        <svg-icon name="information" type="svg" size="20"></svg-icon>
        <text>创建商机后，可以跟踪、管理业务机会直至成交</text>
      </view>
    </view>
    <!-- 表单内容 -->
    <view class="form-container">
      <!-- 基本信息 -->
      <view class="info-section">
        <view class="section-title">基本信息</view>
        <!-- 客户选择 -->
        <view class="form-item">
          <view class="form-label">
            <text class="required">*</text>
            <text>客户</text>
          </view>
          <view class="input-container" @tap="selectCustomer">
            <svg-icon v-if="opportunity.customName" name="building" type="svg" size="20" class="prev-icon"></svg-icon>
            <text v-if="opportunity.customName">{{ opportunity.customName }}</text>
            <text v-else class="placeholder">选择客户</text>
            <svg-icon name="arrow-right" type="svg" size="20" class="arrow-icon"></svg-icon>
          </view>
        </view>
        <!-- 商机名称 -->
        <view class="form-item">
          <view class="form-label">
            <text class="required">*</text>
            <text>商机名称</text>
          </view>
          <view class="input-container">
            <input
              class="form-input"
              type="text"
              v-model="opportunity.name"
              placeholder="输入商机名称"
            />
          </view>
        </view>
        <!-- 资金币种 -->
        <view class="form-item">
          <view class="form-label">
            <text class="required">*</text>
            <text>资金币种</text>
          </view>
          <picker
            class="source-picker-inline"
            :value="capitalOptions.findIndex(item => item.id === opportunity.capitalTypeId)"
            :range="capitalOptions"
            range-key="displayText"
            @change="onCapitalSelect"
          >
            <view class="input-container">
              <svg-icon name="bank-card" type="svg" size="20" class="prev-icon"></svg-icon>
              <text v-if="opportunity.capitalTypeName">{{ opportunity.capitalTypeName }}</text>
              <text v-else class="placeholder">选择资金币种</text>
              <svg-icon name="arrow-down" type="svg" size="20" class="arrow-icon"></svg-icon>
            </view>
          </picker>
        </view>
        <!-- 商机税率(%) -->
        <view class="form-item">
          <view class="form-label">
            <text>商机税率(%)</text>
          </view>
          <picker
            class="source-picker-inline"
            :value="rateOptions.findIndex(item => item.displayText === opportunity.rate)"
            :range="rateOptions"
            range-key="displayText"
            @change="onRateSelect"
          >
            <view class="input-container">
              <svg-icon name="bank-card" type="svg" size="20" class="prev-icon"></svg-icon>
              <text v-if="opportunity.rate">{{ opportunity.rate }}</text>
              <text v-else class="placeholder">选择税率</text>
              <svg-icon name="arrow-down" type="svg" size="20" class="arrow-icon"></svg-icon>
            </view>
          </picker>
        </view>
        <!-- 预计成交含税金额 -->
        <view class="form-item">
          <view class="form-label">
            <text class="required">*</text>
            <text>预计成交含税金额</text>
          </view>
          <view class="input-container">
            <input
              class="form-input money-input"
              type="digit"
              v-model="opportunity.expectedTransAmount"
              placeholder="0.00"
              @input="handleRateAmount"
            />
          </view>
        </view>
        <!-- 预计成交未税金额 -->
        <view class="form-item">
          <view class="form-label">
            <text class="required">*</text>
            <text>预计成交未税金额</text>
          </view>
          <view class="input-container">
            <input
              class="form-input money-input"
              type="digit"
              v-model="opportunity.expectedTransNoRateAmount"
              placeholder="0.00"
              @input="handleNoRateAmount"
            />
          </view>
        </view>
        <!-- 预计成单日期 -->
        <view class="form-item">
          <view class="form-label">
            <text>预计成单日期</text>
          </view>
          <picker 
            class="date-picker-inline" 
            mode="date" 
            :value="opportunity.expectedCompleteDate"
            @change="onDateChange"
          >
            <view class="input-container">
              <svg-icon name="calendar" type="svg" size="20" class="prev-icon"></svg-icon>
              <text v-if="opportunity.expectedCompleteDate">{{ opportunity.expectedCompleteDate }}</text>
              <text v-else class="placeholder">选择日期</text>
              <svg-icon name="arrow-down" type="svg" size="20" class="arrow-icon"></svg-icon>
            </view>
          </picker>
        </view>
        <!-- 公司 -->
        <view class="form-item">
          <view class="form-label">
            <text>公司</text>
          </view>
          <picker
            class="source-picker-inline"
            :value="companyOptions.findIndex(item => item.id === opportunity.companyId)"
            :range="companyOptions"
            range-key="displayName"
            @change="onCompanySelect"
          >
            <view class="input-container">
              <svg-icon name="bank-card" type="svg" size="20" class="prev-icon"></svg-icon>
              <text v-if="opportunity.companyName">{{ opportunity.companyName }}</text>
              <text v-else class="placeholder">选择公司</text>
              <svg-icon name="arrow-down" type="svg" size="20" class="arrow-icon"></svg-icon>
            </view>
          </picker>
        </view>
      </view>
      <!-- 商机信息 -->
      <view class="info-section">
        <view class="section-title">商机信息</view>
        <!-- 商机阶段 -->
        <view class="form-item">
          <view class="form-label">
            <text>商机阶段</text>
          </view>
          <picker 
            class="stage-picker-inline" 
            :value="stageOptions.findIndex(item => item.id === opportunity.businessProcessId)"
            :range="stageOptions" 
            range-key="displayText"
            @change="onStageSelect"
          >
            <view class="input-container">
              <svg-icon name="stage" type="svg" size="20" class="prev-icon"></svg-icon>
              <text v-if="opportunity.businessProcessName">{{ opportunity.businessProcessName }}</text>
              <text v-else class="placeholder">选择阶段</text>
              <svg-icon name="arrow-down" type="svg" size="20" class="arrow-icon"></svg-icon>
            </view>
          </picker>
        </view>
        <!-- 商机来源 -->
        <view class="form-item">
          <view class="form-label">
            <text>商机来源</text>
          </view>
          <picker 
            class="source-picker-inline" 
            :value="sourceOptions.findIndex(item => item.id === opportunity.businessSourceId)"
            :range="sourceOptions" 
            range-key="displayText"
            @change="onSourceSelect"
          >
            <view class="input-container">
              <svg-icon name="bar-chart" type="svg" size="20" class="prev-icon"></svg-icon>
              <text v-if="opportunity.businessSourceName">{{ opportunity.businessSourceName }}</text>
              <text v-else class="placeholder">选择来源</text>
              <svg-icon name="arrow-down" type="svg" size="20" class="arrow-icon"></svg-icon>
            </view>
          </picker>
        </view>
        <!-- 商机类型 -->
        <view class="form-item">
          <view class="form-label">
            <text>商机类型</text>
          </view>
          <picker
            class="source-picker-inline"
            :value="typeOptions.findIndex(item => item.id === opportunity.businessTypeId)"
            :range="typeOptions"
            range-key="displayText"
            @change="onTypeSelect"
          >
            <view class="input-container">
              <svg-icon name="bar-chart" type="svg" size="20" class="prev-icon"></svg-icon>
              <text v-if="opportunity.businessTypeName">{{ opportunity.businessTypeName }}</text>
              <text v-else class="placeholder">选择类型</text>
              <svg-icon name="arrow-down" type="svg" size="20" class="arrow-icon"></svg-icon>
            </view>
          </picker>
        </view>
        <!-- 商机优先级 -->
        <view class="form-item">
          <view class="form-label">
            <text>商机优先级</text>
          </view>
          <picker
              class="source-picker-inline"
              :value="priorityOptions.findIndex(item => item.id === opportunity.businessPriorityId)"
              :range="priorityOptions"
              range-key="displayText"
              @change="onPrioritySelect"
          >
            <view class="input-container">
              <svg-icon name="bar-chart" type="svg" size="20" class="prev-icon"></svg-icon>
              <text v-if="opportunity.businessPriorityName">{{ opportunity.businessPriorityName }}</text>
              <text v-else class="placeholder">选择优先级</text>
              <svg-icon name="arrow-down" type="svg" size="20" class="arrow-icon"></svg-icon>
            </view>
          </picker>
        </view>
        <!-- 预计成交概率(%) -->
        <view class="form-item">
          <view class="form-label">
            <text>预计成交概率(%)</text>
          </view>
          <view class="slider-container">
            <slider
              :value="opportunity.expectedTransProbability"
              min="0"
              max="100"
              show-value
              @change="onProbabilityChange"
              class="probability-slider"
              activeColor="var(--primary-color)"
              backgroundColor="var(--border-color-light)"
            />
          </view>
        </view>
      </view>
      <!-- 产品信息 -->
      <view class="info-section">
        <view class="section-title">产品信息</view>
        <view class="product-card-list">
          <view class="product-card" v-for="(item, index) in opportunity.businessProducts" :key="index">
            <view class="product-card-header">
              <view class="product-index">{{ index + 1 }}</view>
              <view class="product-title" @click="editProduct(index)">{{ item.name || '请选择产品' }}</view>
              <view class="product-delete" @click="removeProduct(index)">
                <svg-icon name="delete" type="svg" size="28" color="#ff4d4f"></svg-icon>
              </view>
            </view>
            <view class="product-card-body">
              <view class="product-row">
                <text class="label">数量</text>
                <input type="number" v-model="item.quantity" class="input-qty" placeholder="请输入数量" @input="calculateItemAmount(index)" />
              </view>
              <view class="product-row">
                <text class="label">未税单价</text>
                <input type="number" v-model="item.productPrice" class="input-qty" placeholder="请输入未税单价" @input="calculateItemAmount(index)" />
              </view>
              <view class="product-row">
                <text class="label">未税总价</text>
                <text class="value highlight">¥{{ item.productTotalAmount || (item.productPrice * item.quantity).toFixed(2) || '0.00' }}</text>
              </view>
            </view>
          </view>
          <view class="add-product-btn" @click="addProduct">+ 添加产品</view>
          <view class="product-total-bar">
            <text>未税合计金额：</text>
            <text class="total-amount">{{ totalAmount | price }}</text>
          </view>
        </view>
      </view>
      <!-- 备注信息 -->
      <view class="info-section">
        <view class="section-title">商机描述</view>
        <view class="form-item">
          <view class="textarea-container">
            <svg-icon name="file-text" type="svg" size="30" class="textarea-icon"></svg-icon>
            <textarea
              v-model="opportunity.description"
              class="form-textarea"
              placeholder="请简要描述商机情况、客户需求等"
            ></textarea>
          </view>
        </view>
      </view>
      <!-- 负责人信息 -->
      <view class="info-section">
        <view class="section-title">负责人</view>
        <view class="form-item">
          <picker 
            class="owner-picker-inline" 
            :value="ownerOptions.findIndex(item => item.id === opportunity.ownerId)"
            :range="ownerOptions" 
            range-key="name" 
            @change="onOwnerSelect"
          >
            <view class="input-container">
              <view class="owner-info" v-if="opportunity.owner">
                <view class="owner-avatar">{{ opportunity.owner.charAt(0) }}</view>
                <text>{{ opportunity.owner }}</text>
              </view>
              <text v-else class="placeholder">选择负责人</text>
              <svg-icon name="arrow-down" type="svg" size="20" class="arrow-icon"></svg-icon>
            </view>
          </picker>
        </view>
      </view>
      <!-- 后续活动 -->
<!--      <view class="info-section">
        <view class="section-title">后续活动安排</view>
        <view class="actions-container">
          <view
            class="action-item"
            v-for="(action, index) in opportunityActions"
            :key="action.id"
            @tap="selectAction(action.id)"
            :class="{ active: selectedActions.includes(action.id) }"
          >
            <svg-icon :name="action.icon" type="svg" size="24" class="action-icon"></svg-icon>
            <text>{{ action.name }}</text>
          </view>
        </view>
      </view>-->
    </view>
    <!-- 底部操作栏 -->
    <view class="action-bar">
      <button class="btn btn-outline" @tap="cancelCreate">取消</button>
      <button class="btn btn-primary" @tap="saveOpportunity">保存商机</button>
    </view>
  </view>
</template>

<script>
import getSelectOptions from "@/utils/dictionary";
import { AddNewBusiness, getAllCompanyList } from '@/api/business.api';
import { getAllOwnerList } from '@/api/clue.api';
import dayjs from "dayjs";

export default {
  data() {
    return {
      opportunity: {
        name: '',
        customId: '',
        customName: '',
        companyId: '',
        companyName: '',
        rate: '',
        expectedTransAmount: '',
        expectedTransNoRateAmount: '',
        expectedCompleteDate: '',
        businessProcessId: '',
        businessProcessName: '',
        businessSourceId: '',
        businessSourceName: '',
        businessTypeId: '',
        businessTypeName: '',
        capitalTypeId: '',
        capitalTypeName: '',
        businessPriorityId: '',
        businessPriorityName: '',
        expectedTransProbability: 0,
        description: '',
        ownerId: '',
        owner: '',
        businessProducts: [],
      },
      capitalOptions: [], // 资金币种
      rateOptions: [], // 税率
      stageOptions: [], // 阶段
      sourceOptions: [], // 来源
      typeOptions: [], // 类型
      priorityOptions: [], // 优先级
      ownerOptions: [], // 负责人
      companyOptions: [], // 公司
      // 后续活动
      opportunityActions: [
        {
          id: 1,
          name: '电话沟通',
          icon: 'phone',
          type: 'call'
        },
        {
          id: 2,
          name: '客户拜访',
          icon: 'search',
          type: 'visit'
        },
        {
          id: 3,
          name: '方案准备',
          icon: 'file-list',
          type: 'proposal'
        },
        {
          id: 4,
          name: '产品演示',
          icon: 'discuss',
          type: 'demo'
        },
        {
          id: 5,
          name: '跟进确认',
          icon: 'check',
          type: 'followup'
        }
      ],
      selectedActions: [],
      editingProductIndex: null
    }
  },
  computed: {
    // 计算未税总金额
    totalAmount() {
      return this.opportunity.businessProducts.reduce((sum, product) => {
        return sum + (parseFloat(product.productPrice) * product.quantity)
      }, 0)
    }
  },
  methods: {
    // 获取数据字典
    async loadDictionaryOptions() {
      try {
        this.capitalOptions = await getSelectOptions('CapitalType');
        this.rateOptions = await getSelectOptions('ContractRate');
        this.stageOptions = await getSelectOptions('BusinessProcess');
        this.sourceOptions = await getSelectOptions('BusinessSource');
        this.typeOptions = await getSelectOptions('BusinessType');
        this.priorityOptions = await getSelectOptions('BusinessPriority');
        let ownerResult = await getAllOwnerList({pageIndex: 1, pageSize: 9999});
        this.ownerOptions = ownerResult.items;
        this.companyOptions = await getAllCompanyList();
        if (this.companyOptions.length) {
          this.opportunity.companyId = this.companyOptions[0].id;
          this.opportunity.companyName = this.companyOptions[0].displayName;
        }
      } catch (error) {
        this.$message.error('加载字典数据失败');
      }
    },
    // 获取默认成单日期（当前日期+30天）
    getDefaultDate() {
      const newDate = dayjs(new Date()).add(30, 'day');
      return newDate.format('YYYY-MM-DD');
    },
    // 选择日期
    onDateChange(e) {
      this.opportunity.expectedCompleteDate = e.detail.value;
    },
    // 选择资金币种
    onCapitalSelect(e) {
      const index = e.detail.value;
      this.opportunity.capitalTypeId = this.capitalOptions[index].id;
      this.opportunity.capitalTypeName = this.capitalOptions[index].displayText;
    },
    // 选择税率
    onRateSelect(e) {
      const index = e.detail.value;
      this.opportunity.rate = this.rateOptions[index].displayText;
      let rat = Number(this.opportunity.rate?.replace('%', ''));
      if (this.opportunity.expectedTransAmount) {
        this.opportunity.expectedTransNoRateAmount = Number(this.opportunity.expectedTransAmount && (Number(this.opportunity.expectedTransAmount) / (rat / 100 + 1)).toFixed(2)) || '0.00';
      }
      if (this.opportunity.expectedTransNoRateAmount) {
        this.opportunity.expectedTransAmount = Number((this.opportunity.expectedTransNoRateAmount * (rat / 100 + 1)).toFixed(2)) || '0.00';
      }
    },
    // 含税金额算未税
    handleRateAmount(e) {
      let rateAmountValue = Number(e.detail.value);
      if (this.opportunity.rate) {
        let rat = Number(this.opportunity.rate?.replace('%', ''));
        this.opportunity.expectedTransNoRateAmount = Number((rateAmountValue / (rat / 100 + 1)).toFixed(2)) || '0.00';
      }
    },
    // 未税金额算含税
    handleNoRateAmount(e) {
      let amountValue = Number(e.detail.value);
      if (this.opportunity.rate) {
        let rat = Number(this.opportunity.rate?.replace('%', ''));
        this.opportunity.expectedTransAmount = Number((amountValue * (rat / 100 + 1)).toFixed(2)) || '0.00';
      }
    },
    // 选择阶段
    onStageSelect(e) {
      const index = e.detail.value;
      this.opportunity.businessProcessId = this.stageOptions[index].id;
      this.opportunity.businessProcessName = this.stageOptions[index].displayText;
    },
    // 选择来源
    onSourceSelect(e) {
      const index = e.detail.value;
      this.opportunity.businessSourceId = this.sourceOptions[index].id;
      this.opportunity.businessSourceName = this.sourceOptions[index].displayText;
    },
    // 选择类型
    onTypeSelect(e) {
      const index = e.detail.value;
      this.opportunity.businessTypeId = this.typeOptions[index].id;
      this.opportunity.businessTypeName = this.typeOptions[index].displayText;
    },
    // 选择优先级
    onPrioritySelect(e) {
      const index = e.detail.value;
      this.opportunity.businessPriorityId = this.priorityOptions[index].id;
      this.opportunity.businessPriorityName = this.priorityOptions[index].displayText;
    },
    // 选择负责人
    onOwnerSelect(e) {
      const index = e.detail.value;
      this.opportunity.ownerId = this.ownerOptions[index].id;
      this.opportunity.owner = this.ownerOptions[index].name;
    },
    // 选择公司
    onCompanySelect(e) {
      const index = e.detail.value;
      this.opportunity.companyId = this.companyOptions[index].id;
      this.opportunity.companyName = this.companyOptions[index].displayName;
    },
    // 成功几率滑块变化
    onProbabilityChange(e) {
      this.opportunity.expectedTransProbability = e.detail.value;
    },
    // 选择客户
    selectCustomer() {
      // 保存当前表单状态到缓存
      // uni.setStorageSync('opportunity_draft', JSON.stringify(this.opportunity));
      uni.navigateTo({
        url: './customer-select?from=opportunity-create',
        events: {
          selectCustomer: (customer) => {
            if (customer) {
              this.opportunity.customId = customer.id || '';
              this.opportunity.customName = customer.name || '';
            }
          }
        }
      });
    },
    // 添加产品
    addProduct() {
      const newProduct = {
        productId: '',
        name: '',
        quantity: 1,
        productPrice: '',
        productTotalAmount: '',
      };
      this.opportunity.businessProducts.push(newProduct);
      this.editingProductIndex = this.opportunity.businessProducts.length - 1;
      this.openProductSelect();
    },
    // 打开产品选择页面
    openProductSelect() {
      uni.navigateTo({
        url: '/pages/products/product-select',
        events: {
          selectProduct: (product) => {
            const newProduct = {
              productId: product.id,
              name: product.name,
            };
            this.$set(this.opportunity.businessProducts, this.editingProductIndex, {
              ...newProduct,
              quantity: 1,
              productPrice: '',
              productTotalAmount: '',
            });
            this.opportunity.expectedTransNoRateAmount = this.totalAmount.toString();
          }
        }
      });
    },
    // 编辑产品（重新选择）
    editProduct(index) {
      this.editingProductIndex = index;
      this.openProductSelect();
    },
    // 移除产品
    removeProduct(index) {
      this.opportunity.businessProducts.splice(index, 1)
      // 更新金额
      this.opportunity.expectedTransNoRateAmount = this.totalAmount.toString();
      if (this.opportunity.businessProducts.length === 0) {
        this.opportunity.expectedTransNoRateAmount = ''
      }
    },
    // 保存商机
    saveOpportunity() {
      // 表单验证
      if (!this.opportunity.name) {
        uni.showToast({
          title: '请输入商机名称',
          icon: 'none'
        })
        return
      }
      if (!this.opportunity.customName) {
        uni.showToast({
          title: '请选择客户',
          icon: 'none'
        })
        return
      }
      if (!this.opportunity.expectedTransAmount) {
        uni.showToast({
          title: '请输入预计成交含税金额',
          icon: 'none'
        })
        return
      }
      if (!this.opportunity.expectedTransNoRateAmount) {
        uni.showToast({
          title: '请输入预计成交未税金额',
          icon: 'none'
        })
        return
      }
      uni.showLoading({
        title: '保存中...'
      })
      this.opportunity.expectedTransAmount = Number(this.opportunity.expectedTransAmount);
      this.opportunity.expectedTransNoRateAmount = Number(this.opportunity.expectedTransNoRateAmount);
      this.opportunity.expectedTransProbability = this.opportunity.expectedTransProbability + '%';
      AddNewBusiness(this.opportunity).then(res => {
        uni.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 500,
          success: () => {
            uni.removeStorageSync('opportunity_draft')
            uni.navigateBack();
          }
        });
      }).catch(error => {
        uni.showToast({
          title: error.error.message,
          icon: 'error'
        });
      }).finally(res => {
        uni.hideLoading();
      })
    },
    // 取消创建
    cancelCreate() {
      uni.showModal({
        title: '确认取消',
        content: '是否放弃当前编辑的内容？',
        success: (res) => {
          if (res.confirm) {
            uni.removeStorageSync('opportunity_draft')
            uni.navigateBack()
          }
        }
      })
    },
    // 加载草稿数据
    loadDraftData() {
      const draft = uni.getStorageSync('opportunity_draft')
      if (draft) {
        try {
          const draftData = JSON.parse(draft)
          this.opportunity = { ...this.opportunity, ...draftData }
        } catch (e) {
          console.error('解析草稿数据失败', e)
        }
      }
    },
    // 检查URL参数
    checkUrlParams(options) {
      if (options.customer) {
        this.opportunity.company = options.company || ''
        this.opportunity.customId = options.customId || ''
      }
    },
    // 计算未税总价
    calculateItemAmount(index) {
      const item = this.opportunity.businessProducts[index];
      if (item.productPrice && item.quantity) {
        item.productTotalAmount = (parseFloat(item.productPrice) * parseFloat(item.quantity)).toFixed(2);
      } else {
        item.productTotalAmount = '0.00';
      }
      // 实时同步预计金额
      this.opportunity.expectedTransNoRateAmount = this.totalAmount.toString();
      if (this.opportunity.rate) {
        let rat = Number(this.opportunity.rate?.replace('%', ''));
        this.opportunity.expectedTransAmount = Number((this.opportunity.expectedTransNoRateAmount * (rat / 100 + 1)).toFixed(2));
      }
    },
    // 选择后续活动
    selectAction(actionId) {
      const index = this.selectedActions.indexOf(actionId)
      if (index === -1) {
        this.selectedActions.push(actionId)
      } else {
        this.selectedActions.splice(index, 1)
      }
    },
  },
  onLoad(options) {
    this.loadDictionaryOptions();
    // 首先检查URL参数
    this.checkUrlParams(options)
    // 设置默认成单日期（当前日期+30天）
    if (!this.opportunity.expectedCompleteDate) {
      this.opportunity.expectedCompleteDate = this.getDefaultDate()
    }
    // 其次加载草稿数据（如果有）
    // this.loadDraftData()
    // 监听从产品选择页面返回的事件
    uni.$on('updateProducts', (products) => {
      if (products && Array.isArray(products)) {
        this.opportunity.businessProducts = products
        this.opportunity.expectedTransNoRateAmount = this.totalAmount.toString()
      }
    })
    // 监听从客户选择页面返回的事件
    uni.$on('selectCustomer', (customer) => {
      if (customer) {
        this.opportunity.company = customer.company || ''
        this.opportunity.customId = customer.id || ''
      }
    })
  },
  onUnload() {
    // 清除事件监听
    uni.$off('updateProducts')
    uni.$off('selectCustomer')
  }
}
</script>

<style>
.container {
  background-color: var(--bg-color);
  min-height: 100vh;
  padding: 0;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1rpx solid var(--border-color);
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.page-title {
  font-size: var(--font-md);
  font-weight: bold;
  color: var(--text-primary);
}

.back-button {
  color: var(--text-secondary);
  display: flex;
  align-items: center;
}

.save-button {
  color: var(--primary-color);
  font-weight: 500;
  font-size: var(--font-sm);
  padding: var(--spacing-sm) var(--spacing-md);
}

.tips-container {
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: #ffffff;
  margin-bottom: var(--spacing-sm);
}

.tips {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--primary-light);
  border-radius: var(--radius-md);
  color: var(--primary-color);
  font-size: var(--font-xs);
}

.info-section {
  background-color: #ffffff;
  border-radius: var(--radius-md);
  margin: 0 var(--spacing-md) var(--spacing-md);
  padding: var(--spacing-md);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  border: 1rpx solid var(--border-color);
}

.section-title {
  font-size: var(--font-sm);
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-xs);
  border-bottom: 1rpx solid var(--border-color-light);
}

.form-item {
  margin-bottom: var(--spacing-md);
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: var(--font-sm);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  display: flex;
  align-items: center;
}

.required {
  color: var(--danger-color);
  margin-right: 4rpx;
}

.input-container {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--font-sm);
  color: var(--text-primary);
  background-color: #ffffff;
  display: flex;
  align-items: center;
  min-height: 72rpx;
  box-sizing: border-box;
}

.form-input {
  flex: 1;
  height: 60rpx;
  font-size: var(--font-sm);
}

.money-input {
  font-weight: 500;
}

.prev-icon {
  color: var(--text-secondary);
  margin-right: var(--spacing-xs);
}
.placeholder {
  color: var(--text-tertiary);
}
.arrow-icon {
  margin-left: auto;
  color: var(--text-tertiary);
}

.textarea-container {
  position: relative;
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  padding-left: 70rpx;
  background-color: #ffffff;
}

.textarea-icon {
  position: absolute;
  left: var(--spacing-md);
  top: var(--spacing-md);
  color: var(--text-tertiary);
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  font-size: var(--font-sm);
  line-height: 1.5;
}

.product-card-list {
  padding: 0;
}
.product-card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);
  margin-bottom: 24rpx;
  padding: 24rpx;
}
.product-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.product-index {
  width: 48rpx; height: 48rpx;
  background: #3a86ff;
  color: #fff;
  border-radius: 50%;
  display: flex; align-items: center; justify-content: center;
  font-weight: bold;
  margin-right: 16rpx;
}
.product-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  color: #222;
}
.product-delete {
  color: #ff4d4f;
  font-size: 32rpx;
  margin-left: 8rpx;
}
.product-card-body .product-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}
.product-row .label {
  color: #888;
  font-size: 26rpx;
}
.input-qty {
  width: 240rpx;
  height: 56rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  text-align: right;
  font-size: 28rpx;
  background: #f9f9f9;
  padding: 0 16rpx;
}
.product-row .value {
  color: #333;
  font-size: 28rpx;
}
.product-row .highlight {
  color: #ff6b18;
  font-weight: bold;
  font-size: 32rpx;
}
.add-product-btn {
  margin: 24rpx 0;
  background: #f0f7ff;
  color: #3a86ff;
  border-radius: 8rpx;
  text-align: center;
  padding: 24rpx 0;
  font-size: 30rpx;
  font-weight: 500;
}
.product-total-bar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: #ff6b18;
}

.owner-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.owner-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: var(--radius-full);
  background-color: var(--primary-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-xs);
  font-weight: bold;
}

.actions-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.action-item {
  width: calc(33.33% - var(--spacing-md) * 2/3);
  padding: var(--spacing-md);
  background-color: var(--light-color);
  border-radius: var(--radius-md);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
  text-align: center;
  border: 1rpx solid transparent;
  transition: all 0.2s ease;
}

.action-item.active {
  background-color: var(--primary-light);
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.action-icon {
  margin-bottom: var(--spacing-xs);
}

.slider-container {
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: #ffffff;
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-md);
}

.probability-slider {
  width: 100%;
  margin: 0;
  padding: 0;
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: var(--spacing-md);
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-md);
  border-top: 1rpx solid var(--border-color);
  z-index: 10;
}

.action-bar .btn {
  flex: 1;
  height: 80rpx;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-sm);
}

.btn-primary {
  background-color: var(--primary-color);
  color: #ffffff;
}

.btn-outline {
  background-color: #ffffff;
  color: var(--text-secondary);
  border: 1rpx solid var(--border-color);
}

.date-picker-inline, .stage-picker-inline, .source-picker-inline, .owner-picker-inline {
  width: 100%;
}

.total-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--light-color);
  border-radius: var(--radius-md);
  border: 1rpx solid var(--border-color-light);
}

.amount-label {
  font-size: var(--font-sm);
  font-weight: 500;
  color: var(--text-secondary);
}

.amount-value {
  font-size: var(--font-sm);
  font-weight: 500;
  color: var(--text-primary);
}
</style> 
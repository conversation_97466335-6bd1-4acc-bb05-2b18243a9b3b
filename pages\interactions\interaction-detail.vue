<template>
  <view class="page">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @tap="navBack">
        <svg-icon name="arrow-left" type="svg" size="24"></svg-icon>
      </view>
      <text class="page-title">沟通记录详情</text>
      <view class="header-actions">
        <view class="header-icon" @tap="showMoreActions">
          <svg-icon name="more" type="svg" size="28"></svg-icon>
        </view>
      </view>
    </view>

    <scroll-view scroll-y class="page-container">
      <!-- 基本信息区域 -->
      <view class="info-section">
        <view class="info-header">
          <view class="record-type-icon" :class="recordData.type">
            <svg-icon :name="getTypeIconName(recordData)" type="svg" size="28"></svg-icon>
          </view>
          <view class="info-title">
            <text class="record-title">{{recordData.subject}}</text>
            <text class="record-time">{{recordData.date}} {{recordData.time}}</text>
          </view>
        </view>
      </view>
      
      <!-- 关联信息区域 -->
      <view class="info-section" v-if="recordData.related">
        <view class="section-header">
          <svg-icon name="link" type="svg" size="20"></svg-icon>
          <text class="section-title">关联信息</text>
        </view>
        <view class="section-content">
          <view class="info-item">
            <text class="info-label">关联对象</text>
            <text class="info-value">{{recordData.related}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">关联类型</text>
            <text class="info-value">{{getRelatedTypeName(recordData.relatedType)}}</text>
          </view>
        </view>
      </view>
      
      <!-- 联系人信息区域 -->
      <view class="info-section" v-if="recordData.contacts">
        <view class="section-header">
          <svg-icon name="user" type="svg" size="20"></svg-icon>
          <text class="section-title">联系人</text>
        </view>
        <view class="section-content">
          <view class="info-item">
            <text class="info-label">联系人</text>
            <text class="info-value">{{recordData.contacts}}</text>
          </view>
        </view>
      </view>
      
      <!-- 详情描述区域 -->
      <view class="info-section" v-if="recordData.content">
        <view class="section-header">
          <svg-icon name="align-left" type="svg" size="20"></svg-icon>
          <text class="section-title">详情描述</text>
        </view>
        <view class="section-content">
          <text class="description-text">{{recordData.content}}</text>
        </view>
      </view>
      
      <!-- 下一步计划区域 (仅跟进记录) -->
      <view class="info-section" v-if="recordData.recordType === 'follow-up' && recordData.nextSteps">
        <view class="section-header">
          <svg-icon name="check-circle" type="svg" size="20"></svg-icon>
          <text class="section-title">下一步计划</text>
        </view>
        <view class="section-content">
          <view class="next-steps-list">
            <view class="next-step-item" v-for="(step, index) in recordData.nextSteps" :key="index">
              <view class="step-content">{{step}}</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 标签区域 -->
      <view class="info-section" v-if="recordData.tags && recordData.tags.length">
        <view class="section-header">
          <svg-icon name="tag" type="svg" size="20"></svg-icon>
          <text class="section-title">标签</text>
        </view>
        <view class="section-content">
          <view class="tag-list">
            <view class="tag-item" v-for="(tag, index) in recordData.tags" :key="index">
              <text>{{tag}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 操作记录 -->
      <view class="info-section">
        <view class="section-header">
          <svg-icon name="history" type="svg" size="20"></svg-icon>
          <text class="section-title">操作记录</text>
        </view>
        <view class="section-content">
          <view class="history-list">
            <view class="history-item" v-for="(record, index) in recordData.history" :key="index">
              <view class="history-time">{{record.time}}</view>
              <view class="history-content">{{record.content}}</view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部操作栏 -->
    <view class="action-bar">
      <button class="btn btn-outline" @tap="editRecord">编辑</button>
      <button class="btn btn-primary" @tap="createFollowUp" v-if="recordData.recordType === 'communication'">创建跟进</button>
    </view>
  </view>
</template>

<script>
import SvgIcon from '@/components/svg-icon.vue';

export default {
  components: {
    SvgIcon
  },
  data() {
    return {
      recordData: {
        id: '',
        recordType: 'follow-up',
        type: 'call',
        subject: '',
        date: '',
        time: '',
        related: '',
        relatedType: 'company',
        contacts: '',
        content: '',
        nextSteps: [],
        tags: [],
        history: []
      }
    }
  },
  methods: {
    navBack() {
      uni.navigateBack();
    },
    showMoreActions() {
      uni.showActionSheet({
        itemList: ['删除', '分享', '复制'],
        success: (res) => {
          switch(res.tapIndex) {
            case 0:
              this.deleteRecord();
              break;
            case 1:
              this.shareRecord();
              break;
            case 2:
              this.copyRecord();
              break;
          }
        }
      });
    },
    getTypeIconName(record) {
      const iconMap = {
        'call': 'phone',
        'meeting': 'team',
        'email': 'mail',
        'visit': 'navigation',
        'other': 'more'
      };
      
      return iconMap[record.type] || 'more';
    },
    getRelatedTypeName(type) {
      const typeMap = {
        'customer': '客户',
        'opportunity': '商机',
        'contract': '合同',
        'contact': '联系人'
      };
      
      return typeMap[type] || type;
    },
    editRecord() {
      uni.navigateTo({
        url: `/pages/interactions/interaction-create?id=${this.recordData.id}`
      });
    },
    createFollowUp() {
      uni.navigateTo({
        url: `/pages/interactions/interaction-create?recordType=follow-up&relatedId=${this.recordData.id}&relatedType=communication`
      });
    },
    deleteRecord() {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除此沟通记录吗？',
        success: (res) => {
          if (res.confirm) {
            // 实际应用中应该调用API删除
            uni.showToast({
              title: '删除成功',
              icon: 'success',
              duration: 2000,
              success: () => {
                setTimeout(() => {
                  uni.navigateBack();
                }, 1500);
              }
            });
          }
        }
      });
    },
    shareRecord() {
      uni.showToast({
        title: '分享功能开发中',
        icon: 'none'
      });
    },
    copyRecord() {
      uni.showToast({
        title: '复制功能开发中',
        icon: 'none'
      });
    },
    loadRecordDetail(id) {
      // 模拟数据
      this.recordData = {
        id: id,
        recordType: 'follow-up',
        type: 'call',
        subject: '客户需求沟通',
        date: '2023-10-25',
        time: '14:30',
        related: 'ABC科技有限公司',
        relatedType: 'customer',
        contacts: '张三',
        content: '与客户沟通了产品需求，客户对现有功能表示满意，但希望增加一些新功能。具体需求如下：\n1. 增加数据导出功能\n2. 优化用户界面\n3. 添加多语言支持',
        nextSteps: [
          '准备产品需求文档',
          '安排技术团队评估开发周期',
          '下周与客户确认具体实施方案'
        ],
        tags: ['重要', '产品需求', '客户沟通'],
        history: [
          {
            time: '2023-10-25 14:30',
            content: '创建记录'
          },
          {
            time: '2023-10-25 15:00',
            content: '添加了下一步计划'
          }
        ]
      };
    }
  },
  onLoad(options) {
    if (options.id) {
      this.loadRecordDetail(options.id);
    }
  }
}
</script>

<style>
.page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #e0e0e0;
  background-color: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

.back-button {
  padding: 10rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 30rpx;
}

.header-icon {
  color: #666;
  font-size: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-container {
  flex: 1;
  padding: 30rpx;
}

.info-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.info-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.record-type-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  margin-right: 20rpx;
  color: white;
}

.record-type-icon.call {
  background-color: #52c41a;
}

.record-type-icon.meeting {
  background-color: #1890ff;
}

.record-type-icon.email {
  background-color: #722ed1;
}

.record-type-icon.visit {
  background-color: #fa8c16;
}

.record-type-icon.other {
  background-color: #bfbfbf;
}

.info-title {
  flex: 1;
}

.record-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-left: 10rpx;
  color: #333;
}

.section-content {
  padding: 10rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
}

.description-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

.next-steps-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.next-step-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
}

.step-content {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag-item {
  padding: 8rpx 20rpx;
  background-color: #f5f5f5;
  color: #666;
  border-radius: 100rpx;
  font-size: 24rpx;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.history-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.history-time {
  font-size: 24rpx;
  color: #999;
}

.history-content {
  font-size: 28rpx;
  color: #333;
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background-color: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  gap: 30rpx;
}

.btn {
  flex: 1;
  height: 88rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}

.btn-outline {
  border: 1rpx solid #d9d9d9;
  color: #666;
}

.btn-primary {
  background-color: #3370ff;
  color: white;
}
</style> 
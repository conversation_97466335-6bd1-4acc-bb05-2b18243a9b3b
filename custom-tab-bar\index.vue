<template>
  <view class="tab-bar">
    <view 
      v-for="(item, index) in list" 
      :key="index" 
      class="tab-bar-item" 
      :class="{ active: selected === index }" 
      @tap="switchTab(index, item.pagePath)">
      <view class="icon-container">
        <svg-icon 
          :name="item.iconName" 
          type="svg" 
          :size="24" 
          :color="selected === index ? selectedColor : color"
        ></svg-icon>
      </view>
      <view class="text-container">
        <text>{{ item.text }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import SvgIcon from '@/components/svg-icon.vue';

export default {
  components: {
    SvgIcon
  },
  data() {
    return {
      selected: 0,
      color: "#6b7280",
      selectedColor: "#4a6fff",
      list: [
        {
          pagePath: "/pages/dashboard/main-dashboard",
          text: "首页",
          iconName: "home"
        },
        {
          pagePath: "/pages/customers/customer-list",
          text: "客户",
          iconName: "customer"
        },
        {
          pagePath: "/pages/sales/opportunity-list",
          text: "销售",
          iconName: "sales"
        },
        {
          pagePath: "/pages/tasks/task-list",
          text: "任务",
          iconName: "task"
        },
        {
          pagePath: "/pages/settings/profile",
          text: "我的",
          iconName: "profile"
        }
      ]
    };
  },
  methods: {
    switchTab(index, url) {
      if (this.selected !== index) {
        this.selected = index;
        uni.switchTab({ url });
      }
    },
    setSelected(url) {
      const index = this.list.findIndex(item => item.pagePath === url);
      if (index !== -1) {
        this.selected = index;
      }
    }
  },
  created() {
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      const route = `/${currentPage.route}`;
      this.setSelected(route);
    }
    
    // 页面切换时更新选中状态
    uni.$on('tabChange', (route) => {
      this.setSelected(route);
    });
  },
  beforeDestroy() {
    uni.$off('tabChange');
  }
};
</script>

<style>
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: white;
  display: flex;
  border-top: 1px solid #f0f0f0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.icon-container {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.text-container {
  font-size: 10px;
  color: #6b7280;
  margin-top: 2px;
}

.tab-bar-item.active .text-container {
  color: #4a6fff;
}
</style> 
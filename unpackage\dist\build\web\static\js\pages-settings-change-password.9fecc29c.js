(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-settings-change-password"],{"0454":function(s,t,e){var a=e("6021");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[s.i,a,""]]),a.locals&&(s.exports=a.locals);var r=e("967d").default;r("07278496",a,!0,{sourceMap:!1,shadowMode:!1})},"2ac3":function(s,t,e){"use strict";e("6a54");var a=e("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,e("5c47"),e("0506");var r=a(e("8a0f")),i={components:{SvgIcon:r.default},data:function(){return{currentPassword:"",newPassword:"",confirmPassword:"",currentPasswordVisible:!1,newPasswordVisible:!1,confirmPasswordVisible:!1,errors:{currentPassword:"",newPassword:"",confirmPassword:""}}},computed:{hasValidLength:function(){return this.newPassword.length>=8&&this.newPassword.length<=20},hasLowerCase:function(){return/[a-z]/.test(this.newPassword)},hasUpperCase:function(){return/[A-Z]/.test(this.newPassword)},hasNumber:function(){return/[0-9]/.test(this.newPassword)},hasSpecialChar:function(){return/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(this.newPassword)},passwordStrength:function(){if(!this.newPassword)return 0;var s=0;return this.hasValidLength&&(s+=1),this.hasLowerCase&&(s+=1),this.hasUpperCase&&(s+=1),this.hasNumber&&(s+=1),this.hasSpecialChar&&(s+=1),s},strengthClass:function(){var s=this.passwordStrength;return s<=2?"weak":s<=3?"medium":"strong"},strengthText:function(){var s=this.passwordStrength;return s<=2?"弱":s<=3?"中":"强"},isFormValid:function(){return this.currentPassword&&this.newPassword&&this.confirmPassword&&this.hasValidLength&&this.hasLowerCase&&this.hasUpperCase&&this.hasNumber&&this.hasSpecialChar&&this.newPassword===this.confirmPassword},iconColorLength:function(){return this.hasValidLength?"#10b981":"#999999"},iconColorLower:function(){return this.hasLowerCase?"#10b981":"#999999"},iconColorUpper:function(){return this.hasUpperCase?"#10b981":"#999999"},iconColorNumber:function(){return this.hasNumber?"#10b981":"#999999"},iconColorSpecial:function(){return this.hasSpecialChar?"#10b981":"#999999"}},watch:{currentPassword:function(s){this.errors.currentPassword=s?"":"请输入当前密码"},newPassword:function(s){s?s===this.currentPassword?this.errors.newPassword="新密码不能与当前密码相同":this.errors.newPassword="":this.errors.newPassword="请输入新密码",this.confirmPassword&&this.confirmPassword!==s?this.errors.confirmPassword="两次输入的密码不一致":this.errors.confirmPassword=""},confirmPassword:function(s){s?s!==this.newPassword?this.errors.confirmPassword="两次输入的密码不一致":this.errors.confirmPassword="":this.errors.confirmPassword="请确认新密码"}},methods:{toggleCurrentPasswordVisibility:function(){this.currentPasswordVisible=!this.currentPasswordVisible},toggleNewPasswordVisibility:function(){this.newPasswordVisible=!this.newPasswordVisible},toggleConfirmPasswordVisibility:function(){this.confirmPasswordVisible=!this.confirmPasswordVisible},changePassword:function(){this.validateForm(),this.isFormValid&&(uni.showLoading({title:"修改中..."}),setTimeout((function(){uni.hideLoading(),uni.showModal({title:"修改成功",content:"密码已成功修改，下次登录请使用新密码",showCancel:!1,success:function(){uni.navigateBack()}})}),1500))},validateForm:function(){this.currentPassword||(this.errors.currentPassword="请输入当前密码"),this.newPassword?this.newPassword===this.currentPassword&&(this.errors.newPassword="新密码不能与当前密码相同"):this.errors.newPassword="请输入新密码",this.confirmPassword?this.confirmPassword!==this.newPassword&&(this.errors.confirmPassword="两次输入的密码不一致"):this.errors.confirmPassword="请确认新密码"},navigateToResetPassword:function(){uni.navigateTo({url:"/pages/auth/forgot-password"})}}};t.default=i},"32f8":function(s,t,e){"use strict";e.r(t);var a=e("2ac3"),r=e.n(a);for(var i in a)["default"].indexOf(i)<0&&function(s){e.d(t,s,(function(){return a[s]}))}(i);t["default"]=r.a},6021:function(s,t,e){var a=e("c86c");t=a(!1),t.push([s.i,".container[data-v-49cd79a6]{display:flex;flex-direction:column;height:100vh;background-color:#f5f7fa}.page-header[data-v-49cd79a6]{display:flex;flex-direction:row;align-items:center;justify-content:space-between;padding:%?30?% %?40?%;background-color:#fff;border-bottom:1px solid #e0e0e0;position:relative;z-index:10}.page-title[data-v-49cd79a6]{font-size:%?36?%;font-weight:700;color:#333}.flex-row[data-v-49cd79a6]{display:flex;flex-direction:row}.items-center[data-v-49cd79a6]{align-items:center}.gap-sm[data-v-49cd79a6]{gap:%?20?%}.page-content[data-v-49cd79a6]{flex:1;padding:%?40?% %?30?%}.password-icon-container[data-v-49cd79a6]{display:flex;justify-content:center;margin:%?40?% 0}.password-icon[data-v-49cd79a6]{width:%?160?%;height:%?160?%;border-radius:50%;background-color:rgba(74,111,255,.06274509803921569);display:flex;align-items:center;justify-content:center}.password-icon .iconfont[data-v-49cd79a6]{display:none}.form-container[data-v-49cd79a6]{background-color:#fff;border-radius:%?16?%;padding:%?40?%;box-shadow:0 %?2?% %?8?% rgba(0,0,0,.05);border:%?1?% solid #e0e0e0}.form-item[data-v-49cd79a6]{margin-bottom:%?30?%}.form-label[data-v-49cd79a6]{font-size:%?28?%;color:#333;margin-bottom:%?16?%;display:block}.password-input-container[data-v-49cd79a6]{display:flex;align-items:center;border:%?1?% solid #e0e0e0;border-radius:%?8?%;padding:0 %?24?%;background-color:#fff;height:%?80?%}.form-input[data-v-49cd79a6]{flex:1;height:%?80?%;font-size:%?28?%}.password-toggle-icon[data-v-49cd79a6]{padding:%?20?%;flex-shrink:0;display:flex;align-items:center;justify-content:center}.form-error[data-v-49cd79a6]{font-size:%?24?%;color:#ef4444;margin-top:%?10?%}.password-strength[data-v-49cd79a6]{display:flex;align-items:center;margin:%?30?% 0}.strength-label[data-v-49cd79a6]{font-size:%?26?%;color:#666;margin-right:%?20?%}.strength-indicator-container[data-v-49cd79a6]{flex:1;height:%?8?%;background-color:#f0f0f0;border-radius:%?4?%;overflow:hidden;margin-right:%?20?%}.strength-indicator[data-v-49cd79a6]{height:100%;border-radius:%?4?%}.strength-indicator.weak[data-v-49cd79a6]{width:30%;background-color:#ef4444}.strength-indicator.medium[data-v-49cd79a6]{width:60%;background-color:#f59e0b}.strength-indicator.strong[data-v-49cd79a6]{width:100%;background-color:#10b981}.strength-text[data-v-49cd79a6]{font-size:%?26?%;font-weight:700}.strength-text.weak[data-v-49cd79a6]{color:#ef4444}.strength-text.medium[data-v-49cd79a6]{color:#f59e0b}.strength-text.strong[data-v-49cd79a6]{color:#10b981}.password-tips[data-v-49cd79a6]{background-color:#f9fafb;border-radius:%?8?%;padding:%?20?%;margin:%?30?% 0}.tips-title[data-v-49cd79a6]{font-size:%?26?%;color:#666;margin-bottom:%?16?%;display:block}.tips-item[data-v-49cd79a6]{display:flex;align-items:center;margin-bottom:%?12?%;font-size:%?24?%;color:#999}.tips-item svg-icon[data-v-49cd79a6]{margin-right:%?10?%;flex-shrink:0}.tips-item.fulfilled[data-v-49cd79a6]{color:#333}.submit-btn[data-v-49cd79a6]{background-color:#4a6fff;color:#fff;border-radius:%?8?%;margin-top:%?40?%;padding:%?24?% 0;font-size:%?32?%;width:100%}.submit-btn[data-v-49cd79a6]:disabled{background-color:#ccc;color:#fff}.forgot-password[data-v-49cd79a6]{text-align:center;margin-top:%?30?%;font-size:%?26?%;color:#666}.forgot-link[data-v-49cd79a6]{color:#4a6fff;margin-left:%?10?%}",""]),s.exports=t},"75db":function(s,t,e){"use strict";e.d(t,"b",(function(){return r})),e.d(t,"c",(function(){return i})),e.d(t,"a",(function(){return a}));var a={svgIcon:e("8a0f").default},r=function(){var s=this,t=s.$createElement,e=s._self._c||t;return e("v-uni-view",{staticClass:"container"},[e("v-uni-view",{staticClass:"page-header"},[e("v-uni-view",{staticClass:"flex-row items-center gap-sm"},[e("v-uni-text",{staticClass:"page-title"},[s._v("修改密码")])],1)],1),e("v-uni-view",{staticClass:"page-content"},[e("v-uni-view",{staticClass:"password-icon-container"},[e("v-uni-view",{staticClass:"password-icon"},[e("svg-icon",{attrs:{name:"password",type:"svg",size:"80",color:"#4a6fff"}})],1)],1),e("v-uni-view",{staticClass:"form-container"},[e("v-uni-view",{staticClass:"form-item"},[e("v-uni-text",{staticClass:"form-label"},[s._v("当前密码")]),e("v-uni-view",{staticClass:"password-input-container"},["checkbox"===(s.currentPasswordVisible?"text":"password")?e("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入当前密码",type:"checkbox"},model:{value:s.currentPassword,callback:function(t){s.currentPassword=t},expression:"currentPassword"}}):"radio"===(s.currentPasswordVisible?"text":"password")?e("input",{directives:[{name:"model",rawName:"v-model",value:s.currentPassword,expression:"currentPassword"}],staticClass:"form-input",attrs:{placeholder:"请输入当前密码",type:"radio"},domProps:{checked:s._q(s.currentPassword,null)},on:{change:function(t){s.currentPassword=null}}}):e("input",{directives:[{name:"model",rawName:"v-model",value:s.currentPassword,expression:"currentPassword"}],staticClass:"form-input",attrs:{placeholder:"请输入当前密码",type:s.currentPasswordVisible?"text":"password"},domProps:{value:s.currentPassword},on:{input:function(t){t.target.composing||(s.currentPassword=t.target.value)}}}),e("v-uni-view",{staticClass:"password-toggle-icon",on:{click:function(t){arguments[0]=t=s.$handleEvent(t),s.toggleCurrentPasswordVisibility.apply(void 0,arguments)}}},[e("svg-icon",{attrs:{name:s.currentPasswordVisible?"eye":"eye-off",type:"svg",size:"40",color:"#999999"}})],1)],1),s.errors.currentPassword?e("v-uni-text",{staticClass:"form-error"},[s._v(s._s(s.errors.currentPassword))]):s._e()],1),e("v-uni-view",{staticClass:"form-item"},[e("v-uni-text",{staticClass:"form-label"},[s._v("新密码")]),e("v-uni-view",{staticClass:"password-input-container"},["checkbox"===(s.newPasswordVisible?"text":"password")?e("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入新密码",type:"checkbox"},model:{value:s.newPassword,callback:function(t){s.newPassword=t},expression:"newPassword"}}):"radio"===(s.newPasswordVisible?"text":"password")?e("input",{directives:[{name:"model",rawName:"v-model",value:s.newPassword,expression:"newPassword"}],staticClass:"form-input",attrs:{placeholder:"请输入新密码",type:"radio"},domProps:{checked:s._q(s.newPassword,null)},on:{change:function(t){s.newPassword=null}}}):e("input",{directives:[{name:"model",rawName:"v-model",value:s.newPassword,expression:"newPassword"}],staticClass:"form-input",attrs:{placeholder:"请输入新密码",type:s.newPasswordVisible?"text":"password"},domProps:{value:s.newPassword},on:{input:function(t){t.target.composing||(s.newPassword=t.target.value)}}}),e("v-uni-view",{staticClass:"password-toggle-icon",on:{click:function(t){arguments[0]=t=s.$handleEvent(t),s.toggleNewPasswordVisibility.apply(void 0,arguments)}}},[e("svg-icon",{attrs:{name:s.newPasswordVisible?"eye":"eye-off",type:"svg",size:"40",color:"#999999"}})],1)],1),s.errors.newPassword?e("v-uni-text",{staticClass:"form-error"},[s._v(s._s(s.errors.newPassword))]):s._e()],1),e("v-uni-view",{staticClass:"form-item"},[e("v-uni-text",{staticClass:"form-label"},[s._v("确认新密码")]),e("v-uni-view",{staticClass:"password-input-container"},["checkbox"===(s.confirmPasswordVisible?"text":"password")?e("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请再次输入新密码",type:"checkbox"},model:{value:s.confirmPassword,callback:function(t){s.confirmPassword=t},expression:"confirmPassword"}}):"radio"===(s.confirmPasswordVisible?"text":"password")?e("input",{directives:[{name:"model",rawName:"v-model",value:s.confirmPassword,expression:"confirmPassword"}],staticClass:"form-input",attrs:{placeholder:"请再次输入新密码",type:"radio"},domProps:{checked:s._q(s.confirmPassword,null)},on:{change:function(t){s.confirmPassword=null}}}):e("input",{directives:[{name:"model",rawName:"v-model",value:s.confirmPassword,expression:"confirmPassword"}],staticClass:"form-input",attrs:{placeholder:"请再次输入新密码",type:s.confirmPasswordVisible?"text":"password"},domProps:{value:s.confirmPassword},on:{input:function(t){t.target.composing||(s.confirmPassword=t.target.value)}}}),e("v-uni-view",{staticClass:"password-toggle-icon",on:{click:function(t){arguments[0]=t=s.$handleEvent(t),s.toggleConfirmPasswordVisibility.apply(void 0,arguments)}}},[e("svg-icon",{attrs:{name:s.confirmPasswordVisible?"eye":"eye-off",type:"svg",size:"40",color:"#999999"}})],1)],1),s.errors.confirmPassword?e("v-uni-text",{staticClass:"form-error"},[s._v(s._s(s.errors.confirmPassword))]):s._e()],1),s.newPassword?e("v-uni-view",{staticClass:"password-strength"},[e("v-uni-text",{staticClass:"strength-label"},[s._v("密码强度：")]),e("v-uni-view",{staticClass:"strength-indicator-container"},[e("v-uni-view",{staticClass:"strength-indicator",class:s.strengthClass})],1),e("v-uni-text",{staticClass:"strength-text",class:s.strengthClass},[s._v(s._s(s.strengthText))])],1):s._e(),e("v-uni-view",{staticClass:"password-tips"},[e("v-uni-text",{staticClass:"tips-title"},[s._v("密码要求：")]),e("v-uni-view",{staticClass:"tips-item",class:{fulfilled:s.hasValidLength}},[e("svg-icon",{attrs:{name:s.hasValidLength?"check":"close",type:"svg",size:"28",color:s.iconColorLength}}),e("v-uni-text",[s._v("长度为8-20个字符")])],1),e("v-uni-view",{staticClass:"tips-item",class:{fulfilled:s.hasLowerCase}},[e("svg-icon",{attrs:{name:s.hasLowerCase?"check":"close",type:"svg",size:"28",color:s.iconColorLower}}),e("v-uni-text",[s._v("至少包含1个小写字母")])],1),e("v-uni-view",{staticClass:"tips-item",class:{fulfilled:s.hasUpperCase}},[e("svg-icon",{attrs:{name:s.hasUpperCase?"check":"close",type:"svg",size:"28",color:s.iconColorUpper}}),e("v-uni-text",[s._v("至少包含1个大写字母")])],1),e("v-uni-view",{staticClass:"tips-item",class:{fulfilled:s.hasNumber}},[e("svg-icon",{attrs:{name:s.hasNumber?"check":"close",type:"svg",size:"28",color:s.iconColorNumber}}),e("v-uni-text",[s._v("至少包含1个数字")])],1),e("v-uni-view",{staticClass:"tips-item",class:{fulfilled:s.hasSpecialChar}},[e("svg-icon",{attrs:{name:s.hasSpecialChar?"check":"close",type:"svg",size:"28",color:s.iconColorSpecial}}),e("v-uni-text",[s._v("至少包含1个特殊字符")])],1)],1),e("v-uni-button",{staticClass:"submit-btn",attrs:{disabled:!s.isFormValid},on:{click:function(t){arguments[0]=t=s.$handleEvent(t),s.changePassword.apply(void 0,arguments)}}},[s._v("修改密码")]),e("v-uni-view",{staticClass:"forgot-password"},[e("v-uni-text",[s._v("忘记当前密码？")]),e("v-uni-text",{staticClass:"forgot-link",on:{click:function(t){arguments[0]=t=s.$handleEvent(t),s.navigateToResetPassword.apply(void 0,arguments)}}},[s._v("通过邮箱重置")])],1)],1)],1)],1)},i=[]},"782c":function(s,t,e){"use strict";e.r(t);var a=e("75db"),r=e("32f8");for(var i in r)["default"].indexOf(i)<0&&function(s){e.d(t,s,(function(){return r[s]}))}(i);e("a6c7");var o=e("828b"),n=Object(o["a"])(r["default"],a["b"],a["c"],!1,null,"49cd79a6",null,!1,a["a"],void 0);t["default"]=n.exports},a6c7:function(s,t,e){"use strict";var a=e("0454"),r=e.n(a);r.a}}]);
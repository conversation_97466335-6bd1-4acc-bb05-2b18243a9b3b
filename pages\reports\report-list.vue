<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="flex-row items-center gap-sm">
        <text class="page-title">报表中心</text>
      </view>
      <view class="header-actions">
        <button class="action-button" @click="shareReport">
          <text class="ri-share-line"></text>
        </button>
      </view>
    </view>

    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <view class="search-icon">
          <text class="ri-search-line"></text>
        </view>
        <input type="text" class="search-input" v-model="searchKeyword" placeholder="搜索报表" />
      </view>
    </view>

    <!-- 报表分类 -->
    <view class="category-tabs">
      <view 
        v-for="(tab, index) in categoryTabs" 
        :key="index"
        class="category-tab" 
        :class="{ active: activeCategory === tab.value }"
        @click="switchCategory(tab.value)"
      >
        {{ tab.name }}
      </view>
    </view>

    <!-- 报表列表 -->
    <scroll-view class="reports-list" scroll-y>
      <!-- 常用报表 -->
      <view class="section-header" v-if="activeCategory === 'all' || activeCategory === 'frequent'">
        <text class="section-title">常用报表</text>
      </view>
      <view class="reports-grid" v-if="activeCategory === 'all' || activeCategory === 'frequent'">
        <navigator 
          v-for="(report, index) in frequentReports" 
          :key="index"
          :url="report.url"
          class="report-card"
        >
          <view class="report-icon" :style="{ backgroundColor: report.bgColor }">
            <text :class="report.icon"></text>
          </view>
          <view class="report-details">
            <text class="report-name">{{ report.name }}</text>
            <text class="report-desc">{{ report.description }}</text>
          </view>
        </navigator>
      </view>

      <!-- 销售报表 -->
      <view class="section-header" v-if="activeCategory === 'all' || activeCategory === 'sales'">
        <text class="section-title">销售报表</text>
      </view>
      <view class="reports-grid" v-if="activeCategory === 'all' || activeCategory === 'sales'">
        <navigator 
          v-for="(report, index) in salesReports" 
          :key="index"
          :url="report.url"
          class="report-card"
        >
          <view class="report-icon" :style="{ backgroundColor: report.bgColor }">
            <text :class="report.icon"></text>
          </view>
          <view class="report-details">
            <text class="report-name">{{ report.name }}</text>
            <text class="report-desc">{{ report.description }}</text>
          </view>
        </navigator>
      </view>

      <!-- 客户报表 -->
      <view class="section-header" v-if="activeCategory === 'all' || activeCategory === 'customer'">
        <text class="section-title">客户报表</text>
      </view>
      <view class="reports-grid" v-if="activeCategory === 'all' || activeCategory === 'customer'">
        <navigator 
          v-for="(report, index) in customerReports" 
          :key="index"
          :url="report.url"
          class="report-card"
        >
          <view class="report-icon" :style="{ backgroundColor: report.bgColor }">
            <text :class="report.icon"></text>
          </view>
          <view class="report-details">
            <text class="report-name">{{ report.name }}</text>
            <text class="report-desc">{{ report.description }}</text>
          </view>
        </navigator>
      </view>

      <!-- 团队报表 -->
      <view class="section-header" v-if="activeCategory === 'all' || activeCategory === 'team'">
        <text class="section-title">团队报表</text>
      </view>
      <view class="reports-grid" v-if="activeCategory === 'all' || activeCategory === 'team'">
        <navigator 
          v-for="(report, index) in teamReports" 
          :key="index"
          :url="report.url"
          class="report-card"
        >
          <view class="report-icon" :style="{ backgroundColor: report.bgColor }">
            <text :class="report.icon"></text>
          </view>
          <view class="report-details">
            <text class="report-name">{{ report.name }}</text>
            <text class="report-desc">{{ report.description }}</text>
          </view>
        </navigator>
      </view>

      <!-- 自定义报表 -->
      <view class="section-header" v-if="activeCategory === 'all' || activeCategory === 'custom'">
        <text class="section-title">自定义报表</text>
        <navigator url="/pages/reports/custom-reports" class="add-custom-report">
          <text class="ri-add-line"></text> 创建
        </navigator>
      </view>
      <view class="reports-grid" v-if="(activeCategory === 'all' || activeCategory === 'custom') && customReports.length > 0">
        <navigator 
          v-for="(report, index) in customReports" 
          :key="index"
          :url="report.url"
          class="report-card"
        >
          <view class="report-icon" :style="{ backgroundColor: report.bgColor }">
            <text :class="report.icon"></text>
          </view>
          <view class="report-details">
            <text class="report-name">{{ report.name }}</text>
            <text class="report-desc">{{ report.description }}</text>
          </view>
        </navigator>
      </view>
      <view class="empty-custom" v-if="(activeCategory === 'all' || activeCategory === 'custom') && customReports.length === 0">
        <view class="empty-icon">
          <text class="ri-file-chart-line"></text>
        </view>
        <text class="empty-text">暂无自定义报表</text>
        <navigator url="/pages/reports/custom-reports" class="empty-action">
          <text>创建自定义报表</text>
        </navigator>
      </view>
    </scroll-view>

    <!-- 底部标签栏 -->
    <custom-tab-bar :active="4"></custom-tab-bar>
  </view>
</template>

<script>
import CustomTabBar from '@/components/CustomTabBar.vue'

export default {
  components: {
    CustomTabBar
  },
  data() {
    return {
      searchKeyword: '',
      activeCategory: 'all',
      categoryTabs: [
        { name: '全部', value: 'all' },
        { name: '常用', value: 'frequent' },
        { name: '销售', value: 'sales' },
        { name: '客户', value: 'customer' },
        { name: '团队', value: 'team' },
        { name: '自定义', value: 'custom' }
      ],
      frequentReports: [
        {
          name: '销售报表',
          description: '查看销售趋势、渠道分析和产品销售情况',
          icon: 'ri-bar-chart-2-line',
          bgColor: '#4a6fff20',
          url: '/pages/reports/sales-reports'
        },
        {
          name: '团队绩效',
          description: '团队销售业绩和个人绩效排名',
          icon: 'ri-team-line',
          bgColor: '#00b5ad20',
          url: '/pages/reports/team-performance'
        },
        {
          name: '客户分析',
          description: '客户分布、行为和价值分析',
          icon: 'ri-user-search-line',
          bgColor: '#f2711c20',
          url: '/pages/reports/customer-analytics'
        }
      ],
      salesReports: [
        {
          name: '销售报表',
          description: '查看销售趋势、渠道分析和产品销售情况',
          icon: 'ri-bar-chart-2-line',
          bgColor: '#4a6fff20',
          url: '/pages/reports/sales-reports'
        },
        {
          name: '销售漏斗',
          description: '商机转换率和销售流程分析',
          icon: 'ri-filter-3-line',
          bgColor: '#6435c920',
          url: '/pages/reports/sales-reports?type=funnel'
        },
        {
          name: '产品销售分析',
          description: '各产品销售情况和趋势分析',
          icon: 'ri-shopping-bag-line',
          bgColor: '#db273620',
          url: '/pages/reports/sales-reports?type=product'
        },
        {
          name: '销售预测',
          description: '未来销售预测和目标达成分析',
          icon: 'ri-line-chart-line',
          bgColor: '#21ba4520',
          url: '/pages/reports/sales-reports?type=forecast'
        }
      ],
      customerReports: [
        {
          name: '客户分析',
          description: '客户分布、行为和价值分析',
          icon: 'ri-user-search-line',
          bgColor: '#f2711c20',
          url: '/pages/reports/customer-analytics'
        },
        {
          name: '客户生命周期',
          description: '客户从获取到忠诚的全流程分析',
          icon: 'ri-recycle-line',
          bgColor: '#a333c820',
          url: '/pages/reports/customer-analytics?type=lifecycle'
        },
        {
          name: '客户满意度',
          description: '客户反馈和满意度分析',
          icon: 'ri-emotion-happy-line',
          bgColor: '#e0374520',
          url: '/pages/reports/customer-analytics?type=satisfaction'
        }
      ],
      teamReports: [
        {
          name: '团队绩效',
          description: '团队销售业绩和个人绩效排名',
          icon: 'ri-team-line',
          bgColor: '#00b5ad20',
          url: '/pages/reports/team-performance'
        },
        {
          name: '销售活动分析',
          description: '团队活动效率和转化率分析',
          icon: 'ri-calendar-check-line',
          bgColor: '#2185d020',
          url: '/pages/reports/team-performance?type=activities'
        }
      ],
      customReports: []
    }
  },
  computed: {
    filteredFrequentReports() {
      if (!this.searchKeyword) return this.frequentReports;
      return this.frequentReports.filter(report => 
        report.name.includes(this.searchKeyword) || 
        report.description.includes(this.searchKeyword)
      );
    },
    filteredSalesReports() {
      if (!this.searchKeyword) return this.salesReports;
      return this.salesReports.filter(report => 
        report.name.includes(this.searchKeyword) || 
        report.description.includes(this.searchKeyword)
      );
    },
    filteredCustomerReports() {
      if (!this.searchKeyword) return this.customerReports;
      return this.customerReports.filter(report => 
        report.name.includes(this.searchKeyword) || 
        report.description.includes(this.searchKeyword)
      );
    },
    filteredTeamReports() {
      if (!this.searchKeyword) return this.teamReports;
      return this.teamReports.filter(report => 
        report.name.includes(this.searchKeyword) || 
        report.description.includes(this.searchKeyword)
      );
    },
    filteredCustomReports() {
      if (!this.searchKeyword) return this.customReports;
      return this.customReports.filter(report => 
        report.name.includes(this.searchKeyword) || 
        report.description.includes(this.searchKeyword)
      );
    }
  },
  methods: {
    switchCategory(category) {
      this.activeCategory = category;
    },
    shareReport() {
      uni.showActionSheet({
        itemList: ['分享到微信', '发送邮件', '导出PDF'],
        success: (res) => {
          uni.showToast({
            title: '分享功能开发中',
            icon: 'none'
          });
        }
      });
    },
    loadCustomReports() {
      // 这里应该是从API加载用户自定义的报表
      // 这里仅使用模拟数据
      this.customReports = [];
    }
  },
  onLoad() {
    this.loadCustomReports();
  },
  onShow() {
    // 可以在这里刷新数据
  }
}
</script>

<style>
.container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.page-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.action-button {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  border-radius: 50%;
  border: none;
  padding: 0;
  line-height: 1;
}

.action-button text {
  font-size: 40rpx;
  color: #333;
}

.search-container {
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.search-box {
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
}

.search-icon {
  margin-right: 20rpx;
}

.search-icon text {
  font-size: 40rpx;
  color: #999;
}

.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
}

.category-tabs {
  display: flex;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  overflow-x: auto;
  white-space: nowrap;
}

.category-tab {
  padding: 10rpx 30rpx;
  margin-right: 20rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f5f5f5;
}

.category-tab.active {
  background-color: #4a6fff;
  color: #fff;
}

.reports-list {
  flex: 1;
  overflow-y: auto;
}

.section-header {
  padding: 30rpx 30rpx 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.add-custom-report {
  font-size: 28rpx;
  color: #4a6fff;
  display: flex;
  align-items: center;
}

.add-custom-report text {
  font-size: 32rpx;
  margin-right: 6rpx;
}

.reports-grid {
  padding: 0 20rpx;
  display: flex;
  flex-wrap: wrap;
}

.report-card {
  width: calc(50% - 20rpx);
  margin: 10rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  border: 1px solid #e0e0e0;
}

.report-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.report-icon text {
  font-size: 40rpx;
  color: #4a6fff;
}

.report-details {
  flex: 1;
}

.report-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 6rpx;
}

.report-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.3;
}

.empty-custom {
  padding: 60rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  font-size: 100rpx;
  color: #ccc;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.empty-action {
  padding: 20rpx 60rpx;
  background-color: #4a6fff;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
}
</style> 
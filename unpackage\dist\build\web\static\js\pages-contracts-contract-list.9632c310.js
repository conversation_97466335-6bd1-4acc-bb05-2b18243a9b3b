(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-contracts-contract-list"],{"0342":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return r}));var r={svgIcon:a("8a0f").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"container",style:t.cssVars},[a("v-uni-scroll-view",{staticClass:"tabs-container",attrs:{"scroll-x":!0}},t._l(t.contractStatuses,(function(e,r){return a("v-uni-view",{key:r,staticClass:"tab",class:{active:t.filters.status===e.value},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.setStatusFilter(e.value)}}},[t._v(t._s(e.label))])})),1),a("v-uni-view",{staticClass:"search-container"},[a("v-uni-view",{staticClass:"search-box"},[a("v-uni-view",{staticClass:"search-icon"},[a("svg-icon",{attrs:{name:"search",type:"svg",size:"32"}})],1),a("v-uni-input",{staticClass:"search-input",attrs:{type:"text",placeholder:"搜索合同名称/客户"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.onSearchInput.apply(void 0,arguments)}},model:{value:t.searchQuery,callback:function(e){t.searchQuery=e},expression:"searchQuery"}})],1)],1),a("v-uni-view",{staticClass:"filter-bar"},[a("v-uni-button",{staticClass:"filter-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toggleFilterPanel.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"filter",type:"svg",size:"28"}}),a("v-uni-text",[t._v("筛选")])],1),a("v-uni-button",{staticClass:"sort-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showSortOptions.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"sort",type:"svg",size:"28"}}),a("v-uni-text",[t._v("排序")])],1)],1),a("v-uni-view",{staticClass:"contracts-list"},t._l(t.contracts,(function(e,r){return a("v-uni-view",{key:r,staticClass:"contract-card",attrs:{"data-status":e.contractStatus.code}},[a("v-uni-view",{staticClass:"card-overlay",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.navigateToContractDetail(e.id)}}}),a("v-uni-view",{staticClass:"contract-header"},[a("v-uni-view",{staticClass:"contract-info"},[a("v-uni-view",{staticClass:"contract-name",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.navigateToContractDetail(e.id)}}},[t._v(t._s(e.name))]),a("v-uni-view",{staticClass:"contract-tags"},[a("v-uni-text",{staticClass:"tag",class:"tag-"+e.contractStatus.code},[t._v(t._s(t.getStatusLabel(e.contractStatus.code)))]),a("v-uni-text",{staticClass:"tag tag-company"},[t._v(t._s(e.customName))])],1)],1),a("v-uni-view",{staticClass:"contract-value"},[t._v("¥"+t._s(t.formatMoney(e.amount)))])],1),a("v-uni-view",{staticClass:"contract-content"},[a("v-uni-view",{staticClass:"contract-details"},[a("v-uni-view",{staticClass:"detail-item"},[a("v-uni-view",{staticClass:"detail-label"},[t._v("签订日期:")]),a("v-uni-view",{staticClass:"detail-value"},[t._v(t._s(t._f("formatDateFilter")(e.creationTime)))])],1),a("v-uni-view",{staticClass:"detail-item"},[a("v-uni-view",{staticClass:"detail-label"},[t._v("负责人:")]),a("v-uni-view",{staticClass:"detail-value"},[t._v(t._s(e.owner))])],1),a("v-uni-view",{staticClass:"detail-item"},[a("v-uni-view",{staticClass:"detail-label"},[t._v("关联商机:")]),a("v-uni-view",{staticClass:"detail-value"},[t._v(t._s(e.opportunityName||"无"))])],1),a("v-uni-view",{staticClass:"detail-item"},[a("v-uni-view",{staticClass:"detail-label"},[t._v("客户:")]),a("v-uni-view",{staticClass:"detail-value"},[t._v(t._s(e.customName))])],1)],1)],1),a("v-uni-view",{staticClass:"contract-footer"},[a("v-uni-view",{staticClass:"contract-action",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.viewContractFiles(e.id)}}},[a("svg-icon",{attrs:{name:"file-list",type:"svg",size:"28"}}),t._v("查看附件")],1),a("v-uni-view",{staticClass:"contract-action",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.navigateToPaymentRecords(e.id)}}},[a("svg-icon",{attrs:{name:"money-dollar-circle",type:"svg",size:"28"}}),t._v("收款记录")],1),a("v-uni-view",{staticClass:"contract-action",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.editContract(e.id)}}},[a("svg-icon",{attrs:{name:"edit",type:"svg",size:"28"}}),t._v("编辑")],1)],1)],1)})),1),t.showFilterPanel?a("v-uni-view",{staticClass:"modal-filter"},[a("v-uni-view",{staticClass:"modal-mask",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toggleFilterPanel.apply(void 0,arguments)}}}),a("v-uni-view",{staticClass:"modal-dialog"},[a("v-uni-view",{staticClass:"modal-header"},[a("v-uni-view",{staticClass:"modal-title"},[t._v("筛选条件")]),a("v-uni-view",{staticClass:"modal-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toggleFilterPanel.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"close",type:"svg",size:"32"}})],1)],1),a("v-uni-scroll-view",{staticClass:"modal-content",attrs:{"scroll-y":!0}},[a("v-uni-view",{staticClass:"filter-group"},[a("v-uni-text",{staticClass:"filter-label"},[t._v("合同状态")]),a("v-uni-view",{staticClass:"checkbox-grid"},t._l(t.contractStatuses.slice(1),(function(e,r){return a("v-uni-view",{key:r,class:["status-option",{active:t.filters.status===e.value}],on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.setStatusFilter(e.value)}}},[t._v(t._s(e.label))])})),1)],1),a("v-uni-view",{staticClass:"filter-group"},[a("v-uni-text",{staticClass:"filter-label"},[t._v("时间范围")]),a("v-uni-view",{staticClass:"date-range"},[a("v-uni-view",{staticClass:"date-picker",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showStartDatePicker.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"date-value"},[t._v(t._s(t.filters.dateRange.start||"开始日期"))]),a("v-uni-view",{staticClass:"date-icon"},[a("svg-icon",{attrs:{name:"calendar",type:"svg",size:"24"}})],1)],1),a("v-uni-view",{staticClass:"date-separator"},[t._v("至")]),a("v-uni-view",{staticClass:"date-picker",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showEndDatePicker.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"date-value"},[t._v(t._s(t.filters.dateRange.end||"结束日期"))]),a("v-uni-view",{staticClass:"date-icon"},[a("svg-icon",{attrs:{name:"calendar",type:"svg",size:"24"}})],1)],1)],1)],1)],1),a("v-uni-view",{staticClass:"modal-footer"},[a("v-uni-button",{staticClass:"btn btn-reset",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.resetFilters.apply(void 0,arguments)}}},[t._v("重置")]),a("v-uni-button",{staticClass:"btn btn-confirm",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.applyFilters.apply(void 0,arguments)}}},[t._v("应用")])],1)],1)],1):t._e(),a("v-uni-view",{staticClass:"fab",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.navigateToCreateContract.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"add",type:"svg",size:"60",color:"#FFFFFF"}})],1),a("custom-tab-bar",{ref:"customTabBar"})],1)},o=[]},"0e21":function(t,e,a){var r=a("c86c");e=r(!1),e.push([t.i,'.container[data-v-1106ebae]{background-color:#f8fafc;min-height:100vh}.tabs-container[data-v-1106ebae]{display:flex;white-space:nowrap;background-color:#fff;border-bottom:%?1?% solid var(--border-color);position:-webkit-sticky;position:sticky;top:0;z-index:10;box-shadow:0 %?2?% %?8?% rgba(0,0,0,.05)}.tab[data-v-1106ebae]{display:inline-block;padding:var(--spacing-md) var(--spacing-lg);color:var(--text-secondary);font-size:%?28?%;position:relative;transition:all .2s ease}.tab.active[data-v-1106ebae]{color:var(--primary-color);font-weight:600}.tab.active[data-v-1106ebae]::after{content:"";position:absolute;bottom:0;left:var(--spacing-lg);right:var(--spacing-lg);height:%?4?%;background-color:var(--primary-color);border-radius:var(--radius-full)}\n\n/* 搜索栏样式 */.search-container[data-v-1106ebae]{padding:var(--spacing-md) var(--spacing-lg);background-color:#fff;border-bottom:%?1?% solid var(--border-color);box-shadow:0 %?2?% %?8?% rgba(0,0,0,.02)}.search-box[data-v-1106ebae]{display:flex;align-items:center;background-color:var(--light-color);border:%?1?% solid var(--border-color);border-radius:var(--radius-lg);padding:0 var(--spacing-sm);overflow:hidden;box-shadow:inset 0 %?2?% %?5?% rgba(0,0,0,.03)}.search-icon[data-v-1106ebae]{color:var(--text-secondary);padding:var(--spacing-xs)}.search-input[data-v-1106ebae]{flex:1;border:none;padding:var(--spacing-sm);background-color:initial;color:var(--text-primary);font-size:%?28?%}\n\n/* 筛选栏样式 */.filter-bar[data-v-1106ebae]{display:flex;align-items:center;justify-content:space-between;padding:var(--spacing-md) var(--spacing-lg);background-color:#fff;border-bottom:%?1?% solid var(--border-color)}.filter-button[data-v-1106ebae],\n.sort-button[data-v-1106ebae]{display:flex;align-items:center;font-size:%?28?%;color:var(--text-secondary);padding:%?16?% %?24?%;border:%?1?% solid var(--border-color);border-radius:var(--radius-md);background-color:var(--light-color);transition:all .2s ease}.filter-button[data-v-1106ebae]:active,\n.sort-button[data-v-1106ebae]:active{background-color:rgba(var(--primary-color-rgb),.1);color:var(--primary-color);border-color:var(--primary-color-light)}.filter-button uni-text[data-v-1106ebae],\n.sort-button uni-text[data-v-1106ebae]{margin-left:%?8?%}\n\n/* 合同列表和卡片样式 */.contracts-list[data-v-1106ebae]{padding:var(--spacing-md) var(--spacing-lg);padding-bottom:calc(var(--spacing-xl) * 4) /* 增加底部填充空间 */}.contract-card[data-v-1106ebae]{background-color:#fff;border-radius:var(--radius-md);box-shadow:0 %?4?% %?12?% rgba(0,0,0,.08);margin-bottom:var(--spacing-md);overflow:hidden;border:%?1?% solid var(--border-color);position:relative;transition:all .3s ease}.contract-card[data-v-1106ebae]:active{-webkit-transform:scale(.98);transform:scale(.98);box-shadow:0 %?2?% %?8?% rgba(0,0,0,.05)}\n\n/* 合同状态标识条 */.contract-card[data-v-1106ebae]::before{content:"";position:absolute;left:0;top:0;bottom:0;width:%?6?%;background-color:var(--border-color);z-index:2}.contract-card[data-status="Draft"][data-v-1106ebae]::before{background-color:#9ca3af}.contract-card[data-status="Signed"][data-v-1106ebae]::before{background-color:#f59e0b}.contract-card[data-status="Closed"][data-v-1106ebae]::before{background-color:#3b82f6}.contract-card[data-status="Completed"][data-v-1106ebae]::before{background-color:#10b981}.contract-card[data-status="Cancelled"][data-v-1106ebae]::before{background-color:#ef4444}.card-overlay[data-v-1106ebae]{position:absolute;top:0;left:0;right:0;bottom:0;z-index:1}.contract-header[data-v-1106ebae],\n.contract-content[data-v-1106ebae],\n.contract-footer[data-v-1106ebae]{position:relative;z-index:2}\n\n/* 确保所有可点击元素在overlay之上 */.contract-name[data-v-1106ebae],\n.contract-action[data-v-1106ebae]{position:relative;z-index:3}.contract-header[data-v-1106ebae]{padding:var(--spacing-md);border-bottom:%?1?% solid var(--border-color-light);display:flex;justify-content:space-between;align-items:flex-start;background:linear-gradient(90deg,rgba(249,250,251,.5),hsla(0,0%,100%,.8))}.contract-info[data-v-1106ebae]{flex:1}.contract-name[data-v-1106ebae]{font-size:%?32?%;font-weight:600;margin:0 0 %?12?% 0;color:var(--text-primary);cursor:pointer;position:relative;display:inline-block;padding:%?4?% 0}.contract-name[data-v-1106ebae]:hover,\n.contract-name[data-v-1106ebae]:active{color:var(--primary-color)}.contract-name[data-v-1106ebae]:active:after{content:"";position:absolute;bottom:0;left:0;right:0;height:%?2?%;background-color:var(--primary-color)}.contract-tags[data-v-1106ebae]{display:flex;flex-wrap:wrap;gap:%?8?%;margin-top:var(--spacing-xs)}.tag[data-v-1106ebae]{padding:%?4?% %?16?%;border-radius:var(--radius-full);font-size:%?22?%;font-weight:500}.tag-Draft[data-v-1106ebae]{background-color:#e5e7eb;color:#6b7280}.tag-Signed[data-v-1106ebae]{background-color:#fef3c7;color:#d97706}.tag-Closed[data-v-1106ebae]{background-color:#dbeafe;color:#2563eb}.tag-completed[data-v-1106ebae]{background-color:#d1fae5;color:#059669}.tag-cancelled[data-v-1106ebae]{background-color:#fee2e2;color:#dc2626}.tag-company[data-v-1106ebae]{background-color:#f3f4f6;color:#4b5563}.contract-value[data-v-1106ebae]{font-weight:700;color:var(--primary-color);font-size:%?32?%;background-color:rgba(var(--primary-color-rgb),.1);padding:%?8?% %?16?%;border-radius:var(--radius-md);text-align:right}.contract-content[data-v-1106ebae]{padding:var(--spacing-md)}.contract-details[data-v-1106ebae]{display:grid;grid-template-columns:repeat(2,1fr);gap:var(--spacing-md);background-color:rgba(var(--light-color-rgb),.3);padding:var(--spacing-md) var(--spacing-sm);border-radius:var(--radius-md)}.detail-item[data-v-1106ebae]{display:flex;align-items:baseline;margin-bottom:var(--spacing-xs)}.detail-label[data-v-1106ebae]{font-size:%?24?%;color:var(--text-tertiary);margin-right:%?8?%;white-space:nowrap}.detail-value[data-v-1106ebae]{font-size:%?26?%;color:var(--text-secondary);font-weight:500;flex:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:%?158?%}.contract-footer[data-v-1106ebae]{display:flex;border-top:%?1?% solid var(--border-color);background:linear-gradient(180deg,#fff,#f9fafb)}.contract-action[data-v-1106ebae]{flex:1;display:flex;align-items:center;justify-content:center;padding:%?24?% 0;color:var(--text-secondary);font-size:%?26?%;transition:all .2s ease}.contract-action[data-v-1106ebae]:active{background-color:rgba(var(--primary-color-rgb),.1);color:var(--primary-color)}.contract-action[data-v-1106ebae]:not(:last-child){border-right:%?1?% solid var(--border-color-light)}\n\n/* 浮动操作按钮 */.fab[data-v-1106ebae]{position:fixed;bottom:calc(%?128?% + var(--spacing-xl)); /* 调整底部位置，避开TabBar */right:var(--spacing-xl);width:%?110?%; /* 减小尺寸 */height:%?110?%; /* 减小尺寸 */border-radius:50%;background:linear-gradient(135deg,#0a6bff,#0057ff);color:#fff;display:flex;align-items:center;justify-content:center;box-shadow:0 %?10?% %?20?% rgba(0,87,255,.6),0 %?6?% %?6?% rgba(0,87,255,.4);z-index:100;transition:all .3s ease;border:%?4?% solid hsla(0,0%,100%,.7);-webkit-animation:pulse-data-v-1106ebae 2s infinite;animation:pulse-data-v-1106ebae 2s infinite /* 添加脉动动画 */}.fab[data-v-1106ebae]:active{-webkit-transform:scale(.95);transform:scale(.95);box-shadow:0 %?5?% %?10?% rgba(0,87,255,.5),0 %?3?% %?3?% rgba(0,87,255,.3);-webkit-animation:none;animation:none /* 点击时停止动画 */}\n\n/* 添加脉动动画 */@-webkit-keyframes pulse-data-v-1106ebae{0%{-webkit-transform:scale(1);transform:scale(1);box-shadow:0 %?10?% %?20?% rgba(0,87,255,.6),0 %?6?% %?6?% rgba(0,87,255,.4)}50%{-webkit-transform:scale(1.05);transform:scale(1.05);box-shadow:0 %?15?% %?25?% rgba(0,87,255,.7),0 %?8?% %?10?% rgba(0,87,255,.5)}100%{-webkit-transform:scale(1);transform:scale(1);box-shadow:0 %?10?% %?20?% rgba(0,87,255,.6),0 %?6?% %?6?% rgba(0,87,255,.4)}}@keyframes pulse-data-v-1106ebae{0%{-webkit-transform:scale(1);transform:scale(1);box-shadow:0 %?10?% %?20?% rgba(0,87,255,.6),0 %?6?% %?6?% rgba(0,87,255,.4)}50%{-webkit-transform:scale(1.05);transform:scale(1.05);box-shadow:0 %?15?% %?25?% rgba(0,87,255,.7),0 %?8?% %?10?% rgba(0,87,255,.5)}100%{-webkit-transform:scale(1);transform:scale(1);box-shadow:0 %?10?% %?20?% rgba(0,87,255,.6),0 %?6?% %?6?% rgba(0,87,255,.4)}}\n\n/* 筛选面板样式 */.modal-filter[data-v-1106ebae]{position:fixed;left:0;right:0;top:0;bottom:0;z-index:1001}.modal-mask[data-v-1106ebae]{position:absolute;left:0;right:0;top:0;bottom:0;background-color:rgba(0,0,0,.5)}.modal-dialog[data-v-1106ebae]{position:absolute;left:0;right:0;bottom:0;background-color:#fff;border-radius:%?24?% %?24?% 0 0;max-height:85vh;display:flex;flex-direction:column;overflow:hidden;-webkit-animation:slideUp-data-v-1106ebae .3s ease;animation:slideUp-data-v-1106ebae .3s ease;box-shadow:0 %?-8?% %?24?% rgba(0,0,0,.12)}@-webkit-keyframes slideUp-data-v-1106ebae{from{-webkit-transform:translateY(100%);transform:translateY(100%)}to{-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes slideUp-data-v-1106ebae{from{-webkit-transform:translateY(100%);transform:translateY(100%)}to{-webkit-transform:translateY(0);transform:translateY(0)}}.modal-header[data-v-1106ebae]{padding:%?24?%;border-bottom:%?1?% solid var(--border-color);display:flex;justify-content:space-between;align-items:center}.modal-title[data-v-1106ebae]{font-size:%?32?%;font-weight:600;color:var(--text-primary)}.modal-close[data-v-1106ebae]{width:%?64?%;height:%?64?%;display:flex;align-items:center;justify-content:center;border-radius:50%;color:var(--text-secondary)}.modal-content[data-v-1106ebae]{padding:%?24?%;max-height:60vh;overflow-y:auto;overflow-x:hidden;width:100%;box-sizing:border-box}.modal-footer[data-v-1106ebae]{padding:%?24?%;display:flex;border-top:%?1?% solid var(--border-color-light);background-color:#f9fafb}.filter-group[data-v-1106ebae]{margin-bottom:%?32?%;width:100%;box-sizing:border-box}.filter-label[data-v-1106ebae]{display:block;margin-bottom:%?16?%;font-size:%?28?%;font-weight:500;color:var(--text-secondary)}.checkbox-grid[data-v-1106ebae]{display:flex;flex-wrap:wrap;gap:%?16?%;margin-bottom:%?20?%}.status-option[data-v-1106ebae]{padding:%?12?% %?20?%;border-radius:%?100?%;font-size:%?26?%;background-color:#f5f7fa;color:var(--text-secondary);transition:all .2s ease;border:%?1?% solid transparent;box-sizing:border-box}.status-option.active[data-v-1106ebae]{background-color:rgba(var(--primary-color-rgb),.1);color:var(--primary-color);font-weight:500;border:%?1?% solid var(--primary-color)}.status-option[data-v-1106ebae]:active{-webkit-transform:scale(.95);transform:scale(.95)}.date-range[data-v-1106ebae]{display:flex;align-items:center;gap:%?16?%}.date-picker[data-v-1106ebae]{flex:1;padding:%?20?% %?24?%;background-color:#f5f7fa;border-radius:%?12?%;position:relative;border:%?1?% solid #e0e5ed;height:%?80?%;box-sizing:border-box}.date-value[data-v-1106ebae]{font-size:%?28?%;color:var(--text-primary);padding-right:%?40?%}.date-icon[data-v-1106ebae]{position:absolute;right:%?20?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:var(--text-secondary)}.date-separator[data-v-1106ebae]{font-size:%?24?%;color:var(--text-secondary)}.btn[data-v-1106ebae]{padding:%?20?% %?40?%;border-radius:%?12?%;font-size:%?28?%;flex:1;text-align:center}.btn-reset[data-v-1106ebae]{background-color:#f5f7fa;color:var(--text-secondary);margin-right:%?16?%;border:%?1?% solid #e0e5ed}.btn-reset[data-v-1106ebae]:active{background-color:#e5e7eb}.btn-confirm[data-v-1106ebae]{background-color:var(--primary-color);color:#fff;font-weight:500}.btn-confirm[data-v-1106ebae]:active{background-color:var(--primary-color-dark)}\n\n/* 空状态样式 */.empty-state[data-v-1106ebae]{padding:var(--spacing-xl) var(--spacing-lg);text-align:center;background-color:#fff;border-radius:var(--radius-lg);box-shadow:0 %?4?% %?12?% rgba(0,0,0,.05);margin:var(--spacing-lg) 0}.empty-icon[data-v-1106ebae],\n.loading-icon[data-v-1106ebae]{margin-bottom:var(--spacing-md);color:var(--border-color);display:inline-block}.loading-icon[data-v-1106ebae]{-webkit-animation:rotating-data-v-1106ebae 2s linear infinite;animation:rotating-data-v-1106ebae 2s linear infinite;color:var(--primary-color)}@-webkit-keyframes rotating-data-v-1106ebae{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes rotating-data-v-1106ebae{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}.empty-title[data-v-1106ebae]{font-size:%?32?%;font-weight:600;color:var(--text-primary);margin-bottom:var(--spacing-sm)}.empty-description[data-v-1106ebae]{color:var(--text-secondary);margin-bottom:var(--spacing-xl);line-height:1.5}.btn-primary[data-v-1106ebae]{padding:%?20?% %?40?%;border-radius:%?12?%;font-size:%?28?%;background-color:var(--primary-color);color:#fff;display:inline-flex;align-items:center;justify-content:center}\n\n/* 列表底部加载更多样式 */.loading-more[data-v-1106ebae]{padding:%?24?% 0;display:flex;align-items:center;justify-content:center;color:var(--text-secondary);font-size:%?24?%}.loading-more .loading-indicator[data-v-1106ebae]{width:%?24?%;height:%?24?%;border:%?2?% solid #eee;border-top-color:var(--primary-color);border-radius:50%;-webkit-animation:rotating-data-v-1106ebae .8s linear infinite;animation:rotating-data-v-1106ebae .8s linear infinite;margin-right:%?16?%}.no-more-data[data-v-1106ebae]{text-align:center;padding:%?24?% 0;color:var(--text-tertiary);font-size:%?24?%}',""]),t.exports=e},"104c":function(t,e,a){var r=a("0e21");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var n=a("967d").default;n("0e36dbbb",r,!0,{sourceMap:!1,shadowMode:!1})},"14eb":function(t,e,a){"use strict";a.r(e);var r=a("0342"),n=a("6d57");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("67a4");var i=a("828b"),s=Object(i["a"])(n["default"],r["b"],r["c"],!1,null,"1106ebae",null,!1,r["a"],void 0);e["default"]=s.exports},1880:function(t,e,a){"use strict";a.r(e);var r=a("1a37"),n=a.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);e["default"]=n.a},"1a37":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4626"),a("5ac7"),a("bf0f"),a("bd06");var r={name:"CustomTabBar",data:function(){return{current:0,color:"#333333",activeColor:"#007AFF",showMoreMenu:!1,tabList:[{pagePath:"/pages/dashboard/main-dashboard",text:"首页",iconPath:"dashboard",selectedIconPath:"dashboard"},{pagePath:"/pages/customers/customer-list",text:"客户",iconPath:"customer",selectedIconPath:"customer"},{pagePath:"/pages/sales/opportunity-list",text:"销售",iconPath:"sales",selectedIconPath:"sales"},{type:"more",text:"更多",iconPath:"more",selectedIconPath:"more"},{pagePath:"/pages/settings/profile",text:"我的",iconPath:"user",selectedIconPath:"user"}],moreMenuList:[{pagePath:"/pages/marketing/leads",text:"线索",iconPath:"lead"},{pagePath:"/pages/interactions/interaction-list",text:"沟通",iconPath:"communication"},{pagePath:"/pages/sales/quotation-list",text:"报价",iconPath:"quotation"},{pagePath:"/pages/contracts/contract-list",text:"合同",iconPath:"contract"},{pagePath:"/pages/contracts/invoice-list",text:"发票",iconPath:"file-text"},{pagePath:"/pages/contracts/payment-list",text:"收款",iconPath:"money"},{pagePath:"/pages/reports/report-list",text:"报表",iconPath:"report"}]}},created:function(){this.updateCurrentTab()},onLoad:function(){this.updateCurrentTab()},onShow:function(){var t=this;setTimeout((function(){t.updateCurrentTab()}),100)},methods:{updateCurrentTab:function(){try{var t=getCurrentPages(),e=t[t.length-1];if(!e||!e.route)return;var a=e.route;console.log("当前路由:",a),a.includes("/pages/dashboard/")?this.current=0:a.includes("/pages/customers/")?this.current=1:a.includes("/pages/sales/")?this.current=2:a.includes("/pages/actions/")?this.current=3:a.includes("/pages/settings/")&&(this.current=5)}catch(r){console.error("更新Tab出错:",r)}},handleTabClick:function(t,e){"more"===t.type?(this.toggleMoreMenu(),this.current=e):this.switchTab(t.pagePath,e)},switchTab:function(t,e){this.current!==e&&(this.current=e,uni.switchTab({url:t}))},toggleMoreMenu:function(){this.showMoreMenu=!this.showMoreMenu},closeMoreMenu:function(){this.showMoreMenu=!1},navigateToPage:function(t){var e=this.tabList.some((function(e){return e.pagePath===t}));if(e){uni.switchTab({url:t});var a=this.tabList.findIndex((function(e){return e.pagePath===t}));-1!==a&&(this.current=a)}else uni.navigateTo({url:t});this.closeMoreMenu()}},watch:{$route:{handler:function(){this.updateCurrentTab()},immediate:!0}}};e.default=r},"208c":function(t,e,a){"use strict";a("6a54");var r=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("5c47"),a("a1c1"),a("e966"),a("c223"),a("8f71"),a("bf0f"),a("4626"),a("5ac7"),a("f7a5"),a("aa77"),a("c9b5"),a("ab80");var n=r(a("b7c7")),o=r(a("2634")),i=r(a("2fdc")),s=r(a("eab4")),c=r(a("8a0f")),l=a("d86f"),d={components:{CustomTabBar:s.default,SvgIcon:c.default},computed:{cssVars:function(){var t=this.hexToRgb("#0057ff");return{"--primary-color-rgb":t,"--primary-color-light":"#3a80ff","--primary-color-dark":"#0046cc","--light-color-rgb":"245, 247, 250","--primary-color":"#0057ff"}}},data:function(){return{searchQuery:"",showFilterPanel:!1,contracts:[],loading:!0,hasMoreContracts:!0,page:1,pageSize:10,likeString:void 0,currentDateType:"",contractStatuses:[{label:"草稿",value:"Draft"},{label:"已签署",value:"Signed"},{label:"已关闭",value:"Closed"}],filters:{status:"",dateRange:{start:"",end:""}}}},onReady:function(){var t=uni.getSystemInfoSync(),e=this.showFilterPanel?260:0;this.listHeight=t.windowHeight-60-e-50},onLoad:function(){this.getList()},onShow:function(){"undefined"!==typeof this.$refs.customTabBar&&(this.$refs.customTabBar.current=4)},methods:{getList:function(){var t=this;return(0,i.default)((0,o.default)().mark((function e(){var a,r;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,a={pageIndex:t.page,pageSize:t.pageSize,filter:{}},e.next=4,(0,l.getContactList)(a);case 4:r=e.sent,t.contracts=r.items,e.next=11;break;case 8:e.prev=8,e.t0=e["catch"](0),console.log(e.t0);case 11:case"end":return e.stop()}}),e,null,[[0,8]])})))()},hexToRgb:function(t){t=t.replace(/^#/,"");var e=parseInt(t,16),a=e>>16&255,r=e>>8&255,n=255&e;return"".concat(a,", ").concat(r,", ").concat(n)},onSearchInput:function(){this.debounceSearch()},debounceSearch:function(){var t=this;this.searchTimeout&&clearTimeout(this.searchTimeout),this.searchTimeout=setTimeout((function(){t.page=1,t.contracts=[],t.hasMoreContracts=!0,t.fetchContracts()}),500)},clearSearch:function(){this.searchQuery="",this.page=1,this.contracts=[],this.hasMoreContracts=!0,this.fetchContracts()},toggleFilterPanel:function(){var t=this;this.showFilterPanel=!this.showFilterPanel,setTimeout((function(){t.updateListHeight()}),0)},updateListHeight:function(){var t=uni.getSystemInfoSync(),e=this.showFilterPanel?260:0;this.listHeight=t.windowHeight-60-e-50},setStatusFilter:function(t){this.filters.status=t},onStartDateChange:function(t){this.filters.dateRange.start=t.detail.value},onEndDateChange:function(t){this.filters.dateRange.end=t.detail.value},resetFilters:function(){this.filters={status:"",dateRange:{start:"",end:""}}},applyFilters:function(){this.page=1,this.contracts=[],this.hasMoreContracts=!0,this.fetchContracts(),this.showFilterPanel=!1,this.updateListHeight()},fetchContracts:function(){var t=this;this.loading=!0,setTimeout((function(){var e=[].concat([{id:"1",name:"软件开发服务合同",status:"active",customerName:"上海科技有限公司",amount:15e4,signDate:"2023-10-15",owner:"张经理"},{id:"2",name:"设备采购合同",status:"pending",customerName:"北京电子科技有限公司",amount:23e4,signDate:"2023-09-20",owner:"李经理"},{id:"3",name:"咨询服务协议",status:"completed",customerName:"广州互联网科技有限公司",amount:5e4,signDate:"2023-08-05",owner:"王经理"},{id:"4",name:"市场推广合同",status:"active",customerName:"深圳营销有限公司",amount:8e4,signDate:"2023-11-18",owner:"刘经理"},{id:"5",name:"产品维护协议",status:"draft",customerName:"杭州科技有限公司",amount:35e3,signDate:"2023-12-01",owner:"赵经理"}]);if(t.searchQuery){var a=t.searchQuery.toLowerCase();e=e.filter((function(t){return t.name.toLowerCase().includes(a)||t.customerName.toLowerCase().includes(a)}))}if(t.filters.status&&(e=e.filter((function(e){return e.status===t.filters.status}))),t.filters.dateRange.start){var r=new Date(t.filters.dateRange.start);e=e.filter((function(t){return new Date(t.signDate)>=r}))}if(t.filters.dateRange.end){var o=new Date(t.filters.dateRange.end);e=e.filter((function(t){return new Date(t.signDate)<=o}))}var i=e.slice((t.page-1)*t.pageSize,t.page*t.pageSize);t.contracts=[].concat((0,n.default)(t.contracts),(0,n.default)(i)),t.hasMoreContracts=i.length===t.pageSize,t.loading=!1}),1e3)},loadMoreContracts:function(){this.hasMoreContracts&&!this.loading&&(this.page++,this.fetchContracts())},getStatusLabel:function(t){var e=this.contractStatuses.find((function(e){return e.value===t}));return e?e.label:"未知状态"},formatMoney:function(t){return t.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")},navigateToContractDetail:function(t){uni.navigateTo({url:"/pages/contracts/contract-detail?id=".concat(t)})},navigateToCreateContract:function(){uni.navigateTo({url:"/pages/contracts/contract-create"})},viewContractFiles:function(t){uni.navigateTo({url:"/pages/contracts/contract-files?id=".concat(t)})},navigateToPaymentRecords:function(t){uni.navigateTo({url:"/pages/contracts/payment-records?id=".concat(t)})}}};e.default=d},2634:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
e.default=function(){return t};var t={},a=Object.prototype,n=a.hasOwnProperty,o=Object.defineProperty||function(t,e,a){t[e]=a.value},i="function"==typeof Symbol?Symbol:{},s=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function d(t,e,a){return Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{d({},"")}catch(M){d=function(t,e,a){return t[e]=a}}function u(t,e,a,r){var n=e&&e.prototype instanceof g?e:g,i=Object.create(n.prototype),s=new T(r||[]);return o(i,"_invoke",{value:C(t,a,s)}),i}function f(t,e,a){try{return{type:"normal",arg:t.call(e,a)}}catch(M){return{type:"throw",arg:M}}}t.wrap=u;var v={};function g(){}function b(){}function p(){}var h={};d(h,s,(function(){return this}));var m=Object.getPrototypeOf,y=m&&m(m(S([])));y&&y!==a&&n.call(y,s)&&(h=y);var w=p.prototype=g.prototype=Object.create(h);function x(t){["next","throw","return"].forEach((function(e){d(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){var a;o(this,"_invoke",{value:function(o,i){function s(){return new e((function(a,s){(function a(o,i,s,c){var l=f(t[o],t,i);if("throw"!==l.type){var d=l.arg,u=d.value;return u&&"object"==(0,r.default)(u)&&n.call(u,"__await")?e.resolve(u.__await).then((function(t){a("next",t,s,c)}),(function(t){a("throw",t,s,c)})):e.resolve(u).then((function(t){d.value=t,s(d)}),(function(t){return a("throw",t,s,c)}))}c(l.arg)})(o,i,a,s)}))}return a=a?a.then(s,s):s()}})}function C(t,e,a){var r="suspendedStart";return function(n,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===n)throw o;return z()}for(a.method=n,a.arg=o;;){var i=a.delegate;if(i){var s=_(i,a);if(s){if(s===v)continue;return s}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if("suspendedStart"===r)throw r="completed",a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);r="executing";var c=f(t,e,a);if("normal"===c.type){if(r=a.done?"completed":"suspendedYield",c.arg===v)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(r="completed",a.method="throw",a.arg=c.arg)}}}function _(t,e){var a=e.method,r=t.iterator[a];if(void 0===r)return e.delegate=null,"throw"===a&&t.iterator["return"]&&(e.method="return",e.arg=void 0,_(t,e),"throw"===e.method)||"return"!==a&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+a+"' method")),v;var n=f(r,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,v;var o=n.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,v):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,v)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function L(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function S(t){if(t){var e=t[s];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,r=function e(){for(;++a<t.length;)if(n.call(t,a))return e.value=t[a],e.done=!1,e;return e.value=void 0,e.done=!0,e};return r.next=r}}return{next:z}}function z(){return{value:void 0,done:!0}}return b.prototype=p,o(w,"constructor",{value:p,configurable:!0}),o(p,"constructor",{value:b,configurable:!0}),b.displayName=d(p,l,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,d(t,l,"GeneratorFunction")),t.prototype=Object.create(w),t},t.awrap=function(t){return{__await:t}},x(k.prototype),d(k.prototype,c,(function(){return this})),t.AsyncIterator=k,t.async=function(e,a,r,n,o){void 0===o&&(o=Promise);var i=new k(u(e,a,r,n),o);return t.isGeneratorFunction(a)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},x(w),d(w,l,"Generator"),d(w,s,(function(){return this})),d(w,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),a=[];for(var r in e)a.push(r);return a.reverse(),function t(){for(;a.length;){var r=a.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},t.values=S,T.prototype={constructor:T,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(L),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function a(a,r){return i.type="throw",i.arg=t,e.next=a,r&&(e.method="next",e.arg=void 0),!!r}for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r],i=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var s=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var a=this.tryEntries.length-1;a>=0;--a){var r=this.tryEntries[a];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.finallyLoc===t)return this.complete(a.completion,a.afterLoc),L(a),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.tryLoc===t){var r=a.completion;if("throw"===r.type){var n=r.arg;L(a)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,a){return this.delegate={iterator:S(t),resultName:e,nextLoc:a},"next"===this.method&&(this.arg=void 0),v}},t},a("6a54"),a("01a2"),a("e39c"),a("bf0f"),a("844d"),a("18f7"),a("de6c"),a("3872e"),a("4e9b"),a("114e"),a("c240"),a("926e"),a("7a76"),a("c9b5"),a("aa9c"),a("2797"),a("8a8d"),a("dc69"),a("f7a5");var r=function(t){return t&&t.__esModule?t:{default:t}}(a("fcf3"))},"2fdc":function(t,e,a){"use strict";function r(t,e,a,r,n,o,i){try{var s=t[o](i),c=s.value}catch(l){return void a(l)}s.done?e(c):Promise.resolve(c).then(r,n)}a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return function(){var e=this,a=arguments;return new Promise((function(n,o){var i=t.apply(e,a);function s(t){r(i,n,o,s,c,"next",t)}function c(t){r(i,n,o,s,c,"throw",t)}s(void 0)}))}},a("bf0f")},"30f7":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},a("7a76"),a("c9b5")},4733:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if(Array.isArray(t))return(0,r.default)(t)};var r=function(t){return t&&t.__esModule?t:{default:t}}(a("8d0b"))},"67a4":function(t,e,a){"use strict";var r=a("104c"),n=a.n(r);n.a},"6d57":function(t,e,a){"use strict";a.r(e);var r=a("208c"),n=a.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);e["default"]=n.a},7775:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return r}));var r={svgIcon:a("8a0f").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"custom-tab-bar"},[t._l(t.tabList,(function(e,r){return a("v-uni-view",{key:r,staticClass:"tab-item",class:{active:t.current===r},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.handleTabClick(e,r)}}},[a("svg-icon",{attrs:{name:t.current===r?e.selectedIconPath:e.iconPath,type:"svg",size:24,color:t.current===r?t.activeColor:t.color}}),a("v-uni-text",{staticClass:"tab-text",class:{"active-text":t.current===r}},[t._v(t._s(e.text))])],1)})),t.showMoreMenu?a("v-uni-view",{staticClass:"more-menu"},[a("v-uni-view",{staticClass:"menu-overlay",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeMoreMenu.apply(void 0,arguments)}}}),a("v-uni-view",{staticClass:"menu-content"},[a("v-uni-view",{staticClass:"menu-header"},[a("v-uni-text",{staticClass:"menu-title"},[t._v("更多功能")]),a("v-uni-view",{staticClass:"menu-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeMoreMenu.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"close",type:"svg",size:32,color:"#666"}})],1)],1),a("v-uni-view",{staticClass:"menu-list"},t._l(t.moreMenuList,(function(e,r){return a("v-uni-view",{key:r,staticClass:"menu-item",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.navigateToPage(e.pagePath)}}},[a("svg-icon",{attrs:{name:e.iconPath,type:"svg",size:24,color:"#333333"}}),a("v-uni-text",{staticClass:"menu-item-text"},[t._v(t._s(e.text))])],1)})),1)],1)],1):t._e()],2)},o=[]},b7c7:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,r.default)(t)||(0,n.default)(t)||(0,o.default)(t)||(0,i.default)()};var r=s(a("4733")),n=s(a("d14d")),o=s(a("5d6b")),i=s(a("30f7"));function s(t){return t&&t.__esModule?t:{default:t}}},bf69:function(t,e,a){var r=a("c86c");e=r(!1),e.push([t.i,".custom-tab-bar[data-v-6a709636]{display:flex;justify-content:space-around;align-items:center;background-color:#fff;box-shadow:0 -1px 5px rgba(0,0,0,.1);height:%?100?%;position:fixed;bottom:0;left:0;right:0;z-index:999;padding-bottom:env(safe-area-inset-bottom)}.tab-item[data-v-6a709636]{display:flex;flex-direction:column;align-items:center;justify-content:center;flex:1;height:100%;padding:%?10?% 0}.tab-text[data-v-6a709636]{font-size:%?22?%;color:#333;margin-top:%?4?%}.active-text[data-v-6a709636]{color:#007aff}\n\n/* 更多菜单样式 */.more-menu[data-v-6a709636]{position:fixed;top:0;right:0;bottom:0;left:0;z-index:1000}.menu-overlay[data-v-6a709636]{position:absolute;top:0;right:0;bottom:0;left:0;background-color:rgba(0,0,0,.5)}.menu-content[data-v-6a709636]{position:absolute;bottom:%?100?%;left:0;right:0;background-color:#fff;border-top-left-radius:%?20?%;border-top-right-radius:%?20?%;overflow:hidden;-webkit-animation:slideUp-data-v-6a709636 .3s ease;animation:slideUp-data-v-6a709636 .3s ease;box-shadow:0 -2px 10px rgba(0,0,0,.1)}.menu-header[data-v-6a709636]{display:flex;justify-content:space-between;align-items:center;padding:%?30?%;border-bottom:1px solid #f0f0f0}.menu-title[data-v-6a709636]{font-size:%?32?%;font-weight:500;color:#333}.menu-close[data-v-6a709636]{padding:%?10?%}.menu-list[data-v-6a709636]{display:flex;flex-wrap:wrap;padding:%?20?%}.menu-item[data-v-6a709636]{width:25%;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?20?% 0}.menu-item-text[data-v-6a709636]{font-size:%?24?%;color:#333;margin-top:%?10?%;text-align:center}@-webkit-keyframes slideUp-data-v-6a709636{from{-webkit-transform:translateY(100%);transform:translateY(100%)}to{-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes slideUp-data-v-6a709636{from{-webkit-transform:translateY(100%);transform:translateY(100%)}to{-webkit-transform:translateY(0);transform:translateY(0)}}",""]),t.exports=e},c475:function(t,e,a){"use strict";a("6a54");var r=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.request=e.getTenantInfo=void 0;var n=r(a("9b1b"));a("bf0f"),a("4626"),a("5ac7");var o=null;e.getTenantInfo=function(t){return new Promise((function(e,a){uni.request({url:"/api/Tenants/getTenantsByLogin",method:"POST",data:t,header:{"Content-Type":"application/json"},success:function(t){200===t.statusCode?(o=t.data,e(t.data)):a(t.data)},fail:function(t){a(t)}})}))};e.request=function(t){return t.url.includes("/login")&&o&&(t.header=(0,n.default)((0,n.default)({},t.header),{},{__tenant:o[0].id})),new Promise((function(e,a){uni.request({url:t.url,method:t.method||"GET",data:t.data||{},header:(0,n.default)({Authorization:"Bearer "+uni.getStorageSync("token"),"Content-Type":"application/json"},t.header),success:function(t){200===t.statusCode?e(t.data):a(t.data)},fail:function(t){a(t)}})}))}},c9cf:function(t,e,a){"use strict";var r=a("e9f7"),n=a.n(r);n.a},d14d:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},a("01a2"),a("e39c"),a("bf0f"),a("844d"),a("18f7"),a("de6c"),a("08eb")},d86f:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.getUserList=e.getProductList=e.getOpportunityList=e.getContactList=e.getCompanyList=e.getAgreementList=void 0;var r=a("c475");e.getContactList=function(t){return(0,r.request)({url:"/api/crm/contract/getList",method:"POST",data:t})};e.getCompanyList=function(){return(0,r.request)({url:"/api/crm/contract/getAllCompanys",method:"GET"})};e.getUserList=function(t){return(0,r.request)({url:"/api/Users/<USER>",method:"POST",data:t})};e.getOpportunityList=function(t){return(0,r.request)({url:"/api/crm/business/getKanbanList",method:"POST",data:t})};e.getAgreementList=function(t){return(0,r.request)({url:"/api/crm/contract/getAgreementList",method:"POST",data:t})};e.getProductList=function(t){return(0,r.request)({url:"/api/crm/product/getList",method:"POST",data:t})}},e9f7:function(t,e,a){var r=a("bf69");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var n=a("967d").default;n("9e21a296",r,!0,{sourceMap:!1,shadowMode:!1})},eab4:function(t,e,a){"use strict";a.r(e);var r=a("7775"),n=a("1880");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("c9cf");var i=a("828b"),s=Object(i["a"])(n["default"],r["b"],r["c"],!1,null,"6a709636",null,!1,r["a"],void 0);e["default"]=s.exports}}]);
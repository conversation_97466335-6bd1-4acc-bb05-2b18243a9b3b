<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="goBack">
        <svg-icon name="arrow-left" type="svg" size="24"></svg-icon>
      </view>
      <text class="page-title">发票管理</text>
      <view class="header-actions">
        <button type="button" class="action-button" @click="showFilterOptions">
          <svg-icon name="filter-3" type="svg" size="24"></svg-icon>
        </button>
        <button type="button" class="action-button" @click="showSortOptions">
          <svg-icon name="sort-desc" type="svg" size="24"></svg-icon>
        </button>
      </view>
    </view>
    
    <!-- 搜索栏 -->
    <view class="search-bar">
      <svg-icon name="search" type="svg" size="24" class="search-icon"></svg-icon>
      <input 
        type="text" 
        class="search-input" 
        v-model="searchQuery" 
        placeholder="搜索发票号、客户名称..." 
        @input="onSearch"
      />
    </view>
    
    <!-- 筛选栏 -->
    <scroll-view scroll-x class="filter-bar">
      <view 
        v-for="(filter, index) in filters" 
        :key="index" 
        class="filter-button"
        :class="{ active: currentFilter === filter.value }"
        @click="setFilter(filter.value)"
      >
        <text>{{filter.label}}</text>
      </view>
    </scroll-view>
    
    <!-- 发票列表 -->
    <scroll-view 
      scroll-y 
      class="invoice-list"
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="loadMore"
    >
      <view v-if="filteredInvoices.length > 0">
        <view 
          v-for="(invoice, index) in filteredInvoices" 
          :key="index" 
          class="invoice-item"
          @click="goToDetail(invoice.id)"
        >
          <view class="invoice-header">
            <text class="invoice-title">{{invoice.title}}</text>
            <text :class="['invoice-status', 'status-' + invoice.status]">{{getStatusText(invoice.status)}}</text>
          </view>
          
          <view class="invoice-info">
            <view class="info-item">
              <text class="info-label">发票号</text>
              <text class="info-value">{{invoice.invoiceNumber}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">客户</text>
              <text class="info-value">{{invoice.customer}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">开票日期</text>
              <text class="info-value">{{invoice.invoiceDate || '-'}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">付款期限</text>
              <text class="info-value">{{invoice.dueDate || '-'}}</text>
            </view>
          </view>
          
          <view class="invoice-footer">
            <text class="invoice-amount">¥{{formatMoney(invoice.amount)}}</text>
            <view class="invoice-actions">
              <view 
                class="action-icon" 
                v-if="invoice.status !== 'draft'"
                @click.stop="sendInvoice(invoice)"
              >
                <svg-icon name="mail-send" type="svg" size="20"></svg-icon>
              </view>
              <view 
                class="action-icon" 
                v-if="invoice.status !== 'draft'"
                @click.stop="downloadInvoice(invoice)"
              >
                <svg-icon name="download" type="svg" size="20"></svg-icon>
              </view>
              <view 
                class="action-icon" 
                v-if="invoice.status === 'draft'"
                @click.stop="editInvoice(invoice)"
              >
                <svg-icon name="edit" type="svg" size="20"></svg-icon>
              </view>
              <view 
                class="action-icon" 
                v-if="invoice.status === 'draft'"
                @click.stop="deleteInvoice(invoice)"
              >
                <svg-icon name="delete-bin-6" type="svg" size="20"></svg-icon>
              </view>
            </view>
          </view>
        </view>
        
        <view v-if="loadingMore" class="loading-more">
          <text>加载更多...</text>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-else class="empty-state">
        <view class="empty-icon">
          <svg-icon name="file-list-3" type="svg" size="64"></svg-icon>
        </view>
        <text class="empty-title">暂无发票</text>
        <text class="empty-text">创建您的第一张发票，开始管理您的财务</text>
        <button class="create-button" @click="createInvoice">
          <svg-icon name="add" type="svg" size="20"></svg-icon>
          <text>创建发票</text>
        </button>
      </view>
    </scroll-view>
    
    <!-- 创建发票浮动按钮 -->
    <view class="floating-button" @click="createInvoice">
      <svg-icon name="add" type="svg" size="60" color="#ffffff"></svg-icon>
    </view>

    <!-- 自定义TabBar组件 -->
    <custom-tab-bar ref="customTabBar"></custom-tab-bar>
  </view>
</template>

<script>
import CustomTabBar from '@/components/CustomTabBar.vue';
import SvgIcon from '@/components/svg-icon.vue';

export default {
  components: {
    CustomTabBar,
    SvgIcon
  },
  data() {
    return {
      searchQuery: '',
      currentFilter: 'all',
      filters: [
        { label: '全部', value: 'all' },
        { label: '已付款', value: 'paid' },
        { label: '待付款', value: 'pending' },
        { label: '已逾期', value: 'overdue' },
        { label: '草稿', value: 'draft' }
      ],
      invoices: [
        {
          id: '1',
          title: '系统集成项目 - 第一期',
          status: 'paid',
          invoiceNumber: 'INV-2023-11-001',
          customer: '上海智能科技',
          invoiceDate: '2023-11-05',
          dueDate: '2023-11-15',
          amount: 290975
        },
        {
          id: '2',
          title: '软件维护服务 - 季度账单',
          status: 'pending',
          invoiceNumber: 'INV-2023-11-002',
          customer: '北京电子科技',
          invoiceDate: '2023-11-10',
          dueDate: '2023-11-25',
          amount: 75000
        },
        {
          id: '3',
          title: '数据迁移项目',
          status: 'overdue',
          invoiceNumber: 'INV-2023-10-005',
          customer: '广州数联网络',
          invoiceDate: '2023-10-25',
          dueDate: '2023-11-10',
          amount: 125400
        },
        {
          id: '4',
          title: '培训服务费用',
          status: 'draft',
          invoiceNumber: 'INV-2023-11-D01',
          customer: '成都星辰科技',
          invoiceDate: '',
          dueDate: '',
          amount: 45000
        }
      ],
      isRefreshing: false,
      loadingMore: false,
      page: 1,
      hasMore: true
    }
  },
  computed: {
    filteredInvoices() {
      let result = [...this.invoices];
      
      // 根据搜索关键词筛选
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        result = result.filter(invoice => 
          invoice.title.toLowerCase().includes(query) ||
          invoice.invoiceNumber.toLowerCase().includes(query) ||
          invoice.customer.toLowerCase().includes(query)
        );
      }
      
      // 根据状态筛选
      if (this.currentFilter !== 'all') {
        result = result.filter(invoice => invoice.status === this.currentFilter);
      }
      
      return result;
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    formatMoney(amount) {
      return amount.toLocaleString('zh-CN');
    },
    getStatusText(status) {
      const statusMap = {
        'paid': '已付款',
        'pending': '待付款',
        'overdue': '已逾期',
        'draft': '草稿'
      };
      return statusMap[status] || status;
    },
    setFilter(filter) {
      this.currentFilter = filter;
    },
    onSearch(e) {
      // 搜索功能实现
      this.searchQuery = e.detail.value;
    },
    onRefresh() {
      this.isRefreshing = true;
      
      // 模拟刷新
      setTimeout(() => {
        this.loadInvoices();
        this.isRefreshing = false;
        uni.showToast({
          title: '刷新成功',
          icon: 'success'
        });
      }, 1000);
    },
    loadMore() {
      if (!this.hasMore || this.loadingMore) return;
      
      this.loadingMore = true;
      
      // 模拟加载更多
      setTimeout(() => {
        // 这里实际应用中应该是从API获取更多数据
        this.page++;
        if (this.page > 3) {
          this.hasMore = false;
        }
        this.loadingMore = false;
      }, 1000);
    },
    loadInvoices() {
      // 实际应用中，这里应该是从API获取发票数据
      // 这里使用模拟数据
      this.page = 1;
      this.hasMore = true;
    },
    goToDetail(id) {
      uni.navigateTo({
        url: `/pages/contracts/invoice-detail?id=${id}`
      });
    },
    createInvoice() {
      uni.navigateTo({
        url: '/pages/contracts/invoice-create'
      });
    },
    editInvoice(invoice) {
      uni.navigateTo({
        url: `/pages/contracts/invoice-edit?id=${invoice.id}`
      });
    },
    deleteInvoice(invoice) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除此发票草稿吗？',
        success: (res) => {
          if (res.confirm) {
            // 实际应用中，这里应该是调用API删除发票
            const index = this.invoices.findIndex(item => item.id === invoice.id);
            if (index !== -1) {
              this.invoices.splice(index, 1);
            }
            uni.showToast({
              title: '发票草稿已删除',
              icon: 'success'
            });
          }
        }
      });
    },
    sendInvoice(invoice) {
      uni.showToast({
        title: '发送电子邮件功能开发中...',
        icon: 'none'
      });
    },
    downloadInvoice(invoice) {
      uni.showToast({
        title: '下载发票功能开发中...',
        icon: 'none'
      });
    },
    showFilterOptions() {
      uni.showToast({
        title: '高级筛选功能开发中...',
        icon: 'none'
      });
    },
    showSortOptions() {
      uni.showActionSheet({
        itemList: ['最新创建', '到期日期', '金额大小', '客户名称'],
        success: (res) => {
          // 实际应用中，这里应该是根据选择进行排序
          const sortTypes = ['date', 'due', 'amount', 'customer'];
          const selectedSort = sortTypes[res.tapIndex];
          console.log('选择的排序方式:', selectedSort);
        }
      });
    }
  },
  onLoad() {
    this.loadInvoices();
  },
  onShow() {
    // 设置TabBar当前选中项
    if (typeof this.$refs.customTabBar !== 'undefined') {
      this.$refs.customTabBar.current = 4; // 对应"更多"菜单
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  background-color: #fff;
  z-index: 10;
}

.page-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.back-button {
  color: #666;
  font-size: 24px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #666;
  background-color: #f5f5f5;
  border: 1px solid #eee;
  padding: 0;
  margin: 0;
  font-size: 20px;
}

.search-bar {
  margin: 12px;
  position: relative;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  padding-left: 40px;
  border-radius: 8px;
  border: 1px solid #eee;
  background-color: #f5f5f5;
  font-size: 14px;
  color: #333;
  box-sizing: border-box;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 20px;
}

.filter-bar {
  display: flex;
  white-space: nowrap;
  padding: 0 12px;
  margin-bottom: 12px;
}

.filter-button {
  display: inline-block;
  background-color: #f5f5f5;
  border: 1px solid #eee;
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.filter-button.active {
  background-color: #3a86ff;
  color: white;
  border-color: #3a86ff;
}

.invoice-list {
  flex: 1;
  margin-bottom: 80px;
}

.invoice-item {
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
  border-radius: 8px;
  margin: 0 12px 12px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  border: 1px solid #eee;
}

.invoice-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.invoice-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-right: 12px;
}

.invoice-status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-paid {
  background-color: #e6f7ee;
  color: #00b578;
}

.status-pending {
  background-color: #fff5e6;
  color: #ff9a2a;
}

.status-overdue {
  background-color: #ffece8;
  color: #ff4d4f;
}

.status-draft {
  background-color: #f5f5f5;
  color: #666;
}

.invoice-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-bottom: 8px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
}

.info-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.invoice-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.invoice-amount {
  font-size: 18px;
  font-weight: 600;
  color: #3a86ff;
}

.invoice-actions {
  display: flex;
  gap: 8px;
}

.action-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #666;
  background-color: #f5f5f5;
}

.floating-button {
  position: fixed;
  bottom: calc(128rpx + var(--spacing-xl)); /* 调整底部位置，避开TabBar */
  right: var(--spacing-xl);
  width: 110rpx; /* 减小尺寸 */
  height: 110rpx; /* 减小尺寸 */
  border-radius: 50%;
  background: linear-gradient(135deg, #0a6bff, #0057ff);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6), 0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  z-index: 100;
  transition: all 0.3s ease;
  border: 4rpx solid rgba(255, 255, 255, 0.7);
  animation: pulse 2s infinite; /* 添加脉动动画 */
}

.floating-button:active {
  transform: scale(0.95);
  box-shadow: 0 5rpx 10rpx rgba(0, 87, 255, 0.5), 0 3rpx 3rpx rgba(0, 87, 255, 0.3);
  animation: none; /* 点击时停止动画 */
}

/* 添加脉动动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6), 0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 15rpx 25rpx rgba(0, 87, 255, 0.7), 0 8rpx 10rpx rgba(0, 87, 255, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6), 0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 20px;
  text-align: center;
  margin-top: 20px;
}

.empty-icon {
  margin-bottom: 16px;
  color: #ddd;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.empty-text {
  font-size: 14px;
  color: #999;
  margin-bottom: 24px;
}

.create-button {
  padding: 10px 20px;
  background-color: #3a86ff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-more {
  text-align: center;
  padding: 20px 0;
  color: #999;
  font-size: 14px;
}
</style> 
<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="goBack">
        <text class="ri-arrow-left-line"></text>
      </view>
      <text class="page-title">员工绩效</text>
      <view class="header-actions">
        <button type="button" class="action-button" @click="exportPerformance">
          <text class="ri-download-line"></text>
        </button>
      </view>
    </view>
    
    <!-- 员工信息卡片 -->
    <view class="employee-card">
      <view class="employee-info">
        <view class="employee-avatar">
          <image :src="employee.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
        </view>
        <view class="employee-details">
          <text class="employee-name">{{employee.name}}</text>
          <text class="employee-position">{{employee.department}} | {{employee.position}}</text>
          <text class="employee-id">工号: {{employee.employeeId}}</text>
        </view>
      </view>
    </view>
    
    <!-- 绩效时间筛选 -->
    <view class="filter-bar">
      <text class="current-period">{{currentPeriod}}</text>
      <view class="period-selector">
        <text class="period-arrow" @click="changePeriod('prev')">
          <text class="ri-arrow-left-s-line"></text>
        </text>
        <picker 
          mode="date" 
          fields="month" 
          :value="selectedDate" 
          @change="onDateChange"
          class="date-picker"
        >
          <text class="period-text">{{selectedDate.substring(0, 7)}}</text>
        </picker>
        <text class="period-arrow" @click="changePeriod('next')">
          <text class="ri-arrow-right-s-line"></text>
        </text>
      </view>
    </view>
    
    <!-- 绩效概览 -->
    <view class="performance-overview">
      <view class="overview-header">
        <text class="overview-title">绩效概览</text>
        <text class="overview-rating" :class="'rating-' + employee.performanceRating">
          {{ratingLabels[employee.performanceRating]}}
        </text>
      </view>
      
      <view class="rating-progress">
        <view class="rating-bar">
          <view class="rating-fill" :style="{ width: ratingPercentage + '%' }"></view>
        </view>
        <view class="rating-labels">
          <text>不合格</text>
          <text>待改进</text>
          <text>良好</text>
          <text>优秀</text>
          <text>卓越</text>
        </view>
      </view>
    </view>
    
    <!-- KPI 指标 -->
    <view class="metrics-section">
      <view class="section-header">
        <text class="section-title">关键业绩指标 (KPI)</text>
      </view>
      
      <view class="metrics-list">
        <view class="metric-item" v-for="(metric, index) in performanceData.kpis" :key="index">
          <view class="metric-header">
            <view class="metric-title-area">
              <text class="metric-title">{{metric.name}}</text>
              <text class="metric-subtitle">{{metric.description}}</text>
            </view>
            <view class="metric-value-area">
              <text class="metric-value">{{formatMetricValue(metric)}}</text>
              <text :class="['metric-trend', 'trend-' + metric.trend]">
                <text :class="metric.trend === 'up' ? 'ri-arrow-up-s-line' : (metric.trend === 'down' ? 'ri-arrow-down-s-line' : 'ri-subtract-line')"></text>
                {{metric.changePercentage}}%
              </text>
            </view>
          </view>
          
          <view class="metric-progress">
            <view class="progress-bar">
              <view 
                class="progress-fill" 
                :style="{ width: metric.achievementPercentage + '%' }"
                :class="'fill-' + getProgressLevel(metric.achievementPercentage)"
              ></view>
            </view>
            <view class="progress-labels">
              <text>{{metric.actual}}/{{metric.target}}</text>
              <text>{{metric.achievementPercentage}}%</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 能力评估 -->
    <view class="skills-section">
      <view class="section-header">
        <text class="section-title">能力评估</text>
      </view>
      
      <view class="skills-radar-chart">
        <!-- 这里应该是雷达图，但在uni-app中实现复杂，用模拟代替 -->
        <view class="radar-placeholder">
          <image src="/static/images/radar-chart-placeholder.png" mode="aspectFit"></image>
        </view>
        
        <view class="skills-legend">
          <view class="legend-item" v-for="(skill, index) in performanceData.skills" :key="index">
            <view class="legend-color" :style="{ backgroundColor: skillColors[index % skillColors.length] }"></view>
            <text class="legend-text">{{skill.name}} ({{skill.score}}/5)</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 绩效评价 -->
    <view class="evaluation-section">
      <view class="section-header">
        <text class="section-title">绩效评价</text>
      </view>
      
      <view class="evaluation-content">
        <view class="eval-group">
          <text class="eval-label">主管评价</text>
          <text class="eval-value">{{performanceData.evaluation.managerComment}}</text>
        </view>
        
        <view class="eval-group">
          <text class="eval-label">优势</text>
          <view class="eval-list">
            <text class="eval-list-item" v-for="(strength, index) in performanceData.evaluation.strengths" :key="index">
              • {{strength}}
            </text>
          </view>
        </view>
        
        <view class="eval-group">
          <text class="eval-label">改进建议</text>
          <view class="eval-list">
            <text class="eval-list-item" v-for="(improvement, index) in performanceData.evaluation.improvements" :key="index">
              • {{improvement}}
            </text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 历史记录 -->
    <view class="history-section">
      <view class="section-header">
        <text class="section-title">绩效历史</text>
        <text class="section-action" @click="viewPerformanceHistory">查看全部</text>
      </view>
      
      <view class="history-chart">
        <!-- 这里应该是折线图，但在uni-app中实现复杂，用模拟代替 -->
        <view class="chart-placeholder">
          <image src="/static/images/performance-chart-placeholder.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      id: '',
      employee: {
        id: '1',
        name: '张三',
        department: '销售部',
        position: '销售经理',
        employeeId: 'EMP001',
        avatar: '/static/images/avatars/avatar1.png',
        performanceRating: 4 // 1-5 的评分
      },
      selectedDate: '2023-04-01',
      currentPeriod: '2023年04月绩效',
      ratingLabels: {
        1: '不合格',
        2: '待改进',
        3: '良好',
        4: '优秀',
        5: '卓越'
      },
      performanceData: {
        kpis: [
          {
            name: '销售业绩',
            description: '月度销售额（元）',
            target: 500000,
            actual: 620000,
            achievementPercentage: 124,
            trend: 'up',
            changePercentage: 15,
            unit: '元'
          },
          {
            name: '新客户开发',
            description: '月度新增客户数',
            target: 10,
            actual: 12,
            achievementPercentage: 120,
            trend: 'up',
            changePercentage: 20,
            unit: '个'
          },
          {
            name: '客户满意度',
            description: '客户反馈评分',
            target: 4.5,
            actual: 4.8,
            achievementPercentage: 107,
            trend: 'up',
            changePercentage: 5,
            unit: '分'
          },
          {
            name: '项目完成率',
            description: '按时完成项目比例',
            target: 100,
            actual: 95,
            achievementPercentage: 95,
            trend: 'down',
            changePercentage: 2,
            unit: '%'
          }
        ],
        skills: [
          { name: '销售能力', score: 4.5 },
          { name: '沟通技巧', score: 4.8 },
          { name: '团队协作', score: 4.0 },
          { name: '问题解决', score: 4.2 },
          { name: '客户关系', score: 4.6 }
        ],
        evaluation: {
          managerComment: '张三是一位优秀的销售经理，他在本季度展现了出色的销售业绩和团队领导能力。他能够快速响应客户需求，并有效管理销售团队，提高整体绩效。',
          strengths: [
            '出色的沟通能力，能够有效地与客户建立信任关系',
            '良好的团队领导能力，激励团队成员达成销售目标',
            '对产品知识有深入了解，能够精准满足客户需求'
          ],
          improvements: [
            '可以更加注重长期客户关系维护，提高客户留存率',
            '进一步提高项目管理能力，确保所有项目按时完成',
            '分享成功经验，帮助新团队成员更快成长'
          ]
        }
      },
      skillColors: ['#4a6fff', '#ff9500', '#00c48c', '#ff4d4f', '#7870ff']
    }
  },
  computed: {
    ratingPercentage() {
      return (this.employee.performanceRating / 5) * 100;
    }
  },
  onLoad(options) {
    if (options.id) {
      this.id = options.id;
      // 获取员工绩效数据
      this.fetchPerformanceData(this.id);
    }
  },
  methods: {
    fetchPerformanceData(id) {
      // 模拟从服务器获取员工绩效数据
      // 实际应用中应该通过API请求获取
      console.log('Fetching performance data for employee ID:', id);
      // 示例中使用的是静态数据，实际应用中应该替换为API调用
    },
    goBack() {
      uni.navigateBack();
    },
    onDateChange(e) {
      this.selectedDate = e.detail.value;
      const date = new Date(this.selectedDate);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      this.currentPeriod = `${year}年${month.toString().padStart(2, '0')}月绩效`;
      
      // 此处应该根据新选择的日期获取对应的绩效数据
      this.fetchPerformanceDataByPeriod(this.id, this.selectedDate);
    },
    changePeriod(direction) {
      const date = new Date(this.selectedDate);
      
      if (direction === 'prev') {
        date.setMonth(date.getMonth() - 1);
      } else {
        date.setMonth(date.getMonth() + 1);
      }
      
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      this.selectedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
      this.currentPeriod = `${year}年${month.toString().padStart(2, '0')}月绩效`;
      
      // 此处应该根据新选择的日期获取对应的绩效数据
      this.fetchPerformanceDataByPeriod(this.id, this.selectedDate);
    },
    fetchPerformanceDataByPeriod(id, period) {
      // 模拟根据时间段获取绩效数据
      console.log(`Fetching performance data for employee ${id} in period ${period}`);
      // 实际应用中应该通过API请求获取
    },
    formatMetricValue(metric) {
      if (metric.unit === '元') {
        return `¥${(metric.actual).toLocaleString('zh-CN')}`;
      } else if (metric.unit === '%' || metric.unit === '分') {
        return `${metric.actual}${metric.unit}`;
      } else {
        return `${metric.actual} ${metric.unit}`;
      }
    },
    getProgressLevel(percentage) {
      if (percentage >= 100) {
        return 'excellent';
      } else if (percentage >= 80) {
        return 'good';
      } else if (percentage >= 60) {
        return 'medium';
      } else {
        return 'poor';
      }
    },
    viewPerformanceHistory() {
      uni.navigateTo({
        url: `/pages/hr/employee-performance-history?id=${this.id}`
      });
    },
    exportPerformance() {
      uni.showToast({
        title: '绩效报告导出中',
        icon: 'loading',
        duration: 2000,
        success: () => {
          setTimeout(() => {
            uni.showToast({
              title: '导出成功',
              icon: 'success',
              duration: 1500
            });
          }, 2000);
        }
      });
    }
  }
};
</script>

<style>
.container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 30rpx;
}

.page-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  position: relative;
  border-bottom: 1rpx solid #eaeaea;
}

.back-button {
  font-size: 40rpx;
  color: #333;
  padding: 10rpx;
}

.page-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
}

.action-button {
  background: none;
  border: none;
  font-size: 40rpx;
  color: #666;
  padding: 10rpx;
}

.employee-card {
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx;
}

.employee-info {
  display: flex;
  align-items: center;
}

.employee-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}

.employee-avatar image {
  width: 100%;
  height: 100%;
}

.employee-details {
  flex: 1;
}

.employee-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 5rpx;
}

.employee-position {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.employee-id {
  font-size: 24rpx;
  color: #999;
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20rpx 30rpx;
  padding: 20rpx;
  border-radius: 20rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.current-period {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.period-selector {
  display: flex;
  align-items: center;
}

.period-arrow {
  font-size: 40rpx;
  color: #666;
  padding: 0 10rpx;
}

.period-text {
  font-size: 28rpx;
  color: #4a6fff;
  padding: 0 10rpx;
}

.date-picker {
  display: inline-block;
}

.performance-overview, .metrics-section, .skills-section, .evaluation-section, .history-section {
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eaeaea;
}

.overview-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.overview-rating {
  font-size: 28rpx;
  font-weight: 500;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
}

.rating-1 {
  background-color: #fff1f0;
  color: #ff4d4f;
}

.rating-2 {
  background-color: #fff7e6;
  color: #fa8c16;
}

.rating-3 {
  background-color: #e6f7ff;
  color: #1890ff;
}

.rating-4 {
  background-color: #f6ffed;
  color: #52c41a;
}

.rating-5 {
  background-color: #f9f0ff;
  color: #722ed1;
}

.rating-progress {
  padding: 30rpx;
}

.rating-bar {
  height: 16rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
  overflow: hidden;
}

.rating-fill {
  height: 100%;
  background-color: #4a6fff;
  border-radius: 8rpx;
}

.rating-labels {
  display: flex;
  justify-content: space-between;
  font-size: 22rpx;
  color: #999;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eaeaea;
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.section-action {
  font-size: 26rpx;
  color: #4a6fff;
}

.metrics-list {
  padding: 0 30rpx;
}

.metric-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.metric-item:last-child {
  border-bottom: none;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.metric-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 5rpx;
}

.metric-subtitle {
  font-size: 24rpx;
  color: #999;
}

.metric-value {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  text-align: right;
  margin-bottom: 5rpx;
}

.metric-trend {
  font-size: 24rpx;
  text-align: right;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.trend-flat {
  color: #999;
}

.metric-progress {
  margin-top: 10rpx;
}

.progress-bar {
  height: 12rpx;
  background-color: #f0f0f0;
  border-radius: 6rpx;
  margin-bottom: 8rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 6rpx;
}

.fill-excellent {
  background-color: #52c41a;
}

.fill-good {
  background-color: #1890ff;
}

.fill-medium {
  background-color: #fa8c16;
}

.fill-poor {
  background-color: #ff4d4f;
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  font-size: 22rpx;
  color: #999;
}

.skills-radar-chart {
  padding: 30rpx;
}

.radar-placeholder, .chart-placeholder {
  width: 100%;
  height: 400rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.radar-placeholder image, .chart-placeholder image {
  width: 100%;
  height: 100%;
}

.skills-legend {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 10rpx;
}

.legend-color {
  width: 20rpx;
  height: 20rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
}

.legend-text {
  font-size: 24rpx;
  color: #666;
}

.evaluation-content {
  padding: 30rpx;
}

.eval-group {
  margin-bottom: 20rpx;
}

.eval-group:last-child {
  margin-bottom: 0;
}

.eval-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.eval-value {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.eval-list {
  margin-top: 10rpx;
}

.eval-list-item {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  display: block;
  margin-bottom: 10rpx;
}

.history-chart {
  padding: 30rpx;
}
</style> 
<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <navigator class="back-button" open-type="navigateBack">
        <text class="iconfont icon-arrow-left"></text>
      </navigator>
      <view class="page-title">客户标签</view>
      <view class="header-actions">
        <button class="action-button" @tap="navigateToCreate">
          <text class="iconfont icon-add"></text>
        </button>
      </view>
    </view>

    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <view class="search-icon">
          <text class="iconfont icon-search"></text>
        </view>
        <input type="text" class="search-input" v-model="searchKeyword" placeholder="搜索标签" />
      </view>
    </view>

    <!-- 系统标签 -->
    <view class="tags-section">
      <view class="section-header">
        <view class="section-title">
          <text class="iconfont icon-tag"></text>
          <text>系统标签</text>
        </view>
        <view class="section-action" @tap="manageSystemTags">
          <text>管理</text>
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
      <view class="tags-list">
        <view 
          class="tag-item system-tag" 
          v-for="(tag, index) in filteredSystemTags" 
          :key="'sys-' + index"
          @tap="tagDetail(tag)"
        >
          <text>{{ tag.name }}</text>
          <text class="tag-count">{{ tag.count }}</text>
        </view>
      </view>
    </view>

    <!-- 状态标签 -->
    <view class="tags-section">
      <view class="section-header">
        <view class="section-title">
          <text class="iconfont icon-flag"></text>
          <text>客户状态</text>
        </view>
      </view>
      <view class="tags-list">
        <view 
          class="tag-item status-tag" 
          :class="tag.class"
          v-for="(tag, index) in statusTags" 
          :key="'status-' + index"
          @tap="tagDetail(tag)"
        >
          <text>{{ tag.name }}</text>
          <text class="tag-count">{{ tag.count }}</text>
        </view>
      </view>
    </view>

    <!-- 行业标签 -->
    <view class="tags-section">
      <view class="section-header">
        <view class="section-title">
          <text class="iconfont icon-building"></text>
          <text>行业分类</text>
        </view>
        <view class="section-action" @tap="manageIndustryTags">
          <text>管理</text>
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
      <view class="tags-list">
        <view 
          class="tag-item industry-tag" 
          v-for="(tag, index) in filteredIndustryTags" 
          :key="'ind-' + index"
          @tap="tagDetail(tag)"
        >
          <text>{{ tag.name }}</text>
          <text class="tag-count">{{ tag.count }}</text>
        </view>
      </view>
    </view>

    <!-- 自定义标签 -->
    <view class="tags-section">
      <view class="section-header">
        <view class="section-title">
          <text class="iconfont icon-tag"></text>
          <text>自定义标签</text>
        </view>
        <view class="section-action" @tap="showAddTagModal">
          <text>添加</text>
          <text class="iconfont icon-add"></text>
        </view>
      </view>
      <view class="tags-list">
        <view 
          class="tag-item custom-tag" 
          v-for="(tag, index) in filteredCustomTags" 
          :key="'custom-' + index"
        >
          <text>{{ tag.name }}</text>
          <text class="tag-count">{{ tag.count }}</text>
          <text class="tag-delete iconfont icon-delete" @tap.stop="confirmDeleteTag(tag)"></text>
        </view>
      </view>
    </view>

    <!-- 底部提示 -->
    <view class="tips-section">
      <view class="tips-content">
        <text class="iconfont icon-info"></text>
        <text>标签可以帮助您更好地管理和筛选客户</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      searchKeyword: '',
      
      // 系统标签
      systemTags: [
        { id: 1, name: '新线索', count: 12 },
        { id: 2, name: '已联系', count: 28 },
        { id: 3, name: '意向明确', count: 15 },
        { id: 4, name: '决策阶段', count: 8 },
        { id: 5, name: '长期客户', count: 22 }
      ],
      
      // 状态标签
      statusTags: [
        { id: 101, name: '重点客户', count: 15, class: 'key-tag' },
        { id: 102, name: '普通客户', count: 45, class: 'regular-tag' },
        { id: 103, name: '潜在客户', count: 32, class: 'potential-tag' },
        { id: 104, name: '非活跃客户', count: 18, class: 'inactive-tag' }
      ],
      
      // 行业标签
      industryTags: [
        { id: 201, name: '信息技术', count: 24 },
        { id: 202, name: '金融保险', count: 12 },
        { id: 203, name: '教育培训', count: 18 },
        { id: 204, name: '医疗健康', count: 9 },
        { id: 205, name: '电子制造', count: 15 },
        { id: 206, name: '数据服务', count: 10 },
        { id: 207, name: '人工智能', count: 6 },
        { id: 208, name: '其他行业', count: 14 }
      ],
      
      // 自定义标签
      customTags: [
        { id: 301, name: '高活跃度', count: 15 },
        { id: 302, name: '上海地区', count: 28 },
        { id: 303, name: '年度目标', count: 10 },
        { id: 304, name: '项目客户', count: 18 },
        { id: 305, name: '待跟进', count: 7 }
      ]
    }
  },
  computed: {
    // 根据搜索关键词过滤标签
    filteredSystemTags() {
      if (!this.searchKeyword) return this.systemTags
      const keyword = this.searchKeyword.toLowerCase()
      return this.systemTags.filter(tag => 
        tag.name.toLowerCase().includes(keyword)
      )
    },
    filteredIndustryTags() {
      if (!this.searchKeyword) return this.industryTags
      const keyword = this.searchKeyword.toLowerCase()
      return this.industryTags.filter(tag => 
        tag.name.toLowerCase().includes(keyword)
      )
    },
    filteredCustomTags() {
      if (!this.searchKeyword) return this.customTags
      const keyword = this.searchKeyword.toLowerCase()
      return this.customTags.filter(tag => 
        tag.name.toLowerCase().includes(keyword)
      )
    }
  },
  methods: {
    // 标签详情
    tagDetail(tag) {
      uni.navigateTo({
        url: `/pages/customers/customer-list?tagId=${tag.id}&tagName=${tag.name}`
      })
    },
    
    // 管理系统标签
    manageSystemTags() {
      uni.showToast({
        title: '系统标签管理功能开发中...',
        icon: 'none'
      })
    },
    
    // 管理行业标签
    manageIndustryTags() {
      uni.showToast({
        title: '行业标签管理功能开发中...',
        icon: 'none'
      })
    },
    
    // 显示添加标签弹窗
    showAddTagModal() {
      uni.showModal({
        title: '添加标签',
        placeholderText: '请输入标签名称',
        editable: true,
        success: (res) => {
          if (res.confirm && res.content) {
            this.addCustomTag(res.content)
          }
        }
      })
    },
    
    // 添加自定义标签
    addCustomTag(tagName) {
      // 检查是否已存在
      const exists = this.customTags.some(tag => tag.name === tagName)
      if (exists) {
        uni.showToast({
          title: '标签已存在',
          icon: 'none'
        })
        return
      }
      
      // 创建新标签
      const newTag = {
        id: 300 + this.customTags.length + 1,
        name: tagName,
        count: 0
      }
      
      this.customTags.push(newTag)
      uni.showToast({
        title: '标签添加成功',
        icon: 'success'
      })
    },
    
    // 确认删除标签
    confirmDeleteTag(tag) {
      uni.showModal({
        title: '删除标签',
        content: `确定要删除"${tag.name}"标签吗？`,
        success: (res) => {
          if (res.confirm) {
            this.deleteCustomTag(tag.id)
          }
        }
      })
    },
    
    // 删除自定义标签
    deleteCustomTag(tagId) {
      const index = this.customTags.findIndex(tag => tag.id === tagId)
      if (index !== -1) {
        this.customTags.splice(index, 1)
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
      }
    },
    
    // 导航到创建页面
    navigateToCreate() {
      this.showAddTagModal()
    }
  },
  onLoad() {
    // 页面加载逻辑，实际应用中可能会从服务器获取标签数据
  }
}
</script>

<style>
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1rpx solid var(--border-color);
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.page-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
}

.back-button {
  color: var(--text-secondary);
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.action-button {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  color: var(--text-secondary);
  background-color: var(--light-color);
  border: 1rpx solid var(--border-color);
  padding: 0;
  line-height: 1;
}

/* 搜索栏样式 */
.search-container {
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: #ffffff;
  border-bottom: 1rpx solid var(--border-color);
}

.search-box {
  display: flex;
  align-items: center;
  background-color: var(--light-color);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-md);
  padding: 0 var(--spacing-sm);
  overflow: hidden;
}

.search-icon {
  color: var(--text-secondary);
  padding: var(--spacing-xs);
}

.search-input {
  flex: 1;
  border: none;
  padding: var(--spacing-sm);
  background-color: transparent;
  color: var(--text-primary);
  font-size: 28rpx;
}

/* 标签分类样式 */
.tags-section {
  background-color: #ffffff;
  margin-top: var(--spacing-md);
  border-top: 1rpx solid var(--border-color);
  border-bottom: 1rpx solid var(--border-color);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1rpx solid var(--border-color-light);
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
}

.section-title text:first-child {
  color: var(--text-secondary);
  margin-right: var(--spacing-xs);
}

.section-action {
  color: var(--primary-color);
  font-size: 28rpx;
  display: flex;
  align-items: center;
}

.section-action text:last-child {
  margin-left: 4rpx;
}

/* 标签列表样式 */
.tags-list {
  padding: var(--spacing-md) var(--spacing-lg);
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.tag-item {
  display: flex;
  align-items: center;
  background-color: var(--light-color);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-full);
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  color: var(--text-primary);
}

.tag-item .tag-count {
  font-size: 24rpx;
  color: var(--text-secondary);
  background-color: var(--border-color-light);
  border-radius: var(--radius-full);
  padding: 2rpx 16rpx;
  margin-left: var(--spacing-xs);
}

.tag-item .tag-delete {
  color: var(--danger-color);
  margin-left: var(--spacing-xs);
}

.tag-item.system-tag {
  background-color: var(--border-color-light);
  border-color: var(--border-color);
}

.tag-item.industry-tag {
  background-color: #f3f4f6;
  color: #4b5563;
}

.tag-item.status-tag.key-tag {
  background-color: #fef3c7;
  color: #d97706;
  border-color: #fde68a;
}

.tag-item.status-tag.regular-tag {
  background-color: #e0f2fe;
  color: #0284c7;
  border-color: #bae6fd;
}

.tag-item.status-tag.potential-tag {
  background-color: #dbeafe;
  color: #2563eb;
  border-color: #bfdbfe;
}

.tag-item.status-tag.inactive-tag {
  background-color: #e5e7eb;
  color: #6b7280;
  border-color: #d1d5db;
}

.tag-item.custom-tag {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background-color: var(--primary-light);
}

/* 底部提示 */
.tips-section {
  padding: var(--spacing-lg);
}

.tips-content {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-tertiary);
  font-size: 24rpx;
}

.tips-content text:first-child {
  margin-right: 8rpx;
}
</style> 
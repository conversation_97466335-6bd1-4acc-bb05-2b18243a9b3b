<template>
  <view class="page-container">
    <view class="page-header">
      <text class="page-title">沟通记录</text>
      <view class="header-actions">
        <view class="action-button" @click="toggleSearch">
          <text class="ri-search-line"></text>
        </view>
        <view class="action-button" @click="showMoreActions">
          <text class="ri-more-2-fill"></text>
        </view>
      </view>
    </view>

    <scroll-view scroll-x class="tab-container" :scroll-into-view="'tab-' + currentTab" scroll-with-animation>
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        :id="'tab-' + tab.type"
        class="tab" 
        :class="{ active: currentTab === tab.type }"
        @click="switchTab(tab.type)"
      >
        {{tab.name}}
      </view>
    </scroll-view>

    <view class="search-container">
      <view class="search-box">
        <text class="ri-search-line"></text>
        <input 
          type="text" 
          class="search-input" 
          v-model="searchText"
          placeholder="搜索沟通记录" 
          confirm-type="search"
          @input="onSearch"
        />
      </view>
      <view class="filter-button" @click="showFilter">
        <text class="ri-filter-3-line"></text>
        <text>筛选</text>
      </view>
    </view>

    <scroll-view scroll-y class="communication-list" @scrolltolower="loadMore">
      <block v-if="filteredCommunications.length > 0">
        <view 
          v-for="(item, index) in filteredCommunications" 
          :key="index"
          class="communication-card"
          :data-type="item.type"
          @click="navigateToDetail(item.id)"
        >
          <view class="communication-header">
            <view class="communication-icon" :class="'icon-' + item.type">
              <text :class="getIconClass(item.type)"></text>
            </view>
            <view class="communication-title">
              <text class="communication-type">{{getTypeName(item.type)}}</text>
              <text class="communication-subject">{{item.subject}}</text>
            </view>
            <text class="communication-time">{{item.time}}</text>
          </view>
          
          <view class="communication-detail" v-if="item.relatedObject">
            <text class="detail-label">关联对象</text>
            <text class="detail-value">{{item.relatedObject}}</text>
          </view>
          
          <view class="communication-detail" v-if="item.contacts">
            <text class="detail-label">{{getContactLabel(item.type)}}</text>
            <text class="detail-value">{{item.contacts}}</text>
          </view>
          
          <view class="communication-detail" v-if="item.owner">
            <text class="detail-label">负责人</text>
            <text class="detail-value">{{item.owner}}</text>
          </view>
          
          <view class="communication-content">
            {{item.content}}
          </view>
          
          <view class="tag-container" v-if="item.tags && item.tags.length > 0">
            <text class="tag" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">{{tag}}</text>
          </view>
        </view>
      </block>
      
      <view class="empty-state" v-if="filteredCommunications.length === 0">
        <view class="empty-icon">
          <text class="ri-chat-3-line"></text>
        </view>
        <text class="empty-text">暂无沟通记录</text>
        <button class="btn btn-primary" @click="navigateToCreate">添加沟通记录</button>
      </view>
      
      <view class="loading" v-if="isLoading">
        <text class="loading-text">加载中...</text>
      </view>
    </scroll-view>
    
    <!-- 悬浮操作按钮 -->
    <view class="add-fab" @click="navigateToCreate">
      <svg-icon name="add" type="svg" size="60" color="#FFFFFF"></svg-icon>
    </view>

    <!-- 自定义TabBar组件 -->
    <custom-tab-bar ref="customTabBar"></custom-tab-bar>
  </view>
</template>

<script>
import CustomTabBar from '@/components/CustomTabBar.vue';
import SvgIcon from '@/components/svg-icon.vue';

export default {
  components: {
    CustomTabBar,
    SvgIcon
  },
  data() {
    return {
      currentTab: 'all',
      searchText: '',
      isLoading: false,
      tabs: [
        { name: '全部', type: 'all' },
        { name: '电话', type: 'call' },
        { name: '会议', type: 'meeting' },
        { name: '纪要', type: 'note' },
        { name: '邮件', type: 'email' },
        { name: '客户拜访', type: 'visit' }
      ],
      communicationItems: [
        {
          id: '1',
          type: 'call',
          subject: '产品功能确认电话',
          time: '15:30',
          relatedObject: '北京科技有限公司 - 云数据分析平台项目',
          contacts: '张经理',
          owner: '李销售',
          content: '与张经理通话30分钟，讨论了产品功能细节和交付时间。客户关心数据安全问题，需要准备详细的技术方案。',
          tags: ['跟进中', '重要客户']
        },
        {
          id: '2',
          type: 'meeting',
          subject: '项目需求会议',
          time: '昨天',
          relatedObject: '上海电子科技有限公司 - 企业ERP升级项目',
          contacts: '李总, 王经理, 郑工程师, 赵销售',
          owner: '赵销售',
          content: '与客户进行了2小时的需求讨论，确定了项目范围和主要功能模块。客户希望3个月内完成第一阶段上线，预算有限，需要优化方案。',
          tags: ['需求确认', '进行中']
        },
        {
          id: '3',
          type: 'email',
          subject: '合同条款确认',
          time: '周一',
          relatedObject: '广州贸易有限公司 - 进销存系统',
          contacts: '陈总经理',
          owner: '王销售',
          content: '向客户发送了修改后的合同条款，主要调整了付款方式和实施周期。等待客户确认后安排签约。',
          tags: ['合同阶段', '等待回复']
        },
        {
          id: '4',
          type: 'visit',
          subject: '新产品演示',
          time: '上周五',
          relatedObject: '杭州电子商务有限公司',
          contacts: '林总监',
          owner: '周销售',
          content: '前往客户公司进行新产品演示，客户对我们的数据分析功能很感兴趣，但对价格有顾虑。需要准备详细的ROI分析和竞品对比。',
          tags: ['需跟进', '潜在大客户']
        }
      ]
    }
  },
  computed: {
    filteredCommunications() {
      // 根据当前标签和搜索文本过滤沟通记录
      return this.communicationItems.filter(item => {
        // 标签筛选
        const typeMatch = this.currentTab === 'all' || item.type === this.currentTab;
        
        // 搜索筛选
        if (!this.searchText) {
          return typeMatch;
        }
        
        // 搜索所有文本字段
        const searchText = this.searchText.toLowerCase();
        const subject = item.subject.toLowerCase();
        const content = item.content.toLowerCase();
        const relatedObject = (item.relatedObject || '').toLowerCase();
        const contacts = (item.contacts || '').toLowerCase();
        
        return typeMatch && (
          subject.includes(searchText) || 
          content.includes(searchText) || 
          relatedObject.includes(searchText) || 
          contacts.includes(searchText)
        );
      });
    }
  },
  methods: {
    switchTab(type) {
      this.currentTab = type;
    },
    onSearch() {
      // 搜索逻辑已通过计算属性处理
      console.log('搜索:', this.searchText);
    },
    toggleSearch() {
      // 切换搜索框的显示状态
      // 可以根据需要实现
      uni.showToast({
        title: '搜索功能开发中',
        icon: 'none'
      });
    },
    showMoreActions() {
      uni.showActionSheet({
        itemList: ['批量操作', '排序方式', '导出记录'],
        success: (res) => {
          uni.showToast({
            title: '功能开发中',
            icon: 'none'
          });
        }
      });
    },
    showFilter() {
      uni.showToast({
        title: '筛选功能开发中',
        icon: 'none'
      });
    },
    navigateToDetail(id) {
      uni.navigateTo({
        url: `/pages/communication/communication-detail?id=${id}`
      });
    },
    navigateToCreate() {
      uni.navigateTo({
        url: '/pages/communication/communication-create'
      });
    },
    loadMore() {
      if (this.isLoading) return;
      
      this.isLoading = true;
      
      // 模拟加载更多数据
      setTimeout(() => {
        this.isLoading = false;
        uni.showToast({
          title: '没有更多数据了',
          icon: 'none'
        });
      }, 1000);
    },
    getIconClass(type) {
      const iconMap = {
        'call': 'ri-phone-line',
        'meeting': 'ri-team-line',
        'email': 'ri-mail-line',
        'note': 'ri-file-list-line',
        'visit': 'ri-building-line'
      };
      return iconMap[type] || 'ri-chat-3-line';
    },
    getTypeName(type) {
      const typeMap = {
        'call': '电话沟通',
        'meeting': '会议记录',
        'email': '邮件沟通',
        'note': '沟通纪要',
        'visit': '客户拜访'
      };
      return typeMap[type] || '其他沟通';
    },
    getContactLabel(type) {
      if (type === 'meeting') {
        return '参与人';
      } else if (type === 'email') {
        return '收件人';
      } else {
        return '联系人';
      }
    },
    onShow() {
      // 设置TabBar当前选中项
      if (typeof this.$refs.customTabBar !== 'undefined') {
        this.$refs.customTabBar.current = 4; // 对应"更多"菜单
      }
    }
  }
}
</script>

<style>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;
  background-color: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

.page-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.action-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #666;
  background-color: #f5f7fa;
  border: 1px solid #e0e0e0;
}

.tab-container {
  background-color: white;
  padding: 0 15px;
  white-space: nowrap;
  border-bottom: 1px solid #e0e0e0;
  position: sticky;
  top: 56px;
  z-index: 9;
}

.tab {
  display: inline-block;
  padding: 15px 10px;
  font-size: 14px;
  color: #666;
  position: relative;
}

.tab.active {
  color: #3370ff;
  font-weight: 500;
}

.tab.active:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 10px;
  right: 10px;
  height: 2px;
  background-color: #3370ff;
  border-radius: 2px;
}

.search-container {
  padding: 15px;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  gap: 15px;
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 20px;
  padding: 0 15px;
  height: 36px;
}

.search-box text {
  color: #666;
  margin-right: 5px;
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  height: 36px;
  font-size: 14px;
  color: #333;
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #666;
  height: 36px;
  background-color: #f5f7fa;
  border-radius: 20px;
  padding: 0 15px;
}

.communication-list {
  flex: 1;
  height: calc(100vh - 165px);
}

.communication-card {
  background-color: white;
  border-radius: 8px;
  margin: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid #e0e0e0;
}

.communication-header {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.communication-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  flex-shrink: 0;
  background-color: #f5f7fa;
}

.icon-call {
  background-color: #fef3c7;
  color: #d97706;
}

.icon-meeting {
  background-color: #e0e7ff;
  color: #4f46e5;
}

.icon-email {
  background-color: #dcfce7;
  color: #16a34a;
}

.icon-note {
  background-color: #dbeafe;
  color: #2563eb;
}

.icon-visit {
  background-color: #f3e8ff;
  color: #9333ea;
}

.communication-title {
  flex: 1;
}

.communication-type {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
  display: block;
}

.communication-subject {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  display: block;
}

.communication-time {
  font-size: 12px;
  color: #666;
  text-align: right;
  margin-left: 10px;
  flex-shrink: 0;
}

.communication-detail {
  display: flex;
  padding: 0 15px;
  margin-top: 10px;
  font-size: 14px;
}

.detail-label {
  width: 80px;
  color: #666;
  flex-shrink: 0;
}

.detail-value {
  flex: 1;
  color: #333;
}

.communication-content {
  padding: 15px;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  padding: 0 15px 15px;
  margin-top: 5px;
}

.tag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 20px;
  background-color: #f5f7fa;
  color: #666;
}

.add-fab {
  position: fixed;
  bottom: calc(128rpx + var(--spacing-xl)); /* 调整底部位置，避开TabBar */
  right: var(--spacing-xl);
  width: 110rpx; /* 减小尺寸 */
  height: 110rpx; /* 减小尺寸 */
  border-radius: 50%;
  background: linear-gradient(135deg, #0a6bff, #0057ff);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6), 0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  z-index: 100;
  transition: all 0.3s ease;
  border: 4rpx solid rgba(255, 255, 255, 0.7);
  animation: pulse 2s infinite; /* 添加脉动动画 */
}

.add-fab:active {
  transform: scale(0.95);
  box-shadow: 0 5rpx 10rpx rgba(0, 87, 255, 0.5), 0 3rpx 3rpx rgba(0, 87, 255, 0.3);
  animation: none; /* 点击时停止动画 */
}

/* 添加脉动动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6), 0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 15rpx 25rpx rgba(0, 87, 255, 0.7), 0 8rpx 10rpx rgba(0, 87, 255, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6), 0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  text-align: center;
  padding: 20px;
}

.empty-icon {
  font-size: 48px;
  color: #ccc;
  margin-bottom: 15px;
}

.empty-text {
  font-size: 18px;
  color: #666;
  margin-bottom: 15px;
}

.btn-primary {
  background-color: #3370ff;
  color: white;
  padding: 8px 20px;
  border-radius: 4px;
  font-size: 14px;
}

.loading {
  text-align: center;
  padding: 15px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}
</style> 
<template>
  <view class="auth-container">
    <view class="auth-header">
      <view class="logo">CRM</view>
      <text class="welcome-text">创建新账号</text>
      <text class="sub-text">请填写以下信息完成注册</text>
    </view>
    
    <view class="auth-form">
      <view class="form-group">
        <text class="form-label">手机号</text>
        <input type="number" class="form-input" placeholder="请输入手机号" v-model="registerForm.phone" maxlength="11" />
      </view>
      
      <view class="form-group">
        <text class="form-label">验证码</text>
        <view class="verify-code-group">
          <input type="number" class="form-input verify-input" placeholder="请输入验证码" v-model="registerForm.verifyCode" maxlength="6" />
          <button class="verify-btn" :disabled="countdown > 0" @tap="getVerifyCode">
            {{ countdown > 0 ? `${countdown}秒后重试` : '获取验证码' }}
          </button>
        </view>
      </view>
      
      <view class="form-group">
        <text class="form-label">设置密码</text>
        <view class="password-input-group">
          <input :type="passwordVisible ? 'text' : 'password'" class="form-input" placeholder="请设置密码 (至少6位)" v-model="registerForm.password" />
          <text class="password-toggle" @tap="togglePasswordVisibility">
            <text v-if="passwordVisible" class="ri-eye-off-line"></text>
            <text v-else class="ri-eye-line"></text>
          </text>
        </view>
      </view>
      
      <view class="form-group">
        <text class="form-label">确认密码</text>
        <view class="password-input-group">
          <input :type="confirmPasswordVisible ? 'text' : 'password'" class="form-input" placeholder="请再次输入密码" v-model="registerForm.confirmPassword" />
          <text class="password-toggle" @tap="toggleConfirmPasswordVisibility">
            <text v-if="confirmPasswordVisible" class="ri-eye-off-line"></text>
            <text v-else class="ri-eye-line"></text>
          </text>
        </view>
      </view>
      
      <view class="terms-agreement">
        <checkbox :checked="termsAgreed" @tap="toggleTermsAgreement" color="#3a86ff" />
        <view class="terms-text">
          <text>我已阅读并同意</text>
          <text class="terms-link" @tap="showTerms">用户协议</text>
          <text>和</text>
          <text class="terms-link" @tap="showPrivacyPolicy">隐私政策</text>
        </view>
      </view>
      
      <button class="btn btn-primary btn-full" :disabled="!canRegister" @tap="handleRegister">注册</button>
    </view>
    
    <view class="auth-footer">
      <text class="auth-footer-text">
        已有账号？ 
      </text>
      <navigator url="/pages/auth/login" class="auth-footer-link">立即登录</navigator>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      registerForm: {
        phone: '',
        verifyCode: '',
        password: '',
        confirmPassword: ''
      },
      passwordVisible: false,
      confirmPasswordVisible: false,
      termsAgreed: false,
      countdown: 0,
      timer: null,
      isLoading: false
    }
  },
  computed: {
    canRegister() {
      return this.registerForm.phone && 
             this.registerForm.phone.length === 11 && 
             this.registerForm.verifyCode && 
             this.registerForm.verifyCode.length === 6 &&
             this.registerForm.password && 
             this.registerForm.password.length >= 6 &&
             this.registerForm.confirmPassword === this.registerForm.password &&
             this.termsAgreed
    }
  },
  onUnload() {
    // 清除定时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },
  methods: {
    togglePasswordVisibility() {
      this.passwordVisible = !this.passwordVisible
    },
    toggleConfirmPasswordVisibility() {
      this.confirmPasswordVisible = !this.confirmPasswordVisible
    },
    toggleTermsAgreement() {
      this.termsAgreed = !this.termsAgreed
    },
    getVerifyCode() {
      if (this.countdown > 0) {
        return
      }
      
      if (!this.registerForm.phone || this.registerForm.phone.length !== 11) {
        uni.showToast({
          title: '请输入有效的手机号',
          icon: 'none'
        })
        return
      }
      
      // 模拟发送验证码
      uni.showLoading({
        title: '发送中...'
      })
      
      setTimeout(() => {
        uni.hideLoading()
        
        // 开始倒计时
        this.countdown = 60
        this.timer = setInterval(() => {
          this.countdown--
          if (this.countdown <= 0) {
            clearInterval(this.timer)
            this.timer = null
          }
        }, 1000)
        
        uni.showToast({
          title: '验证码已发送',
          icon: 'success'
        })
      }, 1000)
    },
    handleRegister() {
      if (!this.canRegister) {
        return
      }
      
      // 验证手机号
      if (this.registerForm.phone.length !== 11) {
        uni.showToast({
          title: '请输入有效的手机号',
          icon: 'none'
        })
        return
      }
      
      // 验证验证码
      if (this.registerForm.verifyCode.length !== 6) {
        uni.showToast({
          title: '请输入6位验证码',
          icon: 'none'
        })
        return
      }
      
      // 验证密码
      if (this.registerForm.password.length < 6) {
        uni.showToast({
          title: '密码至少6位',
          icon: 'none'
        })
        return
      }
      
      // 验证两次密码是否一致
      if (this.registerForm.password !== this.registerForm.confirmPassword) {
        uni.showToast({
          title: '两次密码不一致',
          icon: 'none'
        })
        return
      }
      
      // 验证是否同意用户协议
      if (!this.termsAgreed) {
        uni.showToast({
          title: '请同意用户协议和隐私政策',
          icon: 'none'
        })
        return
      }
      
      this.isLoading = true
      uni.showLoading({
        title: '注册中...'
      })
      
      // 这里应调用注册接口，以下是模拟注册过程
      setTimeout(() => {
        uni.hideLoading()
        this.isLoading = false
        
        uni.showToast({
          title: '注册成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            // 注册成功后延迟跳转到登录页
            setTimeout(() => {
              uni.redirectTo({
                url: '/pages/auth/login'
              })
            }, 2000)
          }
        })
      }, 1500)
    },
    showTerms() {
      uni.showToast({
        title: '用户协议功能开发中',
        icon: 'none'
      })
    },
    showPrivacyPolicy() {
      uni.showToast({
        title: '隐私政策功能开发中',
        icon: 'none'
      })
    }
  }
}
</script>

<style>
.auth-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 30rpx;
  background-color: #ffffff;
}

.auth-header {
  text-align: center;
  margin: 60rpx 0;
}

.logo {
  font-size: 80rpx;
  font-weight: bold;
  color: #3a86ff;
  margin-bottom: 20rpx;
}

.welcome-text {
  font-size: 48rpx;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}

.sub-text {
  font-size: 32rpx;
  color: #666666;
  display: block;
}

.auth-form {
  margin-top: 20rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  margin-bottom: 10rpx;
  color: #333333;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 90rpx;
  padding: 0 30rpx;
  border: 1px solid #dddddd;
  border-radius: 12rpx;
  font-size: 32rpx;
  box-sizing: border-box;
}

.verify-code-group {
  display: flex;
  align-items: center;
}

.verify-input {
  flex: 1;
}

.verify-btn {
  width: 220rpx;
  height: 90rpx;
  margin-left: 20rpx;
  background-color: #f0f7ff;
  color: #3a86ff;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  border: 1px solid #3a86ff;
}

.verify-btn[disabled] {
  background-color: #f5f5f5;
  color: #999999;
  border-color: #dddddd;
}

.password-input-group {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #666666;
}

.terms-agreement {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.terms-text {
  margin-left: 10rpx;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.4;
  flex: 1;
}

.terms-link {
  color: #3a86ff;
  display: inline;
}

.btn {
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.btn-primary {
  background-color: #3a86ff;
  color: #ffffff;
}

.btn-primary[disabled] {
  background-color: #a6c8ff;
}

.btn-full {
  width: 100%;
}

.auth-footer {
  text-align: center;
  margin-top: auto;
  padding: 40rpx 0;
  display: flex;
  justify-content: center;
}

.auth-footer-text {
  color: #666666;
  font-size: 28rpx;
}

.auth-footer-link {
  color: #3a86ff;
  font-size: 28rpx;
  font-weight: 500;
  margin-left: 10rpx;
}
</style> 
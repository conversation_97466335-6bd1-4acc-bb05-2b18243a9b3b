/* 
 * 全局CSS变量定义
 * 用于统一样式管理和主题设置
 */

:root {
  /* 主色调 */
  --primary-color: #4a6fff;
  --primary-light: #e0e7ff;
  --success-color: #34d399;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  
  /* 文本颜色 */
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --text-light: #999999;
  
  /* 背景颜色 */
  --bg-color: #f5f7fa;
  --bg-card: #ffffff;
  --border-color: #e0e0e0;
  --border-color-light: #f0f0f0;
  
  /* 阴影 */
  --card-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  
  /* 圆角 */
  --radius-sm: 4rpx;
  --radius-md: 8rpx;
  --radius-lg: 16rpx;
  --radius-full: 999rpx;
  
  /* 间距 */
  --spacing-xs: 10rpx;
  --spacing-sm: 20rpx;
  --spacing-md: 30rpx;
  --spacing-lg: 40rpx;
  --spacing-xl: 60rpx;
  
  /* 字体大小 */
  --font-xs: 24rpx;
  --font-sm: 28rpx;
  --font-md: 32rpx;
  --font-lg: 36rpx;
  --font-xl: 40rpx;
  
  /* 其他 */
  --light-color: #f9fafb;
  --transition-normal: all 0.3s ease;
}

/* 暗色主题变量 - 待实现 */
.dark-theme {
  --primary-color: #5d7eff;
  --primary-light: #2e3c80;
  --bg-color: #1a1a1a;
  --bg-card: #2c2c2c;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-tertiary: #999999;
  --border-color: #444444;
  --border-color-light: #333333;
  --light-color: #2a2a2a;
} 
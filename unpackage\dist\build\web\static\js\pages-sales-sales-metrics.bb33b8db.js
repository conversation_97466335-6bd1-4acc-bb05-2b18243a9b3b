(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-sales-sales-metrics"],{"09f2":function(e,t,a){var i=a("e97b");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var n=a("967d").default;n("684c202e",i,!0,{sourceMap:!1,shadowMode:!1})},"3b782":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={data:function(){return{currentTimeFrame:"today",timeOptions:[{label:"今日",value:"today"},{label:"本周",value:"week"},{label:"本月",value:"month"}],metrics:[{title:"销售额",value:"¥128,560",trend:12.5},{title:"订单数",value:"24",trend:8.3},{title:"客单价",value:"¥5,357",trend:-3.2},{title:"转化率",value:"32.5%",trend:2.1}],teamMembers:[{name:"张经理",sales:"¥45,280"},{name:"李经理",sales:"¥38,920"},{name:"王经理",sales:"¥44,360"}]}},methods:{navigateBack:function(){uni.navigateBack()},changeTimeFrame:function(e){this.currentTimeFrame=e,this.fetchMetricsData(e)},fetchMetricsData:function(e){switch(console.log("请求".concat(e,"的销售指标数据")),e){case"today":this.metrics=[{title:"销售额",value:"¥128,560",trend:12.5},{title:"订单数",value:"24",trend:8.3},{title:"客单价",value:"¥5,357",trend:-3.2},{title:"转化率",value:"32.5%",trend:2.1}];break;case"week":this.metrics=[{title:"销售额",value:"¥589,420",trend:8.2},{title:"订单数",value:"98",trend:10.5},{title:"客单价",value:"¥6,014",trend:-2.1},{title:"转化率",value:"35.2%",trend:4.3}];break;case"month":this.metrics=[{title:"销售额",value:"¥2,156,800",trend:15.6},{title:"订单数",value:"312",trend:12.3},{title:"客单价",value:"¥6,913",trend:3.5},{title:"转化率",value:"38.7%",trend:6.8}];break}}}};t.default=i},4873:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"metrics-container"},[a("v-uni-view",{staticClass:"page-header"},[a("v-uni-view",{staticClass:"header-left"},[a("v-uni-view",{staticClass:"back-button",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.navigateBack.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-arrow-left-line"})],1),a("v-uni-text",{staticClass:"page-title"},[e._v("销售指标")])],1),a("v-uni-view",{staticClass:"time-selector"},e._l(e.timeOptions,(function(t,i){return a("v-uni-view",{key:i,staticClass:"time-btn",class:{active:e.currentTimeFrame===t.value},on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.changeTimeFrame(t.value)}}},[e._v(e._s(t.label))])})),1)],1),a("v-uni-view",{staticClass:"chart-card"},[a("v-uni-view",{staticClass:"chart-header"},[a("v-uni-text",{staticClass:"chart-title"},[e._v("销售趋势")])],1),a("v-uni-view",{staticClass:"chart-container"},[a("v-uni-text",{staticClass:"ri-line-chart-line chart-icon"}),a("v-uni-text",{staticClass:"chart-loading"},[e._v("图表加载中...")])],1)],1),a("v-uni-view",{staticClass:"metrics-grid"},e._l(e.metrics,(function(t,i){return a("v-uni-view",{key:i,staticClass:"metric-card"},[a("v-uni-text",{staticClass:"metric-title"},[e._v(e._s(t.title))]),a("v-uni-text",{staticClass:"metric-value"},[e._v(e._s(t.value))]),a("v-uni-view",{staticClass:"metric-trend",class:{negative:t.trend<0}},[a("v-uni-text",{class:t.trend>=0?"ri-arrow-up-line":"ri-arrow-down-line"}),a("v-uni-text",[e._v(e._s(Math.abs(t.trend))+"%")])],1)],1)})),1),a("v-uni-view",{staticClass:"team-performance"},[a("v-uni-text",{staticClass:"chart-title"},[e._v("团队业绩")]),e._l(e.teamMembers,(function(t,i){return a("v-uni-view",{key:i,staticClass:"team-member"},[a("v-uni-view",{staticClass:"member-avatar"},[a("v-uni-text",{staticClass:"ri-user-line"})],1),a("v-uni-view",{staticClass:"member-info"},[a("v-uni-text",{staticClass:"member-name"},[e._v(e._s(t.name))]),a("v-uni-view",{staticClass:"member-performance"},[a("v-uni-text",[e._v("销售额：")]),a("v-uni-text",{staticClass:"performance-value"},[e._v(e._s(t.sales))])],1)],1)],1)}))],2)],1)},n=[]},"8cca":function(e,t,a){"use strict";a.r(t);var i=a("3b782"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);t["default"]=n.a},acc0:function(e,t,a){"use strict";var i=a("09f2"),n=a.n(i);n.a},d0e4:function(e,t,a){"use strict";a.r(t);var i=a("4873"),n=a("8cca");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("acc0");var s=a("828b"),c=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"30896e24",null,!1,i["a"],void 0);t["default"]=c.exports},e97b:function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,".metrics-container[data-v-30896e24]{padding:15px;background-color:#f5f7fa;min-height:100vh}.page-header[data-v-30896e24]{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px}.header-left[data-v-30896e24]{display:flex;align-items:center}.back-button[data-v-30896e24]{margin-right:10px;color:#666;font-size:24px}.page-title[data-v-30896e24]{font-size:18px;font-weight:700;color:#333}.time-selector[data-v-30896e24]{display:flex;gap:8px}.time-btn[data-v-30896e24]{padding:6px 12px;border:1px solid #e0e0e0;border-radius:8px;font-size:14px;color:#666;background-color:#fff}.time-btn.active[data-v-30896e24]{background-color:#3370ff;color:#fff;border-color:#3370ff}.chart-card[data-v-30896e24]{background-color:#fff;border-radius:12px;padding:15px;margin-bottom:20px;box-shadow:0 2px 8px rgba(0,0,0,.05)}.chart-header[data-v-30896e24]{display:flex;justify-content:space-between;align-items:center;margin-bottom:15px}.chart-title[data-v-30896e24]{font-size:16px;font-weight:500;color:#333}.chart-container[data-v-30896e24]{height:200px;display:flex;flex-direction:column;align-items:center;justify-content:center;color:#666}.chart-icon[data-v-30896e24]{font-size:40px;margin-bottom:10px}.chart-loading[data-v-30896e24]{font-size:14px}.metrics-grid[data-v-30896e24]{display:flex;flex-wrap:wrap;margin:0 -5px;margin-bottom:20px}.metric-card[data-v-30896e24]{background-color:#fff;border-radius:12px;padding:15px;box-shadow:0 2px 8px rgba(0,0,0,.05);width:calc(50% - 10px);margin:0 5px 10px 5px;box-sizing:border-box}.metric-title[data-v-30896e24]{font-size:14px;color:#666;margin-bottom:5px}.metric-value[data-v-30896e24]{font-size:20px;font-weight:700;color:#333;margin-bottom:5px}.metric-trend[data-v-30896e24]{font-size:12px;color:#4caf50;display:flex;align-items:center}.metric-trend.negative[data-v-30896e24]{color:#f44336}.team-performance[data-v-30896e24]{background-color:#fff;border-radius:12px;padding:15px;box-shadow:0 2px 8px rgba(0,0,0,.05)}.team-member[data-v-30896e24]{display:flex;align-items:center;padding:10px 0;border-bottom:1px solid #e0e0e0}.team-member[data-v-30896e24]:last-child{border-bottom:none}.member-avatar[data-v-30896e24]{width:40px;height:40px;border-radius:50%;background-color:#e6f0ff;display:flex;align-items:center;justify-content:center;margin-right:15px}.member-avatar uni-text[data-v-30896e24]{font-size:20px;color:#3370ff}.member-info[data-v-30896e24]{flex:1}.member-name[data-v-30896e24]{font-size:14px;color:#333;margin-bottom:5px}.member-performance[data-v-30896e24]{font-size:12px;color:#666;display:flex;align-items:center}.performance-value[data-v-30896e24]{font-weight:500;color:#3370ff}",""]),e.exports=t}}]);
import { request }  from '@/utils/request';

// 获取线索列表
export const getClueList = (data) => {
  return request({
    url: '/api/crm/clue/getList',
    method: 'POST',
    data: data
  });
};
// 新增线索
export const AddNewClue = (data) => {
  return request({
    url: '/api/crm/clue/create',
    method: 'POST',
    data: data
  });
};
// 删除线索
export const deleteClue = (id) => {
  return request({
    url: `/api/crm/clue/delete?id=${id}`,
    method: 'POST',
  });
};
// 获取线索详情
export const getClueDetail = (id) => {
  return request({
    url: `/api/crm/clue/getClueById?id=${id}`,
    method: 'GET',
  });
};
// 编辑线索
export const UpdateClue = (id, data) => {
  return request({
    url: `/api/crm/clue/update?id=${id}`,
    method: 'POST',
    data: data
  });
};
// 获取负责人列表
export const getAllOwnerList = (data) => {
  return request({
    url: '/api/Users/<USER>',
    method: 'POST',
    data: data
  });
};
<template>
  <view class="tab-bar">
    <view 
      v-for="(item, index) in list" 
      :key="index" 
      class="tab-bar-item" 
      :class="{ active: active === index }" 
      @tap="switchTab(item.pagePath)">
      <svg-icon 
        :name="item.iconName" 
        type="svg" 
        :size="48" 
        :color="active === index ? '#4a6fff' : '#6b7280'"
        class="tab-icon"
      ></svg-icon>
      <text class="tab-text">{{ item.text }}</text>
    </view>
  </view>
</template>

<script>
import SvgIcon from './svg-icon.vue';

export default {
  name: 'TabBar',
  components: {
    SvgIcon
  },
  data() {
    return {
      active: 0,
      list: [
        {
          pagePath: "/pages/dashboard/main-dashboard",
          text: "首页",
          iconName: "home"
        },
        {
          pagePath: "/pages/customers/customer-list",
          text: "客户",
          iconName: "customer"
        },
        {
          pagePath: "/pages/sales/opportunity-list",
          text: "销售",
          iconName: "sales"
        },
        {
          pagePath: "/pages/tasks/task-list",
          text: "任务",
          iconName: "task"
        },
        {
          pagePath: "/pages/settings/profile",
          text: "我的",
          iconName: "profile"
        }
      ]
    };
  },
  created() {
    // 确定当前页面对应的tabBar索引
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentRoute = pages[pages.length - 1].route;
      for (let i = 0; i < this.list.length; i++) {
        if (this.list[i].pagePath.includes(currentRoute)) {
          this.active = i;
          break;
        }
      }
    }
  },
  methods: {
    switchTab(url) {
      uni.switchTab({
        url: url
      });
    }
  }
};
</script>

<style>
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #ffffff;
  display: flex;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 999;
}

.tab-bar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 10rpx 0;
}

.tab-icon {
  margin-bottom: 4rpx;
}

.tab-text {
  font-size: 24rpx;
  color: #6b7280;
}

.tab-bar-item.active .tab-text {
  color: #4a6fff;
}
</style> 
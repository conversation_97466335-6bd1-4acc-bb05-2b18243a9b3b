<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="goBack">
        <text class="ri-arrow-left-line"></text>
      </view>
      <text class="page-title">收款历史</text>
    </view>
    
    <scroll-view scroll-y class="history-container">
      <!-- 收款基本信息 -->
      <view class="payment-info">
        <text class="payment-title">{{paymentInfo.title}}</text>
        <view class="payment-meta">
          <text class="payment-id">收款编号: {{paymentInfo.code}}</text>
          <text class="payment-status" :class="'status-' + paymentInfo.statusCode">{{paymentInfo.status}}</text>
        </view>
        <view class="payment-details">
          <text class="payment-customer">客户: {{paymentInfo.customer}}</text>
          <text class="payment-amount">¥{{paymentInfo.amount}}</text>
        </view>
      </view>
      
      <!-- 筛选栏 -->
      <scroll-view scroll-x class="filter-bar">
        <view 
          v-for="(filter, index) in filters" 
          :key="index" 
          :class="['filter-button', currentFilter === filter.type ? 'active' : '']"
          @click="setFilter(filter.type)"
        >
          <text :class="filter.icon"></text>
          <text>{{filter.name}}</text>
        </view>
      </scroll-view>
      
      <!-- 时间轴内容 -->
      <view class="timeline">
        <view 
          v-for="(item, index) in filteredTimeline" 
          :key="index" 
          class="timeline-item"
        >
          <view class="timeline-icon" :class="'icon-' + item.type">
            <text :class="getIconClass(item.type)"></text>
          </view>
          <view class="timeline-content">
            <view class="timeline-header">
              <text class="timeline-title">{{item.title}}</text>
              <text class="timeline-datetime">{{item.date}}</text>
            </view>
            <text class="timeline-operator">操作人: {{item.operator}} | {{item.time}}</text>
            <text class="timeline-message">{{item.message}}</text>
            
            <view class="timeline-details" v-if="item.changes && item.changes.length > 0">
              <view class="change-item" v-for="(change, cIndex) in item.changes" :key="cIndex">
                <text class="change-label">{{change.label}}</text>
                <view class="change-values">
                  <text class="old-value" v-if="change.oldValue">{{change.oldValue}}</text>
                  <text class="arrow-icon ri-arrow-right-line" v-if="change.oldValue && change.newValue"></text>
                  <text class="new-value">{{change.newValue}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 空状态（无记录时显示） -->
      <view class="empty-state" v-if="filteredTimeline.length === 0">
        <text class="ri-history-line empty-icon"></text>
        <text class="empty-title">暂无历史记录</text>
        <text class="empty-description">
          该收款记录尚未产生任何操作历史，修改记录将在此处显示。
        </text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      paymentInfo: {
        title: '系统集成项目 - 第一期',
        code: 'PAY-2023-11-001',
        status: '已完成',
        statusCode: 'completed',
        customer: '上海智能科技',
        amount: '290,975.00'
      },
      currentFilter: 'all',
      filters: [
        { type: 'all', name: '全部', icon: 'ri-filter-line' },
        { type: 'create', name: '创建', icon: 'ri-add-line' },
        { type: 'edit', name: '编辑', icon: 'ri-edit-line' },
        { type: 'status', name: '状态变更', icon: 'ri-exchange-line' },
        { type: 'payment', name: '收款记录', icon: 'ri-bank-card-line' },
        { type: 'comment', name: '备注', icon: 'ri-chat-1-line' }
      ],
      timeline: [
        {
          type: 'payment',
          title: '确认收款',
          date: '2023-11-12',
          operator: '李财务',
          time: '14:30',
          message: '已确认收到客户全额付款，金额¥290,975.00，付款方式为银行转账。',
          changes: [
            { label: '收款状态', oldValue: '待收款', newValue: '已完成' },
            { label: '实收金额', oldValue: '¥0.00', newValue: '¥290,975.00' },
            { label: '交易参考号', oldValue: '-', newValue: 'REF2023111201254895' }
          ]
        },
        {
          type: 'edit',
          title: '编辑收款信息',
          date: '2023-11-12',
          operator: '王销售',
          time: '11:25',
          message: '更新了收款信息，修改了客户银行账号信息。',
          changes: [
            { label: '开户行', oldValue: '中国建设银行上海分行', newValue: '中国建设银行上海张江支行' },
            { label: '客户账号', oldValue: '31050161393600000789', newValue: '31050161393600000123' }
          ]
        },
        {
          type: 'comment',
          title: '添加备注',
          date: '2023-11-12',
          operator: '李财务',
          time: '10:45',
          message: '客户已确认收款金额，将于今日下午通过银行转账支付。',
          changes: []
        },
        {
          type: 'status',
          title: '状态变更',
          date: '2023-11-11',
          operator: '系统',
          time: '00:00',
          message: '系统自动将收款状态从【待收款】变更为【逾期未收】。',
          changes: [
            { label: '收款状态', oldValue: '待收款', newValue: '逾期未收' }
          ]
        },
        {
          type: 'create',
          title: '创建收款记录',
          date: '2023-11-05',
          operator: '李财务',
          time: '15:20',
          message: '基于发票 INV-2023-11-001 创建了收款记录。',
          changes: [
            { label: '收款编号', newValue: 'PAY-2023-11-001' },
            { label: '收款标题', newValue: '系统集成项目 - 第一期' },
            { label: '应收金额', newValue: '¥290,975.00' },
            { label: '收款状态', newValue: '待收款' }
          ]
        }
      ]
    }
  },
  computed: {
    filteredTimeline() {
      if (this.currentFilter === 'all') {
        return this.timeline;
      } else {
        return this.timeline.filter(item => item.type === this.currentFilter);
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    setFilter(type) {
      this.currentFilter = type;
    },
    getIconClass(type) {
      const iconMap = {
        'create': 'ri-add-line',
        'edit': 'ri-edit-line',
        'status': 'ri-exchange-line',
        'payment': 'ri-bank-card-line',
        'comment': 'ri-chat-1-line'
      };
      return iconMap[type] || 'ri-question-line';
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 15px;
  border-bottom: 1px solid #e6e6e6;
  background-color: #ffffff;
}

.page-title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

.back-button {
  color: #666666;
  font-size: 24px;
}

.history-container {
  flex: 1;
  padding: 15px;
  margin-bottom: 60px;
}

.payment-info {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  border: 1px solid #e6e6e6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.payment-title {
  font-size: 17px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333333;
}

.payment-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.payment-id {
  font-size: 14px;
  color: #666666;
}

.payment-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.status-completed {
  background-color: rgba(0, 200, 81, 0.1);
  color: #00c851;
}

.payment-details {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.payment-customer {
  font-size: 14px;
  color: #666666;
}

.payment-amount {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
}

.filter-bar {
  display: flex;
  white-space: nowrap;
  margin-bottom: 15px;
}

.filter-button {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  border-radius: 20px;
  background-color: #f5f5f5;
  color: #666666;
  font-size: 13px;
  border: 1px solid #e6e6e6;
  margin-right: 8px;
}

.filter-button.active {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  border-color: #1890ff;
  font-weight: 500;
}

.timeline {
  position: relative;
  margin-bottom: 15px;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 16px;
  top: 0;
  height: 100%;
  width: 2px;
  background-color: #e6e6e6;
}

.timeline-item {
  position: relative;
  margin-bottom: 25px;
  padding-left: 52px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-icon {
  position: absolute;
  left: 0;
  top: 0;
  width: 34px;
  height: 34px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 18px;
  z-index: 2;
}

.icon-create {
  background-color: #1890ff;
}

.icon-edit {
  background-color: #faad14;
}

.icon-status {
  background-color: #13c2c2;
}

.icon-payment {
  background-color: #52c41a;
}

.icon-comment {
  background-color: #722ed1;
}

.timeline-content {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #e6e6e6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.timeline-title {
  font-weight: 600;
  color: #333333;
}

.timeline-datetime {
  font-size: 13px;
  color: #666666;
}

.timeline-operator {
  font-size: 14px;
  color: #666666;
  margin-bottom: 8px;
}

.timeline-message {
  line-height: 1.5;
  color: #333333;
}

.timeline-details {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 8px;
  font-size: 14px;
  margin-top: 10px;
}

.change-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  padding-bottom: 5px;
  border-bottom: 1px dashed #e6e6e6;
}

.change-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.change-label {
  color: #666666;
}

.change-values {
  display: flex;
  align-items: center;
  gap: 15px;
}

.old-value {
  color: #f5222d;
  text-decoration: line-through;
}

.new-value {
  color: #52c41a;
  font-weight: 500;
}

.arrow-icon {
  color: #666666;
  font-size: 13px;
}

.empty-state {
  text-align: center;
  padding: 30px 15px;
  color: #666666;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 15px;
  color: #e6e6e6;
}

.empty-title {
  font-size: 17px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333333;
}

.empty-description {
  font-size: 14px;
  max-width: 280px;
  margin: 0 auto;
  line-height: 1.5;
}
</style> 
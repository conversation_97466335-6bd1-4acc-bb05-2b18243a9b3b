<template>
  <view class="container" :style="cssVars">
    <!-- 页面顶部Tabs -->
    <scroll-view class="tabs-container" scroll-x>
      <view 
        class="tab" 
        v-for="(stage) in stages"
        :key="stage.id"
        :class="{ active: currentStage === stage.id }"
        @tap="selectStage(stage.id)"
      >
        {{ stage.displayText }}
      </view>
    </scroll-view>
    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <view class="search-icon">
          <svg-icon name="search" type="svg" size="32"></svg-icon>
        </view>
        <input type="text" class="search-input" v-model="searchKeyword" placeholder="搜索商机名称、客户或联系人" />
      </view>
    </view>
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <button class="filter-button" @tap="showFilterPanel">
        <svg-icon name="filter" type="svg" size="28"></svg-icon>
        <text>筛选</text>
      </button>
      <button class="sort-button" @tap="showSortOptions">
        <svg-icon name="sort" type="svg" size="28"></svg-icon>
        <text>排序</text>
      </button>
    </view>
    <!-- 商机列表 -->
    <view class="opportunities-list">
      <!-- 加载中状态 -->
      <view class="empty-state" v-if="isLoading">
        <view class="loading-icon">
          <svg-icon name="loading" type="svg" size="64"></svg-icon>
        </view>
        <view class="empty-title">正在加载</view>
        <view class="empty-description">正在获取商机数据，请稍候...</view>
      </view>
      <!-- 加载错误状态 -->
      <view class="empty-state" v-else-if="loadError">
        <view class="empty-icon">
          <svg-icon name="error" type="svg" size="64"></svg-icon>
        </view>
        <view class="empty-title">加载失败</view>
        <view class="empty-description">获取商机数据失败，请检查网络连接后重试。</view>
        <button class="btn btn-primary" @tap="loadOpportunities">
          <svg-icon name="refresh" type="svg" size="32"></svg-icon> 重新加载
        </button>
      </view>
      <!-- 空状态提示 -->
      <view class="empty-state" v-else-if="opportunities.length === 0">
        <view class="empty-icon">
          <svg-icon name="opportunity" type="svg" size="96"></svg-icon>
        </view>
        <view class="empty-title">暂无商机</view>
        <view class="empty-description">您还没有创建任何商机，点击右下角的加号创建新商机。</view>
        <button class="btn btn-primary" @tap="navigateToCreate">
          <svg-icon name="add" type="svg" size="32"></svg-icon> 创建商机
        </button>
      </view>
      <!-- 商机卡片列表 -->
      <view 
        class="opportunity-card" 
        v-for="(opportunity, index) in opportunities"
        :key="index"
        :data-status="opportunity.businessProcess === null ? 'default' : opportunity.businessProcess.code"
      >
        <view class="card-overlay" @tap="viewOpportunity(opportunity)"></view>
        <view class="opportunity-header">
          <view class="opportunity-info">
            <view class="opportunity-title" @tap="viewOpportunity(opportunity)">{{ opportunity.name }}</view>
            <view class="opportunity-tags">
              <text
                v-if="opportunity.businessProcessName"
                class="tag"
                :class="opportunity.businessProcess === null ? 'tag-default' : 'tag-' + opportunity.businessProcess.code"
              >
                {{ opportunity.businessProcessName }}
              </text>
              <text class="tag tag-company">{{ opportunity.customName }}</text>
            </view>
          </view>
          <view class="opportunity-value">{{ opportunity.expectedTransAmount | price }}</view>
        </view>
        <view class="opportunity-content">
          <view class="opportunity-details">
            <view class="detail-item">
              <view class="detail-label">商机类型:</view>
              <view class="detail-value">{{ opportunity.businessTypeName }}</view>
            </view>
            <view class="detail-item">
              <view class="detail-label">资金币种:</view>
              <view class="detail-value">{{ opportunity.capitalTypeName }}</view>
            </view>
            <view class="detail-item">
              <view class="detail-label">预计成交未税:</view>
              <view class="detail-value">{{ opportunity.expectedTransNoRateAmount | price}}</view>
            </view>
            <view class="detail-item">
              <view class="detail-label">预计成交含税:</view>
              <view class="detail-value">{{ opportunity.expectedTransAmount | price }}</view>
            </view>
            <view class="detail-item">
              <view class="detail-label">预计成单:</view>
              <view class="detail-value">{{ opportunity.expectedCompleteDate | formatDateFilter }}</view>
            </view>
            <view class="detail-item">
              <view class="detail-label">创建日期:</view>
              <view class="detail-value">{{ opportunity.creationTime | formatDateFilter }}</view>
            </view>
          </view>
        </view>
        <view class="opportunity-footer">
<!--          <view class="opportunity-action" @tap="makePhoneCall(opportunity.phone)">
            <svg-icon name="phone" type="svg" size="28"></svg-icon> 电话
          </view>
          <view class="opportunity-action" @tap="navigateToQuotation(opportunity.id)">
            <svg-icon name="file-list" type="svg" size="28"></svg-icon> 报价单
          </view>-->
          <view class="opportunity-action" @tap="editOpportunity(opportunity)">
            <svg-icon name="edit" type="svg" size="28"></svg-icon> 编辑
          </view>
        </view>
      </view>
    </view>
    <!-- 浮动添加按钮 -->
    <view class="fab" @tap="navigateToCreate">
      <svg-icon name="add" type="svg" size="60" color="#FFFFFF"></svg-icon>
    </view>
    <!-- 筛选面板 -->
    <view class="modal-filter" v-if="showFilter">
      <view class="modal-mask" @tap="hideFilterPanel"></view>
      <view class="modal-dialog">
        <view class="modal-header">
          <view class="modal-title">筛选条件</view>
          <view class="modal-close" @tap="hideFilterPanel">
            <svg-icon name="close" type="svg" size="32"></svg-icon>
          </view>
        </view>
        <scroll-view class="modal-content" scroll-y>
          <view class="filter-group">
            <text class="filter-label">预计成交金额范围</text>
            <view class="input-group">
              <input type="number" class="form-input" v-model="filterOptions.expectedTransAmountStart" placeholder="最小金额" />
              <input type="number" class="form-input" v-model="filterOptions.expectedTransAmountEnd" placeholder="最大金额" />
            </view>
          </view>
          <view class="filter-group">
            <text class="filter-label">预计成交日期</text>
            <view class="date-range">
              <view class="date-picker" @tap="showStartDatePicker">
                <view class="date-value">{{ filterOptions.expectedCompleteDateStart || '开始日期' }}</view>
                <view class="date-icon">
                  <svg-icon name="calendar" type="svg" size="24"></svg-icon>
                </view>
              </view>
              <view class="date-separator">至</view>
              <view class="date-picker" @tap="showEndDatePicker">
                <view class="date-value">{{ filterOptions.expectedCompleteDateEnd || '结束日期' }}</view>
                <view class="date-icon">
                  <svg-icon name="calendar" type="svg" size="24"></svg-icon>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
        <view class="modal-footer">
          <button class="btn btn-reset" @tap="resetFilter">重置</button>
          <button class="btn btn-confirm" @tap="applyFilter">应用</button>
        </view>
      </view>
    </view>
    <!-- 日期选择器弹窗 -->
    <view class="uni-picker-popup" v-if="showDatePickerPopup">
      <view class="picker-mask" @tap="showDatePickerPopup = false"></view>
      <view class="picker-content">
        <view class="picker-header">
          <view class="picker-action" @tap="showDatePickerPopup = false">取消</view>
          <view class="picker-title">选择日期</view>
          <view class="picker-action confirm" @tap="confirmDatePicker">确定</view>
        </view>
        <picker-view class="picker-view" :value="datePickerValue" @change="onDatePickerChange">
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in years" :key="index">{{item}}年</view>
          </picker-view-column>
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in months" :key="index">{{item}}月</view>
          </picker-view-column>
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in days" :key="index">{{item}}日</view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>
    <!-- 遮罩层 -->
    <view class="backdrop" :class="{ active: showFilter }" @tap="hideFilterPanel"></view>
    <!-- 自定义TabBar组件 -->
    <custom-tab-bar ref="customTabBar"></custom-tab-bar>
  </view>
</template>

<script>
import SvgIcon from '@/components/svg-icon.vue';
import CustomTabBar from '@/components/CustomTabBar.vue';
import getSelectOptions from "@/utils/dictionary";
import { getBusinessList } from "@/api/business.api";

export default {
  components: {
    SvgIcon,
    CustomTabBar
  },
  computed: {
    // CSS变量计算值
    cssVars() {
      // 将主题色转换为RGB以便在rgba()中使用
      const primaryColor = '#0057ff'; // 假设的主色值
      const primaryColorRGB = this.hexToRgb(primaryColor);
      // 其他主题色变体
      const primaryColorLight = '#3a80ff';
      const primaryColorDark = '#0046cc';
      return {
        '--primary-color-rgb': primaryColorRGB,
        '--primary-color-light': primaryColorLight,
        '--primary-color-dark': primaryColorDark,
        '--light-color-rgb': '245, 247, 250'
      };
    },
  },
  data() {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;
    const currentDay = currentDate.getDate();
    const years = [];
    for (let i = currentYear - 10; i <= currentYear + 10; i++) {
      years.push(i);
    }
    const months = [];
    for (let i = 1; i <= 12; i++) {
      months.push(i);
    }
    const days = [];
    for (let i = 1; i <= 31; i++) {
      days.push(i);
    }
    return {
      searchKeyword: '',
      currentStage: undefined,
      conversionRate: 35,
      showFilter: false,
      // 日期选择器相关
      showDatePickerPopup: false,
      datePickerValue: [10, currentMonth - 1, currentDay - 1],
      tempDatePickerValue: [10, currentMonth - 1, currentDay - 1],
      currentDateType: '', // 'start' 或 'end'
      years,
      months,
      days,
      stages: [],
      filterOptions: {
        expectedTransAmountStart: undefined,
        expectedTransAmountEnd: undefined,
        expectedCompleteDateStart: undefined,
        expectedCompleteDateEnd: undefined,
        sortProperty: undefined,
        sortAsc: undefined
      },
      opportunities: [],
      pageIndex: 1,
      pageSize: 10,
      total: 0,
      isLoading: false,
      loadError: false,
      noMore: false,
    }
  },
  methods: {
    // 将十六进制颜色转换为RGB
    hexToRgb(hex) {
      // 移除#前缀如果存在
      hex = hex.replace(/^#/, '');
      // 解析十六进制
      let bigint = parseInt(hex, 16);
      let r = (bigint >> 16) & 255;
      let g = (bigint >> 8) & 255;
      let b = bigint & 255;
      return `${r}, ${g}, ${b}`;
    },
    // 获取数据字典
    async loadDictionaryOptions() {
      try {
        this.stages = await getSelectOptions('BusinessProcess');
        this.stages.unshift({
          displayText: '全部商机',
          id: undefined,
        })
      } catch (error) {
        this.$message.error('加载字典数据失败');
      }
    },
    // 选择阶段
    selectStage(stageId) {
      this.currentStage = stageId
      this.loadOpportunities(true);
    },
    // 拨打电话
    // makePhoneCall(phone) {
    //   uni.makePhoneCall({
    //     phoneNumber: phone,
    //     fail: () => {
    //       uni.showToast({
    //         title: '拨号取消',
    //         icon: 'none'
    //       })
    //     }
    //   })
    // },
    // 导航到报价单页面
    // navigateToQuotation(id) {
    //   // 获取当前商机的数据
    //   const opportunity = this.opportunities.find(opp => opp.id === id);
    //   if (!opportunity) return;
    //   // 打开操作选择菜单
    //   uni.showActionSheet({
    //     itemList: ['查看关联报价单', '创建新报价单'],
    //     success: (res) => {
    //       if (res.tapIndex === 0) {
    //         // 查看关联报价单 - 跳转到报价单列表页面并筛选
    //         uni.navigateTo({
    //           url: `./quotation-list?opportunityId=${id}&company=${encodeURIComponent(opportunity.company)}`
    //         });
    //       } else if (res.tapIndex === 1) {
    //         // 创建新报价单 - 跳转到报价单创建页面并传递商机ID
    //         uni.navigateTo({
    //           url: `./quotation-create?opportunityId=${id}&opportunityName=${encodeURIComponent(opportunity.title)}&customerId=${encodeURIComponent(opportunity.company)}&customerName=${encodeURIComponent(opportunity.customer)}`
    //         });
    //       }
    //     }
    //   });
    // },
    // 显示/隐藏筛选面板
    showFilterPanel() {
      this.showFilter = true;
      document.body.style.overflow = "hidden";
    },
    hideFilterPanel() {
      this.showFilter = false
      document.body.style.overflow = "";
    },
    // 显示排序选项
    showSortOptions() {
      uni.showActionSheet({
        itemList: ['商机名称', '创建时间'],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.filterOptions.sortProperty = 'Name';
          } else if (res.tapIndex === 1) {
            this.filterOptions.sortProperty = 'CreationTime';
          }
          this.filterOptions.sortAsc = false;
          this.loadOpportunities(true);
        }
      })
    },
    // 重置筛选
    resetFilter() {
      this.hideFilterPanel();
      this.filterOptions = {
        expectedTransAmountStart: undefined,
        expectedTransAmountEnd: undefined,
        expectedCompleteDateStart: undefined,
        expectedCompleteDateEnd: undefined,
        sortProperty: undefined,
        sortAsc: undefined
      }
      uni.showToast({
        title: '筛选条件已重置',
        icon: "none",
      });
      this.loadOpportunities(true);
    },
    // 应用筛选
    applyFilter() {
      this.hideFilterPanel()
      uni.showToast({
        title: '筛选条件已应用',
        icon: 'none'
      })
      this.loadOpportunities(true);
    },
    // 显示开始日期选择器
    showStartDatePicker() {
      this.currentDateType = 'start';
      if (this.filterOptions.expectedCompleteDateStart) {
        const [year, month, day] = this.filterOptions.expectedCompleteDateStart.split('-').map(Number);
        this.datePickerValue = [
          this.years.indexOf(year),
          this.months.indexOf(month),
          this.days.indexOf(day)
        ];
      }
      this.tempDatePickerValue = [...this.datePickerValue];
      this.showDatePickerPopup = true;
    },
    // 显示结束日期选择器
    showEndDatePicker() {
      this.currentDateType = 'end';
      if (this.filterOptions.expectedCompleteDateEnd) {
        const [year, month, day] = this.filterOptions.expectedCompleteDateEnd.split('-').map(Number);
        this.datePickerValue = [
          this.years.indexOf(year),
          this.months.indexOf(month),
          this.days.indexOf(day)
        ];
      }
      this.tempDatePickerValue = [...this.datePickerValue];
      this.showDatePickerPopup = true;
    },
    // 确认日期选择
    confirmDatePicker() {
      const year = this.years[this.tempDatePickerValue[0]];
      const month = this.months[this.tempDatePickerValue[1]];
      const day = this.days[this.tempDatePickerValue[2]];
      const formattedDate = `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`;
      if (this.currentDateType === 'start') {
        this.filterOptions.expectedCompleteDateStart = formattedDate;
      } else {
        this.filterOptions.expectedCompleteDateEnd = formattedDate;
      }
      this.showDatePickerPopup = false;
      console.log(this.filterOptions.expectedCompleteDateStart)
    },
    // 日期选择变化
    onDatePickerChange(e) {
      this.tempDatePickerValue = e.detail.value;
    },
    // 导航到创建商机页面
    navigateToCreate() {
      uni.navigateTo({
        url: './opportunity-create'
      })
    },
    // 查看商机详情
    viewOpportunity(opportunity) {
      uni.navigateTo({
        url: `./opportunity-detail?id=${opportunity.id}`
      })
    },
    // 编辑商机
    editOpportunity(opportunity) {
      uni.navigateTo({
        url: `./opportunity-edit?id=${opportunity.id}`
      })
    },
    // 加载商机数据
    async loadOpportunities(isRefresh = false) {
      if (this.isLoading && !isRefresh) return;
      this.isLoading = true;
      this.loadError = false;
      try {
        const params = {
          pageIndex: isRefresh ? 1 : this.pageIndex,
          pageSize: this.pageSize,
          filter: {
            name: this.searchKeyword,
            businessProcessId: this.currentStage,
            ...this.filterOptions,
            expectedTransAmountStart: Number(this.filterOptions.expectedTransAmountStart),
            expectedTransAmountEnd: Number(this.filterOptions.expectedTransAmountEnd),
          }
        };
        const res = await getBusinessList(params);
        if (isRefresh) {
          this.opportunities = res.items;
          this.pageIndex = 1;
        } else {
          this.opportunities = [...this.opportunities, ...res.items];
        }
        this.total = res.totalCount;
        this.noMore = this.opportunities.length >= this.total;
        if (isRefresh) {
          uni.stopPullDownRefresh();
        }
      } catch (error) {
        this.loadError = true;
      } finally {
        this.isLoading = false;
      }
    },
    // 加载更多数据
    loadMore() {
      if (this.noMore || this.isLoading) return;
      this.pageIndex++;
      this.loadOpportunities();
    },
  },
  onLoad() {
    this.loadDictionaryOptions();
    this.loadOpportunities(true);
  },
  onShow() {
    this.loadOpportunities(true);
    // 设置TabBar选中项
    if (typeof this.$refs.customTabBar !== 'undefined') {
      this.$refs.customTabBar.current = 2;
    } else {
      // 如果refs还没准备好，尝试延迟设置
      setTimeout(() => {
        if (typeof this.$refs.customTabBar !== 'undefined') {
          this.$refs.customTabBar.current = 2;
          console.log('商机页面设置TabBar当前项为2');
        }
      }, 300);
    }
  },
  onPullDownRefresh() {
    this.loadOpportunities(true);
  },
  onReachBottom() {
    this.loadMore();
  },
}
</script>

<style lang="scss">
.container {
  background-color: #f8fafc;
  min-height: 100vh;
}

.tabs-container {
  display: flex;
  white-space: nowrap;
  background-color: #ffffff;
  border-bottom: 1rpx solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.tab {
  display: inline-block;
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--text-secondary);
  font-size: 28rpx;
  position: relative;
  transition: all 0.2s ease;
}

.tab.active {
  color: var(--primary-color);
  font-weight: 600;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: var(--spacing-lg);
  right: var(--spacing-lg);
  height: 4rpx;
  background-color: var(--primary-color);
  border-radius: var(--radius-full);
}

/* 搜索栏样式 */
.search-container {
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: #ffffff;
  border-bottom: 1rpx solid var(--border-color);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
}

.search-box {
  display: flex;
  align-items: center;
  background-color: var(--light-color);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 0 var(--spacing-sm);
  overflow: hidden;
  box-shadow: inset 0 2rpx 5rpx rgba(0, 0, 0, 0.03);
}

.search-icon {
  color: var(--text-secondary);
  padding: var(--spacing-xs);
}

.search-input {
  flex: 1;
  border: none;
  padding: var(--spacing-sm);
  background-color: transparent;
  color: var(--text-primary);
  font-size: 28rpx;
}

/* 筛选栏样式 */
.filter-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: #ffffff;
  border-bottom: 1rpx solid var(--border-color);
}

.filter-button, .sort-button {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: var(--text-secondary);
  padding: 16rpx 24rpx;
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--light-color);
  transition: all 0.2s ease;
}

.filter-button:active, .sort-button:active {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  border-color: var(--primary-color-light);
}

.filter-button text, .sort-button text {
  margin-left: 8rpx;
}

/* 商机列表和卡片样式 */
.opportunities-list {
  padding: var(--spacing-md) 0;
  padding-bottom: calc(var(--spacing-xl) * 4); /* 增加底部填充空间 */
}

.opportunity-card {
  background-color: #ffffff;
  border-radius: var(--radius-md);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  margin-bottom: var(--spacing-md);
  overflow: hidden;
  border: 1rpx solid var(--border-color);
  position: relative;
  transition: all 0.3s ease;
}

.opportunity-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 商机状态标识条 */
.opportunity-card::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background-color: var(--border-color);
  z-index: 2;
}
.opportunity-card[data-status="Survey"]::before {
  background-color: #4f46e5;
}
.opportunity-card[data-status="Proposal"]::before {
  background-color: #d97706;
}
.opportunity-card[data-status="Quote"]::before {
  background-color: #2563eb;
}
.opportunity-card[data-status="Negotiate"]::before {
  background-color: #dc2626;
}
.opportunity-card[data-status="Win"]::before {
  background-color: #059669;
}
.opportunity-card[data-status="Lose"]::before {
  background-color: #6b7280;
}
.opportunity-card[data-status="Cancel"]::before {
  background-color: #6b7280;
}
.opportunity-card[data-status="default"]::before {
  background-color: #6b7280;
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.opportunity-header, .opportunity-content, .opportunity-footer {
  position: relative;
  z-index: 2;
}

/* 确保所有可点击元素在overlay之上 */
.opportunity-title, .opportunity-action {
  position: relative;
  z-index: 3;
}

.opportunity-header {
  padding: var(--spacing-md);
  border-bottom: 1rpx solid var(--border-color-light);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: linear-gradient(to right, rgba(249, 250, 251, 0.5), rgba(255, 255, 255, 0.8));
}

.opportunity-info {
  flex: 1;
}

.opportunity-title {
  font-size: 32rpx;
  font-weight: 600;
  margin: 0 0 12rpx 0;
  color: var(--text-primary);
  cursor: pointer;
  position: relative;
  display: inline-block;
  padding: 4rpx 0;
}

.opportunity-title:hover, .opportunity-title:active {
  color: var(--primary-color);
}

.opportunity-title:active:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background-color: var(--primary-color);
}

.opportunity-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: var(--spacing-xs);
}

.tag {
  padding: 4rpx 16rpx;
  border-radius: var(--radius-full);
  font-size: 22rpx;
  font-weight: 500;
  &.tag-Survey {
    background-color: #e0e7ff;
    color: #4f46e5;
  }
  &.tag-Proposal {
    background-color: #fef3c7;
    color: #d97706;
  }
  &.tag-Quote {
    background-color: #dbeafe;
    color: #2563eb;
  }
  &.tag-Negotiate {
    background-color: #fee2e2;
    color: #dc2626;
  }
  &.tag-Win {
    background-color: #d1fae5;
    color: #059669;
  }
  &.tag-Lose, &.tag-Cancel, &.tag-default {
    background-color: #e5e7eb;
    color: #6b7280;
  }
  &.tag-company {
    background-color: #f3f4f6;
    color: #4b5563;
  }
}

.opportunity-value {
  font-weight: 700;
  color: var(--primary-color);
  font-size: 32rpx;
  background-color: rgba(var(--primary-color-rgb), 0.1);
  padding: 8rpx 16rpx;
  border-radius: var(--radius-md);
  text-align: right;
}

.opportunity-content {
  padding: var(--spacing-md);
}

.opportunity-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
  background-color: rgba(var(--light-color-rgb), 0.3);
  padding: var(--spacing-md) var(--spacing-sm);
  border-radius: var(--radius-md);
}

.detail-item {
  min-width: 0;
  display: flex;
  align-items: baseline;
  margin-bottom: var(--spacing-xs);
}

.detail-label {
  font-size: 24rpx;
  color: var(--text-tertiary);
  margin-right: 8rpx;
  white-space: nowrap;
}

.detail-value {
  font-size: 26rpx;
  color: var(--text-secondary);
  font-weight: 500;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.opportunity-footer {
  display: flex;
  border-top: 1rpx solid var(--border-color);
  background: linear-gradient(to bottom, #ffffff, #f9fafb);
}

.opportunity-action {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 0;
  color: var(--text-secondary);
  font-size: 26rpx;
  transition: all 0.2s ease;
}

.opportunity-action:active {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
}

.opportunity-action:not(:last-child) {
  border-right: 1rpx solid var(--border-color-light);
}

/* 浮动操作按钮 */
.fab {
  position: fixed;
  bottom: calc(128rpx + var(--spacing-xl)); /* 调整底部位置，避开TabBar */
  right: var(--spacing-xl);
  width: 110rpx; /* 减小尺寸 */
  height: 110rpx; /* 减小尺寸 */
  border-radius: 50%;
  background: linear-gradient(135deg, #0a6bff, #0057ff);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6), 0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  z-index: 100;
  transition: all 0.3s ease;
  border: 4rpx solid rgba(255, 255, 255, 0.7);
  animation: pulse 2s infinite; /* 添加脉动动画 */
}

.fab:active {
  transform: scale(0.95);
  box-shadow: 0 5rpx 10rpx rgba(0, 87, 255, 0.5), 0 3rpx 3rpx rgba(0, 87, 255, 0.3);
  animation: none; /* 点击时停止动画 */
}

/* 添加脉动动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6), 0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 15rpx 25rpx rgba(0, 87, 255, 0.7), 0 8rpx 10rpx rgba(0, 87, 255, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6), 0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  }
}

/* 筛选面板样式 */
.modal-filter {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1001;
}

.modal-mask {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-dialog {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideUp 0.3s ease;
  box-shadow: 0 -8rpx 24rpx rgba(0, 0, 0, 0.12);
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.modal-header {
  padding: 24rpx;
  border-bottom: 1rpx solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: var(--text-secondary);
}

.modal-content {
  padding: 24rpx;
  max-height: 60vh;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
}

.modal-footer {
  padding: 24rpx;
  display: flex;
  border-top: 1rpx solid var(--border-color-light);
  background-color: #f9fafb;
}

.filter-group {
  margin-bottom: 32rpx;
  width: 100%;
  box-sizing: border-box;
}

.filter-label {
  display: block;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-secondary);
}

.checkbox-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.input-group {
  display: flex;
  align-items: center;
  gap: 16rpx;
  width: 100%;
  box-sizing: border-box;
}

.input-group text {
  color: var(--text-secondary);
}

.form-input {
  flex: 1;
  padding: 20rpx 24rpx;
  background-color: #f5f7fa;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: var(--text-primary);
  border: 1rpx solid #e0e5ed;
  width: 0;
  min-width: 0;
  box-sizing: border-box;
  height: 80rpx; /* 确保与日期控件高度一致 */
}

.form-input:focus {
  border-color: var(--primary-color);
  background-color: #ffffff;
  outline: none;
}
.date-range {
  display: flex;
  align-items: center;
  gap: 16rpx;
  .date-picker {
    flex: 1;
    padding: 20rpx 24rpx;
    background-color: #f5f7fa;
    border-radius: 12rpx;
    position: relative;
    .date-value {
      font-size: 28rpx;
      color: var(--text-primary);
      padding-right: 40rpx;
    }
    .date-icon {
      position: absolute;
      right: 20rpx;
      top: 50%;
      transform: translateY(-50%);
      color: var(--text-secondary);
    }
  }
  .date-separator {
    font-size: 24rpx;
    color: var(--text-secondary);
  }
}
.btn {
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  flex: 1;
  text-align: center;
}

.btn-reset {
  background-color: #f5f7fa;
  color: var(--text-secondary);
  margin-right: 16rpx;
  border: 1rpx solid #e0e5ed;
}

.btn-reset:active {
  background-color: #e5e7eb;
}

.btn-confirm {
  background-color: var(--primary-color);
  color: #ffffff;
  font-weight: 500;
}

.btn-confirm:active {
  background-color: var(--primary-color-dark);
}

/* 选择器弹窗样式 */
.uni-picker-popup {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1002;
}

.picker-mask {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.picker-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  animation: slideUp 0.3s ease;
  box-shadow: 0 -8rpx 24rpx rgba(0, 0, 0, 0.12);
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.picker-action {
  font-size: 28rpx;
  color: var(--text-secondary);
  padding: 8rpx 16rpx;
}

.picker-action.confirm {
  color: var(--primary-color);
  font-weight: 500;
}

.picker-action:active {
  opacity: 0.7;
}

.picker-title {
  font-size: 30rpx;
  font-weight: 500;
  color: var(--text-primary);
}

.picker-view {
  height: 400rpx;
  width: 100%;
}

.picker-item {
  line-height: 100rpx;
  text-align: center;
  font-size: 28rpx;
  color: var(--text-primary);
}

/* 空状态样式 */
.empty-state {
  padding: var(--spacing-xl) var(--spacing-lg);
  text-align: center;
  background-color: #ffffff;
  border-radius: var(--radius-lg);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  margin: var(--spacing-lg) 0;
}

.empty-icon, .loading-icon {
  margin-bottom: var(--spacing-md);
  color: var(--border-color);
  display: inline-block;
}

.loading-icon {
  animation: rotating 2s linear infinite;
  color: var(--primary-color);
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.empty-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: 1.5;
}

/* 增加底部间距，避免内容被底部操作栏遮挡 */
.page-container {
  padding-bottom: calc(128rpx + var(--spacing-md) * 2 + var(--spacing-lg));
}
</style> 
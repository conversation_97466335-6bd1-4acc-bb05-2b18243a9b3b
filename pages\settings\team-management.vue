<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="flex-row items-center gap-sm">
        <text class="page-title">团队管理</text>
      </view>
      <view class="header-actions">
        <button class="action-button" @tap="addMember">
          <svg-icon name="add" type="svg" size="36" color="#666666"></svg-icon>
        </button>
      </view>
    </view>
    
    <scroll-view scroll-y class="page-content">
      <!-- 团队统计数据 -->
      <view class="stats-container">
        <view class="stat-card">
          <text class="stat-value">8</text>
          <text class="stat-label">团队成员</text>
        </view>
        <view class="stat-card">
          <text class="stat-value">3</text>
          <text class="stat-label">管理人员</text>
        </view>
        <view class="stat-card">
          <text class="stat-value">5</text>
          <text class="stat-label">销售人员</text>
        </view>
      </view>
      
      <!-- 筛选和搜索 -->
      <view class="filter-container">
        <view class="search-box">
          <svg-icon name="search" type="svg" size="32" color="#999999"></svg-icon>
          <input type="text" placeholder="搜索成员" v-model="searchText" />
        </view>
        <view class="filter-pills">
          <view class="filter-pill" :class="{'active': currentFilter === 'all'}" @tap="setFilter('all')">
            <text>全部</text>
          </view>
          <view class="filter-pill" :class="{'active': currentFilter === 'manager'}" @tap="setFilter('manager')">
            <text>管理人员</text>
          </view>
          <view class="filter-pill" :class="{'active': currentFilter === 'sales'}" @tap="setFilter('sales')">
            <text>销售人员</text>
          </view>
        </view>
      </view>
      
      <!-- 团队成员列表 -->
      <view class="members-list">
        <view class="member-card" v-for="(member, index) in filteredMembers" :key="index" @tap="viewMember(member)">
          <view class="member-avatar" :style="{'background-color': member.avatarBg}">
            <text>{{member.avatar}}</text>
          </view>
          <view class="member-info">
            <view class="member-name-row">
              <text class="member-name">{{member.name}}</text>
              <text class="member-role" :class="{'role-manager': member.role === '管理员'}">{{member.role}}</text>
            </view>
            <text class="member-title">{{member.title}}</text>
            <view class="member-stats">
              <view class="member-stat">
                <svg-icon name="user" type="svg" size="28" color="#4a6fff"></svg-icon>
                <text>{{member.customers}} 客户</text>
              </view>
              <view class="member-stat">
                <svg-icon name="medal" type="svg" size="28" color="#4a6fff"></svg-icon>
                <text>{{member.deals}} 成交</text>
              </view>
            </view>
          </view>
          <view class="member-actions">
            <button class="action-btn" @tap.stop="callMember(member)">
              <svg-icon name="phone" type="svg" size="28" color="#666666"></svg-icon>
            </button>
            <button class="action-btn" @tap.stop="messageMember(member)">
              <svg-icon name="message" type="svg" size="28" color="#666666"></svg-icon>
            </button>
            <button class="action-btn" @tap.stop="showMemberOptions(member)">
              <svg-icon name="more" type="svg" size="28" color="#666666"></svg-icon>
            </button>
          </view>
        </view>
      </view>
      
      <!-- 邀请提示 -->
      <view class="invite-card">
        <text class="invite-title">邀请新团队成员</text>
        <text class="invite-desc">发送邀请链接，让新同事加入团队</text>
        <button class="invite-btn" @tap="showInviteOptions">
          <text>生成邀请链接</text>
        </button>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import SvgIcon from '@/components/svg-icon.vue';

export default {
  components: {
    SvgIcon
  },
  data() {
    return {
      searchText: '',
      currentFilter: 'all',
      members: [
        {
          id: 1,
          name: '李经理',
          avatar: 'LJ',
          avatarBg: '#4a6fff20',
          role: '管理员',
          title: '销售经理',
          customers: 42,
          deals: 18,
          phone: '13812345678',
          email: '<EMAIL>'
        },
        {
          id: 2,
          name: '张小明',
          avatar: 'ZM',
          avatarBg: '#34d39920',
          role: '管理员',
          title: '市场总监',
          customers: 35,
          deals: 15,
          phone: '13812345679',
          email: '<EMAIL>'
        },
        {
          id: 3,
          name: '王丽',
          avatar: 'WL',
          avatarBg: '#f59e0b20',
          role: '管理员',
          title: '客户经理',
          customers: 56,
          deals: 24,
          phone: '13812345680',
          email: '<EMAIL>'
        },
        {
          id: 4,
          name: '赵强',
          avatar: 'ZQ',
          avatarBg: '#ef444420',
          role: '销售员',
          title: '资深销售顾问',
          customers: 38,
          deals: 16,
          phone: '13812345681',
          email: '<EMAIL>'
        },
        {
          id: 5,
          name: '陈静',
          avatar: 'CJ',
          avatarBg: '#8b5cf620',
          role: '销售员',
          title: '销售顾问',
          customers: 27,
          deals: 9,
          phone: '13812345682',
          email: '<EMAIL>'
        },
        {
          id: 6,
          name: '刘洋',
          avatar: 'LY',
          avatarBg: '#ec489920',
          role: '销售员',
          title: '销售顾问',
          customers: 24,
          deals: 8,
          phone: '13812345683',
          email: '<EMAIL>'
        },
        {
          id: 7,
          name: '孙亮',
          avatar: 'SL',
          avatarBg: '#10b98120',
          role: '销售员',
          title: '初级销售顾问',
          customers: 18,
          deals: 4,
          phone: '13812345684',
          email: '<EMAIL>'
        },
        {
          id: 8,
          name: '林小华',
          avatar: 'LH',
          avatarBg: '#64748b20',
          role: '销售员',
          title: '初级销售顾问',
          customers: 15,
          deals: 3,
          phone: '13812345685',
          email: '<EMAIL>'
        }
      ]
    }
  },
  computed: {
    filteredMembers() {
      let result = this.members;
      
      // 根据角色筛选
      if (this.currentFilter === 'manager') {
        result = result.filter(member => member.role === '管理员');
      } else if (this.currentFilter === 'sales') {
        result = result.filter(member => member.role === '销售员');
      }
      
      // 根据搜索文本筛选
      if (this.searchText) {
        const searchLower = this.searchText.toLowerCase();
        result = result.filter(member => 
          member.name.toLowerCase().includes(searchLower) ||
          member.title.toLowerCase().includes(searchLower)
        );
      }
      
      return result;
    }
  },
  methods: {
    setFilter(filter) {
      this.currentFilter = filter;
    },
    viewMember(member) {
      uni.showToast({
        title: `查看${member.name}的详细信息`,
        icon: 'none'
      });
    },
    addMember() {
      uni.navigateTo({
        url: '/pages/settings/add-team-member'
      });
    },
    callMember(member) {
      uni.makePhoneCall({
        phoneNumber: member.phone,
        success: () => {
          console.log("拨打电话成功");
        },
        fail: (err) => {
          console.log("拨打电话失败", err);
          uni.showToast({
            title: '拨号失败',
            icon: 'none'
          });
        }
      });
    },
    messageMember(member) {
      uni.showToast({
        title: `发消息给${member.name}`,
        icon: 'none'
      });
    },
    showMemberOptions(member) {
      uni.showActionSheet({
        itemList: ['查看详情', '编辑信息', '设置权限', '调整部门', '停用账号'],
        success: (res) => {
          switch(res.tapIndex) {
            case 0:
              this.viewMember(member);
              break;
            case 1:
              uni.showToast({ title: '编辑信息功能开发中', icon: 'none' });
              break;
            case 2:
              uni.showToast({ title: '权限设置功能开发中', icon: 'none' });
              break;
            case 3:
              uni.showToast({ title: '部门调整功能开发中', icon: 'none' });
              break;
            case 4:
              this.confirmDisableMember(member);
              break;
          }
        }
      });
    },
    confirmDisableMember(member) {
      uni.showModal({
        title: '停用账号',
        content: `确定要停用${member.name}的账号吗？停用后该成员将无法登录系统。`,
        confirmText: '停用',
        confirmColor: '#ef4444',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '账号已停用',
              icon: 'success'
            });
          }
        }
      });
    },
    showInviteOptions() {
      uni.showActionSheet({
        itemList: ['生成链接', '发送邮件邀请', '分享到微信'],
        success: (res) => {
          uni.showToast({
            title: '邀请功能开发中',
            icon: 'none'
          });
        }
      });
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.page-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  position: relative;
  z-index: 10;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.header-actions {
  display: flex;
  flex-direction: row;
  gap: 20rpx;
}

.action-button {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  color: #666;
  padding: 0;
  margin: 0;
  line-height: 1;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.gap-sm {
  gap: 20rpx;
}

.page-content {
  flex: 1;
  padding: 30rpx;
}

.stats-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.stat-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx;
  flex: 1;
  margin: 0 10rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #e0e0e0;
}

.stat-value {
  font-size: 40rpx;
  font-weight: bold;
  color: #4a6fff;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666666;
}

.filter-container {
  margin-bottom: 30rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid #e0e0e0;
}

.search-box svg-icon {
  margin-right: 20rpx;
  flex-shrink: 0;
}

.search-box input {
  flex: 1;
  height: 40rpx;
  font-size: 28rpx;
}

.filter-pills {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 10rpx;
}

.filter-pill {
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  background-color: #ffffff;
  border: 1rpx solid #e0e0e0;
  font-size: 24rpx;
  color: #666666;
}

.filter-pill.active {
  background-color: #4a6fff;
  color: #ffffff;
  border-color: #4a6fff;
}

.members-list {
  margin-bottom: 30rpx;
}

.member-card {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #e0e0e0;
}

.member-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  font-weight: bold;
  color: #4a6fff;
  margin-right: 30rpx;
}

.member-info {
  flex: 1;
}

.member-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.member-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-right: 20rpx;
}

.member-role {
  font-size: 22rpx;
  background-color: #e0e0e0;
  color: #666666;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
}

.role-manager {
  background-color: #4a6fff20;
  color: #4a6fff;
}

.member-title {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 16rpx;
}

.member-stats {
  display: flex;
  gap: 30rpx;
}

.member-stat {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999999;
}

.member-stat svg-icon {
  margin-right: 8rpx;
  flex-shrink: 0;
}

.member-actions {
  display: flex;
  gap: 10rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border: 1rpx solid #e0e0e0;
  color: #666666;
  padding: 0;
  margin: 0;
  line-height: 1;
  flex-shrink: 0;
}

.invite-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #e0e0e0;
  margin-bottom: 30rpx;
}

.invite-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 10rpx;
}

.invite-desc {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 30rpx;
}

.invite-btn {
  background-color: #4a6fff;
  color: #ffffff;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  display: inline-block;
}
</style> 
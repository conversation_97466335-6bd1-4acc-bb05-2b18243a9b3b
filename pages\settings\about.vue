<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="flex-row items-center gap-sm">
        <text class="page-title">关于应用</text>
      </view>
    </view>
    <scroll-view scroll-y class="page-content">
      <!-- 应用信息 -->
      <view class="app-logo-container">
        <view class="app-logo">
          <svg-icon name="dashboard" type="svg" size="80" color="#4a6fff"></svg-icon>
        </view>
        <text class="app-name">CRM移动助手</text>
        <text class="app-version">版本 1.0.0</text>
      </view>
      <!-- 功能介绍 -->
      <view class="info-card">
        <text class="card-title">应用介绍</text>
        <text class="card-desc">
          CRM移动助手是一款专为销售人员设计的移动端客户关系管理工具，
          提供客户管理、销售跟踪、任务管理等全方位功能，帮助您随时随地管理业务。
        </text>
      </view>
      <!-- 功能列表 -->
      <view class="info-card">
        <text class="card-title">主要功能</text>
        <view class="feature-list">
          <view class="feature-item">
            <svg-icon name="customer" type="svg" size="40" color="#4a6fff"></svg-icon>
            <text class="feature-name">客户管理</text>
          </view>
          <view class="feature-item">
            <svg-icon name="opportunity" type="svg" size="40" color="#4a6fff"></svg-icon>
            <text class="feature-name">商机跟踪</text>
          </view>
          <view class="feature-item">
            <svg-icon name="tasks" type="svg" size="40" color="#4a6fff"></svg-icon>
            <text class="feature-name">任务管理</text>
          </view>
          <view class="feature-item">
            <svg-icon name="communication" type="svg" size="40" color="#4a6fff"></svg-icon>
            <text class="feature-name">沟通记录</text>
          </view>
          <view class="feature-item">
            <svg-icon name="contract" type="svg" size="40" color="#4a6fff"></svg-icon>
            <text class="feature-name">合同管理</text>
          </view>
          <view class="feature-item">
            <svg-icon name="report" type="svg" size="40" color="#4a6fff"></svg-icon>
            <text class="feature-name">数据报表</text>
          </view>
        </view>
      </view>
      
      <!-- 开发信息 -->
      <view class="info-card">
        <text class="card-title">开发信息</text>
        <view class="info-item">
          <text class="info-label">开发者</text>
          <text class="info-value">苏州奥斯坦丁软件科技有限公司</text>
        </view>
        <view class="info-item">
          <text class="info-label">发布日期</text>
          <text class="info-value">2025年7月15日</text>
        </view>
        <view class="info-item">
          <text class="info-label">技术支持</text>
          <text class="info-value"><EMAIL></text>
        </view>
      </view>
      
      <!-- 联系我们 -->
      <view class="info-card">
        <text class="card-title">联系我们</text>
        <view class="contact-buttons">
          <button class="contact-btn" @tap="contactUs('official')">
            <svg-icon name="building" type="svg" size="32"></svg-icon>
            <text>官方网站</text>
          </button>
          <button class="contact-btn" @tap="contactUs('wechat')">
            <svg-icon name="share" type="svg" size="32"></svg-icon>
            <text>微信公众号</text>
          </button>
          <button class="contact-btn" @tap="contactUs('email')">
            <svg-icon name="mail" type="svg" size="32"></svg-icon>
            <text>电子邮件</text>
          </button>
        </view>
      </view>
      
      <!-- 隐私和条款 -->
      <view class="info-card">
        <text class="card-title">法律信息</text>
        <view class="legal-links">
          <text class="legal-link" @tap="openLegal('privacy')">隐私政策</text>
          <text class="legal-link" @tap="openLegal('terms')">使用条款</text>
          <text class="legal-link" @tap="openLegal('license')">开源许可</text>
        </view>
      </view>
      
      <!-- 版权信息 -->
      <view class="copyright">
        <text>© 2023 销售云科技. 保留所有权利</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import SvgIcon from '@/components/svg-icon.vue';

export default {
  components: {
    SvgIcon
  },
  data() {
    return {
      
    }
  },
  methods: {
    contactUs(type) {
      switch(type) {
        case 'official':
          // 打开官方网站
          uni.showToast({
            title: '即将跳转到官方网站',
            icon: 'none'
          });
          break;
        case 'wechat':
          // 显示微信二维码
          uni.showToast({
            title: '微信公众号: SalesCloudCRM',
            icon: 'none'
          });
          break;
        case 'email':
          // 发送邮件
          uni.showToast({
            title: '正在打开邮件应用',
            icon: 'none'
          });
          break;
      }
    },
    openLegal(type) {
      uni.showToast({
        title: `正在打开${type === 'privacy' ? '隐私政策' : type === 'terms' ? '使用条款' : '开源许可'}`,
        icon: 'none'
      });
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.page-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  position: relative;
  z-index: 10;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.gap-sm {
  gap: 20rpx;
}

.page-content {
  flex: 1;
  padding: 30rpx;
}

.app-logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
}

.app-logo {
  width: 160rpx;
  height: 160rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  background-color: #4a6fff10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.app-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.app-version {
  font-size: 26rpx;
  color: #666666;
}

.info-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #e0e0e0;
}

.card-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.card-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}

.feature-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.feature-item {
  width: 30%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.feature-name {
  font-size: 26rpx;
  color: #666666;
  margin-top: 10rpx;
  text-align: center;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eaecef;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666666;
}

.info-value {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.contact-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 10rpx;
}

.contact-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f9fafb;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 20rpx 0;
  width: 30%;
  margin: 0;
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
}

.contact-btn text {
  margin-top: 10rpx;
}

.legal-links {
  display: flex;
  justify-content: space-between;
}

.legal-link {
  font-size: 28rpx;
  color: #4a6fff;
  padding: 10rpx 0;
}

.copyright {
  text-align: center;
  font-size: 24rpx;
  color: #999999;
  margin: 30rpx 0 60rpx;
}
</style> 
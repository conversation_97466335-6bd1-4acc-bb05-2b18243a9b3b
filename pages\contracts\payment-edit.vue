<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="goBack">
        <text class="ri-arrow-left-line"></text>
      </view>
      <text class="page-title">编辑收款记录</text>
    </view>
    
    <scroll-view scroll-y class="edit-container">
      <!-- 提示信息 -->
      <view class="notification-bar">
        <text class="ri-information-line"></text>
        <text>修改收款记录将产生历史记录，请确认修改内容正确。</text>
      </view>
      
      <form @submit.prevent="savePayment">
        <!-- 基本信息 -->
        <view class="form-section">
          <view class="section-title">
            <text class="ri-file-list-line"></text>
            <text>基本信息</text>
          </view>
          
          <view class="form-row">
            <text class="form-label">
              收款标题 <text class="required-mark">*</text>
            </text>
            <input 
              type="text" 
              class="form-input" 
              v-model="formData.paymentTitle" 
              placeholder="请输入收款标题" 
              :class="{'input-error': errors.paymentTitle}"
            />
            <text v-if="errors.paymentTitle" class="error-message">{{errors.paymentTitle}}</text>
          </view>
          
          <view class="form-grid">
            <view class="form-row">
              <text class="form-label">
                收款编号 <text class="required-mark">*</text>
              </text>
              <input 
                type="text" 
                class="form-input" 
                v-model="formData.paymentNumber" 
                disabled
              />
            </view>
            
            <view class="form-row">
              <text class="form-label">
                收款状态 <text class="required-mark">*</text>
              </text>
              <picker 
                @change="onStatusChange" 
                :value="statusIndex" 
                :range="paymentStatuses" 
                range-key="name"
              >
                <view class="picker-view">
                  <text>{{selectedStatus.name || '请选择收款状态'}}</text>
                  <text class="ri-arrow-down-s-line"></text>
                </view>
              </picker>
              <text v-if="errors.paymentStatus" class="error-message">{{errors.paymentStatus}}</text>
            </view>
            
            <view class="form-row">
              <text class="form-label">
                收款日期 <text class="required-mark">*</text>
              </text>
              <view class="date-input">
                <picker 
                  mode="date" 
                  @change="onDateChange" 
                  :value="formData.paymentDate"
                >
                  <view class="picker-view">
                    <text>{{formData.paymentDate || '请选择日期'}}</text>
                    <text class="ri-calendar-line"></text>
                  </view>
                </picker>
              </view>
              <text v-if="errors.paymentDate" class="error-message">{{errors.paymentDate}}</text>
            </view>
            
            <view class="form-row">
              <text class="form-label">
                关联发票 <text class="required-mark">*</text>
              </text>
              <picker 
                @change="onInvoiceChange" 
                :value="invoiceIndex" 
                :range="invoices" 
                range-key="name"
              >
                <view class="picker-view">
                  <text>{{selectedInvoice.name || '请选择关联发票'}}</text>
                  <text class="ri-arrow-down-s-line"></text>
                </view>
              </picker>
              <text v-if="errors.relatedInvoice" class="error-message">{{errors.relatedInvoice}}</text>
            </view>
            
            <view class="form-row">
              <text class="form-label">关联合同</text>
              <picker 
                @change="onContractChange" 
                :value="contractIndex" 
                :range="contracts" 
                range-key="name"
              >
                <view class="picker-view">
                  <text>{{selectedContract.name || '请选择关联合同'}}</text>
                  <text class="ri-arrow-down-s-line"></text>
                </view>
              </picker>
            </view>
          </view>
        </view>
        
        <!-- 客户信息 -->
        <view class="form-section">
          <view class="section-title">
            <text class="ri-user-3-line"></text>
            <text>客户信息</text>
          </view>
          
          <view class="form-row">
            <text class="form-label">
              客户名称 <text class="required-mark">*</text>
            </text>
            <picker 
              @change="onCustomerChange" 
              :value="customerIndex" 
              :range="customers" 
              range-key="name"
            >
              <view class="picker-view">
                <text>{{selectedCustomer.name || '请选择客户'}}</text>
                <text class="ri-arrow-down-s-line"></text>
              </view>
            </picker>
            <text v-if="errors.customer" class="error-message">{{errors.customer}}</text>
          </view>
          
          <view class="form-grid">
            <view class="form-row">
              <text class="form-label">联系人</text>
              <picker 
                @change="onContactChange" 
                :value="contactIndex" 
                :range="contacts" 
                range-key="name"
                :disabled="!formData.customer"
              >
                <view class="picker-view">
                  <text>{{selectedContact.name || '请选择联系人'}}</text>
                  <text class="ri-arrow-down-s-line"></text>
                </view>
              </picker>
            </view>
            
            <view class="form-row">
              <text class="form-label">客户账号</text>
              <input 
                type="text" 
                class="form-input" 
                v-model="formData.customerBankNumber" 
                placeholder="请输入客户银行账号"
              />
            </view>
            
            <view class="form-row full-width">
              <text class="form-label">开户行</text>
              <input 
                type="text" 
                class="form-input" 
                v-model="formData.customerBankName" 
                placeholder="请输入开户行名称"
              />
            </view>
          </view>
        </view>
        
        <!-- 收款金额 -->
        <view class="form-section">
          <view class="section-title">
            <text class="ri-money-cny-circle-line"></text>
            <text>收款金额</text>
          </view>
          
          <view class="form-grid">
            <view class="form-row">
              <text class="form-label">
                应收金额 <text class="required-mark">*</text>
              </text>
              <view class="input-addon">
                <text class="input-addon-text">¥</text>
                <input 
                  type="digit" 
                  class="form-input" 
                  v-model="formData.expectedAmount" 
                  placeholder="0.00"
                  :class="{'input-error': errors.expectedAmount}"
                />
              </view>
              <text v-if="errors.expectedAmount" class="error-message">{{errors.expectedAmount}}</text>
            </view>
            
            <view class="form-row">
              <text class="form-label">
                实收金额 <text class="required-mark">*</text>
              </text>
              <view class="input-addon">
                <text class="input-addon-text">¥</text>
                <input 
                  type="digit" 
                  class="form-input" 
                  v-model="formData.actualAmount" 
                  placeholder="0.00"
                  :class="{'input-error': errors.actualAmount}"
                />
              </view>
              <text v-if="errors.actualAmount" class="error-message">{{errors.actualAmount}}</text>
            </view>
          </view>
          
          <view class="form-row">
            <text class="form-label">收款方式 <text class="required-mark">*</text></text>
            <scroll-view scroll-x class="payment-method-group">
              <view 
                v-for="(method, index) in paymentMethods" 
                :key="index"
                :class="['payment-method-option', formData.paymentMethod === method.value ? 'selected' : '']"
                @click="selectPaymentMethod(method.value)"
              >
                <text :class="['payment-method-icon', method.icon]"></text>
                <text class="payment-method-label">{{method.label}}</text>
              </view>
            </scroll-view>
            <text v-if="errors.paymentMethod" class="error-message">{{errors.paymentMethod}}</text>
          </view>
          
          <view class="form-row">
            <text class="form-label">交易参考号</text>
            <input 
              type="text" 
              class="form-input" 
              v-model="formData.transactionReference" 
              placeholder="请输入交易参考号或流水号"
            />
          </view>
        </view>
        
        <!-- 备注信息 -->
        <view class="form-section">
          <view class="section-title">
            <text class="ri-chat-1-line"></text>
            <text>备注信息</text>
          </view>
          
          <view class="form-row">
            <text class="form-label">备注内容</text>
            <textarea 
              class="form-input textarea" 
              v-model="formData.notes" 
              placeholder="请输入备注信息，如收款特殊情况说明等"
            ></textarea>
          </view>
        </view>
      </form>
    </scroll-view>
    
    <view class="action-bar">
      <button class="action-btn cancel-button" @click="handleCancel">
        <text class="ri-close-line"></text>
        <text>取消</text>
      </button>
      <button class="action-btn save-button" @click="savePayment">
        <text class="ri-save-line"></text>
        <text>保存修改</text>
      </button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 表单数据
      formData: {
        paymentTitle: '系统集成项目 - 第一期',
        paymentNumber: 'PAY-2023-11-001',
        paymentStatus: 'completed',
        paymentDate: '2023-11-12',
        relatedInvoice: 'INV-2023-11-001',
        relatedContract: 'CT-2023-09-001',
        customer: '1',
        contactPerson: '1',
        customerBankNumber: '31050161393600000123',
        customerBankName: '中国建设银行上海张江支行',
        expectedAmount: '290975.00',
        actualAmount: '290975.00',
        paymentMethod: 'bankTransfer',
        transactionReference: 'REF2023111201254895',
        notes: '客户已通过银行转账方式支付全款，交易参考号: REF2023111201254895。'
      },
      
      // 下拉选项
      paymentStatuses: [
        { code: 'completed', name: '已完成' },
        { code: 'partial', name: '部分收款' },
        { code: 'pending', name: '待收款' },
        { code: 'overdue', name: '逾期未收' }
      ],
      
      invoices: [
        { id: '', name: '请选择关联发票' },
        { id: 'INV-2023-11-001', name: 'INV-2023-11-001 (¥290,975.00)', amount: '290975.00', customerId: '1' },
        { id: 'INV-2023-10-002', name: 'INV-2023-10-002 (¥120,000.00)', amount: '120000.00', customerId: '2' },
        { id: 'INV-2023-09-001', name: 'INV-2023-09-001 (¥168,000.00)', amount: '168000.00', customerId: '3' }
      ],
      
      contracts: [
        { id: '', name: '请选择关联合同' },
        { id: 'CT-2023-09-001', name: 'CT-2023-09-001 (企业系统集成项目合同)' },
        { id: 'CT-2023-08-001', name: 'CT-2023-08-001 (软件定制开发合同)' },
        { id: 'CT-2023-07-001', name: 'CT-2023-07-001 (年度维护服务合同)' }
      ],
      
      customers: [
        { id: '', name: '请选择客户' },
        { id: '1', name: '上海智能科技有限公司' },
        { id: '2', name: '北京数字科技有限公司' },
        { id: '3', name: '深圳创新电子有限公司' }
      ],
      
      contacts: [
        { id: '', name: '请选择联系人', customerId: '' },
        { id: '1', name: '张总经理 (***********)', customerId: '1' },
        { id: '2', name: '李财务 (***********)', customerId: '1' },
        { id: '3', name: '王经理 (***********)', customerId: '2' },
        { id: '4', name: '赵总监 (***********)', customerId: '3' }
      ],
      
      paymentMethods: [
        { value: 'bankTransfer', label: '银行转账', icon: 'ri-bank-line' },
        { value: 'alipay', label: '支付宝', icon: 'ri-alipay-line' },
        { value: 'wechat', label: '微信支付', icon: 'ri-wechat-pay-line' },
        { value: 'cash', label: '现金', icon: 'ri-money-cny-box-line' },
        { value: 'other', label: '其他', icon: 'ri-more-line' }
      ],
      
      // 选择器索引
      statusIndex: 0, // 默认选中"已完成"
      invoiceIndex: 1, // 默认选中第一个发票
      contractIndex: 1, // 默认选中第一个合同
      customerIndex: 1, // 默认选中第一个客户
      contactIndex: 1, // 默认选中第一个联系人
      
      // 表单错误信息
      errors: {}
    }
  },
  
  onLoad() {
    // 在页面加载时，初始化选择器索引
    this.initializeIndexes();
  },
  
  computed: {
    selectedStatus() {
      return this.paymentStatuses[this.statusIndex] || {};
    },
    selectedInvoice() {
      return this.invoices[this.invoiceIndex] || {};
    },
    selectedContract() {
      return this.contracts[this.contractIndex] || {};
    },
    selectedCustomer() {
      return this.customers[this.customerIndex] || {};
    },
    selectedContact() {
      return this.contacts[this.contactIndex] || {};
    },
    availableContacts() {
      if (!this.formData.customer) return [this.contacts[0]];
      return this.contacts.filter(contact => 
        contact.customerId === '' || contact.customerId === this.formData.customer
      );
    }
  },
  
  methods: {
    initializeIndexes() {
      // 设置状态选择器索引
      this.statusIndex = this.paymentStatuses.findIndex(status => 
        status.code === this.formData.paymentStatus
      );
      if (this.statusIndex === -1) this.statusIndex = 0;
      
      // 设置发票选择器索引
      this.invoiceIndex = this.invoices.findIndex(invoice => 
        invoice.id === this.formData.relatedInvoice
      );
      if (this.invoiceIndex === -1) this.invoiceIndex = 0;
      
      // 设置合同选择器索引
      this.contractIndex = this.contracts.findIndex(contract => 
        contract.id === this.formData.relatedContract
      );
      if (this.contractIndex === -1) this.contractIndex = 0;
      
      // 设置客户选择器索引
      this.customerIndex = this.customers.findIndex(customer => 
        customer.id === this.formData.customer
      );
      if (this.customerIndex === -1) this.customerIndex = 0;
      
      // 设置联系人选择器索引
      this.contactIndex = this.contacts.findIndex(contact => 
        contact.id === this.formData.contactPerson
      );
      if (this.contactIndex === -1) this.contactIndex = 0;
    },
    
    goBack() {
      uni.navigateBack();
    },
    
    // 选择器事件处理
    onStatusChange(e) {
      this.statusIndex = e.detail.value;
      this.formData.paymentStatus = this.paymentStatuses[this.statusIndex].code;
    },
    
    onDateChange(e) {
      this.formData.paymentDate = e.detail.value;
    },
    
    onInvoiceChange(e) {
      this.invoiceIndex = e.detail.value;
      const invoice = this.invoices[this.invoiceIndex];
      
      // 自动填充关联信息
      if (invoice && invoice.id) {
        this.formData.relatedInvoice = invoice.id;
        this.formData.expectedAmount = invoice.amount;
        
        // 自动设置客户
        if (invoice.customerId) {
          this.formData.customer = invoice.customerId;
          // 更新客户选择器索引
          this.customerIndex = this.customers.findIndex(item => item.id === invoice.customerId);
          
          // 重置联系人
          this.formData.contactPerson = '';
          this.contactIndex = 0;
        }
      } else {
        this.formData.relatedInvoice = '';
        this.formData.expectedAmount = '';
      }
    },
    
    onContractChange(e) {
      this.contractIndex = e.detail.value;
      this.formData.relatedContract = this.contracts[this.contractIndex].id;
    },
    
    onCustomerChange(e) {
      this.customerIndex = e.detail.value;
      this.formData.customer = this.customers[this.customerIndex].id;
      
      // 重置联系人
      this.formData.contactPerson = '';
      this.contactIndex = 0;
    },
    
    onContactChange(e) {
      this.contactIndex = e.detail.value;
      this.formData.contactPerson = this.contacts[this.contactIndex].id;
    },
    
    // 收款方式选择
    selectPaymentMethod(method) {
      this.formData.paymentMethod = method;
    },
    
    // 处理取消按钮点击
    handleCancel() {
      uni.showModal({
        title: '确认取消',
        content: '确定要取消编辑吗？未保存的修改将丢失。',
        success: (res) => {
          if (res.confirm) {
            uni.navigateBack();
          }
        }
      });
    },
    
    // 表单验证
    validateForm() {
      this.errors = {};
      let isValid = true;
      
      // 必填字段验证
      if (!this.formData.paymentTitle.trim()) {
        this.errors.paymentTitle = '请输入收款标题';
        isValid = false;
      }
      
      if (!this.formData.paymentStatus) {
        this.errors.paymentStatus = '请选择收款状态';
        isValid = false;
      }
      
      if (!this.formData.paymentDate) {
        this.errors.paymentDate = '请选择收款日期';
        isValid = false;
      }
      
      if (!this.formData.relatedInvoice) {
        this.errors.relatedInvoice = '请选择关联发票';
        isValid = false;
      }
      
      if (!this.formData.customer) {
        this.errors.customer = '请选择客户';
        isValid = false;
      }
      
      // 金额验证
      if (!this.formData.expectedAmount) {
        this.errors.expectedAmount = '请输入应收金额';
        isValid = false;
      } else if (!/^\d+(\.\d{1,2})?$/.test(this.formData.expectedAmount)) {
        this.errors.expectedAmount = '请输入有效的金额格式 (例如: 123.45)';
        isValid = false;
      }
      
      if (!this.formData.actualAmount) {
        this.errors.actualAmount = '请输入实收金额';
        isValid = false;
      } else if (!/^\d+(\.\d{1,2})?$/.test(this.formData.actualAmount)) {
        this.errors.actualAmount = '请输入有效的金额格式 (例如: 123.45)';
        isValid = false;
      }
      
      if (!this.formData.paymentMethod) {
        this.errors.paymentMethod = '请选择收款方式';
        isValid = false;
      }
      
      return isValid;
    },
    
    // 保存修改
    savePayment() {
      if (!this.validateForm()) {
        uni.showToast({
          title: '请完善必填信息',
          icon: 'none'
        });
        return;
      }
      
      // 显示加载提示
      uni.showLoading({
        title: '保存中...'
      });
      
      // 模拟API调用
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '收款记录已更新成功！',
          icon: 'success'
        });
        
        // 返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }, 1000);
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  background-color: #fff;
  z-index: 10;
}

.page-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.back-button {
  color: #666;
  font-size: 24px;
}

.edit-container {
  flex: 1;
  padding: 12px;
  margin-bottom: 80px;
}

.notification-bar {
  padding: 8px 12px;
  background-color: rgba(255, 204, 0, 0.1);
  color: #ffcc00;
  border-radius: 8px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.form-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid #eee;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-row {
  margin-bottom: 12px;
}

.form-label {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.required-mark {
  color: #f56c6c;
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  color: #333;
  background-color: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #3a86ff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(58, 134, 255, 0.1);
}

.picker-view {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  color: #333;
  background-color: #fff;
  box-sizing: border-box;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.full-width {
  grid-column: 1 / -1;
}

.input-addon {
  position: relative;
}

.input-addon .form-input {
  padding-left: 24px;
}

.input-addon-text {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  font-weight: 500;
}

.payment-method-group {
  display: flex;
  white-space: nowrap;
  margin-bottom: 12px;
}

.payment-method-option {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px;
  border: 1px solid #eee;
  border-radius: 8px;
  min-width: 80px;
  margin-right: 8px;
}

.payment-method-option.selected {
  border-color: #3a86ff;
  background-color: rgba(58, 134, 255, 0.1);
}

.payment-method-icon {
  font-size: 24px;
  color: #666;
}

.payment-method-option.selected .payment-method-icon {
  color: #3a86ff;
}

.payment-method-label {
  font-size: 12px;
  color: #666;
  text-align: center;
}

.payment-method-option.selected .payment-method-label {
  color: #3a86ff;
  font-weight: 500;
}

.textarea {
  min-height: 100px;
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
}

.input-error {
  border-color: #f56c6c;
}

.helper-text {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 12px;
  background-color: #fff;
  border-top: 1px solid #eee;
  z-index: 100;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

.save-button {
  background-color: #3a86ff;
  color: #fff;
  border: none;
  flex: 1;
  margin-left: 12px;
}

.cancel-button {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.date-input {
  position: relative;
}
</style> 
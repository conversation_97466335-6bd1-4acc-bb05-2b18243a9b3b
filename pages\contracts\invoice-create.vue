<template>
  <view class="invoice-create-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="goBack">
        <text class="ri-arrow-left-line"></text>
      </view>
      <text class="page-title">创建发票</text>
      <view class="header-actions"></view>
    </view>
    
    <scroll-view scroll-y class="invoice-container">
      <!-- 基本信息 -->
      <view class="section-card">
        <view class="section-title">基本信息</view>
        
        <view class="form-item">
          <view class="form-label required">标题</view>
          <input type="text" class="form-input" v-model="invoice.title" placeholder="请输入发票标题"/>
        </view>
        
        <view class="form-item">
          <view class="form-label required">客户</view>
          <view class="selector" @click="showCustomerSelector">
            <text v-if="invoice.customer">{{invoice.customer.name}}</text>
            <text v-else class="placeholder">请选择客户</text>
            <text class="ri-arrow-right-s-line"></text>
          </view>
        </view>
        
        <view class="form-item">
          <view class="form-label">关联合同</view>
          <view class="selector" @click="showContractSelector">
            <text v-if="invoice.contract">{{invoice.contract.name}}</text>
            <text v-else class="placeholder">请选择合同（可选）</text>
            <text class="ri-arrow-right-s-line"></text>
          </view>
        </view>
        
        <view class="form-item">
          <view class="form-label required">发票类型</view>
          <picker @change="onInvoiceTypeChange" :value="invoiceTypeIndex" :range="invoiceTypes">
            <view class="selector">
              <text v-if="invoice.invoiceType">{{invoice.invoiceType}}</text>
              <text v-else class="placeholder">请选择发票类型</text>
              <text class="ri-arrow-down-s-line"></text>
            </view>
          </picker>
        </view>
        
        <view class="form-item">
          <view class="form-label required">开票日期</view>
          <picker mode="date" @change="onInvoiceDateChange" :value="invoice.invoiceDate">
            <view class="selector">
              <text v-if="invoice.invoiceDate">{{invoice.invoiceDate}}</text>
              <text v-else class="placeholder">请选择开票日期</text>
              <text class="ri-calendar-line"></text>
            </view>
          </picker>
        </view>
        
        <view class="form-item">
          <view class="form-label required">付款期限</view>
          <picker mode="date" @change="onDueDateChange" :value="invoice.dueDate">
            <view class="selector">
              <text v-if="invoice.dueDate">{{invoice.dueDate}}</text>
              <text v-else class="placeholder">请选择付款期限</text>
              <text class="ri-calendar-line"></text>
            </view>
          </picker>
        </view>
      </view>
      
      <!-- 发票金额 -->
      <view class="section-card">
        <view class="section-title">发票金额</view>
        
        <view class="invoice-items">
          <view class="invoice-items-header">
            <text class="item-name">项目名称</text>
            <text class="item-price">单价</text>
            <text class="item-quantity">数量</text>
            <text class="item-total">小计</text>
            <text class="item-action"></text>
          </view>
          
          <view class="invoice-item" v-for="(item, index) in invoice.items" :key="index">
            <input type="text" class="item-name" v-model="item.name" placeholder="项目名称"/>
            <input type="digit" class="item-price" v-model="item.price" @input="calculateItemTotal(index)" placeholder="单价"/>
            <input type="number" class="item-quantity" v-model="item.quantity" @input="calculateItemTotal(index)" placeholder="数量"/>
            <text class="item-total">¥{{formatMoney(item.total)}}</text>
            <button type="button" class="delete-btn" @click="removeItem(index)">
              <text class="ri-delete-bin-line"></text>
            </button>
          </view>
          
          <button type="button" class="add-item-btn" @click="addItem">
            <text class="ri-add-line"></text>
            <text>添加项目</text>
          </button>
          
          <view class="invoice-summary">
            <view class="summary-row">
              <text class="summary-label">不含税金额:</text>
              <text class="summary-value">¥{{formatMoney(invoice.amountBeforeTax)}}</text>
            </view>
            <view class="summary-row">
              <text class="summary-label">
                税额:
                <picker @change="onTaxRateChange" :value="taxRateIndex" :range="taxRates">
                  <text class="tax-rate-selector">{{invoice.taxRate}}%</text>
                </picker>
              </text>
              <text class="summary-value">¥{{formatMoney(invoice.taxAmount)}}</text>
            </view>
            <view class="summary-row total-row">
              <text class="summary-label">总计:</text>
              <text class="summary-value">¥{{formatMoney(invoice.totalAmount)}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 客户信息 -->
      <view class="section-card" v-if="invoice.customer">
        <view class="section-title">发票接收信息</view>
        
        <view class="form-item">
          <view class="form-label">税号</view>
          <input type="text" class="form-input" v-model="invoice.customer.taxNumber" placeholder="请输入税号"/>
        </view>
        <view class="form-item">
          <view class="form-label">开户行</view>
          <input type="text" class="form-input" v-model="invoice.customer.bank" placeholder="请输入开户行"/>
        </view>
        <view class="form-item">
          <view class="form-label">账号</view>
          <input type="text" class="form-input" v-model="invoice.customer.accountNumber" placeholder="请输入账号"/>
        </view>
        <view class="form-item">
          <view class="form-label">地址电话</view>
          <input type="text" class="form-input" v-model="invoice.customer.addressAndPhone" placeholder="请输入地址电话"/>
        </view>
      </view>
      
      <!-- 备注信息 -->
      <view class="section-card">
        <view class="section-title">备注信息</view>
        
        <view class="form-item">
          <textarea class="form-textarea" v-model="invoice.notes" placeholder="请输入备注信息"></textarea>
        </view>
      </view>
    </scroll-view>
    
    <view class="float-actions">
      <button type="button" class="action-btn secondary-action" @click="saveDraft">
        <text>保存草稿</text>
      </button>
      <button type="button" class="action-btn primary-action" @click="submitInvoice">
        <text>创建发票</text>
      </button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      invoice: {
        title: '',
        customer: null,
        contract: null,
        invoiceType: '',
        invoiceDate: this.formatDate(new Date()),
        dueDate: this.formatDate(new Date(Date.now() + 15 * 24 * 60 * 60 * 1000)), // 默认15天后
        items: [
          { name: '', price: '', quantity: '', total: 0 }
        ],
        amountBeforeTax: 0,
        taxRate: 13,
        taxAmount: 0,
        totalAmount: 0,
        notes: ''
      },
      invoiceTypes: ['增值税专用发票', '增值税普通发票', '电子发票'],
      invoiceTypeIndex: 0,
      taxRates: [0, 3, 6, 9, 13],
      taxRateIndex: 4, // 默认13%
      isLoading: false
    }
  },
  onLoad(options) {
    // 如果有合同ID参数，则加载合同数据
    if (options.contractId) {
      this.loadContractData(options.contractId);
    }
  },
  methods: {
    loadContractData(contractId) {
      // 此处应该是从API加载合同数据
      console.log('加载合同ID:', contractId);
      // 模拟数据
      this.invoice.contract = {
        id: contractId,
        name: '系统集成服务合同',
        number: 'CT-2023-09-001'
      };
      
      // 模拟从合同加载项目
      this.invoice.title = '系统集成项目 - 第一期发票';
      this.invoice.items = [
        { name: '软件系统集成服务', price: '170000', quantity: '1', total: 170000 },
        { name: '数据迁移服务', price: '87500', quantity: '1', total: 87500 }
      ];
      
      // 加载客户信息
      this.invoice.customer = {
        id: '1001',
        name: '上海智能科技',
        taxNumber: '91310000MA1FL4CT3X',
        bank: '中国建设银行上海张江支行',
        accountNumber: '31050161393600000123',
        addressAndPhone: '上海市浦东新区张江高科技园区科苑路88号 021-********'
      };
      
      // 重新计算金额
      this.calculateTotalAmount();
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    formatMoney(amount) {
      return parseFloat(amount || 0).toLocaleString('zh-CN');
    },
    goBack() {
      uni.navigateBack();
    },
    addItem() {
      this.invoice.items.push({ name: '', price: '', quantity: '', total: 0 });
    },
    removeItem(index) {
      if (this.invoice.items.length > 1) {
        this.invoice.items.splice(index, 1);
        this.calculateTotalAmount();
      } else {
        uni.showToast({
          title: '至少需要一个项目',
          icon: 'none'
        });
      }
    },
    calculateItemTotal(index) {
      const item = this.invoice.items[index];
      const price = parseFloat(item.price) || 0;
      const quantity = parseFloat(item.quantity) || 0;
      item.total = price * quantity;
      this.calculateTotalAmount();
    },
    calculateTotalAmount() {
      // 计算不含税金额
      this.invoice.amountBeforeTax = this.invoice.items.reduce((sum, item) => {
        return sum + (item.total || 0);
      }, 0);
      
      // 计算税额
      this.invoice.taxAmount = this.invoice.amountBeforeTax * (this.invoice.taxRate / 100);
      
      // 计算总金额
      this.invoice.totalAmount = this.invoice.amountBeforeTax + this.invoice.taxAmount;
    },
    onInvoiceTypeChange(e) {
      this.invoiceTypeIndex = e.detail.value;
      this.invoice.invoiceType = this.invoiceTypes[this.invoiceTypeIndex];
    },
    onInvoiceDateChange(e) {
      this.invoice.invoiceDate = e.detail.value;
    },
    onDueDateChange(e) {
      this.invoice.dueDate = e.detail.value;
    },
    onTaxRateChange(e) {
      this.taxRateIndex = e.detail.value;
      this.invoice.taxRate = this.taxRates[this.taxRateIndex];
      this.calculateTotalAmount();
    },
    showCustomerSelector() {
      uni.navigateTo({
        url: '/pages/customers/customer-selector?multiple=false',
        events: {
          customerSelected: (customer) => {
            this.invoice.customer = customer;
          }
        }
      });
    },
    showContractSelector() {
      uni.navigateTo({
        url: '/pages/contracts/contract-selector?multiple=false&customerId=' + 
             (this.invoice.customer ? this.invoice.customer.id : ''),
        events: {
          contractSelected: (contract) => {
            this.invoice.contract = contract;
            // 可以根据合同信息设置发票项目
          }
        }
      });
    },
    validateForm() {
      if (!this.invoice.title) {
        uni.showToast({ title: '请输入发票标题', icon: 'none' });
        return false;
      }
      
      if (!this.invoice.customer) {
        uni.showToast({ title: '请选择客户', icon: 'none' });
        return false;
      }
      
      if (!this.invoice.invoiceType) {
        uni.showToast({ title: '请选择发票类型', icon: 'none' });
        return false;
      }
      
      if (!this.invoice.invoiceDate) {
        uni.showToast({ title: '请选择开票日期', icon: 'none' });
        return false;
      }
      
      if (!this.invoice.dueDate) {
        uni.showToast({ title: '请选择付款期限', icon: 'none' });
        return false;
      }
      
      // 验证发票项目
      let valid = true;
      this.invoice.items.forEach((item, index) => {
        if (!item.name) {
          uni.showToast({ title: `第${index + 1}项未填写名称`, icon: 'none' });
          valid = false;
        }
        if (!item.price) {
          uni.showToast({ title: `第${index + 1}项未填写价格`, icon: 'none' });
          valid = false;
        }
        if (!item.quantity) {
          uni.showToast({ title: `第${index + 1}项未填写数量`, icon: 'none' });
          valid = false;
        }
      });
      
      return valid;
    },
    saveDraft() {
      // 保存草稿，不需要完整验证
      if (!this.invoice.title) {
        uni.showToast({ title: '请至少输入发票标题', icon: 'none' });
        return;
      }
      
      this.isLoading = true;
      // 实际应用中，这里应该调用API保存草稿
      setTimeout(() => {
        this.isLoading = false;
        uni.showToast({ title: '草稿保存成功', icon: 'success' });
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }, 1000);
    },
    submitInvoice() {
      if (!this.validateForm()) {
        return;
      }
      
      this.isLoading = true;
      // 实际应用中，这里应该调用API创建发票
      setTimeout(() => {
        this.isLoading = false;
        uni.showToast({ title: '发票创建成功', icon: 'success' });
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }, 1000);
    }
  }
}
</script>

<style>
.invoice-create-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f6fa;
}

.page-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 10;
}

.back-button {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  flex: 1;
  font-size: 36rpx;
  font-weight: 600;
  text-align: center;
  margin-right: 60rpx;
}

.header-actions {
  width: 60rpx;
}

.invoice-container {
  flex: 1;
  padding: 30rpx;
}

.section-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 30rpx;
}

.form-item {
  margin-bottom: 24rpx;
}

.form-label {
  font-size: 28rpx;
  margin-bottom: 12rpx;
  color: #333;
}

.form-label.required:after {
  content: "*";
  color: #ff4d4f;
  margin-left: 8rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  padding: 20rpx 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.placeholder {
  color: #bbb;
}

.invoice-items {
  margin-top: 20rpx;
}

.invoice-items-header {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #e8e8e8;
  font-size: 24rpx;
  color: #8c8c8c;
}

.invoice-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
  align-items: center;
}

.item-name {
  flex: 2;
  padding: 0 8rpx;
  font-size: 28rpx;
}

.item-price, .item-quantity {
  flex: 1;
  padding: 0 8rpx;
  font-size: 28rpx;
  text-align: right;
}

.item-total {
  flex: 1;
  padding: 0 8rpx;
  font-size: 28rpx;
  text-align: right;
}

.item-action {
  width: 60rpx;
  text-align: center;
}

.delete-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: none;
  color: #ff4d4f;
  padding: 0;
  font-size: 32rpx;
}

.add-item-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  margin-top: 30rpx;
  width: 100%;
  padding: 20rpx;
  background-color: #f5f5f5;
  border: 2rpx dashed #e8e8e8;
  border-radius: 16rpx;
  color: #8c8c8c;
  font-size: 28rpx;
}

.invoice-summary {
  margin-top: 40rpx;
  border-top: 2rpx solid #e8e8e8;
  padding-top: 30rpx;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.tax-rate-selector {
  display: inline-block;
  padding: 4rpx 16rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  color: #1890ff;
}

.total-row {
  margin-top: 20rpx;
  font-weight: 600;
  font-size: 32rpx;
}

.float-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 30rpx;
  background-color: #fff;
  border-top: 2rpx solid #f0f0f0;
  z-index: 100;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 24rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.primary-action {
  background-color: #2979ff;
  color: white;
  border: none;
}

.secondary-action {
  background-color: #f5f5f5;
  color: #333;
  border: 2rpx solid #e8e8e8;
  margin-right: 20rpx;
}
</style> 
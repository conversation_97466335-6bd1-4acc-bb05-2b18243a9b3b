<template>
  <view class="container">
    <!-- 页面头部 -->
    <!-- <view class="page-header">
      <navigator class="back-button" open-type="navigateBack">
        <text class="ri-user-add-fill"></text>
      </navigator>
      <view class="page-title">创建客户</view>
      <button class="save-button" :disabled="!formValid" @tap="saveCustomer">
        保存
      </button>
    </view> -->

    <view class="page-content">
      <form @submit.prevent="saveCustomer">
        <!-- 基本信息 -->
        <view class="form-section">
          <view class="section-title section-title-active">
            <text> <i class="ri-info-card-line"></i>基本信息</text>
            <text @tap="basicInfoShow = !basicInfoShow" v-if="!basicInfoShow"
              ><i class="ri-arrow-down-double-fill"></i
            ></text>
            <text @tap="basicInfoShow = !basicInfoShow" v-if="basicInfoShow"
              ><i class="ri-arrow-up-double-fill"></i
            ></text>
          </view>
          <view v-if="basicInfoShow">
            <view class="form-group">
              <text class="form-label required">客户名称</text>
              <input
                type="text"
                class="form-input"
                v-model="formData.name"
                placeholder="请输入客户名称"
                @input="validateForm"
              />
            </view>
            <view
              class="basic-info-more"
              @tap="basicInfoShow = !basicInfoShow"
              v-if="!basicInfoShow"
            >
              <text class="form-label"
                ><i class="ri-arrow-down-double-fill"></i
              ></text>
            </view>

            <view class="form-group">
              <text class="form-label">所属国家</text>
              <view class="type-selector">
                <view class="form-select">
                  <picker
                    mode="selector"
                    :range="countryList"
                    range-key="name"
                    @change="onCountryChange"
                  >
                    <text>{{ formData.country || "请选择所属国家" }}</text>
                  </picker>
                </view>
              </view>
            </view>

            <view class="form-group">
              <text class="form-label">所属省份</text>
              <view class="type-selector">
                <view class="form-select" v-if="formData.country === '中国'">
                  <picker
                    mode="selector"
                    :range="provinceList"
                    range-key="name"
                    @change="onProvinceChange"
                  >
                    <text>{{ formData.province || "请选择所属省份" }}</text>
                  </picker>
                 
                </view>
                <input v-else type="text" class="form-input" v-model="formData.province" placeholder="请输入所属省份" />
              </view>
            </view>

            <view class="form-group">
              <text class="form-label">所属城市</text>
              <view class="type-selector">
                <view class="form-select" v-if="formData.country === '中国'">
                  <picker
                    mode="selector"
                    :range="cityList"
                    range-key="name"
                    @change="onCityChange"
                  >
                    <text>{{ formData.city || "请选择所属城市" }}</text>
                  </picker>
                </view>
                <input v-else type="text" class="form-input" v-model="formData.city" placeholder="请输入所属城市" />
              </view>
            </view>

            <view class="form-group">
              <text class="form-label">详细地址</text>
              <view>
                <input
                  type="text"
                  class="form-input"
                  v-model="formData.address"
                  placeholder="请输入详细地址"
                  @input="validateForm"
                />
              </view>
            </view>
          </view>
        </view>

        <!--财务信息 -->
        <view class="form-section">
          <view class="section-title section-title-active">
            <text><i class="ri-money-cny-circle-fill"></i>财务信息</text>
            <text
              @tap="financialInfoShow = !financialInfoShow"
              v-if="!financialInfoShow"
              ><i class="ri-arrow-down-double-fill"></i
            ></text>
            <text
              @tap="financialInfoShow = !financialInfoShow"
              v-if="financialInfoShow"
              ><i class="ri-arrow-up-double-fill"></i
            ></text>
          </view>
          <view v-if="financialInfoShow">
            <view class="form-group">
              <text class="form-label">资金币种</text>
              <view class="type-selector">
                <view class="form-select">
                  <picker
                    mode="selector"
                    :range="currencyList"
                    range-key="displayText"
                    @change="onCurrencyChange"
                  >
                    <text>{{ formData.currency || "请选择资金币种" }}</text>
                  </picker>
                </view>
              </view>
            </view>

            <view class="form-group">
              <text class="form-label">注册资金(w)</text>
              <input
                type="text"
                class="form-input"
                v-model="formData.registeredCapital"
                placeholder="请输入注册资金"
              />
            </view>

            <view class="form-group">
              <text class="form-label">实缴资金(w)</text>
              <input
                type="text"
                class="form-input"
                v-model="formData.paidCapital"
                placeholder="请输入实缴资金"
              />
            </view>

            <view class="form-group">
              <text class="form-label">是否上市</text>
              <radio-group class="radio" @change="onListedChange" >
                <label><radio :value="true"  :checked="formData.isListed === true"/>是</label>
                <label><radio :value="false" :checked="formData.isListed === false"/>否</label>
              </radio-group>
            </view>

            <!-- 上市板块 -->
            <view class="form-group">
              <text class="form-label">上市板块</text>
              <view class="type-selector">
                <view class="form-select">
                  <picker
                    mode="selector"
                    :range="listingSectorList"
                    range-key="displayText"
                    @change="onListingSectorChange"
                  >
                    <text>{{
                      formData.listingSector || "请选择上市板块"
                    }}</text>
                  </picker>
                </view>
              </view>
            </view>

            <!-- 社保人数 -->
            <view class="form-group">
              <text class="form-label">社保人数</text>
              <input
                type="text"
                class="form-input"
                v-model="formData.socialInsuranceNum"
                placeholder="请输入社保人数"
              />
            </view>

            <!-- 年销售额 -->
            <view class="form-group">
              <text class="form-label">年销售额</text>
              <view class="type-selector">
                <view class="form-select">
                  <picker
                    mode="selector"
                    :range="annualSalesList"
                    range-key="displayText"
                    @change="onAnnualSalesChange"
                  >
                    <text>{{ formData.annualSales || "请选择年销售额" }}</text>
                  </picker>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 业务信息 -->
        <view class="form-section">
          <view class="section-title section-title-active">
            <text><i class="ri-briefcase-5-fill"></i>业务信息</text>
            <text
              @tap="businessInfoShow = !businessInfoShow"
              v-if="!businessInfoShow"
              ><i class="ri-arrow-down-double-fill"></i
            ></text>
            <text
              @tap="businessInfoShow = !businessInfoShow"
              v-if="businessInfoShow"
              ><i class="ri-arrow-up-double-fill"></i
            ></text>
          </view>
          <!-- 客户上下游 -->
          <view v-if="businessInfoShow">
            <view class="form-group">
              <text class="form-label">客户上下游</text>
              <view>
                <input
                  type="text"
                  class="form-input"
                  v-model="formData.upAndDownCustoms"
                  placeholder="请输入客户上下游"
                />
              </view>
            </view>
            <!-- 客户等级 -->
            <view class="form-group">
              <text class="form-label">客户等级</text>
              <view class="type-selector">
                <view class="form-select">
                  <picker
                    mode="selector"
                    :range="customerLevelList"
                    range-key="displayText"
                    @change="onCustomerLevelChange"
                  >
                    <text>{{
                      formData.customerLevel || "请选择客户等级"
                    }}</text>
                  </picker>
                </view>
              </view>
            </view>
            <!-- 客户类型 -->
            <view class="form-group">
              <text class="form-label">客户类型</text>
              <view class="type-selector">
                <view class="form-select">
                  <picker
                    mode="selector"
                    :range="customerTypes"
                    range-key="displayText"
                    @change="onCustomerTypeChange"
                  >
                    <text>{{ formData.customerType || "请选择客户类型" }}</text>
                  </picker>
                </view>
              </view>
            </view>
            <!-- 客户网址 -->
            <view class="form-group">
              <text class="form-label">客户网址</text>
              <input
                type="text"
                class="form-input"
                v-model="formData.website"
                placeholder="请输入客户网址"
              />
            </view>
            <!-- 所属行业 -->
            <view class="form-group">
              <text class="form-label">所属行业</text>
              <view class="type-selector">
                <view class="form-select">
                  <picker
                    mode="selector"
                    :range="industries"
                    range-key="displayText"
                    @change="onIndustryChange"
                  >
                    <text>{{ formData.industry || "请选择所属行业" }}</text>
                  </picker>
                </view>
              </view>
            </view>
            <!-- 是否已成交 -->
            <view class="form-group">
              <text class="form-label">是否已成交</text>
              <radio-group class="radio" @change="onIsDealChange">
                <label><radio value="true" />是</label>
                <label><radio value="false" />否</label>
              </radio-group>
            </view>
            <!-- 开票信息-->
            <view class="form-group">
              <text class="form-label">开票信息</text>
              <input
                type="text"
                class="form-input"
                v-model="formData.invoiceInfo"
                placeholder="请输入开票信息"
              />
            </view>
          </view>
        </view>

        <!-- 其它信息 -->
        <view class="form-section">
          <view class="section-title section-title-active">
            <text><i class="ri-coin-fill"></i>其它信息</text>
            <text @tap="otherInfoShow = !otherInfoShow" v-if="!otherInfoShow"
              ><i class="ri-arrow-down-double-fill"></i
            ></text>
            <text @tap="otherInfoShow = !otherInfoShow" v-if="otherInfoShow"
              ><i class="ri-arrow-up-double-fill"></i
            ></text>
          </view>
          <view v-if="otherInfoShow">
            <view class="form-group">
              <text class="form-label">公司介绍</text>
              <textarea
                class="form-textarea"
                v-model="formData.introduction"
                placeholder="请输入公司介绍"
              />
            </view>

            <view class="form-group">
              <text class="form-label">备注</text>
              <textarea
                class="form-textarea"
                v-model="formData.remark"
                placeholder="请输入备注"
              />
            </view>
          </view>
        </view>

        <!-- 联系人信息 -->
        <view class="form-section">
          <view class="section-title">
            <text class=" ri-contacts-book-2-fill""></text>
            <text>联系人信息</text>
          </view>

          <view class="contacts-section">
            <view
              class="contact-card"
              v-for="(contact, index) in formData.contacts"
              :key="index"
            >
              <view class="contact-header">
                <view class="contact-title">{{
                  index === 0 ? "主要联系人" : "联系人 " + (index + 1)
                }}</view>
                <view
                  class="remove-contact"
                  v-if="formData.contacts.length > 1 || index > 0"
                  @tap="removeContact(index)"
                >
                  <text class="iconfont icon-delete"></text>
                  <text>移除</text>
                </view>
              </view>

              <view class="input-group">
                <view class="form-group">
                  <text class="form-label ">姓名</text>
                  <input
                    type="text"
                    class="form-input"
                    v-model="contact.name"
                    placeholder="请输入姓名"
                    @input="validateForm"
                  />
                </view>

                <view class="form-group">
                  <text class="form-label">固定电话</text>
                  <input
                    type="text"
                    class="form-input"
                    v-model="contact.fixPhone"
                    placeholder="请输入固定电话"
                  />
                </view>
              </view>

              <view class="input-group">
                <view class="form-group">
                  <text class="form-label">电话</text>
                  <input
                    type="text"
                    class="form-input"
                    v-model="contact.telephone"
                    placeholder="请输入电话"
                  />
                </view>

                <view class="form-group">
                  <text class="form-label">邮箱</text>
                  <input
                    type="text"
                    class="form-input"
                    v-model="contact.email"
                    placeholder="请输入邮箱"
                  />
                </view>
              </view>

              <view class="form-group">
                <text class="form-label">其他联系方式</text>
                <input
                  type="text"
                  class="form-input"
                  v-model="contact.weChat"
                  placeholder="如微信号等"
                />
              </view>
            </view>

            <view class="add-contact-btn" @tap="addContact">
              <svg-icon name="add" type="svg" size="24"></svg-icon>
              <text>添加联系人</text>
            </view>
          </view>
        </view>
      </form>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <button class="btn btn-outline" @tap="cancel">取消</button>
      <button class="btn btn-primary" @tap="saveCustomer">保存</button>
    </view>
  </view>
</template>

<script>
import SvgIcon from "@/components/svg-icon.vue";
import getSelectOptions from "@/utils/dictionary.js";
import countryList from "../../country.json";
import provinceCity from "../../provinceCity.json";
import { createCustomer, getCustomerById, updateCustomerById } from "@/api/customer.api";

export default {
  components: {
    SvgIcon,
  },
  data() {
    return {
      formValid: false,
      countryList: countryList,
      provinceList: provinceCity.province,
      cityList: [],
      currencyList: [],
      listingSectorList: [],
      customerLevelList: [],
      basicInfoShow: false,
      financialInfoShow: false,
      businessInfoShow: false,
      otherInfoShow: false,

      // 表单数据
      formData: {
        name: "",
        code: "",
        contactTelephone: "",
        creationTime: "",
        creatorName: "",
        companyName: "",
        customerType: "",
        customerTypeId: "",
        capitalTypeId: "",
        customerLevelId: "",
        customTypeId: "",
        industry: "",
        size: "",
        foundYear: "",
        owner: "",
        address: "",
        website: "",
        creditCode: "",
        source: "",
        remark: "",
        country: "",
        province: "",
        city: "",
        currency: "",
        isListed: true,
        listingSector: "",
        listingSectorId: "",
        annualSales: "",
        socialInsuranceNum: 0,
        businessType: "",
        customerLevel: "",
        customLevelId:"",
        paidCapital: 0,
        invoiceInfo: "",
        introduction: "",
        upAndDownCustoms: "",
        website: "",
        isDealt: false,
        ownerId: "",
        owner: "",
        registeredCapital: 0,

        // 联系人
        contacts: [
          {
            address: "",
            birthday: "",
            clueId: "",
            code: "",
            creationTime: "",
            creatorName: "",
            department: "",
            experience: "",
            fixPhone: "",
            hobby: "",
            owner: "",
            ownerId: "",
            position: "",
            remark: "",
            telephone: "",
            weChat: "",
            name: "",
            title: "",
            phone: "",
            email: "",
            other: "",
            isPrimary: true,
          },
        ],
      },

      // 下拉选择数据
      customerTypes: [],
      industries: [],
      companySizes: [],
      annualSalesList: [
        "0-1000万",
        "1000-5000万",
        "5000-1亿",
        "1亿-5亿",
        "5亿-10亿",
        "10亿-50亿",
        "50亿-100亿",
      ],
      owners: [],
      sources: [],
    };
  },
  methods: {
    // 加载数据字典
    async loadData() {
      await getSelectOptions("CustomLevel").then((res) => {
        this.customerLevelList = res;
      });
      await getSelectOptions("CapitalType").then((res) => {
        this.currencyList = res;
      });
      await getSelectOptions("listingSector").then((res) => {
        this.listingSectorList = res;
      });
      await getSelectOptions("CustomType").then((res) => {
        this.customerTypes = res;
      });
      await getSelectOptions("CustomIndustry").then((res) => {
        this.industries = res;
      });
    },

    getCustomerList(id){
      getCustomerById(id).then((res) => {
        Object.assign(this.formData, res);
       
        let currency = this.currencyList.find((item) => {
         if(item.id === res.capitalTypeId){
          return item.displayText;      
         }
        })
        let customLevel = this.customerLevelList.find((item) => {
          if(item.id === res.customLevelId){
            return item
          }
        })
        let customType = this.customerTypes.find((item) => {
          if(item.id === res.customTypeId){
            return item
          }
        })



        let contact = res.contacts.map((item) => {
          return {
            name: item.contact.name,
            fixPhone: item.contact.fixPhone,
            telephone: item.contact.telephone,
            email: item.contact.email,
            weChat: item.contact.weChat,
            isPrimary: item.contact.isPrimary,
          }
        })
        this.formData.contacts = contact
        this.formData.isListed = res.isListed
        this.formData.listingSector = res.listingSector.displayText
        this.formData.currency = currency.displayText;
        this.formData.customerLevel = customLevel.displayText
        this.formData.customType = customType.displayText
        
      });
    },

    onCustomerLevelChange(e) {
      this.formData.customerLevel =
        this.customerLevelList[e.detail.value].displayText;
        this.formData.customLevelId = this.customerLevelList[e.detail.value].id
    },
    onCountryChange(e) {
      this.formData.country = this.countryList[e.detail.value].name;
      if(this.formData.country !== '中国'){
        this.formData.province = '';
        this.formData.city = '';
      }
    },
    onListingSectorChange(e) {
      this.formData.listingSector =
        this.listingSectorList[e.detail.value].displayText;
        this.formData.listingSectorId = this.listingSectorList[e.detail.value].id;
    },
    onAnnualSalesChange(e) {
      this.formData.annualSales = this.annualSalesList[e.detail.value];
    },
    onCustomerTypeChange(e) {
      this.formData.customerType =
        this.customerTypes[e.detail.value].displayText;
      this.formData.customerTypeId = this.customerTypes[e.detail.value].id;
    },
    onProvinceChange(e) {
      this.formData.province = this.provinceList[e.detail.value].name;
      this.cityList = provinceCity.city.filter((item) => {
        return item.ProID === this.provinceList[e.detail.value].ProID;
      });
    },
    onCityChange(e) {
      this.formData.city = this.cityList[e.detail.value].name;
    },
    onCurrencyChange(e) {
      this.formData.currency = this.currencyList[e.detail.value].displayText;
      this.formData.capitalTypeId = this.currencyList[e.detail.value].id;
    },
    onListedChange(e) {
      this.formData.isListed = e.detail.value === true;
    },
    onIsDealChange(e) {
      this.formData.isDealt = e.detail.value === "true";
    },

    // 下拉选择器事件处理
    onIndustryChange(e) {
      this.formData.industry = this.industries[e.detail.value].displayText;
    },

    onSizeChange(e) {
      this.formData.size = this.companySizes[e.detail.value];
    },

    onFoundYearChange(e) {
      this.formData.foundYear = e.detail.value;
    },

    onOwnerChange(e) {
      this.formData.owner = this.owners[e.detail.value];
    },

    onSourceChange(e) {
      this.formData.source = this.sources[e.detail.value];
    },

    // 添加联系人
    addContact() {
      this.formData.contacts.push({
        name: "",
        title: "",
        phone: "",
        email: "",
        other: "",
        isPrimary: false,
      });
    },

    // 移除联系人
    removeContact(index) {
      // 如果是删除主联系人，则将下一个联系人设为主联系人
      if (
        this.formData.contacts[index].isPrimary &&
        this.formData.contacts.length > 1
      ) {
        this.formData.contacts[0].isPrimary = true;
      }

      this.formData.contacts.splice(index, 1);
      this.validateForm();
    },

    // 表单验证
    validateForm() {
     
    },

    // 取消创建
    cancel() {
      uni.showModal({
        title: "提示",
        content: "确定要放弃编辑客户吗？",
        success: (res) => {
          if (res.confirm) {
            uni.navigateBack();
          }
        },
      });
    },

    // 保存客户
    saveCustomer() {
      updateCustomerById(this.formData.id,this.formData).then((res) => {
        uni.navigateBack();
      })
      
  
    },
  },
 async onLoad(options) {
    // 页面加载后立即验证表单
    this.validateForm();
   await this.loadData();
    if(options.id){
     await this.getCustomerList(options.id);
    }
  },
  onReady() {
    // 设置页面标题
    uni.setNavigationBarTitle({
      title: "编辑客户",
    });
  },
};
</script>

<style>
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1rpx solid var(--border-color);
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.page-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
}

.back-button {
  color: var(--text-secondary);
  display: flex;
  align-items: center;
}

.save-button {
  color: var(--primary-color);
  font-weight: 500;
  font-size: 28rpx;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: transparent;
  border: none;
  line-height: 1;
}

.save-button[disabled] {
  color: var(--text-tertiary);
  opacity: 0.5;
}

/* 表单样式 */
.form-section {
  background-color: #ffffff;
  padding: var(--spacing-lg);
  margin-top: var(--spacing-md);
  border-top: 1rpx solid var(--border-color);
  border-bottom: 1rpx solid var(--border-color);
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
}

.section-title-active {
  display: flex;
  justify-content: space-between;
}

.section-title text:first-child {
  /* color: var(--primary-color); */
  margin-right: var(--spacing-xs);
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-secondary);
}

.required::after {
  content: "*";
  color: var(--danger-color);
  margin-left: 8rpx;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 24rpx;
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: #ffffff;
  font-size: 28rpx;
  color: var(--text-primary);
}

.form-textarea {
  min-height: 200rpx;
  width: 100%;
}

.input-group {
  display: flex;
  gap: var(--spacing-md);
}

.input-group .form-group {
  flex: 1;
}

/* 联系人区域样式 */
.contact-card {
  background-color: var(--light-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  border: 1rpx solid var(--border-color);
}

.contact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.contact-title {
  font-weight: 500;
  font-size: 30rpx;
  color: var(--text-primary);
}

.remove-contact {
  color: var(--danger-color);
  font-size: 28rpx;
  display: flex;
  align-items: center;
}

.remove-contact text:first-child {
  margin-right: 4rpx;
}

.add-contact-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-md);
  color: var(--primary-color);
  background-color: var(--primary-light);
  padding: var(--spacing-sm) 0;
  border-radius: var(--radius-md);
  border: 1rpx dashed var(--primary-color);
}

/* 客户类型选择器样式 */
.type-selector {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-xs);
}

.type-option {
  flex: 1;
  position: relative;
}

.type-option label {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 84rpx;
  padding: var(--spacing-sm);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: #ffffff;
  transition: all 0.3s ease;
  text-align: center;
  font-size: 28rpx;
}

/* radio:checked + label {
  border-color: var(--primary-color);
  background-color: var(--primary-light);
  color: var(--primary-color);
  font-weight: 500;
} */

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-top: 1rpx solid var(--border-color);
  padding: var(--spacing-md);
  display: flex;
  gap: var(--spacing-md);
  z-index: 100;
}

.bottom-bar .btn {
  flex: 1;
}

/* 内容区域添加底部间距，避免被底部栏遮挡 */
.page-content {
  padding-bottom: 160rpx;
}
.radio {
  display: flex;
  justify-content: space-around;
}
.basic-info-more {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10rpx;
}
</style>

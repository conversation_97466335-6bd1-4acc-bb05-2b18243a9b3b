<template>
  <view class="product-select-page">
    <!-- 头部 -->
<!--    <view class="header">
      <view class="left" @click="goBack">
        <svg-icon name="back" type="svg" size="32" color="#FFFFFF"></svg-icon>
      </view>
      <view class="title">选择产品</view>
      <view class="right" @click="goToCreate">
         <text>新增</text>
      </view>
    </view>-->
    <!-- 搜索区域 -->
    <view class="search-area">
      <view class="search-box">
        <svg-icon name="search" type="svg" size="28" color="#999999"></svg-icon>
        <input
          type="text"
          v-model="searchText"
          placeholder="搜索产品名称、编号等"
          @input="getProductList"
        />
        <view class="clear-icon" v-if="searchText" @click="clearSearch">
          <svg-icon name="close" type="svg" size="24" color="#999999"></svg-icon>
        </view>
      </view>
      <scroll-view scroll-x class="category-scroll" show-scrollbar="false">
        <view class="category-tabs">
          <view
            v-for="(category, index) in categories"
            :key="index"
            class="category-item"
            :class="{ active: currentCategory === category.value }"
            @click="selectCategory(category.value)"
          >
            {{ category.name }}
          </view>
        </view>
      </scroll-view>
    </view>
    <!-- 产品列表 -->
    <scroll-view v-if="products.length > 0" scroll-y class="product-list-container">
      <view class="product-grid">
        <view
          v-for="(product, index) in products"
          :key="index"
          class="product-list-item"
          @click="selectProduct(product)"
        >
          <view class="product-list-info">
            <view class="product-name">{{ product.name }}</view>
            <view class="product-desc">{{ product.description }}</view>
            <view class="product-meta">
              <text class="product-code">产品编号: {{ product.code }}</text>
              <text>产品类型：{{ product.productTypeText }}</text>
            </view>
            <view class="product-meta">
              <text class="product-code">产品规格: {{ product.spec }}</text>
              <text>产品单位：{{ product.productUnit }}</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    <view class="empty-state" v-if="products.length === 0">
      <view class="empty-icon">
        <svg-icon name="search" type="svg" size="48"></svg-icon>
      </view>
      <text class="empty-text">没有找到匹配的产品</text>
    </view>
  </view>
</template>

<script>
import SvgIcon from "@/components/svg-icon.vue";
import { getProductList } from "@/api/contact.api";

export default {
  components: {
    SvgIcon,
  },
  data() {
    return {
      searchText: "",
      currentCategory: "",
      categories: [
        { name: "全部", value: "" },
        { name: "生产", value: "生产" },
        { name: "外采", value: "外采" },
      ],
      products: [],
    };
  },
  methods: {
    async getProductList() {
      try {
        const params = {
          pageIndex: 1,
          pageSize: 999,
          filter: {
            likeString: this.searchText,
            productType: this.currentCategory
          },
        };
        await getProductList(params).then((res) => {
          this.products = res.items;
        });
      } catch (error) {}
    },
    goBack() {
      uni.navigateBack();
    },
    goToCreate() {
      uni.navigateTo({
        url: "/pages/products/product-create",
      });
    },
    clearSearch() {
      this.searchText = "";
      this.getProductList();
    },
    selectCategory(category) {
      this.currentCategory = category;
      this.getProductList();
    },
    selectProduct(product) {
      // 获取 eventChannel
      const eventChannel =
        this.getOpenerEventChannel && this.getOpenerEventChannel();
      if (eventChannel) {
        eventChannel.emit("selectProduct", product);
      }
      uni.navigateBack();
    },
  },
  onLoad() {
    this.getProductList();
  },
};
</script>

<style lang="scss">
.product-select-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 90rpx;
    background-color: #3a86ff;
    color: #fff;
    padding: 0 30rpx;
    position: sticky;
    top: 0;
    z-index: 100;

    .left {
      display: flex;
      align-items: center;
    }

    .title {
      font-size: 34rpx;
      font-weight: bold;
    }

    .right {
      font-size: 30rpx;
    }
  }

  .search-area {
    background-color: #fff;
    padding: 20rpx 30rpx;
    border-bottom: 1rpx solid #eee;

    .search-box {
      display: flex;
      align-items: center;
      background-color: #f5f5f5;
      border-radius: 36rpx;
      padding: 0 20rpx;
      height: 72rpx;
      margin-bottom: 20rpx;

      input {
        flex: 1;
        height: 72rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
      }

      .clear-icon {
        padding: 10rpx;
      }
    }

    .category-scroll {
      white-space: nowrap;
      height: 80rpx;

      .category-tabs {
        display: inline-flex;
        padding: 0 10rpx;

        .category-item {
          padding: 0 30rpx;
          height: 64rpx;
          line-height: 64rpx;
          margin-right: 20rpx;
          border-radius: 32rpx;
          font-size: 28rpx;
          color: #666;
          background-color: #f5f5f5;

          &.active {
            color: #fff;
            background-color: #3a86ff;
          }
        }
      }
    }
  }

  .product-list-container {
    flex: 1;
    padding: 20rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .product-grid {
    display: flex;
    flex-direction: column;
    padding-bottom: 120rpx;
    width: 88%;
    max-width: 720rpx;
  }
  .product-list-item {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
    width: 100%;
  }

  .product-list-info {
    display: flex;
    flex-direction: column;
    flex: 1;
  }

  .product-name {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }

  .product-desc {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 20rpx;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .product-meta {
    display: flex;
    font-size: 24rpx;
    color: #999;
    margin-bottom: 16rpx;
  }

  .product-code {
    margin-right: 20rpx;
  }

  .product-price {
    display: flex;
    align-items: baseline;
    padding-right: 10rpx;
  }

  .price-value {
    font-size: 36rpx;
    color: #ff6b18;
    font-weight: bold;
  }

  .price-unit {
    font-size: 24rpx;
    color: #999;
    margin-left: 4rpx;
  }

  .action-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    padding: 20rpx 30rpx;
    display: flex;
    justify-content: center;
    border-top: 1rpx solid #eee;
  }

  .confirm-button {
    width: 88%;
    max-width: 720rpx;
    height: 88rpx;
    line-height: 88rpx;
    background-color: #3a86ff;
    color: #fff;
    font-size: 32rpx;
    border-radius: 44rpx;
    text-align: center;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;
    color: #999;
    font-size: 28rpx;
    text {
      margin-top: 20rpx;
    }
  }
}
</style>

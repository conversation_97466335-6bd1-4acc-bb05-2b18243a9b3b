<template>
  <view class="dashboard-container">
    <!-- 欢迎区域 -->
    <view class="welcome-section">
      <text class="welcome-text">您好 {{ userName }}</text>
      <text class="date-text">今天是 {{ todayDate }}</text>
    </view>

    <!-- 快速统计 -->
    <view class="quick-stats">
      <view class="stat-card" v-for="(stat, id) in updatedStats" :key="id">
        <text class="stat-title">{{ stat.title }}</text>
        <text class="stat-value">{{ stat.value }}</text>
        <view
          v-if="id !== '3' && id !== '4'"
          :class="['stat-trend', stat.trend < 0 ? 'negative' : '']"
        >
          <svg-icon
            :name="stat.trend >= 0 ? 'arrow-up' : 'arrow-down'"
            type="svg"
            size="24"
          ></svg-icon>
          <text>{{ stat.text }}</text>
        </view>
      </view>
    </view>

    <!-- 功能网格 -->
    <view class="feature-grid">
      <view
        class="feature-item"
        v-for="(feature, index) in features"
        :key="index"
        @click="handleRoute(feature.url)"
      >
        <view class="feature-icon">
          <svg-icon
            :name="feature.iconName"
            :type="feature.iconType"
            size="48"
          ></svg-icon>
        </view>
        <text class="feature-name">{{ feature.name }}</text>
      </view>
    </view>

    <!-- 最近活动 -->
    <!-- <view class="recent-activities">
      <view class="section-title">
        <text>最近动态</text>
        <text class="view-all" @click="viewAllActivities">查看全部</text>
      </view>
      <view class="activity-list">
        <view
          class="activity-item"
          v-for="(activity, index) in activities"
          :key="index"
        >
          <view class="activity-icon">
            <svg-icon
              :name="activity.iconName"
              :type="activity.iconType"
              size="32"
            ></svg-icon>
          </view>
          <view class="activity-content">
            <text class="activity-title">{{ activity.title }}</text>
            <text class="activity-desc">{{ activity.desc }}</text>
            <text class="activity-time">{{ activity.time }}</text>
          </view>
        </view>
      </view>
    </view> -->

    <!-- 自定义TabBar组件 -->
    <custom-tab-bar></custom-tab-bar>
  </view>
</template>

<script>
import CustomTabBar from "@/components/CustomTabBar.vue";
import { getHomeData } from "@/api/home.api";
import { checkLoginStatus } from '@/utils/auth';

export default {
  components: {
    CustomTabBar,
  },
  data() {
    return {
      todayDate: "2024年3月21日 星期四",
      userName: "",
      salePercentData: {
        percent: 0,
        text: "0%",
        color: "#999",
      },
      followedBusiness: 0,
      lastMonthNewCustomers: 0,
      lastMonthSales: 0,
      thisMonthNewCustomers: 0,
      thisMonthSales: 0,
      todoTasks: 0,
      features: [
        {
          name: "线索管理",
          iconName: "leads",
          iconType: "svg",
          url: "/pages/marketing/leads",
        },
        {
          name: "客户管理",
          iconName: "customer",
          iconType: "svg",
          url: "/pages/customers/customer-list",
        },
        {
          name: "商机管理",
          iconName: "opportunity",
          iconType: "svg",
          url: "/pages/sales/opportunity-list",
        },
        // {
        //   name: "沟通记录",
        //   iconName: "communication",
        //   iconType: "svg",
        //   url: "/pages/interactions/interaction-list",
        // },
        // {
        //   name: "行动计划",
        //   iconName: "calendar",
        //   iconType: "svg",
        //   url: "/pages/actions/action-list",
        // },
        // {
        //   name: "报价管理",
        //   iconName: "quotation",
        //   iconType: "svg",
        //   url: "/pages/sales/quotation-list",
        // },
      ],
      activities: [
        {
          iconName: "user-add",
          iconType: "svg",
          title: "新增客户：上海科技有限公司",
          desc: "客户经理：李经理",
          time: "10分钟前",
        },
        {
          iconName: "line-chart",
          iconType: "svg",
          title: "商机更新：智能家居项目",
          desc: "金额：¥500,000",
          time: "30分钟前",
        },
        {
          iconName: "message",
          iconType: "svg",
          title: "新增沟通记录",
          desc: "客户：北京科技有限公司",
          time: "1小时前",
        },
        {
          iconName: "task",
          iconType: "svg",
          title: "任务完成：产品演示",
          desc: "负责人：王经理",
          time: "2小时前",
        },
      ],
    };
  },
  async onLoad() {
    // 进入页面时检查登录态
    if (!checkLoginStatus()) {
      return;
    }
    await this.updateTodayDate();
    await this.loadDashboardData();
    await this.getUserInfo();
  },
  onShow() {
    // 设置TabBar选中项
    if (typeof this.$refs.customTabBar !== 'undefined') {
      this.$refs.customTabBar.current = 0;
    } else {
      // 如果refs还没准备好，尝试延迟设置
      setTimeout(() => {
        if (typeof this.$refs.customTabBar !== 'undefined') {
          this.$refs.customTabBar.current = 3;
          console.log('线索页面设置TabBar当前项为0');
        }
      }, 300);
    }
  },
  methods: {
    updateTodayDate() {
      const today = new Date();
      const year = today.getFullYear();
      const month = today.getMonth() + 1;
      const day = today.getDate();
      const weekdays = ["日", "一", "二", "三", "四", "五", "六"];
      const weekday = weekdays[today.getDay()];
      this.todayDate = `${year}年${month}月${day}日 星期${weekday}`;
    },
    getUserInfo() {
      const userInfo = uni.getStorageSync("userInfo");
      this.userName = userInfo.name;
    },
    async loadDashboardData() {
      const res = await getHomeData();
      this.followedBusiness = res.followedBusiness;
      this.lastMonthNewCustomers = res.lastMonthNewCustomers;
      this.lastMonthSales = res.lastMonthSales;
      this.thisMonthNewCustomers = res.thisMonthNewCustomers;
      this.thisMonthSales = res.thisMonthSales;
      this.todoTasks = res.todoTasks;
    },

    viewAllActivities() {
      uni.showToast({
        title: "查看全部动态功能开发中...",
        icon: "none",
      });
    },
    handleRoute(url) {
      const notTabBarPages = [
        "/pages/marketing/leads",
        "/pages/interactions/interaction-list",
        "/pages/sales/quotation-list",
      ];
      if (notTabBarPages.includes(url)) {
        uni.navigateTo({ url: url });
      } else {
        uni.switchTab({
          url: url,
        });
      }
    },
  },
  computed: {
    getSalesTrend() {
      if (this.lastMonthSales) {
        return ((this.thisMonthSales - this.lastMonthSales) / this.lastMonthSales) * 100;
      } else {
        return 0;
      }
    },
    getSalesTrendText() {
      return `${(this.getSalesTrend > 0 ? this.getSalesTrend : -this.getSalesTrend).toFixed(2)}%`;
    },
    getNewCustomersTrend() {
      if (this.lastMonthNewCustomers) {
        return ((this.thisMonthNewCustomers - this.lastMonthNewCustomers) / this.lastMonthNewCustomers) * 100;
      } else {
        return 0;
      }
    },
    getNewCustomersTrendText() {
      return `${(this.getNewCustomersTrend > 0 ? this.getNewCustomersTrend : -this.getNewCustomersTrend).toFixed(2)}%`;
    },
    updatedStats() {
      return {
        1: {
          title: "本月销售额",
          value: this.thisMonthSales,
          trend: this.getSalesTrend,
          text: this.getSalesTrendText,
        },
        2: {
          title: "新增客户",
          value: this.thisMonthNewCustomers,
          trend: this.getNewCustomersTrend,
          text: this.getNewCustomersTrendText,
        },
        3: { title: "待处理业务", value: this.todoTasks },
        4: { title: "跟进商机", value: this.followedBusiness },
      };
    },
  },
};
</script>

<style>
.dashboard-container {
  padding: 16px;
  padding-bottom: 120px;
  background-color: #f5f7fa;
}

.welcome-section {
  margin-bottom: 20px;
}

.welcome-text {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.date-text {
  font-size: 14px;
  color: #666;
  display: block;
}

.quick-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stat-title {
  font-size: 14px;
  color: #666;
  display: block;
  margin-bottom: 6px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  display: block;
}

.stat-trend {
  font-size: 12px;
  color: #00b578;
  margin-top: 6px;
  display: flex;
  align-items: center;
}

.stat-trend.negative {
  color: #ff4d4f;
}

.stat-trend text {
  margin-right: 4px;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.feature-item {
  background: white;
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;
}

.feature-item:active {
  transform: scale(0.98);
}

.feature-icon {
  width: 48px;
  height: 48px;
  background: #e6f3ff;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 8px;
}

.feature-name {
  font-size: 14px;
  color: #333;
  display: block;
}

.recent-activities {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.view-all {
  font-size: 14px;
  color: #3a86ff;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.activity-item:last-child {
  padding-bottom: 8px;
  border-bottom: none;
}

.activity-icon {
  width: 32px;
  height: 32px;
  background: #e6f3ff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.activity-desc {
  font-size: 12px;
  color: #666;
  display: block;
}

.activity-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  display: block;
}
</style>

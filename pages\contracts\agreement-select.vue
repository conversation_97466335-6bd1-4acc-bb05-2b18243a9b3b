<template>
  <view class="page-container">
    <view class="page-header">
      <view class="back-button" @click="navigateBack">
        <svg-icon name="arrow-left" type="svg" size="24"></svg-icon>
      </view>
      <text class="page-title">选择协议</text>
      <view class="header-spacer"></view>
    </view>

    <view class="search-container">
      <view class="search-icon">
        <svg-icon name="search" type="svg" size="24"></svg-icon>
      </view>
      <input
        type="text"
        class="search-input"
        v-model="searchText"
        placeholder="搜索协议"
        @input="searchCustomers"
      />
    </view>

    <scroll-view scroll-y class="customer-list">
      <block>
        <view
          v-for="customer in filteredCustomers"
          :key="customer.id"
          class="customer-item"
          @click="selectCustomer(customer)"
        >
          <view class="customer-info">
            <text class="customer-name">{{ customer.name }}</text>
            <text class="customer-detail">{{ customer.contact }} </text>
            <view class="detail-item">
              <view class="detail-label">关联商机：</view>
              <view class="detail-value">{{ customer.businessName }}</view>
            </view>
            <view class="detail-item">
              <view class="detail-label">客户名称：</view>
              <view class="detail-value">{{ customer.customName }}</view>
            </view>
            <view class="detail-item">
              <view class="detail-label">付款比例：</view>
              <view class="detail-value">{{ customer.paidTypeName }}</view>
            </view>
          </view>
        </view>
      </block>
    </scroll-view>
  </view>
</template>

<script>
import SvgIcon from "@/components/svg-icon.vue";
import { getAgreementList } from "@/api/contact.api";

export default {
  components: {
    SvgIcon,
  },
  data() {
    return {
      searchText: "",
      currentFilter: "all",
      filterTabs: [
        { label: "全部", value: "all" },
        { label: "重点客户", value: "A" },
        { label: "普通客户", value: "B" },
        { label: "潜在客户", value: "C" },
      ],
      recentCustomers: [],
      allCustomers: [],
    };
  },
  onLoad() {
    this.getList();
  },
  computed: {
    // 根据筛选和搜索过滤
    filteredCustomers() {
      const searchTerm = this.searchText.toLowerCase().trim();

      // 先筛选类型
      let filtered = this.allCustomers;
      if (this.currentFilter !== "all") {
        filtered = filtered.filter((item) => {
          return item.customLevelName === this.currentFilter;
        });
      }

      // 搜索关键词
      if (searchTerm) {
        filtered = filtered.filter((item) => {
          const name = item.name ? item.name.toLowerCase() : "";
          if (name.includes(searchTerm)) {
            return true;
          }
          return false;
        });
      }
      return filtered;
    },
  },
  methods: {
    // 获得客户数据
    async getList() {
      try {
        const params = {
          pageIndex: 1,
          pageSize: 10,
          filter: { name: "" },
        };
        const res = await getAgreementList(params);
        this.allCustomers = res.items;
      } catch (error) {
        console.log(error);
      }
    },
    // 返回上一页
    navigateBack() {
      uni.navigateBack();
    },

    // 获取客户类型标签
    getCustomerTypeLabel(type) {
      const typeMap = {
        A: "重点客户",
        B: "普通客户",
        C: "潜在客户",
        D: "非活跃客户",
      };
      return typeMap[type] || "未知类型";
    },

    // 根据类型筛选客户
    filterCustomers(filter) {
      this.currentFilter = filter;
    },

    // 搜索客户
    searchCustomers() {},

    // 选择客户并返回
    selectCustomer(customer) {
      // 兼容：事件回调 + 本地存储

      const eventChannel =
        this.getOpenerEventChannel && this.getOpenerEventChannel();
      if (eventChannel) {
        // eventChannel.emit("selectCustomer", {
        //   id: customer.id,
        //   name: customer.name,
        // });
        // 兼容合同页面的 updateSelectedCustomer 事件
        eventChannel.emit("agreementSelected", customer);
        console.log("agreementSelected", customer);
      }
        uni.navigateBack();
    },
  },
};
</script>

<style>
.page-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #e0e0e0;
  background-color: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.back-button {
  padding: 10rpx;
}

.header-spacer {
  width: 44rpx;
}

.search-container {
  position: relative;
  padding: 20rpx 30rpx;
  background-color: white;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  padding: 0 20rpx;
}

.search-icon {
  position: absolute;
  left: 40rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  color: #999;
}

.search-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx 0 60rpx;
  font-size: 28rpx;
  background-color: #f5f7fa;
  border-radius: 8rpx;
}

.filter-tabs {
  display: flex;
  padding: 0 30rpx;
  background-color: white;
  border-bottom: 1rpx solid #e0e0e0;
  overflow-x: auto;
}

.filter-tab {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
  white-space: nowrap;
}

.filter-tab.active {
  color: #3370ff;
  font-weight: 500;
}

.filter-tab.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background-color: #3370ff;
}

.customer-list {
  flex: 1;
  padding: 20rpx 30rpx;
}

.section-header {
  font-size: 28rpx;
  font-weight: 500;
  color: #999;
  margin: 20rpx 0;
}

.customer-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.customer-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f0f7ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.customer-info {
  flex: 1;
}

.customer-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.customer-detail {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.customer-tags {
  display: flex;
  gap: 10rpx;
}

.tag {
  padding: 4rpx 16rpx;
  border-radius: 100rpx;
  font-size: 24rpx;
}

.tag-A {
  background-color: #fff1f0;
  color: #f5222d;
}

.tag-B {
  background-color: #f6ffed;
  color: #52c41a;
}

.tag-C {
  background-color: #e6f7ff;
  color: #1890ff;
}

.tag-D {
  background-color: #f5f5f5;
  color: #666;
}

.tag-industry {
  background-color: #f5f5f5;
  color: #666;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  margin-bottom: 20rpx;
  color: #999;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
.detail-item {
  display: flex;
  align-items: baseline;
  margin-bottom: var(--spacing-xs);
}
.detail-label {
  font-size: 24rpx;
  color: var(--text-tertiary);
  margin-right: 8rpx;
  white-space: nowrap;
}
.detail-value {
  font-size: 26rpx;
  color: var(--text-secondary);
  font-weight: 500;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>

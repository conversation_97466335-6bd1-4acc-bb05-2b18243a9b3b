<template>
  <view class="dashboard-container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="nav-bar-left">
        <text class="company-name">奥丁CRM</text>
      </view>
      <view class="nav-bar-right">
        <view class="avatar" @tap="navigateToSettings">
          <image src="/static/images/avatar.png" mode="aspectFill"></image>
          <view class="notification-badge" v-if="notificationCount > 0">
            <text>{{ notificationCount > 99 ? '99+' : notificationCount }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 欢迎区域 -->
    <view class="welcome-section">
      <view class="welcome-text">
        <text class="greeting">Hi，{{ userName }}</text>
        <text class="date">{{ currentDate }}</text>
      </view>
      <view class="search-bar" @tap="navigateToSearch">
        <uni-icons type="search" size="16" color="#999"></uni-icons>
        <text class="search-placeholder">搜索客户、合同、任务等</text>
      </view>
    </view>

    <!-- 业绩指标卡片 -->
    <view class="metrics-card">
      <view class="metrics-header">
        <text class="metrics-title">业绩指标</text>
        <text class="metrics-period">本月</text>
      </view>
      <view class="metrics-row">
        <view class="metric-item">
          <text class="metric-value">{{ salesAmount }}</text>
          <text class="metric-label">销售额 (元)</text>
          <view class="metric-trend" :class="salesTrend >= 0 ? 'up' : 'down'">
            <uni-icons :type="salesTrend >= 0 ? 'arrow-up' : 'arrow-down'" size="12" 
              :color="salesTrend >= 0 ? '#36B37E' : '#FF5630'"></uni-icons>
            <text>{{ Math.abs(salesTrend) }}%</text>
          </view>
        </view>
        <view class="metric-item">
          <text class="metric-value">{{ newCustomers }}</text>
          <text class="metric-label">新增客户</text>
          <view class="metric-trend" :class="customerTrend >= 0 ? 'up' : 'down'">
            <uni-icons :type="customerTrend >= 0 ? 'arrow-up' : 'arrow-down'" size="12" 
              :color="customerTrend >= 0 ? '#36B37E' : '#FF5630'"></uni-icons>
            <text>{{ Math.abs(customerTrend) }}%</text>
          </view>
        </view>
        <view class="metric-item">
          <text class="metric-value">{{ signedContracts }}</text>
          <text class="metric-label">签约合同</text>
          <view class="metric-trend" :class="contractTrend >= 0 ? 'up' : 'down'">
            <uni-icons :type="contractTrend >= 0 ? 'arrow-up' : 'arrow-down'" size="12" 
              :color="contractTrend >= 0 ? '#36B37E' : '#FF5630'"></uni-icons>
            <text>{{ Math.abs(contractTrend) }}%</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 快捷功能入口 -->
    <view class="quick-actions">
      <view class="section-title">
        <text>快捷功能</text>
      </view>
      <view class="action-grid">
        <view class="action-item" v-for="(action, index) in quickActions" :key="index" @tap="navigateToAction(action.path)">
          <view class="action-icon" :style="{ backgroundColor: action.bgColor }">
            <uni-icons :type="action.icon" size="24" color="#fff"></uni-icons>
          </view>
          <text class="action-name">{{ action.name }}</text>
        </view>
      </view>
    </view>

    <!-- 任务待办 -->
    <view class="tasks-section">
      <view class="section-header">
        <view class="section-title">
          <text>待办任务</text>
        </view>
        <view class="section-more" @tap="navigateToAllTasks">
          <text>查看全部</text>
          <uni-icons type="right" size="14" color="#999"></uni-icons>
        </view>
      </view>

      <view class="tasks-list">
        <view class="empty-state" v-if="upcomingTasks.length === 0">
          <image src="/static/images/empty-tasks.png" mode="aspectFit"></image>
          <text>今天没有待办任务</text>
        </view>
        <view class="task-item" v-for="(task, index) in upcomingTasks" :key="index" @tap="navigateToTaskDetail(task.id)">
          <view class="task-left">
            <view class="task-checkbox" :class="{'task-completed': task.completed}" @tap.stop="toggleTaskStatus(task)">
              <uni-icons v-if="task.completed" type="checkbox-filled" size="18" color="#36B37E"></uni-icons>
            </view>
            <view class="task-content">
              <text class="task-title" :class="{'task-title-completed': task.completed}">{{ task.title }}</text>
              <view class="task-info">
                <text class="task-time">{{ task.time }}</text>
                <text class="task-priority" :class="'priority-' + task.priority">{{ task.priorityText }}</text>
              </view>
            </view>
          </view>
          <view class="task-right">
            <view class="task-customer" v-if="task.customerName">
              <text>{{ task.customerName }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 销售漏斗 -->
    <view class="sales-funnel">
      <view class="section-header">
        <view class="section-title">
          <text>销售漏斗</text>
        </view>
        <view class="section-more" @tap="navigateToSalesFunnel">
          <text>查看全部</text>
          <uni-icons type="right" size="14" color="#999"></uni-icons>
        </view>
      </view>

      <view class="funnel-chart">
        <view class="funnel-stage" v-for="(stage, index) in salesFunnel" :key="index">
          <view class="stage-bar" :style="{ width: stage.percentage + '%', backgroundColor: stage.color }">
            <text class="stage-amount">¥{{ stage.amount }}</text>
          </view>
          <view class="stage-info">
            <text class="stage-name">{{ stage.name }}</text>
            <text class="stage-count">{{ stage.count }}个</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 最近活动 -->
    <view class="activities-section">
      <view class="section-header">
        <view class="section-title">
          <text>最近活动</text>
        </view>
        <view class="section-more" @tap="navigateToAllActivities">
          <text>查看全部</text>
          <uni-icons type="right" size="14" color="#999"></uni-icons>
        </view>
      </view>

      <view class="activities-list">
        <view class="empty-state" v-if="recentActivities.length === 0">
          <image src="/static/images/empty-activity.png" mode="aspectFit"></image>
          <text>暂无最近活动</text>
        </view>
        <view class="activity-item" v-for="(activity, index) in recentActivities" :key="index" @tap="navigateToActivityDetail(activity)">
          <view class="activity-icon" :style="{ backgroundColor: activity.iconBg }">
            <uni-icons :type="activity.icon" size="16" color="#fff"></uni-icons>
          </view>
          <view class="activity-content">
            <view class="activity-main">
              <text class="activity-title">{{ activity.title }}</text>
              <text class="activity-time">{{ activity.time }}</text>
            </view>
            <text class="activity-desc">{{ activity.description }}</text>
            <view class="activity-related" v-if="activity.relatedTo">
              <text>关联：{{ activity.relatedTo }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- Tab Bar 由全局组件提供 -->
  </view>
</template>

<script>
export default {
  data() {
    return {
      userName: '张经理',
      currentDate: '',
      notificationCount: 5,
      salesAmount: '87,596.00',
      salesTrend: 12.5,
      newCustomers: 27,
      customerTrend: 8.2,
      signedContracts: 9,
      contractTrend: -3.5,
      quickActions: [
        { name: '新建客户', icon: 'personadd', path: '/pages/customers/customer-create', bgColor: '#5B8FF9' },
        { name: '新建合同', icon: 'compose', path: '/pages/contracts/contract-create', bgColor: '#5AD8A6' },
        { name: '新建计划', icon: 'calendar', path: '/pages/actions/action-create', bgColor: '#F6BD16' },
        { name: '新建商机', icon: 'star', path: '/pages/sales/opportunity-create', bgColor: '#FF9A7F' },
        { name: '客户跟进', icon: 'phone', path: '/pages/customers/customer-follow-up', bgColor: '#6DC8EC' },
        { name: '收款记录', icon: 'wallet', path: '/pages/contracts/payment-create', bgColor: '#9661BC' },
        { name: '扫描名片', icon: 'camera', path: '/pages/tools/scan-card', bgColor: '#FF7C7C' },
        { name: '统计报表', icon: 'chart', path: '/pages/reports/sales-report', bgColor: '#6395F9' }
      ],
      upcomingTasks: [
        { id: 1, title: '与王总讨论合同细节', time: '13:30', priority: 'high', priorityText: '高', completed: false, customerName: '优能科技' },
        { id: 2, title: '准备产品演示PPT', time: '15:00', priority: 'medium', priorityText: '中', completed: false },
        { id: 3, title: '客户回访电话', time: '16:30', priority: 'medium', priorityText: '中', completed: false, customerName: '星辰传媒' },
        { id: 4, title: '更新销售报告', time: '17:00', priority: 'low', priorityText: '低', completed: true }
      ],
      salesFunnel: [
        { name: '线索收集', count: 56, amount: '432,000', percentage: 100, color: '#5B8FF9' },
        { name: '初步接洽', count: 42, amount: '375,500', percentage: 85, color: '#5AD8A6' },
        { name: '需求确认', count: 28, amount: '276,200', percentage: 64, color: '#F6BD16' },
        { name: '方案制定', count: 18, amount: '196,800', percentage: 48, color: '#FF9A7F' },
        { name: '商务谈判', count: 10, amount: '120,500', percentage: 27, color: '#6DC8EC' },
        { name: '签约成交', count: 5, amount: '87,596', percentage: 15, color: '#9661BC' }
      ],
      recentActivities: [
        {
          id: 1,
          icon: 'personadd',
          iconBg: '#5B8FF9',
          title: '新增客户',
          time: '今天 10:30',
          description: '创建了新客户"优能科技有限公司"',
          relatedTo: '客户管理'
        },
        {
          id: 2,
          icon: 'phone',
          iconBg: '#5AD8A6',
          title: '电话沟通',
          time: '今天 09:15',
          description: '与李总就合同细节进行了电话沟通',
          relatedTo: '星辰传媒·李总'
        },
        {
          id: 3,
          icon: 'compose',
          iconBg: '#F6BD16',
          title: '创建合同',
          time: '昨天 16:45',
          description: '创建了新合同"2023年度服务协议"',
          relatedTo: '优能科技·王总'
        },
        {
          id: 4,
          icon: 'wallet',
          iconBg: '#FF9A7F',
          title: '收款记录',
          time: '昨天 14:20',
          description: '记录了¥35,000.00的收款',
          relatedTo: '星辰传媒·合同#C2023056'
        }
      ]
    }
  },
  onLoad() {
    this.getCurrentDate();
  },
  methods: {
    getCurrentDate() {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const day = now.getDate();
      this.currentDate = `${year}年${month}月${day}日`;
    },
    navigateToSettings() {
      uni.navigateTo({
        url: '/pages/settings/profile'
      });
    },
    navigateToSearch() {
      uni.navigateTo({
        url: '/pages/common/search'
      });
    },
    navigateToAction(path) {
      uni.navigateTo({
        url: path
      });
    },
    navigateToAllTasks() {
      uni.navigateTo({
        url: '/pages/actions/action-list'
      });
    },
    navigateToTaskDetail(id) {
      uni.navigateTo({
        url: `/pages/actions/action-detail?id=${id}`
      });
    },
    toggleTaskStatus(task) {
      task.completed = !task.completed;
      // 实际应用中应当调用API更新任务状态
    },
    navigateToSalesFunnel() {
      uni.navigateTo({
        url: '/pages/sales/sales-funnel'
      });
    },
    navigateToAllActivities() {
      uni.navigateTo({
        url: '/pages/activities/activity-list'
      });
    },
    navigateToActivityDetail(activity) {
      // 根据活动类型跳转到不同页面
      let url = '';
      switch(activity.icon) {
        case 'personadd':
          url = `/pages/customers/customer-detail?id=${activity.id}`;
          break;
        case 'phone':
          url = `/pages/interactions/interaction-detail?id=${activity.id}`;
          break;
        case 'compose':
          url = `/pages/contracts/contract-detail?id=${activity.id}`;
          break;
        case 'wallet':
          url = `/pages/contracts/payment-detail?id=${activity.id}`;
          break;
        default:
          url = `/pages/interactions/interaction-detail?id=${activity.id}`;
      }
      uni.navigateTo({ url });
    }
  }
}
</script>

<style>
.dashboard-container {
  padding-bottom: 100rpx;
  background-color: #F5F7FA;
}

/* 导航栏样式 */
.nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #FFFFFF;
}

.company-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.avatar {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.avatar image {
  width: 100%;
  height: 100%;
}

.notification-badge {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background-color: #FF5630;
  color: #FFFFFF;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6rpx;
}

/* 欢迎区域样式 */
.welcome-section {
  padding: 30rpx;
  background-color: #FFFFFF;
}

.welcome-text {
  margin-bottom: 20rpx;
}

.greeting {
  font-size: 48rpx;
  font-weight: bold;
  color: #333333;
  margin-right: 20rpx;
}

.date {
  font-size: 28rpx;
  color: #999999;
}

.search-bar {
  display: flex;
  align-items: center;
  height: 80rpx;
  background-color: #F5F7FA;
  border-radius: 40rpx;
  padding: 0 30rpx;
}

.search-placeholder {
  margin-left: 16rpx;
  font-size: 28rpx;
  color: #999999;
}

/* 业绩指标卡片样式 */
.metrics-card {
  margin: 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.metrics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.metrics-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.metrics-period {
  font-size: 26rpx;
  color: #999999;
}

.metrics-row {
  display: flex;
  justify-content: space-between;
}

.metric-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.metric-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.metric-label {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 10rpx;
}

.metric-trend {
  display: flex;
  align-items: center;
  font-size: 22rpx;
}

.metric-trend.up {
  color: #36B37E;
}

.metric-trend.down {
  color: #FF5630;
}

/* 快捷功能入口样式 */
.quick-actions {
  margin: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.action-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.action-item {
  width: 25%;
  padding: 10rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.action-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.action-name {
  font-size: 24rpx;
  color: #666666;
  text-align: center;
}

/* 任务列表样式 */
.tasks-section, .activities-section {
  margin: 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-more {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #999999;
}

.tasks-list, .activities-list {
  margin-top: 20rpx;
}

.task-item {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
}

.task-item:last-child {
  border-bottom: none;
}

.task-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.task-checkbox {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #CCCCCC;
  margin-right: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.task-completed {
  border-color: #36B37E;
}

.task-content {
  flex: 1;
}

.task-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
}

.task-title-completed {
  color: #999999;
  text-decoration: line-through;
}

.task-info {
  display: flex;
  align-items: center;
}

.task-time {
  font-size: 24rpx;
  color: #999999;
  margin-right: 20rpx;
}

.task-priority {
  font-size: 22rpx;
  padding: 0 10rpx;
  border-radius: 10rpx;
}

.priority-high {
  background-color: #FFEBE6;
  color: #FF5630;
}

.priority-medium {
  background-color: #FFFAE6;
  color: #F6BD16;
}

.priority-low {
  background-color: #E3FCEF;
  color: #36B37E;
}

.task-customer {
  padding: 4rpx 16rpx;
  background-color: #F5F7FA;
  border-radius: 16rpx;
  font-size: 22rpx;
  color: #666666;
}

/* 销售漏斗样式 */
.sales-funnel {
  margin: 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.funnel-stage {
  margin-bottom: 16rpx;
}

.stage-bar {
  height: 36rpx;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 16rpx;
  margin-bottom: 8rpx;
}

.stage-amount {
  font-size: 22rpx;
  color: #FFFFFF;
  font-weight: bold;
}

.stage-info {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
}

.stage-name {
  color: #666666;
}

.stage-count {
  color: #999999;
}

/* 活动列表样式 */
.activity-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.activity-title {
  font-size: 28rpx;
  color: #333333;
  font-weight: bold;
}

.activity-time {
  font-size: 24rpx;
  color: #999999;
}

.activity-desc {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 10rpx;
}

.activity-related {
  font-size: 24rpx;
  color: #5B8FF9;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-state text {
  font-size: 28rpx;
  color: #999999;
}
</style> 
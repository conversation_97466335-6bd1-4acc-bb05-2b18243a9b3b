<template>
  <view class="page-container">
    <view class="page-header">
      <view class="page-title">行动计划</view>
      <view class="header-actions">
        <view class="header-icon" @tap="showFilterPanel">
          <svg-icon name="filter" type="svg" size="28"></svg-icon>
        </view>
        <view class="header-icon" @tap="navigateToSearch">
          <svg-icon name="search" type="svg" size="28"></svg-icon>
        </view>
        <view class="header-icon" @tap="navigateToCreate">
          <svg-icon name="add" type="svg" size="28"></svg-icon>
        </view>
      </view>
    </view>

    <!-- 选项卡 -->
    <view class="tabs-container">
      <view
        v-for="(tab, index) in tabs"
        :key="index"
        class="tab-item"
        :class="{ active: currentTab === tab.value }"
        @tap="changeTab(tab.value)"
      >
        <text>{{ tab.label }}</text>
      </view>
    </view>

    <scroll-view scroll-y class="action-list-container">
      <!-- 行动计划分组 -->
      <block v-for="(group, groupIndex) in filteredActions" :key="groupIndex">
        <view class="actions-group" v-if="group.actions.length > 0">
          <view class="group-header">{{group.title}}</view>
          
          <!-- 行动计划卡片 -->
          <view 
            class="action-card" 
            v-for="(action, actionIndex) in group.actions" 
            :key="actionIndex"
            @tap="navigateToDetail(action.id)"
            hover-class="card-hover"
          >
            <view class="action-content" :class="{ completed: action.completed }">
              <view 
                class="action-type-icon" 
                :class="[action.priority, { completed: action.completed }]"
              >
                <svg-icon :name="getTypeIconName(action)" type="svg" size="28"></svg-icon>
              </view>
              <view class="action-details">
                <view class="action-title">{{action.title}}</view>
                <view class="action-meta">
                  <view class="action-time">
                    <svg-icon name="clock" type="svg" size="24"></svg-icon>
                    <text>{{action.time}}</text>
                  </view>
                  <view class="action-related" v-if="action.related">
                    <svg-icon :name="getRelatedIconName(action)" type="svg" size="24"></svg-icon>
                    <text>{{action.related}}</text>
                  </view>
                </view>
              </view>
              <view class="action-status" @tap.stop="toggleActionStatus(action)" v-if="action.type === 'task'">
                <svg-icon :name="action.completed ? 'checkbox-circle-fill' : 'checkbox-blank-circle-line'" type="svg" size="28"></svg-icon>
              </view>
            </view>
          </view>
        </view>
      </block>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="filteredActions.length === 0 || getTotalActionsCount() === 0">
        <svg-icon name="empty-box" type="svg" size="100"></svg-icon>
        <text class="empty-title">暂无{{getEmptyText()}}</text>
        <text class="empty-desc">点击右上角"+"按钮添加新的{{getEmptyText()}}</text>
      </view>
    </scroll-view>

    <!-- 浮动添加按钮 -->
    <view class="fab" @tap="navigateToCreate">
      <svg-icon name="add" type="svg" size="60" color="#FFFFFF"></svg-icon>
    </view>
    
    <!-- 筛选面板 -->
    <view class="modal-filter" v-if="showFilter">
      <view class="modal-mask" @tap="hideFilterPanel"></view>
      <view class="modal-dialog">
        <view class="modal-header">
          <view class="modal-title">筛选条件</view>
          <view class="modal-close" @tap="hideFilterPanel">
            <svg-icon name="close" type="svg" size="32"></svg-icon>
          </view>
        </view>
        
        <view class="modal-body">
          <view class="filter-section">
            <text class="filter-title">类型</text>
            <view class="filter-options">
              <view 
                v-for="(option, index) in typeOptions" 
                :key="index"
                class="filter-tag"
                :class="{ active: selectedTypes.includes(option.value) }"
                @tap="toggleTypeFilter(option.value)"
              >
                {{option.label}}
              </view>
            </view>
          </view>
          
          <view class="filter-section">
            <text class="filter-title">优先级</text>
            <view class="filter-options">
              <view 
                v-for="(option, index) in priorityOptions" 
                :key="index"
                class="filter-tag"
                :class="{ active: selectedPriorities.includes(option.value) }"
                @tap="togglePriorityFilter(option.value)"
              >
                {{option.label}}
              </view>
            </view>
          </view>
          
          <view class="filter-section">
            <text class="filter-title">时间范围</text>
            <view class="filter-options">
              <view 
                v-for="(option, index) in timeOptions" 
                :key="index"
                class="filter-tag"
                :class="{ active: selectedTimeRange === option.value }"
                @tap="setTimeRangeFilter(option.value)"
              >
                {{option.label}}
              </view>
            </view>
          </view>
        </view>
        
        <view class="modal-footer">
          <view class="filter-actions">
            <button class="btn btn-outline" @tap="resetFilters">重置</button>
            <button class="btn btn-primary" @tap="applyFilters">应用</button>
          </view>
        </view>
      </view>
    </view>
    
    <!-- TabBar -->
    <custom-tab-bar ref="customTabBar"></custom-tab-bar>
  </view>
</template>

<script>
import SvgIcon from '@/components/svg-icon.vue';
import CustomTabBar from '@/components/CustomTabBar.vue';

export default {
  components: {
    SvgIcon,
    CustomTabBar
  },
  data() {
    return {
      currentTab: 'all',
      showFilter: false,
      
      // 选项卡配置
      tabs: [
        { label: '全部', value: 'all' },
        { label: '待办任务', value: 'task' },
        { label: '已安排', value: 'scheduled' },
        { label: '已完成', value: 'completed' }
      ],
      
      // 筛选选项
      typeOptions: [
        { label: '任务', value: 'task' },
        { label: '电话', value: 'call' },
        { label: '会议', value: 'meeting' },
        { label: '拜访', value: 'visit' },
        { label: '邮件', value: 'email' }
      ],
      priorityOptions: [
        { label: '高', value: 'high' },
        { label: '中', value: 'medium' },
        { label: '低', value: 'low' }
      ],
      timeOptions: [
        { label: '今天', value: 'today' },
        { label: '明天', value: 'tomorrow' },
        { label: '本周', value: 'thisWeek' },
        { label: '本月', value: 'thisMonth' },
        { label: '全部', value: 'all' }
      ],
      
      // 筛选条件
      selectedTypes: [],
      selectedPriorities: [],
      selectedTimeRange: 'all',
      
      // 数据源
      actionGroups: [
        {
          id: 'today',
          title: '今日 - ' + this.formatDate(new Date()),
          actions: [
            {
              id: '1',
              type: 'task',
              title: '完成销售报表',
              time: '16:30',
              related: '销售部',
              relatedType: 'department',
              priority: 'high',
              completed: false,
              date: new Date().toISOString().split('T')[0]
            },
            {
              id: '2',
              type: 'call',
              title: '回电广州未来科技公司',
              time: '14:00',
              related: '未来科技有限公司',
              relatedType: 'company',
              priority: 'medium',
              completed: false,
              date: new Date().toISOString().split('T')[0]
            },
            {
              id: '3',
              type: 'meeting',
              title: '产品演示会议',
              time: '10:00-11:00',
              related: '创新科技有限公司',
              relatedType: 'company',
              priority: 'high',
              completed: false,
              date: new Date().toISOString().split('T')[0]
            }
          ]
        },
        {
          id: 'tomorrow',
          title: '明日 - ' + this.formatDate(this.getTomorrow()),
          actions: [
            {
              id: '4',
              type: 'task',
              title: '准备季度销售会议演示文稿',
              time: '10:00',
              related: '会议',
              relatedType: 'meeting',
              priority: 'high',
              completed: false,
              date: this.getTomorrow().toISOString().split('T')[0]
            },
            {
              id: '5',
              type: 'visit',
              title: '客户现场拜访',
              time: '14:00-16:00',
              related: '北京联合科技有限公司',
              relatedType: 'company',
              priority: 'high',
              completed: false,
              date: this.getTomorrow().toISOString().split('T')[0]
            }
          ]
        },
        {
          id: 'completed',
          title: '已完成',
          actions: [
            {
              id: '6',
              type: 'task',
              title: '提交项目周报',
              time: '昨天 18:00',
              related: '报表',
              relatedType: 'report',
              priority: 'medium',
              completed: true,
              date: this.getYesterday().toISOString().split('T')[0]
            },
            {
              id: '7',
              type: 'email',
              title: '发送产品规格文档',
              time: '昨天 16:30',
              related: '上海电子科技有限公司',
              relatedType: 'company',
              priority: 'medium',
              completed: true,
              date: this.getYesterday().toISOString().split('T')[0]
            }
          ]
        }
      ]
    }
  },
  computed: {
    filteredActions() {
      if (this.showFilter) {
        return this.actionGroups;
      }
      
      // 根据tab过滤
      let filteredGroups = JSON.parse(JSON.stringify(this.actionGroups));
      
      if (this.currentTab !== 'all') {
        filteredGroups.forEach(group => {
          if (this.currentTab === 'task') {
            group.actions = group.actions.filter(action => action.type === 'task');
          } else if (this.currentTab === 'scheduled') {
            group.actions = group.actions.filter(action => action.type !== 'task');
          } else if (this.currentTab === 'completed') {
            group.actions = group.actions.filter(action => action.completed);
          }
        });
      }
      
      return filteredGroups;
    }
  },
  methods: {
    changeTab(tab) {
      this.currentTab = tab;
    },
    navigateToSearch() {
      uni.navigateTo({
        url: '/pages/common/search?type=actions'
      });
    },
    navigateToCreate() {
      uni.navigateTo({
        url: '/pages/actions/action-create'
      });
    },
    navigateToDetail(id) {
      // 查找此行动详情，根据类型跳转到不同页面
      let action = null;
      this.actionGroups.forEach(group => {
        const found = group.actions.find(a => a.id === id);
        if (found) {
          action = found;
        }
      });
      
      if (!action) return;
      
      uni.navigateTo({
        url: `/pages/actions/action-detail?id=${id}`
      });
    },
    toggleActionStatus(action) {
      action.completed = !action.completed;
      
      // 实际应用中应当调用API更新任务状态
      uni.showToast({
        title: action.completed ? '已完成' : '已取消',
        icon: 'none'
      });
    },
    showFilterPanel() {
      this.showFilter = true;
    },
    hideFilterPanel() {
      this.showFilter = false;
    },
    toggleTypeFilter(type) {
      const index = this.selectedTypes.indexOf(type);
      if (index > -1) {
        this.selectedTypes.splice(index, 1);
      } else {
        this.selectedTypes.push(type);
      }
    },
    togglePriorityFilter(priority) {
      const index = this.selectedPriorities.indexOf(priority);
      if (index > -1) {
        this.selectedPriorities.splice(index, 1);
      } else {
        this.selectedPriorities.push(priority);
      }
    },
    setTimeRangeFilter(range) {
      this.selectedTimeRange = range;
    },
    resetFilters() {
      this.selectedTypes = [];
      this.selectedPriorities = [];
      this.selectedTimeRange = 'all';
    },
    applyFilters() {
      // 应用筛选条件（实际应用中应该根据筛选条件重新获取数据）
      this.hideFilterPanel();
      
      // 仅作为示例
      uni.showToast({
        title: '筛选已应用',
        icon: 'none'
      });
    },
    getTypeIconName(action) {
      const iconMap = {
        'task': 'check-circle',
        'call': 'phone',
        'meeting': 'team',
        'visit': 'navigation',
        'email': 'mail'
      };
      
      return iconMap[action.type] || 'schedule';
    },
    getRelatedIconName(action) {
      const iconMap = {
        'company': 'building',
        'contact': 'user',
        'opportunity': 'briefcase',
        'contract': 'file-list',
        'meeting': 'presentation',
        'report': 'chart',
        'department': 'team'
      };
      
      return iconMap[action.relatedType] || 'link';
    },
    getEmptyText() {
      if (this.currentTab === 'task') {
        return '任务';
      } else if (this.currentTab === 'scheduled') {
        return '已安排的活动';
      } else {
        return '行动计划';
      }
    },
    formatDate(date) {
      const now = new Date();
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      
      // 如果是当前年份，只显示月日
      if (year === now.getFullYear()) {
        return `${month}月${day}日`;
      }
      
      return `${year}年${month}月${day}日`;
    },
    getTomorrow() {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      return tomorrow;
    },
    getYesterday() {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      return yesterday;
    },
    getTotalActionsCount() {
      return this.filteredActions.reduce((count, group) => {
        return count + group.actions.length;
      }, 0);
    },
    loadActions() {
      // 此处应该从API加载行动计划数据
      console.log('加载行动计划数据');
    }
  },
  onLoad() {
    // 加载数据
    this.loadActions();
  },
  onShow() {
    // 设置TabBar选中项
    if (typeof this.$refs.customTabBar !== 'undefined') {
      this.$refs.customTabBar.current = 3; // 行动计划在TabBar中的位置
    }
  },
  onPullDownRefresh() {
    // 下拉刷新
    setTimeout(() => {
      this.loadActions();
      uni.stopPullDownRefresh();
    }, 1000);
  }
}
</script>

<style>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #e0e0e0;
  background-color: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 30rpx;
}

.header-icon {
  color: #666;
  font-size: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tabs-container {
  display: flex;
  padding: 0 20rpx;
  border-bottom: 1rpx solid #e0e0e0;
  background-color: white;
  position: sticky;
  top: 120rpx;
  z-index: 9;
}

.tab-item {
  padding: 24rpx 40rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #3370ff;
  font-weight: 500;
}

.tab-item.active:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 40rpx;
  right: 40rpx;
  height: 4rpx;
  background-color: #3370ff;
  border-radius: 4rpx;
}

.action-list-container {
  flex: 1;
  padding: 20rpx;
}

.actions-group {
  margin-bottom: 30rpx;
}

.group-header {
  font-size: 28rpx;
  font-weight: bold;
  padding: 20rpx 30rpx;
  color: #666;
}

.action-card {
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.action-content {
  display: flex;
  align-items: center;
}

.action-content.completed {
  opacity: 0.6;
}

.action-type-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  margin-right: 20rpx;
  color: #666;
}

.action-type-icon.high {
  color: #f5222d;
}

.action-type-icon.medium {
  color: #faad14;
}

.action-type-icon.low {
  color: #52c41a;
}

.action-details {
  flex: 1;
}

.action-title {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
  color: #333;
}

.action-meta {
  display: flex;
  font-size: 24rpx;
  color: #999;
}

.action-time, .action-related {
  display: flex;
  align-items: center;
  margin-right: 40rpx;
}

.action-time text, .action-related text {
  margin-left: 8rpx;
}

.action-status {
  color: #a0a0a0;
}

.action-status.completed {
  color: #52c41a;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  color: #a0a0a0;
}

.empty-title {
  font-size: 32rpx;
  margin: 40rpx 0 20rpx;
}

.empty-desc {
  font-size: 28rpx;
}

.fab {
  position: fixed;
  right: 40rpx;
  bottom: 140rpx;
  width: 120rpx;
  height: 120rpx;
  background-color: #3370ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(51, 112, 255, 0.3);
  z-index: 100;
}

.modal-filter {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-dialog {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  padding-bottom: 40rpx;
  max-height: 80vh;
  overflow: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
}

.modal-close {
  color: #999;
}

.modal-body {
  padding: 30rpx;
}

.filter-section {
  margin-bottom: 40rpx;
}

.filter-title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.filter-tag {
  padding: 16rpx 30rpx;
  border-radius: 100rpx;
  font-size: 26rpx;
  background-color: #f5f7fa;
  color: #666;
}

.filter-tag.active {
  background-color: #e6f0ff;
  color: #3370ff;
  font-weight: 500;
}

.modal-footer {
  padding: 0 30rpx;
}

.filter-actions {
  display: flex;
  gap: 30rpx;
}

.btn {
  flex: 1;
  height: 88rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}

.btn-outline {
  border: 1rpx solid #d9d9d9;
  color: #666;
}

.btn-primary {
  background-color: #3370ff;
  color: white;
}

.card-hover {
  opacity: 0.8;
}
</style> 
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-contracts-contract-edit"],{"3aff":function(t,e,a){"use strict";a.r(e);var n=a("9574"),i=a("9fa0");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("cc5a");var s=a("828b"),r=Object(s["a"])(i["default"],n["b"],n["c"],!1,null,"323be797",null,!1,n["a"],void 0);e["default"]=r.exports},4610:function(t,e,a){var n=a("8f13");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("967d").default;i("8c0d885c",n,!0,{sourceMap:!1,shadowMode:!1})},"8f13":function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,".container[data-v-323be797]{display:flex;flex-direction:column;height:100vh;background-color:#f5f5f5}.page-header[data-v-323be797]{display:flex;align-items:center;justify-content:space-between;padding:12px 16px;border-bottom:1px solid #eee;background-color:#fff;z-index:10}.page-title[data-v-323be797]{font-size:18px;font-weight:700;color:#333}.back-button[data-v-323be797]{color:#666;display:flex;align-items:center}.header-actions uni-button[data-v-323be797]{color:#3a86ff;font-weight:500;background:none;border:none;font-size:16px;padding:0}.form-container[data-v-323be797]{flex:1;padding:12px;margin-bottom:100px}.form-section[data-v-323be797]{background-color:#fff;border-radius:8px;padding:16px;margin-bottom:12px;border:1px solid #eee}.section-title[data-v-323be797]{font-size:16px;font-weight:600;margin-bottom:12px;color:#333;display:flex;align-items:center;justify-content:space-between}.form-group[data-v-323be797]{margin-bottom:12px}.form-label[data-v-323be797]{display:block;margin-bottom:4px;font-size:14px;font-weight:500;color:#666}.form-input[data-v-323be797]{width:100%;padding:10px 12px;border:1px solid #ddd;border-radius:8px;font-size:14px;color:#333;background-color:#fff;box-sizing:border-box;height:44px;line-height:24px}.form-hint[data-v-323be797]{font-size:12px;color:#999;margin-left:4px}.picker-view[data-v-323be797]{display:flex;justify-content:space-between;align-items:center;padding:10px 12px;border:1px solid #ddd;border-radius:8px;color:#333;background-color:#fff;font-size:14px;height:44px;box-sizing:border-box}.placeholder[data-v-323be797]{color:#999}.form-date[data-v-323be797]{position:relative}.form-textarea[data-v-323be797]{width:100%;padding:10px 12px;border:1px solid #ddd;border-radius:8px;font-size:14px;color:#333;background-color:#fff;min-height:100px;box-sizing:border-box;line-height:24px}\n\n/* 客户选择器样式 */.customer-selector[data-v-323be797]{display:flex;align-items:center;gap:16px;padding:12px;background-color:#f5f5f5;border-radius:8px;margin-bottom:12px;border:1px solid #eee;min-height:64px;box-sizing:border-box}.customer-avatar[data-v-323be797]{width:40px;height:40px;border-radius:50%;background-color:#e0f0ff;color:#3a86ff;display:flex;align-items:center;justify-content:center;font-weight:700;flex-shrink:0}.customer-info[data-v-323be797]{flex:1}.customer-name[data-v-323be797]{font-size:15px;font-weight:500;color:#333}.customer-meta[data-v-323be797]{font-size:12px;color:#999;margin-top:4px}.customer-action[data-v-323be797]{display:flex;flex-direction:column;align-items:flex-end}.customer-select-button[data-v-323be797]{color:#3a86ff;font-size:14px;font-weight:500;display:flex;align-items:center;gap:4px}\n\n/* 添加按钮 */.add-button[data-v-323be797]{display:flex;align-items:center;justify-content:center;gap:8px;width:100%;padding:12px;border:1px dashed #ddd;border-radius:8px;color:#3a86ff;background-color:#fff;font-size:14px;font-weight:500;margin-bottom:12px;height:44px;box-sizing:border-box}\n\n/* 摘要部分 */.summary-table[data-v-323be797]{width:100%}.summary-row[data-v-323be797]{display:flex;justify-content:space-between;padding:8px 0}.summary-table .label[data-v-323be797]{font-size:14px;color:#666;text-align:left}.summary-table .value[data-v-323be797]{font-size:14px;color:#333;text-align:right;font-weight:500;display:flex;align-items:center;justify-content:flex-end}.summary-table uni-input[data-v-323be797]{text-align:right;border:1px solid transparent;background-color:initial;padding:4px 8px;border-radius:4px;font-size:14px;color:#333;font-weight:500;width:120px;height:36px;line-height:24px;box-sizing:border-box}.summary-table uni-input[data-v-323be797]:focus{border-color:#ddd;background-color:#fff;outline:none}.total-row[data-v-323be797]{display:flex;justify-content:space-between;border-top:1px solid #eee;margin-top:8px;padding-top:8px}.total-row .label[data-v-323be797]{font-size:16px;font-weight:600;color:#333}.total-row .value[data-v-323be797]{font-size:18px;font-weight:600;color:#3a86ff}\n\n/* 付款计划样式 */.payment-plan-item[data-v-323be797]{background-color:#f5f5f5;border-radius:8px;padding:16px;margin-bottom:16px;border:1px solid #eee;box-sizing:border-box}.payment-item-header[data-v-323be797]{display:flex;justify-content:space-between;align-items:center;margin-bottom:8px}.payment-item-title[data-v-323be797]{font-weight:500;color:#333}.payment-item-actions[data-v-323be797]{display:flex;gap:4px}.payment-action-button[data-v-323be797]{width:36px;height:36px;display:flex;align-items:center;justify-content:center;border-radius:50%;border:1px solid #eee;background-color:#fff}.delete-icon[data-v-323be797]{color:#ff4d4f;font-size:28px;font-weight:700;line-height:28px}.payment-item-body[data-v-323be797]{display:grid;grid-template-columns:repeat(2,1fr);gap:8px}\n\n/* 文件项样式 */.file-item[data-v-323be797]{display:flex;align-items:center;padding:12px;background-color:#f5f5f5;border-radius:8px;margin-bottom:12px;height:48px;box-sizing:border-box}.file-item uni-text[data-v-323be797]:first-child{margin-right:8px;font-size:20px;color:#ff5a5f}.file-item uni-text[data-v-323be797]:nth-child(2){flex:1;font-size:14px;color:#333}.file-action-button[data-v-323be797]{width:36px;height:36px;display:flex;align-items:center;justify-content:center;border-radius:50%;border:1px solid #eee;background-color:#fff}\n\n/* 底部操作栏 */.action-bar[data-v-323be797]{position:fixed;bottom:0;left:0;right:0;background-color:#fff;padding:12px;display:flex;justify-content:space-between;gap:12px;border-top:1px solid #eee;z-index:100}.action-bar .btn[data-v-323be797]{flex:1;height:40px;display:flex;align-items:center;justify-content:center;border-radius:8px;font-size:14px;font-weight:500}.btn-primary[data-v-323be797]{background-color:#3a86ff;color:#fff;border:none}.btn-outline[data-v-323be797]{background-color:#fff;color:#333;border:1px solid #ddd}\n\n/* 添加删除按钮样式 */.delete-button[data-v-323be797]{width:36px;height:36px;display:flex;align-items:center;justify-content:center;border-radius:50%;border:1px solid #ff4d4f;background-color:#fff0f0}.delete-icon[data-v-323be797]{color:#ff4d4f;font-size:28px;font-weight:700;line-height:28px}.delete-button[data-v-323be797]:active{background-color:#ffccc7}",""]),t.exports=e},9574:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"container"},[a("v-uni-view",{staticClass:"page-header"},[a("v-uni-view",{staticClass:"back-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-arrow-left-line"})],1),a("v-uni-view",{staticClass:"page-title"},[t._v("编辑合同")]),a("v-uni-view",{staticClass:"header-actions"},[a("v-uni-button",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveContract.apply(void 0,arguments)}}},[t._v("保存")])],1)],1),a("v-uni-scroll-view",{staticClass:"form-container",attrs:{"scroll-y":!0}},[a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-title"},[t._v("基本信息")]),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("合同名称")]),a("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"输入合同名称"},model:{value:t.formData.title,callback:function(e){t.$set(t.formData,"title",e)},expression:"formData.title"}})],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("合同编号")]),a("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"自动生成",disabled:!0},model:{value:t.formData.code,callback:function(e){t.$set(t.formData,"code",e)},expression:"formData.code"}})],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("关联报价单")]),a("v-uni-picker",{attrs:{value:t.quotationIndex,range:t.quotations,"range-key":"name"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onQuotationChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[t.formData.quotation?a("v-uni-text",[t._v(t._s(t.formData.quotation.name))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择关联报价单")]),a("v-uni-text",{staticClass:"ri-arrow-down-s-line"})],1)],1)],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("签署日期")]),a("v-uni-view",{staticClass:"form-date"},[a("v-uni-picker",{attrs:{mode:"date",value:t.formData.signingDate},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onSigningDateChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[t.formData.signingDate?a("v-uni-text",[t._v(t._s(t.formData.signingDate))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择签署日期")]),a("v-uni-text",{staticClass:"ri-calendar-line"})],1)],1)],1)],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("有效期至")]),a("v-uni-view",{staticClass:"form-date"},[a("v-uni-picker",{attrs:{mode:"date",value:t.formData.expiryDate},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onExpiryDateChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[t.formData.expiryDate?a("v-uni-text",[t._v(t._s(t.formData.expiryDate))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择有效期")]),a("v-uni-text",{staticClass:"ri-calendar-line"})],1)],1)],1)],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("合同类型")]),a("v-uni-picker",{attrs:{value:t.contractTypeIndex,range:t.contractTypes,"range-key":"name"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onContractTypeChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[t.formData.contractType?a("v-uni-text",[t._v(t._s(t.formData.contractType.name))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择合同类型")]),a("v-uni-text",{staticClass:"ri-arrow-down-s-line"})],1)],1)],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("负责人")]),a("v-uni-picker",{attrs:{value:t.ownerIndex,range:t.owners,"range-key":"name"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onOwnerChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[t.formData.owner?a("v-uni-text",[t._v(t._s(t.formData.owner.name))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择负责人")]),a("v-uni-text",{staticClass:"ri-arrow-down-s-line"})],1)],1)],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("状态")]),a("v-uni-picker",{attrs:{value:t.statusIndex,range:t.statuses,"range-key":"name"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onStatusChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[t.formData.status?a("v-uni-text",[t._v(t._s(t.formData.status.name))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择状态")]),a("v-uni-text",{staticClass:"ri-arrow-down-s-line"})],1)],1)],1)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-title"},[t._v("客户信息")]),a("v-uni-view",{staticClass:"customer-selector",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectCustomer.apply(void 0,arguments)}}},[t.formData.customer?a("v-uni-view",{staticClass:"customer-avatar"},[t._v(t._s(t.formData.customer.name.charAt(0)))]):a("v-uni-view",{staticClass:"customer-avatar"},[t._v("选")]),a("v-uni-view",{staticClass:"customer-info"},[t.formData.customer?a("v-uni-view",{staticClass:"customer-name"},[t._v(t._s(t.formData.customer.name))]):a("v-uni-view",{staticClass:"customer-name"},[t._v("请选择客户")]),t.formData.customer?a("v-uni-view",{staticClass:"customer-meta"},[t._v(t._s(t.formData.customer.industry)+" · "+t._s(t.formData.customer.size))]):t._e()],1),a("v-uni-view",{staticClass:"customer-action"},[a("v-uni-text",{staticClass:"customer-select-button"},[t._v("更换客户"),a("v-uni-text",{staticClass:"ri-arrow-right-s-line"})],1)],1)],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("联系人")]),a("v-uni-picker",{attrs:{value:t.contactIndex,range:t.contacts,"range-key":"name",disabled:!t.formData.customer},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onContactChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[t.formData.contact?a("v-uni-text",[t._v(t._s(t.formData.contact.name)+" ("+t._s(t.formData.contact.title)+")")]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择联系人")]),a("v-uni-text",{staticClass:"ri-arrow-down-s-line"})],1)],1)],1)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-title"},[t._v("合同金额")]),a("v-uni-view",{staticClass:"summary-table"},[t._l(t.formData.items,(function(e,n){return a("v-uni-view",{key:n,staticClass:"summary-row"},[a("v-uni-text",{staticClass:"label"},[t._v(t._s(e.name))]),a("v-uni-view",{staticClass:"value"},[a("v-uni-input",{attrs:{type:"digit",placeholder:"0"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.calculateTotal.apply(void 0,arguments)}},model:{value:e.amount,callback:function(a){t.$set(e,"amount",a)},expression:"item.amount"}})],1)],1)})),a("v-uni-view",{staticClass:"summary-row"},[a("v-uni-text",{staticClass:"label"},[t._v("增值税 ("+t._s(t.formData.taxRate)+"%)")]),a("v-uni-text",{staticClass:"value"},[t._v("¥"+t._s(t.formatNumber(t.formData.tax)))])],1),a("v-uni-view",{staticClass:"total-row"},[a("v-uni-text",{staticClass:"label"},[t._v("总计")]),a("v-uni-text",{staticClass:"value"},[t._v("¥"+t._s(t.formatNumber(t.formData.totalAmount)))])],1)],2)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-title"},[t._v("付款计划")]),t._l(t.formData.paymentPlan,(function(e,n){return a("v-uni-view",{key:n,staticClass:"payment-plan-item"},[a("v-uni-view",{staticClass:"payment-item-header"},[a("v-uni-view",{staticClass:"payment-item-title"},[t._v("第"+t._s(n+1)+"期付款")]),a("v-uni-view",{staticClass:"payment-item-actions"},[a("v-uni-view",{staticClass:"payment-action-button delete-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.removePaymentPlan(n)}}},[a("v-uni-view",{staticClass:"delete-icon"},[t._v("×")])],1)],1)],1),a("v-uni-view",{staticClass:"payment-item-body"},[a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("付款金额")]),a("v-uni-input",{staticClass:"form-input",attrs:{type:"digit",placeholder:"0"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.calculatePercentage(n)}},model:{value:e.amount,callback:function(a){t.$set(e,"amount",a)},expression:"payment.amount"}})],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("付款比例")]),a("v-uni-input",{staticClass:"form-input",attrs:{type:"text",value:e.percentage+"%",disabled:!0}})],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("预计日期")]),a("v-uni-view",{staticClass:"form-date"},[a("v-uni-picker",{attrs:{mode:"date",value:e.date},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),function(e){return t.onPaymentDateChange(e,n)}.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[a("v-uni-text",[t._v(t._s(e.date||"请选择日期"))]),a("v-uni-text",{staticClass:"ri-calendar-line"})],1)],1)],1)],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("状态")]),a("v-uni-picker",{attrs:{value:t.getPaymentStatusIndex(e.status),range:t.paymentStatuses,"range-key":"name"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),function(e){return t.onPaymentStatusChange(e,n)}.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[a("v-uni-text",[t._v(t._s(t.getPaymentStatusName(e.status)))]),a("v-uni-text",{staticClass:"ri-arrow-down-s-line"})],1)],1)],1)],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("付款说明")]),a("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"输入付款说明"},model:{value:e.description,callback:function(a){t.$set(e,"description",a)},expression:"payment.description"}})],1)],1)})),a("v-uni-button",{staticClass:"add-button",attrs:{type:"button"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.addPaymentPlan.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-add-line"}),t._v("添加付款计划")],1)],2),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-title"},[t._v("合同条款")]),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("项目周期")]),a("v-uni-input",{staticClass:"form-input",attrs:{type:"number",placeholder:"输入项目周期"},model:{value:t.formData.terms.projectDuration,callback:function(e){t.$set(t.formData.terms,"projectDuration",e)},expression:"formData.terms.projectDuration"}}),a("v-uni-text",{staticClass:"form-hint"},[t._v("天")])],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("质保期")]),a("v-uni-input",{staticClass:"form-input",attrs:{type:"number",placeholder:"输入质保期"},model:{value:t.formData.terms.warrantyPeriod,callback:function(e){t.$set(t.formData.terms,"warrantyPeriod",e)},expression:"formData.terms.warrantyPeriod"}}),a("v-uni-text",{staticClass:"form-hint"},[t._v("月")])],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("详细条款")]),a("v-uni-textarea",{staticClass:"form-textarea",attrs:{placeholder:"输入详细条款"},model:{value:t.formData.terms.content,callback:function(e){t.$set(t.formData.terms,"content",e)},expression:"formData.terms.content"}})],1)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-title"},[t._v("相关文档")]),t._l(t.formData.documents,(function(e,n){return a("v-uni-view",{key:n,staticClass:"file-item"},[a("v-uni-text",{staticClass:"ri-file-pdf-line"}),a("v-uni-text",[t._v(t._s(e.name))]),a("v-uni-view",{staticClass:"file-action-button delete-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.removeDocument(n)}}},[a("v-uni-view",{staticClass:"delete-icon"},[t._v("×")])],1)],1)})),a("v-uni-button",{staticClass:"add-button",attrs:{type:"button"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.uploadDocument.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-add-line"}),t._v("上传文档")],1)],2)],1),a("v-uni-view",{staticClass:"action-bar"},[a("v-uni-button",{staticClass:"btn btn-outline",attrs:{type:"button"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.cancel.apply(void 0,arguments)}}},[t._v("取消")]),a("v-uni-button",{staticClass:"btn btn-primary",attrs:{type:"button"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveContract.apply(void 0,arguments)}}},[t._v("保存")])],1)],1)},i=[]},"9fa0":function(t,e,a){"use strict";a.r(e);var n=a("a7ac"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},a7ac:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("5c47"),a("a1c1"),a("c9b5"),a("bf0f"),a("ab80"),a("2797"),a("e838"),a("aa9c"),a("dd2b"),a("aa77"),a("bd06");var n={data:function(){return{formData:{title:"企业系统集成项目合同",code:"CT-2023-09-001",quotation:{id:"QT-2023-09-005",name:"QT-2023-09-005 (企业系统集成项目)"},signingDate:"2023-10-25",expiryDate:"2024-10-24",contractType:{id:"system-integration",name:"系统集成"},owner:{id:"2",name:"王销售"},status:{code:"signed",name:"已签署"},customer:{id:"1",name:"上海智能科技",industry:"科技行业",size:"大型企业"},contact:{id:"1",name:"张总监",title:"技术总监"},items:[{name:"系统集成",amount:"320000"},{name:"定制开发",amount:"150000"},{name:"培训与支持",amount:"45000"}],taxRate:13,tax:66950,totalAmount:581950,paymentPlan:[{amount:"290975",percentage:50,date:"2023-10-25",status:"paid",description:"合同签署后7天内支付"},{amount:"174585",percentage:30,date:"2023-12-25",status:"pending",description:"项目交付后7天内支付"},{amount:"116390",percentage:20,date:"2024-01-25",status:"pending",description:"项目验收通过后7天内支付"}],terms:{projectDuration:"90",warrantyPeriod:"12",content:"1. 项目范围\n   - 系统集成服务包括硬件部署、软件安装与配置、系统联调\n   - 定制开发服务包括需求分析、设计、开发、测试和部署\n   - 培训与支持服务包括用户培训、管理员培训和远程技术支持\n\n2. 交付时间与验收\n   - 项目预计于签约后90天内完成交付\n   - 客户有10个工作日进行验收测试\n   - 验收标准详见附件1《验收标准说明》\n\n3. 保密条款\n   - 双方对项目过程中获知的对方商业秘密负有保密义务\n   - 未经授权不得向第三方透露项目相关信息\n\n4. 知识产权\n   - 定制开发部分的知识产权归客户所有\n   - 供应商预先存在的技术和工具的知识产权仍归供应商所有\n\n5. 违约责任\n   - 任何一方违约，应承担由此给对方造成的损失\n\n6. 不可抗力\n   - 因不可抗力导致合同无法履行的，双方可协商解除合同"},documents:[{id:"1",name:"企业系统集成项目合同.pdf",url:""},{id:"2",name:"技术方案附件.pdf",url:""}]},quotations:[{id:"",name:"无关联报价单"},{id:"QT-2023-09-005",name:"QT-2023-09-005 (企业系统集成项目)"},{id:"QT-2023-10-001",name:"QT-2023-10-001 (云数据分析平台解决方案)"}],contractTypes:[{id:"",name:"请选择合同类型"},{id:"system-integration",name:"系统集成"},{id:"product-sales",name:"产品销售"},{id:"service",name:"服务协议"},{id:"maintenance",name:"维护合同"},{id:"custom",name:"自定义"}],owners:[{id:"",name:"请选择负责人"},{id:"1",name:"李销售"},{id:"2",name:"王销售"},{id:"3",name:"张总监"}],statuses:[{code:"draft",name:"草稿"},{code:"review",name:"审批中"},{code:"signed",name:"已签署"},{code:"completed",name:"已履行"},{code:"terminated",name:"已终止"}],contacts:[{id:"",name:"请选择联系人",title:""},{id:"1",name:"张总监",title:"技术总监"},{id:"2",name:"李经理",title:"采购经理"},{id:"3",name:"王总",title:"总经理"}],paymentStatuses:[{code:"pending",name:"待付款"},{code:"paid",name:"已付款"},{code:"overdue",name:"逾期"}],quotationIndex:1,contractTypeIndex:1,ownerIndex:2,statusIndex:2,contactIndex:1}},onLoad:function(t){t.id},methods:{goBack:function(){uni.navigateBack()},saveContract:function(){this.validateForm()&&(uni.showLoading({title:"保存中..."}),setTimeout((function(){uni.hideLoading(),uni.showToast({title:"合同已保存",icon:"success"}),setTimeout((function(){uni.navigateBack()}),1500)}),1e3))},cancel:function(){uni.showModal({title:"提示",content:"确定要取消编辑吗？未保存的内容将丢失",success:function(t){t.confirm&&uni.navigateBack()}})},validateForm:function(){return this.formData.title?this.formData.customer?!!this.formData.signingDate||(uni.showToast({title:"请选择签署日期",icon:"none"}),!1):(uni.showToast({title:"请选择客户",icon:"none"}),!1):(uni.showToast({title:"请输入合同名称",icon:"none"}),!1)},formatNumber:function(t){return t.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")},calculateTotal:function(){var t=0;this.formData.items.forEach((function(e){t+=parseFloat(e.amount||0)})),this.formData.tax=t*(this.formData.taxRate/100),this.formData.totalAmount=t+this.formData.tax,this.updatePaymentPercentages()},updatePaymentPercentages:function(){var t=this.formData.totalAmount;t>0&&this.formData.paymentPlan.forEach((function(e){e.percentage=Math.round(parseFloat(e.amount)/t*100)}))},calculatePercentage:function(t){var e=this.formData.paymentPlan[t],a=parseFloat(e.amount||0),n=this.formData.totalAmount;e.percentage=n>0?Math.round(a/n*100):0},addPaymentPlan:function(){this.formData.paymentPlan.push({amount:"0",percentage:0,date:"",status:"pending",description:""})},removePaymentPlan:function(t){var e=this;uni.showModal({title:"提示",content:"确定要删除此付款计划吗？",success:function(a){a.confirm&&(e.formData.paymentPlan.splice(t,1),e.updatePaymentPercentages())}})},getPaymentStatusName:function(t){var e=this.paymentStatuses.find((function(e){return e.code===t}));return e?e.name:"待付款"},getPaymentStatusIndex:function(t){return this.paymentStatuses.findIndex((function(e){return e.code===t}))},selectCustomer:function(){var t=this;uni.navigateTo({url:"/pages/customers/customer-select",events:{customerSelected:function(e){t.formData.customer=e,t.formData.contact=null}}})},uploadDocument:function(){uni.showToast({title:"上传文档功能开发中...",icon:"none"})},removeDocument:function(t){var e=this;uni.showModal({title:"提示",content:"确定要删除此文档吗？",success:function(a){a.confirm&&e.formData.documents.splice(t,1)}})},onQuotationChange:function(t){var e=t.detail.value;this.quotationIndex=e,this.formData.quotation=this.quotations[e]},onSigningDateChange:function(t){this.formData.signingDate=t.detail.value},onExpiryDateChange:function(t){this.formData.expiryDate=t.detail.value},onContractTypeChange:function(t){var e=t.detail.value;this.contractTypeIndex=e,this.formData.contractType=this.contractTypes[e]},onOwnerChange:function(t){var e=t.detail.value;this.ownerIndex=e,this.formData.owner=this.owners[e]},onStatusChange:function(t){var e=t.detail.value;this.statusIndex=e,this.formData.status=this.statuses[e]},onContactChange:function(t){var e=t.detail.value;this.contactIndex=e,this.formData.contact=this.contacts[e]},onPaymentDateChange:function(t,e){this.formData.paymentPlan[e].date=t.detail.value},onPaymentStatusChange:function(t,e){var a=t.detail.value;this.formData.paymentPlan[e].status=this.paymentStatuses[a].code}}};e.default=n},cc5a:function(t,e,a){"use strict";var n=a("4610"),i=a.n(n);i.a}}]);
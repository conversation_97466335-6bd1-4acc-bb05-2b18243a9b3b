<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="goBack">
        <text class="ri-arrow-left-line"></text>
      </view>
      <text class="page-title">员工详情</text>
      <view class="header-actions">
        <button type="button" class="action-button" @click="editEmployee">
          <text class="ri-edit-line"></text>
        </button>
      </view>
    </view>
    
    <!-- 员工基本信息 -->
    <view class="employee-card">
      <view class="employee-header">
        <view class="employee-avatar">
          <image :src="employee.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
        </view>
        <view class="employee-main-info">
          <view class="employee-name-row">
            <text class="employee-name">{{employee.name}}</text>
            <text :class="['employee-status', employee.status === 'active' ? 'status-active' : 'status-inactive']">
              {{employee.status === 'active' ? '在职' : '离职'}}
            </text>
          </view>
          <text class="employee-position">{{employee.position}}</text>
          <text class="employee-department">{{employee.department}}</text>
        </view>
      </view>
      
      <view class="employee-quick-contact">
        <view class="contact-action" @click="callEmployee">
          <text class="ri-phone-line contact-icon"></text>
          <text class="contact-label">电话</text>
        </view>
        <view class="contact-action" @click="messageEmployee">
          <text class="ri-message-2-line contact-icon"></text>
          <text class="contact-label">短信</text>
        </view>
        <view class="contact-action" @click="emailEmployee">
          <text class="ri-mail-line contact-icon"></text>
          <text class="contact-label">邮件</text>
        </view>
      </view>
    </view>
    
    <!-- 员工详细信息 -->
    <view class="detail-section">
      <view class="section-header">
        <text class="section-title">个人信息</text>
      </view>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">工号</text>
          <text class="info-value">{{employee.employeeId}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">性别</text>
          <text class="info-value">{{employee.gender}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">出生日期</text>
          <text class="info-value">{{employee.birthday}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">身份证号</text>
          <text class="info-value">{{employee.idNumber}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">手机</text>
          <text class="info-value">{{employee.phone}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">邮箱</text>
          <text class="info-value">{{employee.email}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">居住地址</text>
          <text class="info-value">{{employee.address}}</text>
        </view>
      </view>
    </view>
    
    <!-- 员工工作信息 -->
    <view class="detail-section">
      <view class="section-header">
        <text class="section-title">工作信息</text>
      </view>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">入职日期</text>
          <text class="info-value">{{employee.hireDate}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">部门</text>
          <text class="info-value">{{employee.department}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">职位</text>
          <text class="info-value">{{employee.position}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">直属上级</text>
          <text class="info-value">{{employee.manager}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">工作地点</text>
          <text class="info-value">{{employee.workLocation}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">员工类型</text>
          <text class="info-value">{{employee.employeeType}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">工作状态</text>
          <text class="info-value">{{employee.status === 'active' ? '在职' : '离职'}}</text>
        </view>
      </view>
    </view>
    
    <!-- 数据统计 -->
    <view class="detail-section">
      <view class="section-header">
        <text class="section-title">绩效概览</text>
        <text class="section-action" @click="viewPerformance">查看详情</text>
      </view>
      <view class="performance-overview">
        <view class="performance-card">
          <text class="performance-value">{{employee.performance.sales || 0}}</text>
          <text class="performance-label">销售业绩(元)</text>
        </view>
        <view class="performance-card">
          <text class="performance-value">{{employee.performance.tasks || 0}}</text>
          <text class="performance-label">完成任务数</text>
        </view>
        <view class="performance-card">
          <text class="performance-value">{{employee.performance.customers || 0}}</text>
          <text class="performance-label">新增客户数</text>
        </view>
      </view>
    </view>
    
    <!-- 技能与证书 -->
    <view class="detail-section">
      <view class="section-header">
        <text class="section-title">技能与证书</text>
        <text class="section-action" @click="manageCertifications">管理</text>
      </view>
      <view class="tag-list">
        <view class="skill-tag" v-for="(skill, index) in employee.skills" :key="index">
          {{skill}}
        </view>
      </view>
      <view class="cert-list">
        <view class="cert-item" v-for="(cert, index) in employee.certifications" :key="index">
          <text class="ri-graduation-cap-line cert-icon"></text>
          <view class="cert-detail">
            <text class="cert-name">{{cert.name}}</text>
            <text class="cert-date">获得时间：{{cert.date}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 合同与薪资 -->
    <view class="detail-section">
      <view class="section-header">
        <text class="section-title">合同与薪资</text>
        <text class="section-action" @click="viewContract">查看合同</text>
      </view>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">合同开始日期</text>
          <text class="info-value">{{employee.contract.startDate}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">合同结束日期</text>
          <text class="info-value">{{employee.contract.endDate}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">合同类型</text>
          <text class="info-value">{{employee.contract.type}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">基本工资</text>
          <text class="info-value">¥{{formatSalary(employee.salary.base)}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">社保基数</text>
          <text class="info-value">¥{{formatSalary(employee.salary.socialSecurity)}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">公积金基数</text>
          <text class="info-value">¥{{formatSalary(employee.salary.housingFund)}}</text>
        </view>
      </view>
    </view>
    
    <!-- 操作记录 -->
    <view class="detail-section">
      <view class="section-header">
        <text class="section-title">操作记录</text>
        <text class="section-action" @click="viewAllRecords">全部记录</text>
      </view>
      <view class="timeline">
        <view class="timeline-item" v-for="(record, index) in employee.records" :key="index">
          <view class="timeline-dot"></view>
          <view class="timeline-content">
            <text class="timeline-title">{{record.title}}</text>
            <text class="timeline-desc">{{record.description}}</text>
            <text class="timeline-time">{{record.time}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      id: '',
      employee: {
        id: '1',
        name: '张三',
        status: 'active',
        employeeId: 'EMP001',
        gender: '男',
        birthday: '1985-06-15',
        idNumber: '110101198506150011',
        department: '销售部',
        position: '销售经理',
        manager: '王经理',
        hireDate: '2020-06-15',
        workLocation: '北京总部',
        employeeType: '全职',
        phone: '13812345678',
        email: '<EMAIL>',
        address: '北京市朝阳区长安街1号',
        avatar: '/static/images/avatars/avatar1.png',
        performance: {
          sales: 1250000,
          tasks: 28,
          customers: 15
        },
        skills: ['销售谈判', 'CRM系统', '市场分析', '客户关系管理', '团队领导'],
        certifications: [
          {
            name: '高级销售管理师',
            date: '2019-05-20'
          },
          {
            name: '客户关系管理专业认证',
            date: '2018-11-15'
          }
        ],
        contract: {
          startDate: '2020-06-15',
          endDate: '2023-06-14',
          type: '固定期限合同'
        },
        salary: {
          base: 12000,
          socialSecurity: 8000,
          housingFund: 8000
        },
        records: [
          {
            title: '工资调整',
            description: '基本工资从10000元调整为12000元',
            time: '2022-01-01 09:30'
          },
          {
            title: '晋升',
            description: '职位从销售主管晋升为销售经理',
            time: '2021-07-15 14:20'
          },
          {
            title: '入职',
            description: '正式入职销售部，职位销售主管',
            time: '2020-06-15 09:00'
          }
        ]
      }
    }
  },
  onLoad(options) {
    if (options.id) {
      this.id = options.id;
      // 实际应用中，这里应该根据ID从服务器获取员工信息
      this.fetchEmployeeData(this.id);
    }
  },
  methods: {
    fetchEmployeeData(id) {
      // 实际项目中，这里应该调用API获取员工详细信息
      console.log('Fetching employee data for ID:', id);
      // 示例中使用的是静态数据，实际应用中应该替换为API调用
    },
    formatSalary(value) {
      return value.toLocaleString('zh-CN');
    },
    goBack() {
      uni.navigateBack();
    },
    editEmployee() {
      uni.navigateTo({
        url: `/pages/hr/employee-edit?id=${this.id}`
      });
    },
    callEmployee() {
      uni.makePhoneCall({
        phoneNumber: this.employee.phone,
        success: () => {
          console.log('拨打电话成功');
        },
        fail: (err) => {
          console.error('拨打电话失败', err);
        }
      });
    },
    messageEmployee() {
      // 短信功能实现
      uni.showToast({
        title: '短信功能暂未实现',
        icon: 'none'
      });
    },
    emailEmployee() {
      // 复制邮箱到剪贴板
      uni.setClipboardData({
        data: this.employee.email,
        success: () => {
          uni.showToast({
            title: '邮箱已复制',
            icon: 'success'
          });
        }
      });
    },
    viewPerformance() {
      uni.navigateTo({
        url: `/pages/hr/employee-performance?id=${this.id}`
      });
    },
    manageCertifications() {
      uni.navigateTo({
        url: `/pages/hr/employee-certifications?id=${this.id}`
      });
    },
    viewContract() {
      uni.navigateTo({
        url: `/pages/hr/employee-contract?id=${this.id}`
      });
    },
    viewAllRecords() {
      uni.navigateTo({
        url: `/pages/hr/employee-records?id=${this.id}`
      });
    }
  }
};
</script>

<style>
.container {
  padding-bottom: 50rpx;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  position: relative;
  border-bottom: 1rpx solid #eaeaea;
}

.back-button {
  font-size: 40rpx;
  color: #333;
  padding: 10rpx;
}

.page-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
}

.action-button {
  background: none;
  border: none;
  font-size: 40rpx;
  color: #666;
  padding: 10rpx;
}

.employee-card {
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.employee-header {
  display: flex;
  padding: 30rpx;
  border-bottom: 1rpx solid #eaeaea;
}

.employee-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  overflow: hidden;
  background-color: #eaeaea;
  margin-right: 20rpx;
}

.employee-avatar image {
  width: 100%;
  height: 100%;
}

.employee-main-info {
  flex: 1;
}

.employee-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.employee-name {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.employee-status {
  font-size: 24rpx;
  padding: 6rpx 15rpx;
  border-radius: 15rpx;
}

.status-active {
  background-color: #e6f7ed;
  color: #52c41a;
}

.status-inactive {
  background-color: #fff1f0;
  color: #ff4d4f;
}

.employee-position {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.employee-department {
  font-size: 26rpx;
  color: #999;
}

.employee-quick-contact {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
}

.contact-action {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.contact-icon {
  font-size: 40rpx;
  color: #4a6fff;
  margin-bottom: 8rpx;
}

.contact-label {
  font-size: 24rpx;
  color: #666;
}

.detail-section {
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eaeaea;
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.section-action {
  font-size: 26rpx;
  color: #4a6fff;
}

.info-list {
  padding: 15rpx 30rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
}

.performance-overview {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
}

.performance-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx 0;
}

.performance-value {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.performance-label {
  font-size: 24rpx;
  color: #999;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx 30rpx;
}

.skill-tag {
  background-color: #f0f2f5;
  color: #666;
  font-size: 24rpx;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  margin: 10rpx 15rpx 10rpx 0;
}

.cert-list {
  padding: 0 30rpx 20rpx;
}

.cert-item {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.cert-item:last-child {
  border-bottom: none;
}

.cert-icon {
  font-size: 36rpx;
  color: #4a6fff;
  margin-right: 15rpx;
}

.cert-detail {
  flex: 1;
}

.cert-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.cert-date {
  font-size: 24rpx;
  color: #999;
}

.timeline {
  padding: 20rpx 30rpx;
}

.timeline-item {
  position: relative;
  padding-left: 30rpx;
  margin-bottom: 30rpx;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: 0;
  top: 10rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #4a6fff;
}

.timeline-item:before {
  content: '';
  position: absolute;
  left: 7rpx;
  top: 30rpx;
  width: 2rpx;
  height: calc(100% + 20rpx);
  background-color: #e8e8e8;
}

.timeline-item:last-child:before {
  display: none;
}

.timeline-content {
  display: flex;
  flex-direction: column;
}

.timeline-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 5rpx;
}

.timeline-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.timeline-time {
  font-size: 24rpx;
  color: #999;
}
</style> 
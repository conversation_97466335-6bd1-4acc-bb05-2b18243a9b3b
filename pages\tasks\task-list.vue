<template>
  <view class="page-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <text class="page-title">任务</text>
      <view class="header-actions">
        <view class="header-icon" @tap="navigateTo('/pages/tasks/calendar')">
          <svg-icon name="calendar" type="svg" size="24"></svg-icon>
        </view>
        <view class="header-icon" @tap="navigateTo('/pages/tasks/reminders')">
          <svg-icon name="clock" type="svg" size="24"></svg-icon>
        </view>
      </view>
    </view>
    
    <!-- 筛选器 -->
    <scroll-view scroll-x class="filters">
      <view 
        v-for="(filter, index) in filters" 
        :key="index"
        class="filter"
        :class="{ active: currentFilter === filter.value }"
        @tap="setFilter(filter.value)"
      >
        {{filter.name}}
      </view>
    </scroll-view>
    
    <!-- 任务列表 -->
    <scroll-view scroll-y class="task-list-container">
      <!-- 任务组 -->
      <block v-for="(group, groupIndex) in filteredTasks" :key="groupIndex">
        <view class="tasks-group" v-if="group.tasks.length > 0">
          <view class="group-header">{{group.title}}</view>
          
          <!-- 任务卡片 -->
          <view 
            class="task-card" 
            v-for="(task, taskIndex) in group.tasks" 
            :key="taskIndex"
          >
            <view class="task-content" :class="{ completed: task.completed }">
              <view 
                class="task-checkbox" 
                :class="[task.priority, { completed: task.completed }]"
                @tap="toggleTaskStatus(task)"
              ></view>
              <view class="task-details">
                <view class="task-title">{{task.title}}</view>
                <view class="task-meta">
                  <view class="task-related">
                    <svg-icon name="clock" type="svg" size="24"></svg-icon>
                    <text>{{task.time}}</text>
                  </view>
                  <view class="task-related">
                    <svg-icon :name="getRelatedIconName(task)" type="svg" size="24"></svg-icon>
                    <text>{{task.related}}</text>
                  </view>
                </view>
              </view>
            </view>
            <view class="task-actions">
              <view class="task-action" @tap="viewTask(task.id)">
                <svg-icon name="eye" type="svg" size="20"></svg-icon>
                <text>查看</text>
              </view>
              <view class="task-action" @tap="editTask(task.id)">
                <svg-icon :name="task.completed ? 'delete' : 'edit'" type="svg" size="20"></svg-icon>
                <text>{{task.completed ? '删除' : '编辑'}}</text>
              </view>
              <view class="task-action" @tap="toggleReminder(task)">
                <svg-icon :name="task.completed ? 'refresh' : 'clock'" type="svg" size="20"></svg-icon>
                <text>{{task.completed ? '恢复' : '提醒'}}</text>
              </view>
            </view>
          </view>
        </view>
      </block>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="getTotalTasksCount() === 0">
        <svg-icon name="tasks" type="svg" size="96" class="empty-icon"></svg-icon>
        <text class="empty-text">暂无任务</text>
        <button class="btn-primary" @tap="createTask">添加任务</button>
      </view>
    </scroll-view>
    
    <!-- 悬浮添加按钮 -->
    <view class="add-fab" @tap="createTask">
      <svg-icon name="add" type="svg" size="48" color="#ffffff"></svg-icon>
    </view>
    
    <!-- 自定义TabBar组件 -->
    <custom-tab-bar></custom-tab-bar>
  </view>
</template>

<script>
import CustomTabBar from '@/components/CustomTabBar.vue';
import SvgIcon from '@/components/svg-icon.vue';

export default {
  components: {
    CustomTabBar,
    SvgIcon
  },
  data() {
    return {
      currentFilter: 'all',
      filters: [
        { name: '全部', value: 'all' },
        { name: '今日', value: 'today' },
        { name: '本周', value: 'week' },
        { name: '已完成', value: 'completed' },
        { name: '高优先级', value: 'high' }
      ],
      taskGroups: [
        {
          id: 'today',
          title: '今日 - ' + this.formatDate(new Date()),
          tasks: [
            {
              id: '1',
              title: '与王总进行项目方案讨论',
              time: '15:00',
              related: '创新科技有限公司',
              relatedType: 'company',
              priority: 'high',
              completed: false,
              date: new Date().toISOString().split('T')[0]
            },
            {
              id: '2',
              title: '回电广州未来科技公司',
              time: '16:30',
              related: '未来科技有限公司',
              relatedType: 'company',
              priority: 'medium',
              completed: false,
              date: new Date().toISOString().split('T')[0]
            },
            {
              id: '3',
              title: '完成月度销售报表',
              time: '17:00',
              related: '报表',
              relatedType: 'report',
              priority: 'low',
              completed: false,
              date: new Date().toISOString().split('T')[0]
            }
          ]
        },
        {
          id: 'tomorrow',
          title: '明日 - ' + this.formatDate(this.getTomorrow()),
          tasks: [
            {
              id: '4',
              title: '准备季度销售会议演示文稿',
              time: '10:00',
              related: '会议',
              relatedType: 'meeting',
              priority: 'high',
              completed: false,
              date: this.getTomorrow().toISOString().split('T')[0]
            },
            {
              id: '5',
              title: '跟进李四的报价单',
              time: '14:00',
              related: '未来科技有限公司',
              relatedType: 'company',
              priority: 'medium',
              completed: false,
              date: this.getTomorrow().toISOString().split('T')[0]
            }
          ]
        },
        {
          id: 'completed',
          title: '已完成',
          tasks: [
            {
              id: '6',
              title: '提交项目周报',
              time: '昨天 18:00',
              related: '报表',
              relatedType: 'report',
              priority: 'medium',
              completed: true,
              date: this.getYesterday().toISOString().split('T')[0]
            }
          ]
        }
      ]
    }
  },
  onLoad() {
    // 加载任务数据
    this.loadTasks();
  },
  onShow() {
    // 设置TabBar选中项
    if (typeof this.$refs.customTabBar !== 'undefined') {
      this.$refs.customTabBar.current = 3;
    }
  },
  computed: {
    filteredTasks() {
      if (this.currentFilter === 'all') {
        return this.taskGroups;
      } else if (this.currentFilter === 'today') {
        const today = new Date().toISOString().split('T')[0];
        
        return this.taskGroups.map(group => {
          const filteredTasks = group.tasks.filter(task => 
            !task.completed && task.date === today
          );
          
          return {
            ...group,
            tasks: filteredTasks
          };
        });
      } else if (this.currentFilter === 'week') {
        const today = new Date();
        const endOfWeek = new Date(today);
        endOfWeek.setDate(today.getDate() + (7 - today.getDay()));
        
        return this.taskGroups.map(group => {
          const filteredTasks = group.tasks.filter(task => {
            if (task.completed) return false;
            
            const taskDate = new Date(task.date);
            return taskDate >= today && taskDate <= endOfWeek;
          });
          
          return {
            ...group,
            tasks: filteredTasks
          };
        });
      } else if (this.currentFilter === 'completed') {
        return [{
          id: 'completed',
          title: '已完成',
          tasks: this.taskGroups.reduce((allTasks, group) => {
            return allTasks.concat(group.tasks.filter(task => task.completed));
          }, [])
        }];
      } else if (this.currentFilter === 'high') {
        return this.taskGroups.map(group => {
          const filteredTasks = group.tasks.filter(task => 
            !task.completed && task.priority === 'high'
          );
          
          return {
            ...group,
            tasks: filteredTasks
          };
        });
      }
      
      return this.taskGroups;
    }
  },
  methods: {
    navigateTo(url) {
      uni.navigateTo({ url });
    },
    
    setFilter(filter) {
      this.currentFilter = filter;
    },
    
    toggleTaskStatus(task) {
      task.completed = !task.completed;
      
      // 在实际应用中，这里应该调用API更新任务状态
      
      // 如果任务被标记为完成，移动到已完成组
      if (task.completed) {
        // 查找已完成组
        const completedGroup = this.taskGroups.find(group => group.id === 'completed');
        if (completedGroup) {
          // 仅用于演示，实际应用应该通过API处理
          setTimeout(() => {
            this.taskGroups.forEach(group => {
              if (group.id !== 'completed') {
                const index = group.tasks.findIndex(t => t.id === task.id);
                if (index !== -1) {
                  group.tasks.splice(index, 1);
                  completedGroup.tasks.unshift(task);
                }
              }
            });
          }, 500);
        }
      } else {
        // 如果任务被标记为未完成，从已完成组移回原来的组
        // 实际应用中应通过API处理
      }
    },
    
    viewTask(taskId) {
      uni.navigateTo({
        url: `/pages/tasks/task-detail?id=${taskId}`
      });
    },
    
    editTask(taskId) {
      uni.navigateTo({
        url: `/pages/tasks/task-detail?id=${taskId}&edit=true`
      });
    },
    
    toggleReminder(task) {
      if (task.completed) {
        // 恢复任务
        task.completed = false;
        // 在实际应用中，这里应该调用API更新任务状态
        uni.showToast({
          title: '任务已恢复',
          icon: 'success'
        });
      } else {
        // 设置提醒
        uni.showToast({
          title: '提醒功能开发中',
          icon: 'none'
        });
      }
    },
    
    createTask() {
      uni.navigateTo({
        url: '/pages/tasks/task-create'
      });
    },
    
    getRelatedIconName(task) {
      const iconMap = {
        'company': 'building',
        'report': 'file-list',
        'meeting': 'presentation',
        'customer': 'user',
        'opportunity': 'briefcase'
      };
      
      return iconMap[task.relatedType] || 'link';
    },
    
    formatDate(date) {
      const now = new Date();
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      
      // 如果是当前年份，只显示月日
      if (year === now.getFullYear()) {
        return `${month}月${day}日`;
      }
      
      return `${year}年${month}月${day}日`;
    },
    
    getTomorrow() {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      return tomorrow;
    },
    
    getYesterday() {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      return yesterday;
    },
    
    getTotalTasksCount() {
      return this.filteredTasks.reduce((count, group) => {
        return count + group.tasks.length;
      }, 0);
    },
    
    loadTasks() {
      // 此处应该从API加载任务数据
      console.log('加载任务数据');
    }
  }
}
</script>

<style>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #e0e0e0;
  background-color: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 30rpx;
}

.header-icon {
  color: #666;
  font-size: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filters {
  display: flex;
  padding: 20rpx;
  white-space: nowrap;
  background-color: white;
  border-bottom: 1rpx solid #e0e0e0;
  width: 100%;
  box-sizing: border-box;
}

.filter {
  display: inline-block;
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  border: 1rpx solid #e0e0e0;
  background-color: white;
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
}

.filter.active {
  background-color: #3370ff;
  color: white;
  border-color: #3370ff;
}

.task-list-container {
  flex: 1;
  padding: 20rpx;
  height: calc(100vh - 244rpx); /* 头部高度 + 筛选器高度 */
  width: 100%;
  box-sizing: border-box;
}

.tasks-group {
  margin-bottom: 40rpx;
  width: 100%;
}

.group-header {
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.task-card {
  background-color: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
  overflow: hidden;
  border: 1rpx solid #e0e0e0;
  width: 100%;
  box-sizing: border-box;
}

.task-content {
  padding: 20rpx;
  display: flex;
  align-items: flex-start;
  width: 100%;
  box-sizing: border-box;
}

.task-checkbox {
  margin-right: 30rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 4rpx solid #e0e0e0;
  flex-shrink: 0;
  position: relative;
}

.task-checkbox.high {
  border-color: #ff4d4f;
}

.task-checkbox.medium {
  border-color: #faad14;
}

.task-checkbox.low {
  border-color: #3370ff;
}

.task-checkbox.completed {
  background-color: #52c41a;
  border-color: #52c41a;
}

.task-checkbox.completed::after {
  content: '';
  position: absolute;
  top: 40%;
  left: 50%;
  width: 12rpx;
  height: 20rpx;
  border: solid white;
  border-width: 0 4rpx 4rpx 0;
  transform: translate(-50%, -50%) rotate(45deg);
}

.task-details {
  flex: 1;
  min-width: 0; /* 确保flex子项不会超出父容器 */
  overflow: hidden;
}

.task-title {
  font-size: 32rpx;
  margin-bottom: 10rpx;
  font-weight: 500;
  color: #333;
  white-space: normal;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.completed .task-title {
  text-decoration: line-through;
  color: #666;
}

.task-meta {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
  width: 100%;
}

.task-related {
  display: flex;
  align-items: center;
  max-width: 50%;
  overflow: hidden;
}

.task-related text {
  margin-left: 10rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.task-actions {
  display: flex;
  border-top: 1rpx solid #e0e0e0;
  width: 100%;
}

.task-action {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  color: #666;
  font-size: 28rpx;
}

.task-action:not(:last-child) {
  border-right: 1rpx solid #e0e0e0;
}

.task-action text {
  margin-left: 10rpx;
}

.add-fab {
  position: fixed;
  right: 40rpx;
  bottom: 160rpx;
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  background-color: #3370ff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  z-index: 10;
}

.add-fab:active {
  opacity: 0.9;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  color: #ccc;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.btn-primary {
  background-color: #3370ff;
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 16rpx;
  border: none;
  font-size: 28rpx;
}
</style> 
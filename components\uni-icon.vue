<template>
  <text :class="['remix-icon', 'ri-' + type, customClass]" :style="iconStyle"></text>
</template>

<script>
export default {
  name: 'UniIcon',
  props: {
    type: {
      type: String,
      default: ''
    },
    color: {
      type: String,
      default: ''
    },
    size: {
      type: [Number, String],
      default: 24
    },
    customClass: {
      type: String,
      default: ''
    }
  },
  computed: {
    iconStyle() {
      let style = {};
      if (this.color) {
        style.color = this.color;
      }
      if (this.size) {
        style.fontSize = `${this.size}rpx`;
      }
      return style;
    }
  }
};
</script>

<style>
.remix-icon {
  font-family: 'remixicon' !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style> 
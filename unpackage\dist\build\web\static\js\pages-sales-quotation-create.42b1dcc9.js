(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-sales-quotation-create"],{"04dc":function(t,e,a){"use strict";a.r(e);var i=a("b83e"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=o.a},1880:function(t,e,a){"use strict";a.r(e);var i=a("1a37"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=o.a},"1a37":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4626"),a("5ac7"),a("bf0f"),a("bd06");var i={name:"CustomTabBar",data:function(){return{current:0,color:"#333333",activeColor:"#007AFF",showMoreMenu:!1,tabList:[{pagePath:"/pages/dashboard/main-dashboard",text:"首页",iconPath:"dashboard",selectedIconPath:"dashboard"},{pagePath:"/pages/customers/customer-list",text:"客户",iconPath:"customer",selectedIconPath:"customer"},{pagePath:"/pages/sales/opportunity-list",text:"销售",iconPath:"sales",selectedIconPath:"sales"},{type:"more",text:"更多",iconPath:"more",selectedIconPath:"more"},{pagePath:"/pages/settings/profile",text:"我的",iconPath:"user",selectedIconPath:"user"}],moreMenuList:[{pagePath:"/pages/marketing/leads",text:"线索",iconPath:"lead"},{pagePath:"/pages/interactions/interaction-list",text:"沟通",iconPath:"communication"},{pagePath:"/pages/sales/quotation-list",text:"报价",iconPath:"quotation"},{pagePath:"/pages/contracts/contract-list",text:"合同",iconPath:"contract"},{pagePath:"/pages/contracts/invoice-list",text:"发票",iconPath:"file-text"},{pagePath:"/pages/contracts/payment-list",text:"收款",iconPath:"money"},{pagePath:"/pages/reports/report-list",text:"报表",iconPath:"report"}]}},created:function(){this.updateCurrentTab()},onLoad:function(){this.updateCurrentTab()},onShow:function(){var t=this;setTimeout((function(){t.updateCurrentTab()}),100)},methods:{updateCurrentTab:function(){try{var t=getCurrentPages(),e=t[t.length-1];if(!e||!e.route)return;var a=e.route;console.log("当前路由:",a),a.includes("/pages/dashboard/")?this.current=0:a.includes("/pages/customers/")?this.current=1:a.includes("/pages/sales/")?this.current=2:a.includes("/pages/actions/")?this.current=3:a.includes("/pages/settings/")&&(this.current=5)}catch(i){console.error("更新Tab出错:",i)}},handleTabClick:function(t,e){"more"===t.type?(this.toggleMoreMenu(),this.current=e):this.switchTab(t.pagePath,e)},switchTab:function(t,e){this.current!==e&&(this.current=e,uni.switchTab({url:t}))},toggleMoreMenu:function(){this.showMoreMenu=!this.showMoreMenu},closeMoreMenu:function(){this.showMoreMenu=!1},navigateToPage:function(t){var e=this.tabList.some((function(e){return e.pagePath===t}));if(e){uni.switchTab({url:t});var a=this.tabList.findIndex((function(e){return e.pagePath===t}));-1!==a&&(this.current=a)}else uni.navigateTo({url:t});this.closeMoreMenu()}},watch:{$route:{handler:function(){this.updateCurrentTab()},immediate:!0}}};e.default=i},"2f74":function(t,e,a){var i=a("ed09");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("3e1dc6b6",i,!0,{sourceMap:!1,shadowMode:!1})},"66de":function(t,e,a){"use strict";a.r(e);var i=a("b59b"),o=a("04dc");for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(n);a("8376");var s=a("828b"),r=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"71822488",null,!1,i["a"],void 0);e["default"]=r.exports},7775:function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){return i}));var i={svgIcon:a("8a0f").default},o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"custom-tab-bar"},[t._l(t.tabList,(function(e,i){return a("v-uni-view",{key:i,staticClass:"tab-item",class:{active:t.current===i},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.handleTabClick(e,i)}}},[a("svg-icon",{attrs:{name:t.current===i?e.selectedIconPath:e.iconPath,type:"svg",size:24,color:t.current===i?t.activeColor:t.color}}),a("v-uni-text",{staticClass:"tab-text",class:{"active-text":t.current===i}},[t._v(t._s(e.text))])],1)})),t.showMoreMenu?a("v-uni-view",{staticClass:"more-menu"},[a("v-uni-view",{staticClass:"menu-overlay",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeMoreMenu.apply(void 0,arguments)}}}),a("v-uni-view",{staticClass:"menu-content"},[a("v-uni-view",{staticClass:"menu-header"},[a("v-uni-text",{staticClass:"menu-title"},[t._v("更多功能")]),a("v-uni-view",{staticClass:"menu-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeMoreMenu.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"close",type:"svg",size:32,color:"#666"}})],1)],1),a("v-uni-view",{staticClass:"menu-list"},t._l(t.moreMenuList,(function(e,i){return a("v-uni-view",{key:i,staticClass:"menu-item",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.navigateToPage(e.pagePath)}}},[a("svg-icon",{attrs:{name:e.iconPath,type:"svg",size:24,color:"#333333"}}),a("v-uni-text",{staticClass:"menu-item-text"},[t._v(t._s(e.text))])],1)})),1)],1)],1):t._e()],2)},n=[]},8376:function(t,e,a){"use strict";var i=a("2f74"),o=a.n(i);o.a},b59b:function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){return i}));var i={svgIcon:a("8a0f").default},o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"container"},[a("v-uni-view",{staticClass:"page-header"},[a("v-uni-view",{staticClass:"back-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"arrow-left",type:"svg",size:"24"}})],1),a("v-uni-text",{staticClass:"page-title"},[t._v("创建报价单")]),a("v-uni-view",{staticClass:"header-actions"},[a("v-uni-button",{staticClass:"save-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveQuotation.apply(void 0,arguments)}}},[t._v("保存")])],1)],1),a("v-uni-scroll-view",{staticClass:"form-scroll",attrs:{"scroll-y":!0}},[a("v-uni-view",{staticClass:"form-section"},[a("v-uni-text",{staticClass:"section-title"},[t._v("基本信息")]),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("报价单名称"),a("v-uni-text",{staticClass:"required"},[t._v("*")])],1),a("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入报价单名称"},model:{value:t.quotation.title,callback:function(e){t.$set(t.quotation,"title",e)},expression:"quotation.title"}})],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("报价单编号")]),a("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"自动生成",disabled:!0},model:{value:t.quotation.code,callback:function(e){t.$set(t.quotation,"code",e)},expression:"quotation.code"}})],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("有效期至"),a("v-uni-text",{staticClass:"required"},[t._v("*")])],1),a("v-uni-view",{staticClass:"date-picker"},[a("v-uni-picker",{staticClass:"form-picker",attrs:{mode:"date",value:t.quotation.validUntil},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onValidUntilChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-value"},[t._v(t._s(t.quotation.validUntil||"请选择日期"))])],1),a("svg-icon",{attrs:{name:"calendar",type:"svg",size:"20"}})],1)],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("负责人"),a("v-uni-text",{staticClass:"required"},[t._v("*")])],1),a("v-uni-picker",{staticClass:"form-picker",attrs:{mode:"selector",range:t.owners.map((function(t){return t.name}))},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onOwnerChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-value"},[t._v(t._s(t.selectedOwner?t.selectedOwner.name:"请选择负责人"))])],1)],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("状态")]),a("v-uni-picker",{staticClass:"form-picker",attrs:{mode:"selector",range:t.statuses.map((function(t){return t.name}))},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onStatusChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-value"},[t._v(t._s(t.selectedStatus?t.selectedStatus.name:"请选择状态"))])],1)],1)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-text",{staticClass:"section-title"},[t._v("客户信息")]),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("客户"),a("v-uni-text",{staticClass:"required"},[t._v("*")])],1),a("v-uni-view",{staticClass:"customer-select",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectCustomer.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"customer-name"},[t._v(t._s(t.selectedCustomer&&t.selectedCustomer.name||"请选择客户"))]),a("svg-icon",{attrs:{name:"arrow-right",type:"svg",size:"18"}})],1)],1),t.selectedCustomer?a("v-uni-view",{staticClass:"customer-details"},[a("v-uni-view",{staticClass:"customer-info-item"},[a("v-uni-text",{staticClass:"info-label"},[t._v("联系人")]),a("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.selectedCustomer.contact||"未指定"))])],1),a("v-uni-view",{staticClass:"customer-info-item"},[a("v-uni-text",{staticClass:"info-label"},[t._v("电话")]),a("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.selectedCustomer.phone||"未提供"))])],1),a("v-uni-view",{staticClass:"customer-info-item"},[a("v-uni-text",{staticClass:"info-label"},[t._v("邮箱")]),a("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.selectedCustomer.email||"未提供"))])],1)],1):t._e(),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("关联商机")]),a("v-uni-view",{staticClass:"opportunity-select",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectOpportunity.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"opportunity-name"},[t._v(t._s(t.selectedOpportunity&&t.selectedOpportunity.name||"选择关联商机（可选）"))]),a("svg-icon",{attrs:{name:"arrow-right",type:"svg",size:"18"}})],1)],1)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-header"},[a("v-uni-text",{staticClass:"section-title"},[t._v("产品与服务")]),a("v-uni-button",{staticClass:"add-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.addProduct.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"add",type:"svg",size:"18"}}),a("v-uni-text",[t._v("添加产品")])],1)],1),0===t.quotation.items.length?a("v-uni-view",{staticClass:"empty-products"},[a("svg-icon",{attrs:{name:"shopping",type:"svg",size:"36"}}),a("v-uni-text",{staticClass:"empty-text"},[t._v("暂无产品")]),a("v-uni-text",{staticClass:"empty-desc"},[t._v('点击"添加产品"按钮添加产品或服务项目')])],1):a("v-uni-view",{staticClass:"product-list"},[t._l(t.quotation.items,(function(e,i){return a("v-uni-view",{key:i,staticClass:"product-item"},[a("v-uni-view",{staticClass:"product-header"},[a("v-uni-text",{staticClass:"product-name"},[t._v(t._s(e.name))]),a("v-uni-view",{staticClass:"product-actions"},[a("v-uni-view",{staticClass:"action-icon",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.editProduct(i)}}},[a("svg-icon",{attrs:{name:"edit",type:"svg",size:"18"}})],1),a("v-uni-view",{staticClass:"action-icon",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.removeProduct(i)}}},[a("svg-icon",{attrs:{name:"delete-bin",type:"svg",size:"18"}})],1)],1)],1),a("v-uni-view",{staticClass:"product-details"},[a("v-uni-view",{staticClass:"product-price"},[t._v("¥"+t._s(t.formatPrice(e.amount)))]),e.description?a("v-uni-view",{staticClass:"product-description"},[t._v(t._s(e.description))]):t._e()],1)],1)})),a("v-uni-view",{staticClass:"product-total"},[a("v-uni-text",{staticClass:"total-label"},[t._v("总计")]),a("v-uni-text",{staticClass:"total-amount"},[t._v("¥"+t._s(t.formatPrice(t.calculateTotal())))])],1)],2)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-text",{staticClass:"section-title"},[t._v("备注说明")]),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-textarea",{staticClass:"form-textarea",attrs:{placeholder:"请输入备注信息"},model:{value:t.quotation.remark,callback:function(e){t.$set(t.quotation,"remark",e)},expression:"quotation.remark"}})],1)],1)],1),a("custom-tab-bar",{ref:"customTabBar"}),a("v-uni-view",{staticClass:"submit-bar"},[a("v-uni-button",{staticClass:"save-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveQuotation.apply(void 0,arguments)}}},[t._v("保存")])],1)],1)},n=[]},b83e:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("473f"),a("bf0f"),a("aa9c"),a("01a2"),a("e39c"),a("dd2b"),a("0c26");var o=i(a("9b1b")),n=i(a("eab4")),s=i(a("8a0f")),r={components:{CustomTabBar:n.default,SvgIcon:s.default},data:function(){return{quotation:{title:"",code:"自动生成",validUntil:this.getDefaultValidUntil(),owner:"",status:"draft",customer:"",opportunity:"",items:[],remark:""},selectedCustomer:null,selectedOpportunity:null,selectedOwner:null,selectedStatus:null,owners:[{id:"1",name:"李销售"},{id:"2",name:"王经理"},{id:"3",name:"张总监"}],statuses:[{code:"draft",name:"草稿"},{code:"sent",name:"已发送"},{code:"accepted",name:"已接受"},{code:"rejected",name:"已拒绝"}]}},created:function(){this.selectedOwner=this.owners[0],this.quotation.owner=this.owners[0].id,this.selectedStatus=this.statuses[0],this.quotation.status=this.statuses[0].code},onLoad:function(t){var e=this;t.opportunityId&&t.opportunityName&&(this.selectedOpportunity={id:t.opportunityId,name:decodeURIComponent(t.opportunityName)},this.quotation.opportunity=t.opportunityId,this.quotation.title=decodeURIComponent(t.opportunityName)+" - 报价方案",t.customerId&&t.customerName&&(this.selectedCustomer={id:t.customerId,name:decodeURIComponent(t.customerName)},this.quotation.customer=t.customerId,setTimeout((function(){e.selectedCustomer=(0,o.default)((0,o.default)({},e.selectedCustomer),{},{contact:"联系人",phone:"13800138000",email:"<EMAIL>"})}),300)))},methods:{formatPrice:function(t){return t.toLocaleString("zh-CN")},getDefaultValidUntil:function(){var t=new Date;return t.setDate(t.getDate()+30),t.toISOString().split("T")[0]},calculateTotal:function(){return this.quotation.items.reduce((function(t,e){return t+e.amount}),0)},goBack:function(){uni.navigateBack()},onValidUntilChange:function(t){this.quotation.validUntil=t.detail.value},onOwnerChange:function(t){var e=t.detail.value;this.selectedOwner=this.owners[e],this.quotation.owner=this.owners[e].id},onStatusChange:function(t){var e=t.detail.value;this.selectedStatus=this.statuses[e],this.quotation.status=this.statuses[e].code},selectCustomer:function(){var t=this;uni.navigateTo({url:"/pages/sales/customer-select?mode=select",events:{selectCustomer:function(e){t.selectedCustomer=e,t.quotation.customer=e.id}}})},selectOpportunity:function(){var t=this;this.selectedCustomer?uni.navigateTo({url:"/pages/sales/opportunity-select?customerId=".concat(this.selectedCustomer.id),events:{selectOpportunity:function(e){t.selectedOpportunity=e,t.quotation.opportunity=e.id}}}):uni.showToast({title:"请先选择客户",icon:"none"})},addProduct:function(){var t=this;uni.navigateTo({url:"/pages/sales/product-select?mode=select",events:{selectProduct:function(e){t.quotation.items.push({id:e.id,name:e.name,amount:e.price,description:e.description})}}})},editProduct:function(t){var e=this,a=this.quotation.items[t];uni.navigateTo({url:"/pages/sales/product-edit?mode=edit&index=".concat(t),events:{updateProduct:function(t,a){e.quotation.items[a]=t}},success:function(e){e.eventChannel.emit("productData",{product:a,index:t})}})},removeProduct:function(t){var e=this;uni.showModal({title:"确认删除",content:"确定要删除该产品吗？",success:function(a){a.confirm&&e.quotation.items.splice(t,1)}})},validateForm:function(){return this.quotation.title.trim()?this.quotation.validUntil?this.selectedCustomer?0!==this.quotation.items.length||(uni.showToast({title:"请至少添加一个产品或服务",icon:"none"}),!1):(uni.showToast({title:"请选择客户",icon:"none"}),!1):(uni.showToast({title:"请选择有效期",icon:"none"}),!1):(uni.showToast({title:"请输入报价单名称",icon:"none"}),!1)},saveQuotation:function(){this.validateForm()&&(uni.showLoading({title:"保存中..."}),setTimeout((function(){uni.hideLoading(),uni.showToast({title:"保存成功",icon:"success",success:function(){setTimeout((function(){uni.navigateBack()}),1500)}})}),1e3))}},onShow:function(){"undefined"!==typeof this.$refs.customTabBar&&(this.$refs.customTabBar.current=2)}};e.default=r},bf69:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,".custom-tab-bar[data-v-6a709636]{display:flex;justify-content:space-around;align-items:center;background-color:#fff;box-shadow:0 -1px 5px rgba(0,0,0,.1);height:%?100?%;position:fixed;bottom:0;left:0;right:0;z-index:999;padding-bottom:env(safe-area-inset-bottom)}.tab-item[data-v-6a709636]{display:flex;flex-direction:column;align-items:center;justify-content:center;flex:1;height:100%;padding:%?10?% 0}.tab-text[data-v-6a709636]{font-size:%?22?%;color:#333;margin-top:%?4?%}.active-text[data-v-6a709636]{color:#007aff}\n\n/* 更多菜单样式 */.more-menu[data-v-6a709636]{position:fixed;top:0;right:0;bottom:0;left:0;z-index:1000}.menu-overlay[data-v-6a709636]{position:absolute;top:0;right:0;bottom:0;left:0;background-color:rgba(0,0,0,.5)}.menu-content[data-v-6a709636]{position:absolute;bottom:%?100?%;left:0;right:0;background-color:#fff;border-top-left-radius:%?20?%;border-top-right-radius:%?20?%;overflow:hidden;-webkit-animation:slideUp-data-v-6a709636 .3s ease;animation:slideUp-data-v-6a709636 .3s ease;box-shadow:0 -2px 10px rgba(0,0,0,.1)}.menu-header[data-v-6a709636]{display:flex;justify-content:space-between;align-items:center;padding:%?30?%;border-bottom:1px solid #f0f0f0}.menu-title[data-v-6a709636]{font-size:%?32?%;font-weight:500;color:#333}.menu-close[data-v-6a709636]{padding:%?10?%}.menu-list[data-v-6a709636]{display:flex;flex-wrap:wrap;padding:%?20?%}.menu-item[data-v-6a709636]{width:25%;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?20?% 0}.menu-item-text[data-v-6a709636]{font-size:%?24?%;color:#333;margin-top:%?10?%;text-align:center}@-webkit-keyframes slideUp-data-v-6a709636{from{-webkit-transform:translateY(100%);transform:translateY(100%)}to{-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes slideUp-data-v-6a709636{from{-webkit-transform:translateY(100%);transform:translateY(100%)}to{-webkit-transform:translateY(0);transform:translateY(0)}}",""]),t.exports=e},c9cf:function(t,e,a){"use strict";var i=a("e9f7"),o=a.n(i);o.a},e9f7:function(t,e,a){var i=a("bf69");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("9e21a296",i,!0,{sourceMap:!1,shadowMode:!1})},eab4:function(t,e,a){"use strict";a.r(e);var i=a("7775"),o=a("1880");for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(n);a("c9cf");var s=a("828b"),r=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"6a709636",null,!1,i["a"],void 0);e["default"]=r.exports},ed09:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,".container[data-v-71822488]{display:flex;flex-direction:column;height:100vh;background-color:#f5f5f5;position:relative}.page-header[data-v-71822488]{display:flex;align-items:center;justify-content:space-between;padding:0 16px;height:44px;background-color:#fff;border-bottom:1px solid #eee;width:100%;box-sizing:border-box}.page-title[data-v-71822488]{font-size:18px;font-weight:700;color:#333}.back-button[data-v-71822488]{color:#666;display:flex;align-items:center}.save-button[data-v-71822488]{background-color:#3a86ff;color:#fff;padding:6px 12px;border-radius:6px;font-size:14px;border:none}.form-scroll[data-v-71822488]{flex:1;overflow-y:auto;overflow-x:hidden;-webkit-overflow-scrolling:touch;padding-bottom:100px}.form-section[data-v-71822488]{margin:16px;padding:12px;background-color:#fff;border-radius:8px;position:relative}.section-title[data-v-71822488]{font-size:16px;font-weight:600;margin-bottom:16px;color:#333}.section-header[data-v-71822488]{display:flex;justify-content:space-between;align-items:center;margin-bottom:16px}.add-button[data-v-71822488]{display:flex;align-items:center;gap:4px;background-color:#f5f5f5;border:1px solid #ddd;padding:6px 12px;border-radius:6px;font-size:14px;color:#333}.form-group[data-v-71822488]{margin-bottom:16px}.form-label[data-v-71822488]{font-size:14px;margin-bottom:8px;display:block;color:#333}.required[data-v-71822488]{color:#dc2626}.form-input[data-v-71822488]{width:100%;box-sizing:border-box;padding:10px 12px;border:1px solid #ddd;border-radius:8px;font-size:14px;color:#333;background-color:#fff}.form-textarea[data-v-71822488]{width:100%;box-sizing:border-box;padding:10px 12px;border:1px solid #ddd;border-radius:8px;font-size:14px;color:#333;height:120px;background-color:#fff}.date-picker[data-v-71822488]{position:relative;display:flex;align-items:center}.form-picker[data-v-71822488]{width:100%;box-sizing:border-box;padding:10px 12px;border:1px solid #ddd;border-radius:8px;font-size:14px;color:#333;position:relative;background-color:#fff}.picker-value[data-v-71822488]{padding-right:24px;color:#333}.date-picker svg-icon[data-v-71822488]{position:absolute;right:12px;color:#666;pointer-events:none}.customer-select[data-v-71822488], .opportunity-select[data-v-71822488]{width:100%;box-sizing:border-box;padding:10px 12px;border:1px solid #ddd;border-radius:8px;font-size:14px;color:#333;display:flex;justify-content:space-between;align-items:center;background-color:#fff}.customer-name[data-v-71822488], .opportunity-name[data-v-71822488]{color:#333}.customer-details[data-v-71822488]{background-color:#f9f9f9;padding:12px;border-radius:8px;margin:8px 0 16px;border:1px solid #eee}.customer-info-item[data-v-71822488]{margin-bottom:8px;display:flex;flex-direction:column}.customer-info-item[data-v-71822488]:last-child{margin-bottom:0}.info-label[data-v-71822488]{font-size:12px;color:#666;margin-bottom:4px}.info-value[data-v-71822488]{font-size:14px;color:#333}.empty-products[data-v-71822488]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:32px 0;color:#999}.empty-text[data-v-71822488]{font-size:16px;margin:12px 0 8px}.empty-desc[data-v-71822488]{font-size:14px;color:#999}.product-list[data-v-71822488]{margin:16px 0}.product-item[data-v-71822488]{margin-bottom:12px;padding:12px;background-color:#fff;border-radius:6px;box-shadow:0 1px 3px rgba(0,0,0,.1)}.product-header[data-v-71822488]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:8px}.product-name[data-v-71822488]{font-size:15px;font-weight:500;color:#333}.product-actions[data-v-71822488]{display:flex;gap:8px}.action-icon[data-v-71822488]{width:28px;height:28px;display:flex;align-items:center;justify-content:center;color:#666}.product-details[data-v-71822488]{display:flex;flex-direction:column}.product-price[data-v-71822488]{font-size:15px;font-weight:600;color:#333;margin-bottom:4px}.product-description[data-v-71822488]{font-size:13px;color:#666;line-height:1.5}.product-total[data-v-71822488]{display:flex;justify-content:space-between;padding:12px;margin-top:8px;background-color:#f5f5f5;border-radius:8px;border:1px solid #eee}.total-label[data-v-71822488]{font-size:16px;font-weight:600;color:#333}.total-amount[data-v-71822488]{font-size:16px;font-weight:600;color:#333}.submit-bar[data-v-71822488]{position:fixed;bottom:0;left:0;right:0;background-color:#fff;padding:10px 16px;box-shadow:0 -2px 4px rgba(0,0,0,.1);z-index:100}",""]),t.exports=e}}]);
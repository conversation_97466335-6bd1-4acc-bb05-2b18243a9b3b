(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-hr-employee-performance"],{20794:function(e,t,a){"use strict";var i=a("d55d"),c=a.n(i);c.a},"3b90":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return c})),a.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"container"},[a("v-uni-view",{staticClass:"page-header"},[a("v-uni-view",{staticClass:"back-button",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.goBack.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-arrow-left-line"})],1),a("v-uni-text",{staticClass:"page-title"},[e._v("员工绩效")]),a("v-uni-view",{staticClass:"header-actions"},[a("v-uni-button",{staticClass:"action-button",attrs:{type:"button"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.exportPerformance.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-download-line"})],1)],1)],1),a("v-uni-view",{staticClass:"employee-card"},[a("v-uni-view",{staticClass:"employee-info"},[a("v-uni-view",{staticClass:"employee-avatar"},[a("v-uni-image",{attrs:{src:e.employee.avatar||"/static/images/default-avatar.png",mode:"aspectFill"}})],1),a("v-uni-view",{staticClass:"employee-details"},[a("v-uni-text",{staticClass:"employee-name"},[e._v(e._s(e.employee.name))]),a("v-uni-text",{staticClass:"employee-position"},[e._v(e._s(e.employee.department)+" | "+e._s(e.employee.position))]),a("v-uni-text",{staticClass:"employee-id"},[e._v("工号: "+e._s(e.employee.employeeId))])],1)],1)],1),a("v-uni-view",{staticClass:"filter-bar"},[a("v-uni-text",{staticClass:"current-period"},[e._v(e._s(e.currentPeriod))]),a("v-uni-view",{staticClass:"period-selector"},[a("v-uni-text",{staticClass:"period-arrow",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changePeriod("prev")}}},[a("v-uni-text",{staticClass:"ri-arrow-left-s-line"})],1),a("v-uni-picker",{staticClass:"date-picker",attrs:{mode:"date",fields:"month",value:e.selectedDate},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.onDateChange.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"period-text"},[e._v(e._s(e.selectedDate.substring(0,7)))])],1),a("v-uni-text",{staticClass:"period-arrow",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changePeriod("next")}}},[a("v-uni-text",{staticClass:"ri-arrow-right-s-line"})],1)],1)],1),a("v-uni-view",{staticClass:"performance-overview"},[a("v-uni-view",{staticClass:"overview-header"},[a("v-uni-text",{staticClass:"overview-title"},[e._v("绩效概览")]),a("v-uni-text",{staticClass:"overview-rating",class:"rating-"+e.employee.performanceRating},[e._v(e._s(e.ratingLabels[e.employee.performanceRating]))])],1),a("v-uni-view",{staticClass:"rating-progress"},[a("v-uni-view",{staticClass:"rating-bar"},[a("v-uni-view",{staticClass:"rating-fill",style:{width:e.ratingPercentage+"%"}})],1),a("v-uni-view",{staticClass:"rating-labels"},[a("v-uni-text",[e._v("不合格")]),a("v-uni-text",[e._v("待改进")]),a("v-uni-text",[e._v("良好")]),a("v-uni-text",[e._v("优秀")]),a("v-uni-text",[e._v("卓越")])],1)],1)],1),a("v-uni-view",{staticClass:"metrics-section"},[a("v-uni-view",{staticClass:"section-header"},[a("v-uni-text",{staticClass:"section-title"},[e._v("关键业绩指标 (KPI)")])],1),a("v-uni-view",{staticClass:"metrics-list"},e._l(e.performanceData.kpis,(function(t,i){return a("v-uni-view",{key:i,staticClass:"metric-item"},[a("v-uni-view",{staticClass:"metric-header"},[a("v-uni-view",{staticClass:"metric-title-area"},[a("v-uni-text",{staticClass:"metric-title"},[e._v(e._s(t.name))]),a("v-uni-text",{staticClass:"metric-subtitle"},[e._v(e._s(t.description))])],1),a("v-uni-view",{staticClass:"metric-value-area"},[a("v-uni-text",{staticClass:"metric-value"},[e._v(e._s(e.formatMetricValue(t)))]),a("v-uni-text",{class:["metric-trend","trend-"+t.trend]},[a("v-uni-text",{class:"up"===t.trend?"ri-arrow-up-s-line":"down"===t.trend?"ri-arrow-down-s-line":"ri-subtract-line"}),e._v(e._s(t.changePercentage)+"%")],1)],1)],1),a("v-uni-view",{staticClass:"metric-progress"},[a("v-uni-view",{staticClass:"progress-bar"},[a("v-uni-view",{staticClass:"progress-fill",class:"fill-"+e.getProgressLevel(t.achievementPercentage),style:{width:t.achievementPercentage+"%"}})],1),a("v-uni-view",{staticClass:"progress-labels"},[a("v-uni-text",[e._v(e._s(t.actual)+"/"+e._s(t.target))]),a("v-uni-text",[e._v(e._s(t.achievementPercentage)+"%")])],1)],1)],1)})),1)],1),a("v-uni-view",{staticClass:"skills-section"},[a("v-uni-view",{staticClass:"section-header"},[a("v-uni-text",{staticClass:"section-title"},[e._v("能力评估")])],1),a("v-uni-view",{staticClass:"skills-radar-chart"},[a("v-uni-view",{staticClass:"radar-placeholder"},[a("v-uni-image",{attrs:{src:"/static/images/radar-chart-placeholder.png",mode:"aspectFit"}})],1),a("v-uni-view",{staticClass:"skills-legend"},e._l(e.performanceData.skills,(function(t,i){return a("v-uni-view",{key:i,staticClass:"legend-item"},[a("v-uni-view",{staticClass:"legend-color",style:{backgroundColor:e.skillColors[i%e.skillColors.length]}}),a("v-uni-text",{staticClass:"legend-text"},[e._v(e._s(t.name)+" ("+e._s(t.score)+"/5)")])],1)})),1)],1)],1),a("v-uni-view",{staticClass:"evaluation-section"},[a("v-uni-view",{staticClass:"section-header"},[a("v-uni-text",{staticClass:"section-title"},[e._v("绩效评价")])],1),a("v-uni-view",{staticClass:"evaluation-content"},[a("v-uni-view",{staticClass:"eval-group"},[a("v-uni-text",{staticClass:"eval-label"},[e._v("主管评价")]),a("v-uni-text",{staticClass:"eval-value"},[e._v(e._s(e.performanceData.evaluation.managerComment))])],1),a("v-uni-view",{staticClass:"eval-group"},[a("v-uni-text",{staticClass:"eval-label"},[e._v("优势")]),a("v-uni-view",{staticClass:"eval-list"},e._l(e.performanceData.evaluation.strengths,(function(t,i){return a("v-uni-text",{key:i,staticClass:"eval-list-item"},[e._v("• "+e._s(t))])})),1)],1),a("v-uni-view",{staticClass:"eval-group"},[a("v-uni-text",{staticClass:"eval-label"},[e._v("改进建议")]),a("v-uni-view",{staticClass:"eval-list"},e._l(e.performanceData.evaluation.improvements,(function(t,i){return a("v-uni-text",{key:i,staticClass:"eval-list-item"},[e._v("• "+e._s(t))])})),1)],1)],1)],1),a("v-uni-view",{staticClass:"history-section"},[a("v-uni-view",{staticClass:"section-header"},[a("v-uni-text",{staticClass:"section-title"},[e._v("绩效历史")]),a("v-uni-text",{staticClass:"section-action",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.viewPerformanceHistory.apply(void 0,arguments)}}},[e._v("查看全部")])],1),a("v-uni-view",{staticClass:"history-chart"},[a("v-uni-view",{staticClass:"chart-placeholder"},[a("v-uni-image",{attrs:{src:"/static/images/performance-chart-placeholder.png",mode:"aspectFit"}})],1)],1)],1)],1)},c=[]},"9f97":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,".container[data-v-f83cced8]{background-color:#f5f7fa;min-height:100vh;padding-bottom:%?30?%}.page-header[data-v-f83cced8]{display:flex;align-items:center;padding:%?20?% %?30?%;background-color:#fff;position:relative;border-bottom:%?1?% solid #eaeaea}.back-button[data-v-f83cced8]{font-size:%?40?%;color:#333;padding:%?10?%}.page-title[data-v-f83cced8]{flex:1;text-align:center;font-size:%?36?%;font-weight:500;color:#333}.header-actions[data-v-f83cced8]{display:flex;align-items:center}.action-button[data-v-f83cced8]{background:none;border:none;font-size:%?40?%;color:#666;padding:%?10?%}.employee-card[data-v-f83cced8]{margin:%?20?% %?30?%;border-radius:%?20?%;background-color:#fff;box-shadow:0 %?2?% %?10?% rgba(0,0,0,.05);padding:%?30?%}.employee-info[data-v-f83cced8]{display:flex;align-items:center}.employee-avatar[data-v-f83cced8]{width:%?100?%;height:%?100?%;border-radius:50%;overflow:hidden;margin-right:%?20?%}.employee-avatar uni-image[data-v-f83cced8]{width:100%;height:100%}.employee-details[data-v-f83cced8]{flex:1}.employee-name[data-v-f83cced8]{font-size:%?32?%;font-weight:500;color:#333;margin-bottom:%?5?%}.employee-position[data-v-f83cced8]{font-size:%?26?%;color:#666;margin-bottom:%?5?%}.employee-id[data-v-f83cced8]{font-size:%?24?%;color:#999}.filter-bar[data-v-f83cced8]{display:flex;justify-content:space-between;align-items:center;margin:%?20?% %?30?%;padding:%?20?%;border-radius:%?20?%;background-color:#fff;box-shadow:0 %?2?% %?10?% rgba(0,0,0,.05)}.current-period[data-v-f83cced8]{font-size:%?30?%;font-weight:500;color:#333}.period-selector[data-v-f83cced8]{display:flex;align-items:center}.period-arrow[data-v-f83cced8]{font-size:%?40?%;color:#666;padding:0 %?10?%}.period-text[data-v-f83cced8]{font-size:%?28?%;color:#4a6fff;padding:0 %?10?%}.date-picker[data-v-f83cced8]{display:inline-block}.performance-overview[data-v-f83cced8], .metrics-section[data-v-f83cced8], .skills-section[data-v-f83cced8], .evaluation-section[data-v-f83cced8], .history-section[data-v-f83cced8]{margin:%?20?% %?30?%;border-radius:%?20?%;background-color:#fff;box-shadow:0 %?2?% %?10?% rgba(0,0,0,.05);overflow:hidden}.overview-header[data-v-f83cced8]{display:flex;justify-content:space-between;align-items:center;padding:%?30?%;border-bottom:%?1?% solid #eaeaea}.overview-title[data-v-f83cced8]{font-size:%?30?%;font-weight:500;color:#333}.overview-rating[data-v-f83cced8]{font-size:%?28?%;font-weight:500;padding:%?6?% %?20?%;border-radius:%?30?%}.rating-1[data-v-f83cced8]{background-color:#fff1f0;color:#ff4d4f}.rating-2[data-v-f83cced8]{background-color:#fff7e6;color:#fa8c16}.rating-3[data-v-f83cced8]{background-color:#e6f7ff;color:#1890ff}.rating-4[data-v-f83cced8]{background-color:#f6ffed;color:#52c41a}.rating-5[data-v-f83cced8]{background-color:#f9f0ff;color:#722ed1}.rating-progress[data-v-f83cced8]{padding:%?30?%}.rating-bar[data-v-f83cced8]{height:%?16?%;background-color:#f0f0f0;border-radius:%?8?%;margin-bottom:%?10?%;overflow:hidden}.rating-fill[data-v-f83cced8]{height:100%;background-color:#4a6fff;border-radius:%?8?%}.rating-labels[data-v-f83cced8]{display:flex;justify-content:space-between;font-size:%?22?%;color:#999}.section-header[data-v-f83cced8]{display:flex;justify-content:space-between;align-items:center;padding:%?20?% %?30?%;border-bottom:%?1?% solid #eaeaea}.section-title[data-v-f83cced8]{font-size:%?30?%;font-weight:500;color:#333}.section-action[data-v-f83cced8]{font-size:%?26?%;color:#4a6fff}.metrics-list[data-v-f83cced8]{padding:0 %?30?%}.metric-item[data-v-f83cced8]{padding:%?20?% 0;border-bottom:%?1?% solid #f0f0f0}.metric-item[data-v-f83cced8]:last-child{border-bottom:none}.metric-header[data-v-f83cced8]{display:flex;justify-content:space-between;margin-bottom:%?15?%}.metric-title[data-v-f83cced8]{font-size:%?28?%;font-weight:500;color:#333;margin-bottom:%?5?%}.metric-subtitle[data-v-f83cced8]{font-size:%?24?%;color:#999}.metric-value[data-v-f83cced8]{font-size:%?30?%;font-weight:500;color:#333;text-align:right;margin-bottom:%?5?%}.metric-trend[data-v-f83cced8]{font-size:%?24?%;text-align:right}.trend-up[data-v-f83cced8]{color:#52c41a}.trend-down[data-v-f83cced8]{color:#ff4d4f}.trend-flat[data-v-f83cced8]{color:#999}.metric-progress[data-v-f83cced8]{margin-top:%?10?%}.progress-bar[data-v-f83cced8]{height:%?12?%;background-color:#f0f0f0;border-radius:%?6?%;margin-bottom:%?8?%;overflow:hidden}.progress-fill[data-v-f83cced8]{height:100%;border-radius:%?6?%}.fill-excellent[data-v-f83cced8]{background-color:#52c41a}.fill-good[data-v-f83cced8]{background-color:#1890ff}.fill-medium[data-v-f83cced8]{background-color:#fa8c16}.fill-poor[data-v-f83cced8]{background-color:#ff4d4f}.progress-labels[data-v-f83cced8]{display:flex;justify-content:space-between;font-size:%?22?%;color:#999}.skills-radar-chart[data-v-f83cced8]{padding:%?30?%}.radar-placeholder[data-v-f83cced8], .chart-placeholder[data-v-f83cced8]{width:100%;height:%?400?%;display:flex;justify-content:center;align-items:center;margin-bottom:%?20?%}.radar-placeholder uni-image[data-v-f83cced8], .chart-placeholder uni-image[data-v-f83cced8]{width:100%;height:100%}.skills-legend[data-v-f83cced8]{display:flex;flex-wrap:wrap;margin-top:%?20?%}.legend-item[data-v-f83cced8]{display:flex;align-items:center;margin-right:%?30?%;margin-bottom:%?10?%}.legend-color[data-v-f83cced8]{width:%?20?%;height:%?20?%;border-radius:%?4?%;margin-right:%?10?%}.legend-text[data-v-f83cced8]{font-size:%?24?%;color:#666}.evaluation-content[data-v-f83cced8]{padding:%?30?%}.eval-group[data-v-f83cced8]{margin-bottom:%?20?%}.eval-group[data-v-f83cced8]:last-child{margin-bottom:0}.eval-label[data-v-f83cced8]{font-size:%?28?%;font-weight:500;color:#333;margin-bottom:%?10?%;display:block}.eval-value[data-v-f83cced8]{font-size:%?28?%;color:#666;line-height:1.5}.eval-list[data-v-f83cced8]{margin-top:%?10?%}.eval-list-item[data-v-f83cced8]{font-size:%?28?%;color:#666;line-height:1.5;display:block;margin-bottom:%?10?%}.history-chart[data-v-f83cced8]{padding:%?30?%}",""]),e.exports=t},ce4c:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("c223"),a("795c"),a("c9b5"),a("bf0f"),a("ab80");var i={data:function(){return{id:"",employee:{id:"1",name:"张三",department:"销售部",position:"销售经理",employeeId:"EMP001",avatar:"/static/images/avatars/avatar1.png",performanceRating:4},selectedDate:"2023-04-01",currentPeriod:"2023年04月绩效",ratingLabels:{1:"不合格",2:"待改进",3:"良好",4:"优秀",5:"卓越"},performanceData:{kpis:[{name:"销售业绩",description:"月度销售额（元）",target:5e5,actual:62e4,achievementPercentage:124,trend:"up",changePercentage:15,unit:"元"},{name:"新客户开发",description:"月度新增客户数",target:10,actual:12,achievementPercentage:120,trend:"up",changePercentage:20,unit:"个"},{name:"客户满意度",description:"客户反馈评分",target:4.5,actual:4.8,achievementPercentage:107,trend:"up",changePercentage:5,unit:"分"},{name:"项目完成率",description:"按时完成项目比例",target:100,actual:95,achievementPercentage:95,trend:"down",changePercentage:2,unit:"%"}],skills:[{name:"销售能力",score:4.5},{name:"沟通技巧",score:4.8},{name:"团队协作",score:4},{name:"问题解决",score:4.2},{name:"客户关系",score:4.6}],evaluation:{managerComment:"张三是一位优秀的销售经理，他在本季度展现了出色的销售业绩和团队领导能力。他能够快速响应客户需求，并有效管理销售团队，提高整体绩效。",strengths:["出色的沟通能力，能够有效地与客户建立信任关系","良好的团队领导能力，激励团队成员达成销售目标","对产品知识有深入了解，能够精准满足客户需求"],improvements:["可以更加注重长期客户关系维护，提高客户留存率","进一步提高项目管理能力，确保所有项目按时完成","分享成功经验，帮助新团队成员更快成长"]}},skillColors:["#4a6fff","#ff9500","#00c48c","#ff4d4f","#7870ff"]}},computed:{ratingPercentage:function(){return this.employee.performanceRating/5*100}},onLoad:function(e){e.id&&(this.id=e.id,this.fetchPerformanceData(this.id))},methods:{fetchPerformanceData:function(e){console.log("Fetching performance data for employee ID:",e)},goBack:function(){uni.navigateBack()},onDateChange:function(e){this.selectedDate=e.detail.value;var t=new Date(this.selectedDate),a=t.getFullYear(),i=t.getMonth()+1;this.currentPeriod="".concat(a,"年").concat(i.toString().padStart(2,"0"),"月绩效"),this.fetchPerformanceDataByPeriod(this.id,this.selectedDate)},changePeriod:function(e){var t=new Date(this.selectedDate);"prev"===e?t.setMonth(t.getMonth()-1):t.setMonth(t.getMonth()+1);var a=t.getFullYear(),i=t.getMonth()+1;this.selectedDate="".concat(a,"-").concat(i.toString().padStart(2,"0"),"-01"),this.currentPeriod="".concat(a,"年").concat(i.toString().padStart(2,"0"),"月绩效"),this.fetchPerformanceDataByPeriod(this.id,this.selectedDate)},fetchPerformanceDataByPeriod:function(e,t){console.log("Fetching performance data for employee ".concat(e," in period ").concat(t))},formatMetricValue:function(e){return"元"===e.unit?"¥".concat(e.actual.toLocaleString("zh-CN")):"%"===e.unit||"分"===e.unit?"".concat(e.actual).concat(e.unit):"".concat(e.actual," ").concat(e.unit)},getProgressLevel:function(e){return e>=100?"excellent":e>=80?"good":e>=60?"medium":"poor"},viewPerformanceHistory:function(){uni.navigateTo({url:"/pages/hr/employee-performance-history?id=".concat(this.id)})},exportPerformance:function(){uni.showToast({title:"绩效报告导出中",icon:"loading",duration:2e3,success:function(){setTimeout((function(){uni.showToast({title:"导出成功",icon:"success",duration:1500})}),2e3)}})}}};t.default=i},d55d:function(e,t,a){var i=a("9f97");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var c=a("967d").default;c("4d70aca2",i,!0,{sourceMap:!1,shadowMode:!1})},ec54:function(e,t,a){"use strict";a.r(t);var i=a("ce4c"),c=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=c.a},ee6f:function(e,t,a){"use strict";a.r(t);var i=a("3b90"),c=a("ec54");for(var n in c)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return c[e]}))}(n);a("20794");var o=a("828b"),r=Object(o["a"])(c["default"],i["b"],i["c"],!1,null,"f83cced8",null,!1,i["a"],void 0);t["default"]=r.exports}}]);
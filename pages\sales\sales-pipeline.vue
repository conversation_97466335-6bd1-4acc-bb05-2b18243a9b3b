<template>
  <view class="pipeline-container">
    <view class="page-header">
      <view class="back-button" @click="navigateBack">
        <text class="ri-arrow-left-line"></text>
      </view>
      <text class="page-title">销售漏斗</text>
      <view class="header-actions">
        <view class="action-button" @click="refreshData">
          <text class="ri-refresh-line"></text>
        </view>
        <view class="action-button" @click="showMoreActions">
          <text class="ri-more-2-fill"></text>
        </view>
      </view>
    </view>

    <view class="date-filter">
      <view class="date-selector" @click="showDatePicker">
        <text class="ri-calendar-line"></text>
        <text>{{currentDate}}</text>
        <text class="ri-arrow-down-s-line"></text>
      </view>
      <view class="filter-button" @click="showFilterOptions">
        <text class="ri-filter-3-line"></text>
        <text>筛选</text>
      </view>
    </view>

    <view class="summary-card">
      <view class="summary-header">
        <text class="summary-title">总金额</text>
        <text class="summary-amount">¥{{totalAmount}}</text>
      </view>
      <view class="summary-row">
        <text class="summary-label">商机数量</text>
        <text class="summary-value">{{totalOpportunities}}</text>
      </view>
      <view class="summary-row">
        <text class="summary-label">平均交易金额</text>
        <text class="summary-value">¥{{averageAmount}}</text>
      </view>
      <view class="summary-row">
        <text class="summary-label">预计成交金额</text>
        <text class="summary-value">¥{{expectedAmount}}</text>
      </view>
    </view>

    <view class="funnel-container">
      <view class="funnel-header">
        <text class="funnel-title">销售漏斗</text>
        <view class="view-toggle">
          <view 
            class="toggle-option" 
            :class="{active: viewMode === 'amount'}" 
            @click="viewMode = 'amount'"
          >
            金额
          </view>
          <view 
            class="toggle-option" 
            :class="{active: viewMode === 'count'}" 
            @click="viewMode = 'count'"
          >
            数量
          </view>
        </view>
      </view>
      
      <view class="funnel-chart">
        <view 
          v-for="(stage, index) in stages" 
          :key="index"
          class="funnel-stage" 
          :class="'stage-' + stage.key"
          :style="{width: stage.percentage + '%'}"
        >
          {{stage.name}} - {{viewMode === 'amount' ? '¥' + stage.amount : stage.count + '个'}}
        </view>
      </view>
      
      <view class="funnel-legend">
        <view class="legend-item" v-for="(stage, index) in stages" :key="index">
          <view class="legend-label">
            <view class="legend-color" :class="stage.key"></view>
            <text>{{stage.name}}</text>
          </view>
          <view class="legend-stats">
            <text class="legend-value">¥{{stage.amount}}</text>
            <text class="legend-count">{{stage.count}}个</text>
          </view>
        </view>
      </view>
    </view>

    <view class="conversion-rates">
      <text class="conversion-title">阶段转化率</text>
      <view class="conversion-item" v-for="(rate, index) in conversionRates" :key="index">
        <text class="conversion-label">{{rate.label}}</text>
        <text class="conversion-value">{{rate.value}}%</text>
      </view>
    </view>

    <view class="stage-list">
      <view class="stage-card" v-for="(stage, stageIndex) in visibleStages" :key="stageIndex">
        <view class="stage-header">
          <view class="stage-name">
            <view class="stage-indicator" :style="{backgroundColor: stage.color}"></view>
            <text>{{stage.name}}</text>
          </view>
          <text class="stage-count">{{stage.count}}个商机</text>
        </view>
        <view 
          class="opportunity-pill" 
          v-for="(opportunity, oppIndex) in stage.opportunities" 
          :key="oppIndex"
          @click="navigateToOpportunity(opportunity.id)"
        >
          <text class="pill-name">{{opportunity.name}}</text>
          <text class="pill-amount">¥{{opportunity.amount}}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentDate: '2023年10月',
      viewMode: 'amount', // 'amount' 或 'count'
      totalAmount: '2,450,000',
      totalOpportunities: 28,
      averageAmount: '87,500',
      expectedAmount: '973,500',
      stages: [
        {
          key: 'initial',
          name: '初步接触',
          amount: '2,450,000',
          count: 8,
          percentage: 100,
          color: '#4f46e5',
          opportunities: [
            { id: '1', name: '广州贸易有限公司', amount: '450,000' },
            { id: '2', name: '上海智能科技', amount: '380,000' },
            { id: '3', name: '成都教育咨询', amount: '320,000' }
          ]
        },
        {
          key: 'qualifying',
          name: '需求确认',
          amount: '2,080,000',
          count: 7,
          percentage: 85,
          color: '#2563eb',
          opportunities: [
            { id: '4', name: '深圳网络科技', amount: '520,000' },
            { id: '5', name: '杭州文化传媒', amount: '420,000' },
            { id: '6', name: '南京信息技术', amount: '380,000' }
          ]
        },
        {
          key: 'proposal',
          name: '提案/建议',
          amount: '1,590,000',
          count: 6,
          percentage: 65,
          color: '#7c3aed',
          opportunities: [
            { id: '7', name: '北京科技有限公司', amount: '480,000' },
            { id: '8', name: '重庆工业设备', amount: '380,000' },
            { id: '9', name: '武汉数据服务', amount: '320,000' }
          ]
        },
        {
          key: 'negotiation',
          name: '谈判/审核',
          amount: '980,000',
          count: 4,
          percentage: 40,
          color: '#db2777',
          opportunities: [
            { id: '10', name: '天津贸易公司', amount: '350,000' },
            { id: '11', name: '西安科技企业', amount: '280,000' }
          ]
        },
        {
          key: 'closed-won',
          name: '赢单',
          amount: '612,000',
          count: 3,
          percentage: 25,
          color: '#059669',
          opportunities: [
            { id: '12', name: '青岛制造企业', amount: '220,000' },
            { id: '13', name: '厦门电商公司', amount: '180,000' }
          ]
        }
      ],
      conversionRates: [
        { label: '初步接触 → 需求确认', value: 87.5 },
        { label: '需求确认 → 提案/建议', value: 85.7 },
        { label: '提案/建议 → 谈判/审核', value: 66.7 },
        { label: '谈判/审核 → 赢单', value: 75.0 },
        { label: '整体转化率 (初步接触 → 赢单)', value: 37.5 }
      ]
    }
  },
  computed: {
    visibleStages() {
      // 返回最多展示两个阶段的商机
      return this.stages.slice(0, 2);
    }
  },
  methods: {
    navigateBack() {
      uni.navigateBack();
    },
    refreshData() {
      uni.showToast({
        title: '数据刷新中...',
        icon: 'loading',
        duration: 2000
      });
      
      // 模拟数据刷新
      setTimeout(() => {
        uni.showToast({
          title: '数据已刷新',
          icon: 'success'
        });
      }, 1000);
    },
    showMoreActions() {
      uni.showActionSheet({
        itemList: ['导出数据', '调整漏斗阶段', '查看历史数据'],
        success: (res) => {
          uni.showToast({
            title: '功能开发中...',
            icon: 'none'
          });
        }
      });
    },
    showDatePicker() {
      // 实际开发中可以使用日期选择器组件
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    showFilterOptions() {
      uni.showToast({
        title: '筛选功能开发中',
        icon: 'none'
      });
    },
    navigateToOpportunity(id) {
      uni.navigateTo({
        url: `/pages/sales/opportunity-detail?id=${id}`
      });
    }
  }
}
</script>

<style>
.pipeline-container {
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;
  background-color: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

.page-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.back-button {
  color: #666;
  font-size: 24px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.action-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #666;
  background-color: #f5f7fa;
  border: 1px solid #e0e0e0;
}

.date-filter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
}

.date-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #333;
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f5f7fa;
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #666;
}

.summary-card {
  margin: 15px 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 15px;
  border: 1px solid #e0e0e0;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.summary-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.summary-amount {
  font-size: 20px;
  font-weight: bold;
  color: #3370ff;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 5px;
  font-size: 14px;
}

.summary-label {
  color: #666;
}

.summary-value {
  color: #333;
  font-weight: 500;
}

.funnel-container {
  margin: 15px 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 15px;
  border: 1px solid #e0e0e0;
}

.funnel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.funnel-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.view-toggle {
  display: flex;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.toggle-option {
  padding: 4px 8px;
  font-size: 12px;
  background-color: #f5f7fa;
  color: #666;
}

.toggle-option.active {
  background-color: #3370ff;
  color: white;
}

.funnel-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1px;
  margin-bottom: 15px;
}

.funnel-stage {
  width: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 1px;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 10px;
  box-sizing: border-box;
}

.stage-initial {
  background-color: #4f46e5;
  border-radius: 8px 8px 0 0;
}

.stage-qualifying {
  background-color: #2563eb;
}

.stage-proposal {
  background-color: #7c3aed;
}

.stage-negotiation {
  background-color: #db2777;
}

.stage-closed-won {
  background-color: #059669;
  border-radius: 0 0 8px 8px;
}

.funnel-legend {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 15px;
}

.legend-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.legend-label {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #333;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.initial {
  background-color: #4f46e5;
}

.legend-color.qualifying {
  background-color: #2563eb;
}

.legend-color.proposal {
  background-color: #7c3aed;
}

.legend-color.negotiation {
  background-color: #db2777;
}

.legend-color.closed-won {
  background-color: #059669;
}

.legend-stats {
  display: flex;
  gap: 15px;
  font-size: 14px;
}

.legend-value {
  color: #333;
  font-weight: 500;
}

.legend-count {
  color: #666;
}

.conversion-rates {
  margin: 15px 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 15px;
  border: 1px solid #e0e0e0;
}

.conversion-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.conversion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.conversion-label {
  font-size: 14px;
  color: #666;
}

.conversion-value {
  font-size: 14px;
  font-weight: 500;
  color: #3370ff;
}

.stage-list {
  margin: 15px 20px;
}

.stage-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 15px;
  border: 1px solid #e0e0e0;
  margin-bottom: 15px;
}

.stage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.stage-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 5px;
}

.stage-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.stage-count {
  font-size: 14px;
  color: #666;
}

.opportunity-pill {
  background-color: #f5f7fa;
  border-radius: 20px;
  padding: 8px 12px;
  margin-bottom: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.pill-name {
  color: #333;
  font-weight: 500;
}

.pill-amount {
  color: #666;
}
</style> 
import { request } from "@/utils/request";

// 获取客户列表
export const getCustomerList = (data) => {
  return request({
    url: "/api/crm/custom/getList",
    method: "POST",
    data: data,
  });
};

// 获取客户详情
export const getCustomerDetail = (id) => {
  return request({
    url: "/api/crm/custom/getCustomById?id=" + id,
    method: "GET",
  });
};

// 创建客户
export const createCustomer = (data) => {
  return request({
    url: "/api/crm/custom/create",
    method: "POST",
    data: data,
  });
};

// 通过id获取客户数据
export const getCustomerById = (id) => {
  return request({
    url: "/api/crm/custom/getCustomById?id=" + id,
    method: "GET",
  });
};

// 更新客户数据
export const updateCustomerById = (id, data) => {
  return request({
    url: "/api/crm/custom/update?id=" + id,
    method: "POST",
    data: data,
  });
};

// 获取责任人
export const getOwnerList = (data) => {
  return request({
    url: "/api/Users/<USER>",
    method: "POST",
    data: data,
  });
};

// 删除客户
export const deleteCustomer = (id) => {
  return request({
    url: "/api/crm/custom/delete?id=" + id,
    method: "POST",
  });
};

// 获取组织下的所有用户
export const getOrganizeUsers = () => {
  return request({
    url: "/api/tms/taskTemplate/getOrganizeUsers",
    method: "GET",
  });
};

// 创建任务
export const TmsTaskCreate = (data) => {
  return request({
    url: "/api/tms/taskItem/create",
    method: "POST",
    data: data,
  });
};

// 创建任务结束时间
export const TmsTaskTimeEnd = (id, newDate) => {
  return request({
    url: `/api/tms/taskItem/editPlanDoneDate?id=${id}&newDate=${newDate}`,
    method: "POST",
  });
};

// 更新描述
export const updateTaskDescription = (id, description) => {
  return request({
    url: "/api/tms/taskItem/updateDesc?id=" + id,
    method: "POST",
    data: description,
  });
};

// 获取任务详情
export const getTaskDetail = (data) => {
  return request({
    url: "/api/tms/taskItem/getList",
    method: "POST",
    data: data,
  });
};

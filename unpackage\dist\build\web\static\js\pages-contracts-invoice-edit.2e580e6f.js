(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-contracts-invoice-edit"],{"30f7":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},a("7a76"),a("c9b5")},4733:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if(Array.isArray(t))return(0,i.default)(t)};var i=function(t){return t&&t.__esModule?t:{default:t}}(a("8d0b"))},"4a48":function(t,e,a){"use strict";var i=a("9e58"),n=a.n(i);n.a},"50c9":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'.invoice-edit-container[data-v-1792fe70]{display:flex;flex-direction:column;height:100vh;background-color:#f5f7fa}.page-header[data-v-1792fe70]{display:flex;align-items:center;padding:0 15px;height:56px;background-color:#fff;border-bottom:1px solid #eaeaea;position:relative;z-index:10}.nav-back[data-v-1792fe70]{font-size:24px;line-height:24px;margin-right:10px;color:#333}.page-title[data-v-1792fe70]{flex:1;font-size:18px;font-weight:500;color:#333}.header-actions[data-v-1792fe70]{display:flex}.action-button[data-v-1792fe70]{width:36px;height:36px;display:flex;align-items:center;justify-content:center;border-radius:50%;margin-left:8px;font-size:20px;color:#666}.page-content[data-v-1792fe70]{flex:1;overflow-y:auto;padding-bottom:120px}.form-section[data-v-1792fe70]{background-color:#fff;margin:10px;border-radius:8px;padding:15px;box-shadow:0 1px 3px rgba(0,0,0,.05)}.section-title[data-v-1792fe70]{font-size:16px;font-weight:500;margin-bottom:15px;color:#333;position:relative;padding-left:12px}.section-title[data-v-1792fe70]::before{content:"";position:absolute;left:0;top:2px;bottom:2px;width:4px;background-color:#3b7ff3;border-radius:2px}.form-group[data-v-1792fe70]{margin-bottom:15px}.form-label[data-v-1792fe70]{font-size:14px;color:#666;margin-bottom:8px;display:block}.required[data-v-1792fe70]:after{content:"*";color:#f56c6c;margin-left:4px}.form-input[data-v-1792fe70], .form-picker[data-v-1792fe70], .date-picker[data-v-1792fe70]{width:100%;height:40px;padding:0 12px;border:1px solid #dcdee2;border-radius:4px;background-color:#fff;font-size:14px;color:#333}.form-textarea[data-v-1792fe70]{width:100%;min-height:80px;padding:8px 12px;border:1px solid #dcdee2;border-radius:4px;background-color:#fff;font-size:14px;color:#333}.date-value[data-v-1792fe70], .picker-value[data-v-1792fe70]{height:40px;line-height:40px;font-size:14px;color:#333}.placeholder[data-v-1792fe70]{color:#999}.has-error .form-input[data-v-1792fe70],\n.has-error .form-picker[data-v-1792fe70],\n.form-input.has-error[data-v-1792fe70]{border-color:#f56c6c}.error-message[data-v-1792fe70]{color:#f56c6c;font-size:12px;margin-top:4px}.form-hint[data-v-1792fe70]{font-size:12px;color:#999;margin-top:4px}.status-badge[data-v-1792fe70]{display:inline-block;padding:2px 8px;border-radius:10px;font-size:12px;background-color:#e8e8e8;color:#666}.status-draft[data-v-1792fe70]{background-color:#e8f4ff;color:#3b7ff3}.status-sent[data-v-1792fe70]{background-color:#ecf5ff;color:#409eff}.status-partial[data-v-1792fe70]{background-color:#fdf6ec;color:#e6a23c}.status-paid[data-v-1792fe70]{background-color:#f0f9eb;color:#67c23a}.status-overdue[data-v-1792fe70]{background-color:#fef0f0;color:#f56c6c}.status-cancelled[data-v-1792fe70]{background-color:#f4f4f5;color:#909399}.customer-info[data-v-1792fe70], .contract-info[data-v-1792fe70]{display:flex;flex-direction:column}.customer-name[data-v-1792fe70], .contract-name[data-v-1792fe70]{font-size:14px}\n\n/* 发票明细样式 */.item-row[data-v-1792fe70]{border:1px solid #eaeaea;border-radius:6px;margin-bottom:10px;background-color:#fcfcfc}.item-header[data-v-1792fe70]{display:flex;justify-content:space-between;align-items:center;padding:8px 12px;border-bottom:1px solid #eaeaea;background-color:#f9f9f9;border-radius:6px 6px 0 0}.item-title[data-v-1792fe70]{font-size:14px;font-weight:500;color:#666}.item-delete[data-v-1792fe70]{width:28px;height:28px;display:flex;align-items:center;justify-content:center;color:#f56c6c;font-size:18px}.item-fields[data-v-1792fe70]{padding:12px;display:flex;flex-direction:column;gap:8px}.currency-input[data-v-1792fe70]{position:relative}.currency-symbol[data-v-1792fe70]{position:absolute;left:12px;top:11px;font-size:14px;color:#666}.item-price[data-v-1792fe70]{padding-left:24px}.add-item-button[data-v-1792fe70]{display:flex;align-items:center;justify-content:center;width:100%;height:40px;background-color:#f0f4ff;border:1px dashed #a0c0ff;border-radius:4px;color:#3b7ff3;font-size:14px;margin:15px 0}.add-item-button uni-text[data-v-1792fe70]{margin-right:4px}.tax-summary[data-v-1792fe70]{border-top:1px solid #eaeaea;padding-top:15px;margin-top:10px}.tax-row[data-v-1792fe70]{display:flex;justify-content:space-between;align-items:center;margin-bottom:8px}.tax-label[data-v-1792fe70]{font-size:14px;color:#666}.tax-value[data-v-1792fe70]{font-size:14px;color:#333;font-weight:500}.tax-picker[data-v-1792fe70]{width:100px;height:30px;text-align:right}.total-row[data-v-1792fe70]{margin-top:12px;padding-top:12px;border-top:1px solid #eaeaea}.total-row .tax-label[data-v-1792fe70],\n.total-row .tax-value[data-v-1792fe70]{font-size:16px;font-weight:600}.float-actions[data-v-1792fe70]{position:fixed;left:0;right:0;bottom:0;display:flex;height:70px;background-color:#fff;border-top:1px solid #eaeaea;padding:10px 15px;box-shadow:0 -2px 10px rgba(0,0,0,.05);z-index:100}.action-btn[data-v-1792fe70]{flex:1;height:45px;border-radius:6px;display:flex;align-items:center;justify-content:center;font-size:15px;font-weight:500;margin:0 6px}.action-btn uni-text[data-v-1792fe70]:first-child{margin-right:4px;font-size:18px}.secondary-action[data-v-1792fe70]{background-color:#f2f2f2;color:#666}.primary-action[data-v-1792fe70]{background-color:#3b7ff3;color:#fff}',""]),t.exports=e},"656f":function(t,e,a){"use strict";a.r(e);var i=a("e8a3"),n=a("e775");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("4a48");var s=a("828b"),r=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"1792fe70",null,!1,i["a"],void 0);e["default"]=r.exports},"9e58":function(t,e,a){var i=a("50c9");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("9e70572e",i,!0,{sourceMap:!1,shadowMode:!1})},b7c7:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,i.default)(t)||(0,n.default)(t)||(0,o.default)(t)||(0,s.default)()};var i=r(a("4733")),n=r(a("d14d")),o=r(a("5d6b")),s=r(a("30f7"));function r(t){return t&&t.__esModule?t:{default:t}}},c602:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("b7c7"));a("473f"),a("bf0f"),a("e838"),a("bd06"),a("795c"),a("c223"),a("5c47"),a("a1c1"),a("fd3c"),a("aa9c"),a("dd2b"),a("2797"),a("01a2"),a("e39c");var o={data:function(){return{invoice:{id:null,invoiceNumber:"",invoiceDate:this.formatDateForPicker(new Date),dueDate:this.formatDateForPicker(this.getDefaultDueDate()),type:"VAT",typeIndex:0,status:"draft",statusIndex:0,customerId:null,taxNumber:"",contractId:null,notes:"请在收到发票后30天内付款，谢谢合作。",items:[{id:1,description:"系统集成服务",quantity:1,price:25e3,errors:{}},{id:2,description:"软件授权费用",quantity:5,price:2e3,errors:{}}]},invoiceTypes:["增值税专用发票","增值税普通发票","电子发票"],invoiceStatuses:["草稿","已发送","部分支付","已支付","逾期","已取消"],customers:[{id:1,name:"上海科技有限公司",taxNumber:"91310000MA1FL4CT3X"},{id:2,name:"北京智能科技有限公司",taxNumber:"91110000X09YGHTZ7"},{id:3,name:"广州数字科技有限公司",taxNumber:"91440000MA5CL4RX9B"},{id:4,name:"成都创新科技有限公司",taxNumber:"91510100MA6DGEJK2X"}],customerIndex:0,contracts:[{id:1,name:"CRM系统实施合同"},{id:2,name:"数据分析平台合同"},{id:3,name:"软件维护服务合同"}],contractIndex:0,taxRates:[.13,.09,.06,.03,0],taxRateIndex:0,errors:{customer:!1}}},computed:{selectedCustomer:function(){return this.customerIndex>=0?this.customers[this.customerIndex]:null},selectedContract:function(){return this.contractIndex>=0?this.contracts[this.contractIndex]:null},subtotal:function(){return this.invoice.items.reduce((function(t,e){return t+(parseFloat(e.price)||0)*(parseFloat(e.quantity)||0)}),0)},taxAmount:function(){return this.subtotal*this.taxRates[this.taxRateIndex]},total:function(){return this.subtotal+this.taxAmount}},onLoad:function(t){t.id&&this.loadInvoiceData(t.id),t.contractId&&this.setContractById(t.contractId)},methods:{loadInvoiceData:function(t){console.log("加载发票ID:",t),this.setCustomerById(this.invoice.customerId),this.setContractById(this.invoice.contractId)},setCustomerById:function(t){if(t){var e=this.customers.findIndex((function(e){return e.id==t}));-1!==e&&(this.customerIndex=e,this.invoice.taxNumber=this.customers[e].taxNumber)}},setContractById:function(t){if(t){var e=this.contracts.findIndex((function(e){return e.id==t}));-1!==e&&(this.contractIndex=e,this.invoice.contractId=t)}},formatDateForPicker:function(t){var e=new Date(t),a=e.getFullYear(),i=String(e.getMonth()+1).padStart(2,"0"),n=String(e.getDate()).padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(n)},getDefaultDueDate:function(){var t=new Date;return t.setDate(t.getDate()+30),t},formatDate:function(t){if(!t)return"";var e=t.split("-");return"".concat(e[0],"年").concat(e[1],"月").concat(e[2],"日")},formatMoney:function(t){return t.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,",")},getStatusClass:function(t){return{draft:"status-draft",sent:"status-sent",partial:"status-partial",paid:"status-paid",overdue:"status-overdue",cancelled:"status-cancelled"}[t]||""},getTaxRateLabel:function(t){var e=this.taxRates[t];return e>0?"".concat((100*e).toFixed(0),"%"):"免税"},onInvoiceDateChange:function(t){this.invoice.invoiceDate=t.detail.value},onDueDateChange:function(t){this.invoice.dueDate=t.detail.value},onTypeChange:function(t){this.invoice.typeIndex=t.detail.value;this.invoice.type=["VAT","normal","electronic"][t.detail.value]||"VAT"},onStatusChange:function(t){this.invoice.statusIndex=t.detail.value;this.invoice.status=["draft","sent","partial","paid","overdue","cancelled"][t.detail.value]||"draft"},onCustomerChange:function(t){if(this.customerIndex=t.detail.value,this.customerIndex>=0){var e=this.customers[this.customerIndex];this.invoice.customerId=e.id,this.invoice.taxNumber=e.taxNumber,this.errors.customer=!1}},onContractChange:function(t){this.contractIndex=t.detail.value,this.contractIndex>=0&&(this.invoice.contractId=this.contracts[this.contractIndex].id)},onTaxRateChange:function(t){this.taxRateIndex=t.detail.value},addItem:function(){var t=Math.max.apply(Math,(0,n.default)(this.invoice.items.map((function(t){return t.id}))).concat([0]))+1;this.invoice.items.push({id:t,description:"",quantity:1,price:0,errors:{}})},deleteItem:function(t){this.invoice.items.length>1?this.invoice.items.splice(t,1):uni.showToast({title:"至少需要保留一个明细条目",icon:"none"})},validateForm:function(){var t=!0;return this.invoice.customerId?this.errors.customer=!1:(this.errors.customer=!0,t=!1),this.invoice.items.forEach((function(e){e.errors={},e.description||(e.errors.description=!0,t=!1),(!e.price||parseFloat(e.price)<=0)&&(e.errors.price=!0,t=!1)})),t},saveInvoice:function(){this.validateForm()?(console.log("保存发票数据",this.invoice),uni.showToast({title:"发票修改已保存",icon:"success",success:function(){setTimeout((function(){uni.navigateBack()}),1500)}})):uni.showToast({title:"请填写所有必填项",icon:"none"})},cancel:function(){uni.showModal({title:"确认取消",content:"确定要取消编辑吗？所有未保存的修改将丢失。",success:function(t){t.confirm&&uni.navigateBack()}})},goBack:function(){uni.navigateBack()},goToHistory:function(){uni.navigateTo({url:"/pages/contracts/invoice-history"})}}};e.default=o},d14d:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},a("01a2"),a("e39c"),a("bf0f"),a("844d"),a("18f7"),a("de6c"),a("08eb")},e775:function(t,e,a){"use strict";a.r(e);var i=a("c602"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},e8a3:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"invoice-edit-container"},[a("v-uni-view",{staticClass:"page-header"},[a("v-uni-view",{staticClass:"nav-back",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-arrow-left-s-line"})],1),a("v-uni-view",{staticClass:"page-title"},[t._v("编辑发票")]),a("v-uni-view",{staticClass:"header-actions"},[a("v-uni-view",{staticClass:"action-button history-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goToHistory.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-history-line"})],1)],1)],1),a("v-uni-scroll-view",{staticClass:"page-content",attrs:{"scroll-y":!0}},[a("v-uni-form",{attrs:{id:"invoiceForm"}},[a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-title"},[t._v("基本信息")]),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-view",{staticClass:"form-label"},[t._v("发票号码")]),a("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"系统自动生成"},model:{value:t.invoice.invoiceNumber,callback:function(e){t.$set(t.invoice,"invoiceNumber",e)},expression:"invoice.invoiceNumber"}}),a("v-uni-view",{staticClass:"form-hint"},[t._v("提交后自动生成发票号码")])],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-view",{staticClass:"form-label"},[t._v("发票日期")]),a("v-uni-picker",{staticClass:"date-picker",attrs:{mode:"date",value:t.invoice.invoiceDate},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onInvoiceDateChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"date-value"},[t._v(t._s(t.formatDate(t.invoice.invoiceDate)))])],1)],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-view",{staticClass:"form-label"},[t._v("到期日期")]),a("v-uni-picker",{staticClass:"date-picker",attrs:{mode:"date",value:t.invoice.dueDate},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onDueDateChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"date-value"},[t._v(t._s(t.formatDate(t.invoice.dueDate)))])],1)],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-view",{staticClass:"form-label"},[t._v("发票类型")]),a("v-uni-picker",{staticClass:"form-picker",attrs:{range:t.invoiceTypes,value:t.invoice.typeIndex},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onTypeChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-value"},[t._v(t._s(t.invoiceTypes[t.invoice.typeIndex]))])],1)],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-view",{staticClass:"form-label"},[t._v("发票状态")]),a("v-uni-picker",{staticClass:"form-picker",attrs:{range:t.invoiceStatuses,value:t.invoice.statusIndex},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onStatusChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-value"},[a("v-uni-view",{staticClass:"status-badge",class:t.getStatusClass(t.invoice.status)},[t._v(t._s(t.invoiceStatuses[t.invoice.statusIndex]))])],1)],1)],1)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-title"},[t._v("客户信息")]),a("v-uni-view",{staticClass:"form-group",class:{"has-error":t.errors.customer}},[a("v-uni-view",{staticClass:"form-label required"},[t._v("客户")]),a("v-uni-picker",{staticClass:"form-picker",attrs:{range:t.customers,"range-key":"name",value:t.customerIndex},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onCustomerChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-value"},[t.selectedCustomer?a("v-uni-view",{staticClass:"customer-info"},[a("v-uni-view",{staticClass:"customer-name"},[t._v(t._s(t.selectedCustomer.name))])],1):a("v-uni-view",{staticClass:"placeholder"},[t._v("选择客户")])],1)],1),t.errors.customer?a("v-uni-view",{staticClass:"error-message"},[t._v("必须选择客户")]):t._e()],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-view",{staticClass:"form-label"},[t._v("税号")]),a("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"税号"},model:{value:t.invoice.taxNumber,callback:function(e){t.$set(t.invoice,"taxNumber",e)},expression:"invoice.taxNumber"}})],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-view",{staticClass:"form-label"},[t._v("关联合同")]),a("v-uni-picker",{staticClass:"form-picker",attrs:{range:t.contracts,"range-key":"name",value:t.contractIndex},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onContractChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-value"},[t.selectedContract?a("v-uni-view",{staticClass:"contract-info"},[a("v-uni-view",{staticClass:"contract-name"},[t._v(t._s(t.selectedContract.name))])],1):a("v-uni-view",{staticClass:"placeholder"},[t._v("选择关联合同")])],1)],1)],1)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-title"},[t._v("发票明细")]),a("v-uni-view",{staticClass:"item-list",attrs:{id:"itemList"}},t._l(t.invoice.items,(function(e,i){return a("v-uni-view",{key:i,staticClass:"item-row"},[a("v-uni-view",{staticClass:"item-header"},[a("v-uni-view",{staticClass:"item-title"},[t._v("明细条目 #"+t._s(i+1))]),t.invoice.items.length>1?a("v-uni-view",{staticClass:"item-delete",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.deleteItem(i)}}},[a("v-uni-text",{staticClass:"ri-delete-bin-6-line"})],1):t._e()],1),a("v-uni-view",{staticClass:"item-fields"},[a("v-uni-input",{staticClass:"form-input item-description",class:{"has-error":e.errors&&e.errors.description},attrs:{type:"text",placeholder:"描述",required:!0},model:{value:e.description,callback:function(a){t.$set(e,"description",a)},expression:"item.description"}}),a("v-uni-input",{staticClass:"form-input item-quantity",attrs:{type:"number",placeholder:"数量",min:"1"},model:{value:e.quantity,callback:function(a){t.$set(e,"quantity",a)},expression:"item.quantity"}}),a("v-uni-view",{staticClass:"currency-input"},[a("v-uni-text",{staticClass:"currency-symbol"},[t._v("¥")]),a("v-uni-input",{staticClass:"form-input item-price",class:{"has-error":e.errors&&e.errors.price},attrs:{type:"digit",placeholder:"单价",min:"0",required:!0},model:{value:e.price,callback:function(a){t.$set(e,"price",a)},expression:"item.price"}})],1)],1)],1)})),1),a("v-uni-button",{staticClass:"add-item-button",attrs:{type:"button"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.addItem.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-add-line"}),a("v-uni-text",[t._v("添加明细条目")])],1),a("v-uni-view",{staticClass:"tax-summary"},[a("v-uni-view",{staticClass:"tax-row"},[a("v-uni-view",{staticClass:"tax-label"},[t._v("小计：")]),a("v-uni-view",{staticClass:"tax-value"},[t._v("¥"+t._s(t.formatMoney(t.subtotal)))])],1),a("v-uni-view",{staticClass:"tax-row"},[a("v-uni-view",{staticClass:"tax-label"},[t._v("税率：")]),a("v-uni-view",{staticClass:"tax-value"},[a("v-uni-picker",{staticClass:"tax-picker",attrs:{range:t.taxRates,value:t.taxRateIndex},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onTaxRateChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-value"},[t._v(t._s(t.getTaxRateLabel(t.taxRateIndex)))])],1)],1)],1),a("v-uni-view",{staticClass:"tax-row"},[a("v-uni-view",{staticClass:"tax-label"},[t._v("税额：")]),a("v-uni-view",{staticClass:"tax-value"},[t._v("¥"+t._s(t.formatMoney(t.taxAmount)))])],1),a("v-uni-view",{staticClass:"tax-row total-row"},[a("v-uni-view",{staticClass:"tax-label"},[t._v("总计：")]),a("v-uni-view",{staticClass:"tax-value"},[t._v("¥"+t._s(t.formatMoney(t.total)))])],1)],1)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-title"},[t._v("备注信息")]),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-view",{staticClass:"form-label"},[t._v("备注")]),a("v-uni-textarea",{staticClass:"form-textarea",attrs:{placeholder:"输入备注信息"},model:{value:t.invoice.notes,callback:function(e){t.$set(t.invoice,"notes",e)},expression:"invoice.notes"}})],1)],1)],1)],1),a("v-uni-view",{staticClass:"float-actions"},[a("v-uni-view",{staticClass:"action-btn secondary-action",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.cancel.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-close-line"}),a("v-uni-text",[t._v("取消")])],1),a("v-uni-view",{staticClass:"action-btn primary-action",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveInvoice.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-save-line"}),a("v-uni-text",[t._v("保存修改")])],1)],1)],1)},n=[]}}]);
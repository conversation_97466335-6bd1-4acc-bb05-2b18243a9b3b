<template>
  <view class="action-item" @click="handleClick">
    <view class="action-icon" v-if="icon">
      <image :src="icon" mode="aspectFit"></image>
    </view>
    <view class="action-content">
      <view class="action-title">{{ title }}</view>
      <view class="action-subtitle" v-if="subtitle">{{ subtitle }}</view>
    </view>
    <view class="action-extra" v-if="$slots.extra">
      <slot name="extra"></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ActionItem',
  props: {
    title: {
      type: String,
      required: true
    },
    subtitle: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    },
    data: {
      type: [Object, String, Number],
      default: null
    }
  },
  methods: {
    handleClick() {
      this.$emit('click', this.data)
    }
  }
}
</script>

<style lang="scss">
.action-item {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  background-color: #fff;
  border-bottom: 2rpx solid #f5f5f5;
  
  &:active {
    background-color: #f9f9f9;
  }
  
  .action-icon {
    margin-right: 20rpx;
    
    image {
      width: 40rpx;
      height: 40rpx;
    }
  }
  
  .action-content {
    flex: 1;
    min-width: 0;
    
    .action-title {
      font-size: 28rpx;
      color: #333;
      line-height: 1.4;
    }
    
    .action-subtitle {
      font-size: 24rpx;
      color: #999;
      line-height: 1.4;
      margin-top: 4rpx;
    }
  }
  
  .action-extra {
    margin-left: 20rpx;
  }
}
</style> 
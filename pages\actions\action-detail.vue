<template>
  <view class="page">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @tap="navBack">
        <svg-icon name="arrow-left" type="svg" size="24"></svg-icon>
      </view>
      <text class="page-title">行动计划详情</text>
      <view class="header-actions">
        <view class="header-icon" @tap="showMoreActions">
          <svg-icon name="more" type="svg" size="28"></svg-icon>
        </view>
      </view>
    </view>

    <scroll-view scroll-y class="page-container">
      <!-- 基本信息区域 -->
      <view class="info-section">
        <view class="info-header">
          <view class="action-type-icon" :class="actionData.priority">
            <svg-icon :name="getTypeIconName(actionData)" type="svg" size="28"></svg-icon>
          </view>
          <view class="info-title">
            <text class="action-title">{{actionData.title}}</text>
            <text class="action-time">{{actionData.date}} {{actionData.time}}</text>
          </view>
        </view>
        
        <!-- 任务状态 (仅任务类型) -->
        <view class="action-status" v-if="actionData.type === 'task'">
          <view class="status-item" :class="{ active: actionData.completed }" @tap="toggleStatus">
            <svg-icon :name="actionData.completed ? 'checkbox-circle-fill' : 'checkbox-blank-circle-line'" type="svg" size="28"></svg-icon>
            <text>{{actionData.completed ? '已完成' : '未完成'}}</text>
          </view>
        </view>
      </view>
      
      <!-- 关联信息区域 -->
      <view class="info-section" v-if="actionData.related">
        <view class="section-header">
          <svg-icon name="link" type="svg" size="20"></svg-icon>
          <text class="section-title">关联信息</text>
        </view>
        <view class="section-content">
          <view class="info-item">
            <text class="info-label">关联对象</text>
            <text class="info-value">{{actionData.related}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">关联类型</text>
            <text class="info-value">{{getRelatedTypeName(actionData.relatedType)}}</text>
          </view>
        </view>
      </view>
      
      <!-- 详情描述区域 -->
      <view class="info-section" v-if="actionData.description">
        <view class="section-header">
          <svg-icon name="align-left" type="svg" size="20"></svg-icon>
          <text class="section-title">详情描述</text>
        </view>
        <view class="section-content">
          <text class="description-text">{{actionData.description}}</text>
        </view>
      </view>
      
      <!-- 提醒设置 (仅非任务类型) -->
      <view class="info-section" v-if="actionData.type !== 'task'">
        <view class="section-header">
          <svg-icon name="notification" type="svg" size="20"></svg-icon>
          <text class="section-title">提醒设置</text>
        </view>
        <view class="section-content">
          <view class="info-item">
            <text class="info-label">提醒</text>
            <switch 
              :checked="actionData.hasReminder" 
              @change="onReminderChange" 
              color="#3370ff"
            />
          </view>
          <view class="info-item" v-if="actionData.hasReminder">
            <text class="info-label">提醒时间</text>
            <text class="info-value">{{getReminderTimeText(actionData.reminderTime)}}</text>
          </view>
        </view>
      </view>
      
      <!-- 参与人信息 (仅非任务类型) -->
      <view class="info-section" v-if="actionData.type !== 'task' && actionData.participants">
        <view class="section-header">
          <svg-icon name="user-group" type="svg" size="20"></svg-icon>
          <text class="section-title">参与人</text>
        </view>
        <view class="section-content">
          <view class="participant-list">
            <view class="participant-item" v-for="(participant, index) in actionData.participants" :key="index">
              <view class="participant-avatar">
                <text>{{participant.name.charAt(0)}}</text>
              </view>
              <view class="participant-info">
                <text class="participant-name">{{participant.name}}</text>
                <text class="participant-role">{{participant.role}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 操作记录 -->
      <view class="info-section">
        <view class="section-header">
          <svg-icon name="history" type="svg" size="20"></svg-icon>
          <text class="section-title">操作记录</text>
        </view>
        <view class="section-content">
          <view class="history-list">
            <view class="history-item" v-for="(record, index) in actionData.history" :key="index">
              <view class="history-time">{{record.time}}</view>
              <view class="history-content">{{record.content}}</view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部操作栏 -->
    <view class="action-bar">
      <button class="btn btn-outline" @tap="editAction">编辑</button>
      <button class="btn btn-primary" @tap="completeAction" v-if="actionData.type === 'task' && !actionData.completed">完成</button>
    </view>
  </view>
</template>

<script>
import SvgIcon from '@/components/svg-icon.vue';

export default {
  components: {
    SvgIcon
  },
  data() {
    return {
      actionData: {
        id: '',
        type: 'task',
        title: '',
        priority: 'medium',
        date: '',
        time: '',
        related: '',
        relatedType: 'company',
        description: '',
        completed: false,
        hasReminder: false,
        reminderTime: 15,
        participants: [],
        history: []
      }
    }
  },
  methods: {
    navBack() {
      uni.navigateBack();
    },
    showMoreActions() {
      uni.showActionSheet({
        itemList: ['删除', '分享', '复制'],
        success: (res) => {
          switch(res.tapIndex) {
            case 0:
              this.deleteAction();
              break;
            case 1:
              this.shareAction();
              break;
            case 2:
              this.copyAction();
              break;
          }
        }
      });
    },
    getTypeIconName(action) {
      const iconMap = {
        'task': 'check-circle',
        'call': 'phone',
        'meeting': 'team',
        'visit': 'navigation',
        'email': 'mail'
      };
      
      return iconMap[action.type] || 'schedule';
    },
    getRelatedTypeName(type) {
      const typeMap = {
        'company': '公司',
        'contact': '联系人',
        'opportunity': '商机',
        'contract': '合同',
        'meeting': '会议',
        'report': '报表',
        'department': '部门'
      };
      
      return typeMap[type] || type;
    },
    getReminderTimeText(minutes) {
      const timeMap = {
        5: '活动开始前5分钟',
        15: '活动开始前15分钟',
        30: '活动开始前30分钟',
        60: '活动开始前1小时',
        120: '活动开始前2小时',
        1440: '活动开始前1天'
      };
      
      return timeMap[minutes] || `${minutes}分钟`;
    },
    toggleStatus() {
      this.actionData.completed = !this.actionData.completed;
      
      // 实际应用中应该调用API更新状态
      uni.showToast({
        title: this.actionData.completed ? '已完成' : '已取消',
        icon: 'none'
      });
    },
    onReminderChange(e) {
      this.actionData.hasReminder = e.detail.value;
      
      // 实际应用中应该调用API更新提醒设置
      uni.showToast({
        title: this.actionData.hasReminder ? '提醒已开启' : '提醒已关闭',
        icon: 'none'
      });
    },
    editAction() {
      uni.navigateTo({
        url: `/pages/actions/action-create?id=${this.actionData.id}`
      });
    },
    completeAction() {
      this.actionData.completed = true;
      
      // 实际应用中应该调用API更新状态
      uni.showToast({
        title: '已完成',
        icon: 'success'
      });
    },
    deleteAction() {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除此行动计划吗？',
        success: (res) => {
          if (res.confirm) {
            // 实际应用中应该调用API删除
            uni.showToast({
              title: '删除成功',
              icon: 'success',
              duration: 2000,
              success: () => {
                setTimeout(() => {
                  uni.navigateBack();
                }, 1500);
              }
            });
          }
        }
      });
    },
    shareAction() {
      uni.showToast({
        title: '分享功能开发中',
        icon: 'none'
      });
    },
    copyAction() {
      uni.showToast({
        title: '复制功能开发中',
        icon: 'none'
      });
    },
    loadActionDetail(id) {
      // 模拟数据
      this.actionData = {
        id: id,
        type: 'task',
        title: '完成销售报表',
        priority: 'high',
        date: '2023-10-25',
        time: '16:30',
        related: '销售部',
        relatedType: 'department',
        description: '需要完成本月销售报表，包括销售额、客户增长、产品分析等内容。',
        completed: false,
        hasReminder: true,
        reminderTime: 15,
        participants: [
          {
            name: '张三',
            role: '销售经理'
          },
          {
            name: '李四',
            role: '数据分析师'
          }
        ],
        history: [
          {
            time: '2023-10-24 10:00',
            content: '创建任务'
          },
          {
            time: '2023-10-24 14:30',
            content: '添加了参与人'
          }
        ]
      };
    }
  },
  onLoad(options) {
    if (options.id) {
      this.loadActionDetail(options.id);
    }
  }
}
</script>

<style>
.page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #e0e0e0;
  background-color: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

.back-button {
  padding: 10rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 30rpx;
}

.header-icon {
  color: #666;
  font-size: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-container {
  flex: 1;
  padding: 30rpx;
}

.info-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.info-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.action-type-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  margin-right: 20rpx;
  color: white;
}

.action-type-icon.high {
  background-color: #f5222d;
}

.action-type-icon.medium {
  background-color: #faad14;
}

.action-type-icon.low {
  background-color: #52c41a;
}

.info-title {
  flex: 1;
}

.action-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.action-time {
  font-size: 24rpx;
  color: #999;
}

.action-status {
  display: flex;
  justify-content: flex-end;
  margin-top: 20rpx;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  border-radius: 100rpx;
  background-color: #f5f5f5;
  color: #999;
}

.status-item.active {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-item text {
  margin-left: 8rpx;
  font-size: 26rpx;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-left: 10rpx;
  color: #333;
}

.section-content {
  padding: 10rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
}

.description-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

.participant-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.participant-item {
  display: flex;
  align-items: center;
}

.participant-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #e6f7ff;
  color: #1890ff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 20rpx;
}

.participant-info {
  flex: 1;
}

.participant-name {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
}

.participant-role {
  font-size: 24rpx;
  color: #999;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.history-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.history-time {
  font-size: 24rpx;
  color: #999;
}

.history-content {
  font-size: 28rpx;
  color: #333;
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background-color: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  gap: 30rpx;
}

.btn {
  flex: 1;
  height: 88rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}

.btn-outline {
  border: 1rpx solid #d9d9d9;
  color: #666;
}

.btn-primary {
  background-color: #3370ff;
  color: white;
}
</style> 
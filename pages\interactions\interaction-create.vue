<template>
  <view class="page">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @tap="navBack">
        <svg-icon name="arrow-left" type="svg" size="24"></svg-icon>
      </view>
      <!-- <text class="page-title">创建沟通记录</text> -->
      <text class="page-title">快速创建任务</text>

      <view class="header-spacer"></view>
    </view>

    <scroll-view scroll-y class="page-container">
      <!-- 基本信息区域 -->
      <view class="form-section">
        <view class="section-header">
          <svg-icon name="file-list" type="svg" size="20"></svg-icon>
        </view>
        <view class="section-content">
          <view class="form-group">
            <text class="form-label required">任务名称</text>
            <input
              type="text"
              class="form-control"
              v-model="formData.title"
              placeholder="请输入任务名称"
            />
          </view>

          <view class="form-group">
            <text class="form-label">负责人</text>
            <view class="date-picker">
              <picker
                mode="selector"
                :range="userList"
                placeholder="请选择负责人"
                range-key="name"
                @change="onOwnerChange"
              >
                <view class="uni-input">
                  {{ formData.ownerName }}
                  <view class="input-icon"></view>
                </view>
              </picker>
            </view>
          </view>
          <view class="form-group">
            <text class="form-label required">日期</text>
            <view class="date-picker">
              <picker
                mode="date"
                :value="formData.date"
                @change="onDateChange"
                :start="startDate"
              >
                <view class="uni-input">
                  {{ formData.date }}
                  <view class="input-icon">
                    <svg-icon name="calendar" type="svg" size="20"></svg-icon>
                  </view>
                </view>
              </picker>
            </view>
          </view>

          <view class="form-group">
            <text class="form-label">时间</text>
            <view class="date-picker">
              <picker mode="time" :value="formData.time" @change="onTimeChange">
                <view class="uni-input">
                  {{ formData.time }}
                  <view class="input-icon">
                    <svg-icon name="clock" type="svg" size="20"></svg-icon>
                  </view>
                </view>
              </picker>
            </view>
          </view>
        </view>
      </view>

      <!-- 详情描述区域 -->
      <view class="form-section">
        <view class="section-header">
          <svg-icon name="align-left" type="svg" size="20"></svg-icon>
          <text class="section-title">详情描述</text>
        </view>
        <view class="section-content">
          <view class="form-group">
            <textarea
              class="form-control"
              v-model="formData.description"
              placeholder="请输入详情描述..."
            />
          </view>
        </view>
      </view>

      <!-- 底部占位 -->
      <view class="bottom-spacer"></view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="action-bar">
      <button class="btn btn-outline" @tap="navBack">取消</button>
      <button class="btn btn-primary" @tap="saveRecord">保存</button>
    </view>
  </view>
</template>

<script>
import SvgIcon from "@/components/svg-icon.vue";
import {
  getOrganizeUsers,
  TmsTaskCreate,
  TmsTaskTimeEnd,
  updateTaskDescription,
} from "@/api/customer.api";

export default {
  components: {
    SvgIcon,
  },
  data() {
    const now = new Date();

    return {
      formData: {
        recordType: "follow-up", // 默认为跟进记录
        type: "call", // 默认为电话
        subject: "",
        date: this.formatDate(now),
        time: this.formatTime(now),
        relatedObject: "",
        relatedId: "", // 添加关联对象ID
        relatedName: "",
        relatedType: 5,
        responsibleUserId: "",
        subRelatedType: "Accounts",
        title: "",
        sysUserId: "",
        description: "",

        contacts: "",
        content: "",
        nextSteps: [""],
        tags: [],
        ownerName: "",
      },
      userList: [],
      newTag: "",
      recordTypes: [],
      typeOptions: [],
      typeIndex: 0,
      relatedTypes: [],
      relatedTypeIndex: 0,
      startDate: this.formatDate(now),
    };
  },
  methods: {
    navBack() {
      uni.navigateBack();
    },
    loadData() {
      getOrganizeUsers().then((res) => {
        this.userList = res.items;
        this.setResponsibleUserId();
      });
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    onOwnerChange(e) {
      this.formData.ownerName = this.userList[e.detail.value].name;
      this.formData.responsibleUserId = this.userList[e.detail.value].id;
    },
    formatTime(date) {
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      return `${hours}:${minutes}`;
    },

    onDateChange(e) {
      this.formData.date = e.detail.value;
    },
    onTimeChange(e) {
      this.formData.time = e.detail.value;
    },
    setResponsibleUserId() {
      const res = uni.getStorageSync("userInfo");
      this.formData.responsibleUserId = res.id;
      this.formData.sysUserId = res.id;
      if (this.formData.responsibleUserId) {
        this.userList.forEach((item) => {
          if (item.id === this.formData.responsibleUserId) {
            this.formData.ownerName = item.name;
          }
        });
      }
    },
    saveRecord() {
      // 创建任务
      let params = {
        relatedId: this.formData.relatedId,
        relatedName: this.formData.relatedName,
        relatedType: this.formData.relatedType,
        responsibleUserId: this.formData.responsibleUserId,
        subRelatedType: this.formData.subRelatedType,
        title: this.formData.title,
      };
      let description = `<p>${this.formData.description}</p>`;

      let dateParams = {
        relatedId: this.formData.sysUserId,
        newDate: `${this.formData.date} ${this.formData.time}`,
      };

      TmsTaskCreate(params).then((res) => {
        TmsTaskTimeEnd(res.id, dateParams.newDate).then((res) => {});
        updateTaskDescription(res.id, { description: description });
        uni.navigateBack();
      });
    },
  },
  onLoad(options) {
    this.formData.relatedId = options.relatedId;
    this.formData.relatedName = options.relatedName;
  },
  async onShow() {
    await this.loadData();
  },
};
</script>

<style>
.page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #e0e0e0;
  background-color: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

.back-button {
  padding: 10rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-spacer {
  width: 44rpx;
}

.page-container {
  flex: 1;
  padding: 30rpx;
}

.form-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-left: 10rpx;
  color: #333;
}

.section-content {
  padding: 10rpx;
}

.type-selector {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.type-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
  border-radius: 12rpx;
  border: 1rpx solid #e0e0e0;
  transition: all 0.3s;
}

.type-option.selected {
  border-color: #3370ff;
  background-color: #f0f7ff;
}

.type-option text {
  margin-top: 16rpx;
  font-size: 28rpx;
  color: #666;
}

.type-option.selected text {
  color: #3370ff;
  font-weight: 500;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.form-label.required:after {
  content: "*";
  color: #f5222d;
  margin-left: 8rpx;
}

.form-control {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  box-sizing: border-box;
}

textarea.form-control {
  height: 240rpx;
  padding: 20rpx;
}

.uni-input {
  position: relative;
  height: 80rpx;
  padding: 0 20rpx;
  line-height: 80rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
}

.input-icon {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.next-steps-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.next-step-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.step-actions {
  display: flex;
  align-items: center;
}

.step-action {
  color: #999;
  padding: 10rpx;
}

.add-next-step {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border: 1rpx dashed #d9d9d9;
  border-radius: 8rpx;
  color: #3370ff;
}

.add-next-step text {
  margin-left: 10rpx;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.tag-item {
  display: flex;
  align-items: center;
  padding: 8rpx 20rpx;
  background-color: #f5f5f5;
  color: #666;
  border-radius: 100rpx;
  font-size: 24rpx;
}

.tag-close {
  margin-left: 8rpx;
  color: #999;
}

.add-tag {
  margin-top: 20rpx;
}

.bottom-spacer {
  height: 120rpx;
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background-color: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  gap: 30rpx;
}

.btn {
  flex: 1;
  height: 88rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}

.btn-outline {
  border: 1rpx solid #d9d9d9;
  color: #666;
}

.btn-primary {
  background-color: #3370ff;
  color: white;
}
</style>

<template>
  <view class="invoice-detail-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="goBack">
        <text class="ri-arrow-left-line"></text>
      </view>
      <text class="page-title">发票详情</text>
      <view class="header-actions">
        <button type="button" class="action-button" @click="showActionMenu">
          <text class="ri-more-2-fill"></text>
        </button>
      </view>
    </view>
    
    <scroll-view scroll-y class="invoice-container">
      <!-- 发票基本信息 -->
      <view class="section-card">
        <view class="section-title">
          <text>{{invoice.title}}</text>
          <text :class="['status-badge', 'status-' + invoice.statusCode]">{{invoice.statusText}}</text>
        </view>
        
        <view class="info-grid">
          <view class="info-item">
            <view class="info-label">发票号</view>
            <view class="info-value">{{invoice.invoiceNumber}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">关联合同</view>
            <view class="info-value">{{invoice.contractNumber}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">开票日期</view>
            <view class="info-value">{{invoice.invoiceDate}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">付款期限</view>
            <view class="info-value">{{invoice.dueDate}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">付款日期</view>
            <view class="info-value">{{invoice.paymentDate || '-'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">发票类型</view>
            <view class="info-value">{{invoice.invoiceType}}</view>
          </view>
        </view>
      </view>
      
      <!-- 客户信息 -->
      <view class="section-card">
        <view class="section-title">客户信息</view>
        
        <view class="info-item">
          <view class="info-label">客户名称</view>
          <view class="info-value">{{invoice.customer.name}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">税号</view>
          <view class="info-value">{{invoice.customer.taxNumber}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">开户行</view>
          <view class="info-value">{{invoice.customer.bank}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">账号</view>
          <view class="info-value">{{invoice.customer.accountNumber}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">地址电话</view>
          <view class="info-value">{{invoice.customer.addressAndPhone}}</view>
        </view>
      </view>
      
      <!-- 发票金额 -->
      <view class="section-card">
        <view class="section-title">发票金额</view>
        
        <view class="amount-table">
          <view class="amount-row">
            <view class="label">不含税金额</view>
            <view class="value">¥{{formatMoney(invoice.amountBeforeTax)}}</view>
          </view>
          <view class="amount-row">
            <view class="label">税额 ({{invoice.taxRate}}%)</view>
            <view class="value">¥{{formatMoney(invoice.taxAmount)}}</view>
          </view>
          <view class="amount-row total-row">
            <view class="label">总计</view>
            <view class="value">¥{{formatMoney(invoice.totalAmount)}}</view>
          </view>
        </view>
      </view>
      
      <!-- 发票明细 -->
      <view class="section-card">
        <view class="section-title">发票明细</view>
        
        <view class="invoice-items">
          <view class="invoice-item-row" v-for="(item, index) in invoice.items" :key="index">
            <view class="invoice-item-name">{{item.name}}</view>
            <view class="invoice-item-details">
              <view class="invoice-item-quantity">{{item.quantity}} {{item.unit}}</view>
              <view class="invoice-item-price">¥{{formatMoney(item.price)}}</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 历史记录 -->
      <view class="section-card">
        <view class="section-title">历史记录</view>
        
        <view class="timeline">
          <view class="timeline-item" v-for="(record, index) in invoice.timeline" :key="index">
            <view :class="['timeline-icon', record.type]">
              <text :class="record.icon"></text>
            </view>
            <view class="timeline-content">
              <view class="timeline-title">{{record.title}}</view>
              <view class="timeline-date">{{record.date}}</view>
            </view>
          </view>
        </view>
        
        <view class="add-note-button" @click="viewHistory">
          <text class="ri-history-line"></text>
          <text>查看完整历史记录</text>
        </view>
      </view>
      
      <!-- 发票文件 -->
      <view class="section-card">
        <view class="section-title">发票文件</view>
        
        <view class="file-list">
          <view class="file-item" v-for="(file, index) in invoice.files" :key="index">
            <text :class="file.icon"></text>
            <text>{{file.name}}</text>
            <button type="button" class="download-button" @click="downloadFile(file)">
              <text class="ri-download-line"></text>
            </button>
          </view>
        </view>
      </view>
      
      <!-- 备注信息 -->
      <view class="section-card">
        <view class="section-title">备注信息</view>
        
        <view class="notes-content">
          <text>{{invoice.notes || '暂无备注'}}</text>
        </view>
        
        <view class="add-note-button" @click="addNote">
          <text class="ri-add-line"></text>
          <text>添加备注</text>
        </view>
      </view>
    </scroll-view>
    
    <view class="float-actions">
      <button type="button" class="action-btn secondary-action" @click="sendInvoice">
        <text class="ri-mail-send-line"></text>
        <text>发送发票</text>
      </button>
      <button type="button" class="action-btn primary-action" @click="recordPayment">
        <text class="ri-bank-card-line"></text>
        <text>记录付款</text>
      </button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      invoice: {
        title: '系统集成项目 - 第一期',
        statusCode: 'paid',
        statusText: '已付款',
        invoiceNumber: 'INV-2023-11-001',
        contractNumber: 'CT-2023-09-001',
        invoiceDate: '2023-11-05',
        dueDate: '2023-11-15',
        paymentDate: '2023-11-12',
        invoiceType: '增值税专用发票',
        customer: {
          name: '上海智能科技',
          taxNumber: '91310000MA1FL4CT3X',
          bank: '中国建设银行上海张江支行',
          accountNumber: '31050161393600000123',
          addressAndPhone: '上海市浦东新区张江高科技园区科苑路88号 021-********'
        },
        amountBeforeTax: 257500.00,
        taxRate: 13,
        taxAmount: 33475.00,
        totalAmount: 290975.00,
        items: [
          { name: '软件系统集成服务', quantity: 1, unit: '项', price: 170000.00 },
          { name: '数据迁移服务', quantity: 1, unit: '项', price: 87500.00 }
        ],
        timeline: [
          { type: 'paid', icon: 'ri-bank-card-line', title: '收到付款', date: '2023-11-12 10:25' },
          { type: 'sent', icon: 'ri-mail-send-line', title: '发送发票给客户', date: '2023-11-05 14:30' },
          { type: 'created', icon: 'ri-add-line', title: '创建发票', date: '2023-11-05 11:10' }
        ],
        files: [
          { name: 'INV-2023-11-001_电子发票.pdf', icon: 'ri-file-pdf-line', url: '' },
          { name: '发票明细清单.xlsx', icon: 'ri-file-excel-line', url: '' }
        ],
        notes: '此发票为系统集成项目第一期款项，按照合同CT-2023-09-001约定开具，税率为13%。'
      }
    }
  },
  onLoad(options) {
    // 根据传入的ID加载发票详情
    if (options.id) {
      this.loadInvoiceData(options.id);
    }
  },
  methods: {
    loadInvoiceData(id) {
      // 这里应该是从API获取发票数据
      // 目前使用模拟数据
      console.log('加载发票ID:', id);
      // this.invoice = ...从API获取数据
    },
    formatMoney(amount) {
      return amount.toLocaleString('zh-CN');
    },
    goBack() {
      uni.navigateBack();
    },
    viewHistory() {
      uni.navigateTo({
        url: '/pages/contracts/invoice-history'
      });
    },
    downloadFile(file) {
      uni.showToast({
        title: '下载功能开发中...',
        icon: 'none'
      });
    },
    addNote() {
      uni.showModal({
        title: '添加备注',
        editable: true,
        placeholderText: '请输入备注信息',
        success: (res) => {
          if (res.confirm && res.content) {
            // 在实际应用中，这里应该调用API保存备注
            this.invoice.notes = this.invoice.notes 
              ? this.invoice.notes + '\n' + res.content 
              : res.content;
          }
        }
      });
    },
    sendInvoice() {
      uni.showToast({
        title: '发送发票功能开发中...',
        icon: 'none'
      });
    },
    recordPayment() {
      uni.navigateTo({
        url: '/pages/contracts/payment-create?invoiceId=' + this.invoice.invoiceNumber
      });
    },
    showActionMenu() {
      uni.showActionSheet({
        itemList: ['查看历史记录', '编辑发票', '打印发票', '导出为PDF', '删除发票'],
        success: (res) => {
          switch(res.tapIndex) {
            case 0:
              this.viewHistory();
              break;
            case 1:
              uni.navigateTo({
                url: '/pages/contracts/invoice-edit?id=' + this.invoice.invoiceNumber
              });
              break;
            default:
              uni.showToast({
                title: '该功能开发中...',
                icon: 'none'
              });
          }
        }
      });
    }
  }
}
</script>

<style>
.invoice-detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f6fa;
}

.page-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 10;
}

.back-button {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  flex: 1;
  font-size: 36rpx;
  font-weight: 600;
  text-align: center;
  margin-right: 60rpx;
}

.header-actions {
  width: 60rpx;
  display: flex;
  justify-content: center;
}

.action-button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font-size: 40rpx;
  line-height: 1;
}

.invoice-container {
  flex: 1;
  padding: 30rpx;
}

.section-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 20rpx;
}

.status-badge {
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-weight: normal;
}

.status-paid {
  background-color: #e6f7ee;
  color: #00b578;
}

.status-pending {
  background-color: #fff5e6;
  color: #ff9a2a;
}

.status-overdue {
  background-color: #ffece8;
  color: #ff4d4f;
}

.info-grid {
  display: flex;
  flex-wrap: wrap;
}

.info-item {
  width: 50%;
  margin-bottom: 24rpx;
}

.info-label {
  font-size: 24rpx;
  color: #8c8c8c;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
}

.amount-table {
  width: 100%;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.total-row {
  border-bottom: none;
  padding-top: 30rpx;
  font-weight: 600;
  font-size: 32rpx;
}

.invoice-items {
  margin-top: 20rpx;
}

.invoice-item-row {
  display: flex;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.invoice-item-row:last-child {
  border-bottom: none;
}

.invoice-item-name {
  flex: 1;
  font-size: 28rpx;
}

.invoice-item-details {
  text-align: right;
}

.invoice-item-quantity {
  font-size: 24rpx;
  color: #8c8c8c;
  margin-bottom: 8rpx;
}

.invoice-item-price {
  font-size: 28rpx;
  font-weight: 500;
}

.timeline {
  margin-top: 20rpx;
}

.timeline-item {
  display: flex;
  margin-bottom: 30rpx;
}

.timeline-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.timeline-icon.paid {
  background-color: #e6f7ee;
  color: #00b578;
}

.timeline-icon.sent {
  background-color: #e6f4ff;
  color: #1890ff;
}

.timeline-icon.created {
  background-color: #f5f5f5;
  color: #666;
}

.timeline-content {
  flex: 1;
}

.timeline-title {
  font-size: 28rpx;
  margin-bottom: 8rpx;
}

.timeline-date {
  font-size: 24rpx;
  color: #8c8c8c;
}

.file-list {
  margin-top: 20rpx;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item text:first-child {
  font-size: 40rpx;
  margin-right: 20rpx;
  color: #8c8c8c;
}

.file-item text:nth-child(2) {
  flex: 1;
  font-size: 28rpx;
}

.download-button {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #8c8c8c;
  background-color: #f5f5f5;
  border: none;
  padding: 0;
}

.notes-content {
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
}

.add-note-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  margin-top: 30rpx;
  padding: 20rpx;
  background-color: #f5f5f5;
  border: 2rpx dashed #e8e8e8;
  border-radius: 16rpx;
  color: #8c8c8c;
  font-size: 28rpx;
}

.float-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 30rpx;
  background-color: #fff;
  border-top: 2rpx solid #f0f0f0;
  z-index: 100;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 24rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.primary-action {
  background-color: #2979ff;
  color: white;
  border: none;
}

.secondary-action {
  background-color: #f5f5f5;
  color: #333;
  border: 2rpx solid #e8e8e8;
  margin-right: 20rpx;
}
</style> 
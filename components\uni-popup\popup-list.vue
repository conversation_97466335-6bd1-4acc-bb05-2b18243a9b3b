<template>
	<view class="popup-list">
		<view class="popup-list__title" v-if="title">{{ title }}</view>
		<slot></slot>
	</view>
</template>

<script>
export default {
	name: 'PopupList',
	props: {
		title: {
			type: String,
			default: ''
		}
	}
}
</script>

<style>
.popup-list {
	background-color: #fff;
	border-radius: 8px;
	overflow: hidden;
	margin-bottom: 8px;
}

.popup-list__title {
	font-size: 14px;
	color: var(--color-text-secondary);
	padding: 8px 16px;
	background-color: var(--color-bg-secondary);
}
</style> 
<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="flex-row items-center gap-sm">
        <text class="page-title">修改密码</text>
      </view>
    </view>
    
    <view class="page-content">
      <!-- 头部图标 -->
      <view class="password-icon-container">
        <view class="password-icon">
          <svg-icon name="password" type="svg" size="80" color="#4a6fff"></svg-icon>
        </view>
      </view>
      
      <!-- 密码表单 -->
      <view class="form-container">
        <view class="form-item">
          <text class="form-label">当前密码</text>
          <view class="password-input-container">
            <input 
              :type="currentPasswordVisible ? 'text' : 'password'" 
              placeholder="请输入当前密码" 
              v-model="currentPassword"
              class="form-input"
            />
            <view 
              class="password-toggle-icon" 
              @tap="toggleCurrentPasswordVisibility"
            >
              <svg-icon :name="currentPasswordVisible ? 'eye' : 'eye-off'" type="svg" size="40" color="#999999"></svg-icon>
            </view>
          </view>
          <text class="form-error" v-if="errors.currentPassword">{{errors.currentPassword}}</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">新密码</text>
          <view class="password-input-container">
            <input 
              :type="newPasswordVisible ? 'text' : 'password'" 
              placeholder="请输入新密码" 
              v-model="newPassword"
              class="form-input"
            />
            <view 
              class="password-toggle-icon" 
              @tap="toggleNewPasswordVisibility"
            >
              <svg-icon :name="newPasswordVisible ? 'eye' : 'eye-off'" type="svg" size="40" color="#999999"></svg-icon>
            </view>
          </view>
          <text class="form-error" v-if="errors.newPassword">{{errors.newPassword}}</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">确认新密码</text>
          <view class="password-input-container">
            <input 
              :type="confirmPasswordVisible ? 'text' : 'password'" 
              placeholder="请再次输入新密码" 
              v-model="confirmPassword"
              class="form-input"
            />
            <view 
              class="password-toggle-icon" 
              @tap="toggleConfirmPasswordVisibility"
            >
              <svg-icon :name="confirmPasswordVisible ? 'eye' : 'eye-off'" type="svg" size="40" color="#999999"></svg-icon>
            </view>
          </view>
          <text class="form-error" v-if="errors.confirmPassword">{{errors.confirmPassword}}</text>
        </view>
        
        <!-- 密码强度提示 -->
        <view class="password-strength" v-if="newPassword">
          <text class="strength-label">密码强度：</text>
          <view class="strength-indicator-container">
            <view class="strength-indicator" :class="strengthClass"></view>
          </view>
          <text class="strength-text" :class="strengthClass">{{strengthText}}</text>
        </view>
        
        <!-- 密码要求提示 -->
        <view class="password-tips">
          <text class="tips-title">密码要求：</text>
          <view class="tips-item" :class="{'fulfilled': hasValidLength}">
            <svg-icon :name="hasValidLength ? 'check' : 'close'" type="svg" size="28" :color="iconColorLength"></svg-icon>
            <text>长度为8-20个字符</text>
          </view>
          <view class="tips-item" :class="{'fulfilled': hasLowerCase}">
            <svg-icon :name="hasLowerCase ? 'check' : 'close'" type="svg" size="28" :color="iconColorLower"></svg-icon>
            <text>至少包含1个小写字母</text>
          </view>
          <view class="tips-item" :class="{'fulfilled': hasUpperCase}">
            <svg-icon :name="hasUpperCase ? 'check' : 'close'" type="svg" size="28" :color="iconColorUpper"></svg-icon>
            <text>至少包含1个大写字母</text>
          </view>
          <view class="tips-item" :class="{'fulfilled': hasNumber}">
            <svg-icon :name="hasNumber ? 'check' : 'close'" type="svg" size="28" :color="iconColorNumber"></svg-icon>
            <text>至少包含1个数字</text>
          </view>
          <view class="tips-item" :class="{'fulfilled': hasSpecialChar}">
            <svg-icon :name="hasSpecialChar ? 'check' : 'close'" type="svg" size="28" :color="iconColorSpecial"></svg-icon>
            <text>至少包含1个特殊字符</text>
          </view>
        </view>
        
        <!-- 提交按钮 -->
        <button class="submit-btn" :disabled="!isFormValid" @tap="changePassword">修改密码</button>
        
        <!-- 忘记密码提示 -->
        <view class="forgot-password">
          <text>忘记当前密码？</text>
          <text class="forgot-link" @tap="navigateToResetPassword">通过邮箱重置</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import SvgIcon from '@/components/svg-icon.vue';

export default {
  components: {
    SvgIcon
  },
  data() {
    return {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
      currentPasswordVisible: false,
      newPasswordVisible: false,
      confirmPasswordVisible: false,
      errors: {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
    }
  },
  computed: {
    // 密码验证规则
    hasValidLength() {
      return this.newPassword.length >= 8 && this.newPassword.length <= 20;
    },
    hasLowerCase() {
      return /[a-z]/.test(this.newPassword);
    },
    hasUpperCase() {
      return /[A-Z]/.test(this.newPassword);
    },
    hasNumber() {
      return /[0-9]/.test(this.newPassword);
    },
    hasSpecialChar() {
      return /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(this.newPassword);
    },
    
    // 密码强度计算
    passwordStrength() {
      if (!this.newPassword) return 0;
      
      let strength = 0;
      if (this.hasValidLength) strength += 1;
      if (this.hasLowerCase) strength += 1;
      if (this.hasUpperCase) strength += 1;
      if (this.hasNumber) strength += 1;
      if (this.hasSpecialChar) strength += 1;
      
      return strength;
    },
    
    strengthClass() {
      const strength = this.passwordStrength;
      if (strength <= 2) return 'weak';
      if (strength <= 3) return 'medium';
      return 'strong';
    },
    
    strengthText() {
      const strength = this.passwordStrength;
      if (strength <= 2) return '弱';
      if (strength <= 3) return '中';
      return '强';
    },
    
    // 表单验证
    isFormValid() {
      return (
        this.currentPassword &&
        this.newPassword &&
        this.confirmPassword &&
        this.hasValidLength &&
        this.hasLowerCase &&
        this.hasUpperCase &&
        this.hasNumber &&
        this.hasSpecialChar &&
        this.newPassword === this.confirmPassword
      );
    },
    
    // 根据条件设置图标颜色
    iconColorLength() {
      return this.hasValidLength ? '#10b981' : '#999999';
    },
    iconColorLower() {
      return this.hasLowerCase ? '#10b981' : '#999999';
    },
    iconColorUpper() {
      return this.hasUpperCase ? '#10b981' : '#999999';
    },
    iconColorNumber() {
      return this.hasNumber ? '#10b981' : '#999999';
    },
    iconColorSpecial() {
      return this.hasSpecialChar ? '#10b981' : '#999999';
    }
  },
  watch: {
    // 监听输入变化进行实时验证
    currentPassword(newVal) {
      if (!newVal) {
        this.errors.currentPassword = '请输入当前密码';
      } else {
        this.errors.currentPassword = '';
      }
    },
    newPassword(newVal) {
      if (!newVal) {
        this.errors.newPassword = '请输入新密码';
      } else if (newVal === this.currentPassword) {
        this.errors.newPassword = '新密码不能与当前密码相同';
      } else {
        this.errors.newPassword = '';
      }
      
      // 当新密码变化时，检查确认密码是否匹配
      if (this.confirmPassword && this.confirmPassword !== newVal) {
        this.errors.confirmPassword = '两次输入的密码不一致';
      } else {
        this.errors.confirmPassword = '';
      }
    },
    confirmPassword(newVal) {
      if (!newVal) {
        this.errors.confirmPassword = '请确认新密码';
      } else if (newVal !== this.newPassword) {
        this.errors.confirmPassword = '两次输入的密码不一致';
      } else {
        this.errors.confirmPassword = '';
      }
    }
  },
  methods: {
    // 切换密码可见性
    toggleCurrentPasswordVisibility() {
      this.currentPasswordVisible = !this.currentPasswordVisible;
    },
    toggleNewPasswordVisibility() {
      this.newPasswordVisible = !this.newPasswordVisible;
    },
    toggleConfirmPasswordVisibility() {
      this.confirmPasswordVisible = !this.confirmPasswordVisible;
    },
    
    // 修改密码
    changePassword() {
      // 进行最终验证
      this.validateForm();
      
      if (!this.isFormValid) {
        return;
      }
      
      // 显示加载中
      uni.showLoading({
        title: '修改中...'
      });
      
      // 模拟API调用
      setTimeout(() => {
        uni.hideLoading();
        
        // 这里应该替换为实际的API调用
        uni.showModal({
          title: '修改成功',
          content: '密码已成功修改，下次登录请使用新密码',
          showCancel: false,
          success: () => {
            // 返回上一页
            uni.navigateBack();
          }
        });
      }, 1500);
    },
    
    // 表单验证
    validateForm() {
      // 当前密码验证
      if (!this.currentPassword) {
        this.errors.currentPassword = '请输入当前密码';
      }
      
      // 新密码验证
      if (!this.newPassword) {
        this.errors.newPassword = '请输入新密码';
      } else if (this.newPassword === this.currentPassword) {
        this.errors.newPassword = '新密码不能与当前密码相同';
      }
      
      // 确认密码验证
      if (!this.confirmPassword) {
        this.errors.confirmPassword = '请确认新密码';
      } else if (this.confirmPassword !== this.newPassword) {
        this.errors.confirmPassword = '两次输入的密码不一致';
      }
    },
    
    // 跳转到重置密码页面
    navigateToResetPassword() {
      uni.navigateTo({
        url: '/pages/auth/forgot-password'
      });
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.page-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  position: relative;
  z-index: 10;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.gap-sm {
  gap: 20rpx;
}

.page-content {
  flex: 1;
  padding: 40rpx 30rpx;
}

.password-icon-container {
  display: flex;
  justify-content: center;
  margin: 40rpx 0;
}

.password-icon {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background-color: #4a6fff10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-icon .iconfont {
  display: none;
}

.form-container {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #e0e0e0;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

.password-input-container {
  display: flex;
  align-items: center;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 24rpx;
  background-color: #ffffff;
  height: 80rpx;
}

.form-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
}

.password-toggle-icon {
  padding: 20rpx;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-error {
  font-size: 24rpx;
  color: #ef4444;
  margin-top: 10rpx;
}

.password-strength {
  display: flex;
  align-items: center;
  margin: 30rpx 0;
}

.strength-label {
  font-size: 26rpx;
  color: #666666;
  margin-right: 20rpx;
}

.strength-indicator-container {
  flex: 1;
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.strength-indicator {
  height: 100%;
  border-radius: 4rpx;
}

.strength-indicator.weak {
  width: 30%;
  background-color: #ef4444;
}

.strength-indicator.medium {
  width: 60%;
  background-color: #f59e0b;
}

.strength-indicator.strong {
  width: 100%;
  background-color: #10b981;
}

.strength-text {
  font-size: 26rpx;
  font-weight: bold;
}

.strength-text.weak {
  color: #ef4444;
}

.strength-text.medium {
  color: #f59e0b;
}

.strength-text.strong {
  color: #10b981;
}

.password-tips {
  background-color: #f9fafb;
  border-radius: 8rpx;
  padding: 20rpx;
  margin: 30rpx 0;
}

.tips-title {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 16rpx;
  display: block;
}

.tips-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  font-size: 24rpx;
  color: #999999;
}

.tips-item svg-icon {
  margin-right: 10rpx;
  flex-shrink: 0;
}

.tips-item.fulfilled {
  color: #333333;
}

.submit-btn {
  background-color: #4a6fff;
  color: #ffffff;
  border-radius: 8rpx;
  margin-top: 40rpx;
  padding: 24rpx 0;
  font-size: 32rpx;
  width: 100%;
}

.submit-btn:disabled {
  background-color: #cccccc;
  color: #ffffff;
}

.forgot-password {
  text-align: center;
  margin-top: 30rpx;
  font-size: 26rpx;
  color: #666666;
}

.forgot-link {
  color: #4a6fff;
  margin-left: 10rpx;
}
</style> 
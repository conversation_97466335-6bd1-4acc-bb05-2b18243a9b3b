(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-sales-quotation-edit"],{1880:function(t,n,e){"use strict";e.r(n);var a=e("1a37"),i=e.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);n["default"]=i.a},"1a37":function(t,n,e){"use strict";e("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,e("4626"),e("5ac7"),e("bf0f"),e("bd06");var a={name:"CustomTabBar",data:function(){return{current:0,color:"#333333",activeColor:"#007AFF",showMoreMenu:!1,tabList:[{pagePath:"/pages/dashboard/main-dashboard",text:"首页",iconPath:"dashboard",selectedIconPath:"dashboard"},{pagePath:"/pages/customers/customer-list",text:"客户",iconPath:"customer",selectedIconPath:"customer"},{pagePath:"/pages/sales/opportunity-list",text:"销售",iconPath:"sales",selectedIconPath:"sales"},{type:"more",text:"更多",iconPath:"more",selectedIconPath:"more"},{pagePath:"/pages/settings/profile",text:"我的",iconPath:"user",selectedIconPath:"user"}],moreMenuList:[{pagePath:"/pages/marketing/leads",text:"线索",iconPath:"lead"},{pagePath:"/pages/interactions/interaction-list",text:"沟通",iconPath:"communication"},{pagePath:"/pages/sales/quotation-list",text:"报价",iconPath:"quotation"},{pagePath:"/pages/contracts/contract-list",text:"合同",iconPath:"contract"},{pagePath:"/pages/contracts/invoice-list",text:"发票",iconPath:"file-text"},{pagePath:"/pages/contracts/payment-list",text:"收款",iconPath:"money"},{pagePath:"/pages/reports/report-list",text:"报表",iconPath:"report"}]}},created:function(){this.updateCurrentTab()},onLoad:function(){this.updateCurrentTab()},onShow:function(){var t=this;setTimeout((function(){t.updateCurrentTab()}),100)},methods:{updateCurrentTab:function(){try{var t=getCurrentPages(),n=t[t.length-1];if(!n||!n.route)return;var e=n.route;console.log("当前路由:",e),e.includes("/pages/dashboard/")?this.current=0:e.includes("/pages/customers/")?this.current=1:e.includes("/pages/sales/")?this.current=2:e.includes("/pages/actions/")?this.current=3:e.includes("/pages/settings/")&&(this.current=5)}catch(a){console.error("更新Tab出错:",a)}},handleTabClick:function(t,n){"more"===t.type?(this.toggleMoreMenu(),this.current=n):this.switchTab(t.pagePath,n)},switchTab:function(t,n){this.current!==n&&(this.current=n,uni.switchTab({url:t}))},toggleMoreMenu:function(){this.showMoreMenu=!this.showMoreMenu},closeMoreMenu:function(){this.showMoreMenu=!1},navigateToPage:function(t){var n=this.tabList.some((function(n){return n.pagePath===t}));if(n){uni.switchTab({url:t});var e=this.tabList.findIndex((function(n){return n.pagePath===t}));-1!==e&&(this.current=e)}else uni.navigateTo({url:t});this.closeMoreMenu()}},watch:{$route:{handler:function(){this.updateCurrentTab()},immediate:!0}}};n.default=a},4196:function(t,n,e){"use strict";e.r(n);var a=e("f4bc"),i=e.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);n["default"]=i.a},"66b8":function(t,n,e){"use strict";var a=e("bf94"),i=e.n(a);i.a},"743a":function(t,n,e){"use strict";e.r(n);var a=e("c9f1"),i=e("4196");for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);e("66b8");var s=e("828b"),c=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,"44c4daa5",null,!1,a["a"],void 0);n["default"]=c.exports},7775:function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){return a}));var a={svgIcon:e("8a0f").default},i=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("v-uni-view",{staticClass:"custom-tab-bar"},[t._l(t.tabList,(function(n,a){return e("v-uni-view",{key:a,staticClass:"tab-item",class:{active:t.current===a},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleTabClick(n,a)}}},[e("svg-icon",{attrs:{name:t.current===a?n.selectedIconPath:n.iconPath,type:"svg",size:24,color:t.current===a?t.activeColor:t.color}}),e("v-uni-text",{staticClass:"tab-text",class:{"active-text":t.current===a}},[t._v(t._s(n.text))])],1)})),t.showMoreMenu?e("v-uni-view",{staticClass:"more-menu"},[e("v-uni-view",{staticClass:"menu-overlay",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.closeMoreMenu.apply(void 0,arguments)}}}),e("v-uni-view",{staticClass:"menu-content"},[e("v-uni-view",{staticClass:"menu-header"},[e("v-uni-text",{staticClass:"menu-title"},[t._v("更多功能")]),e("v-uni-view",{staticClass:"menu-close",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.closeMoreMenu.apply(void 0,arguments)}}},[e("svg-icon",{attrs:{name:"close",type:"svg",size:32,color:"#666"}})],1)],1),e("v-uni-view",{staticClass:"menu-list"},t._l(t.moreMenuList,(function(n,a){return e("v-uni-view",{key:a,staticClass:"menu-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.navigateToPage(n.pagePath)}}},[e("svg-icon",{attrs:{name:n.iconPath,type:"svg",size:24,color:"#333333"}}),e("v-uni-text",{staticClass:"menu-item-text"},[t._v(t._s(n.text))])],1)})),1)],1)],1):t._e()],2)},o=[]},9085:function(t,n,e){var a=e("c86c");n=a(!1),n.push([t.i,"\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* 重置所有样式 */.outer-container[data-v-44c4daa5]{width:100%;height:100vh;display:flex;justify-content:center; /* 水平居中 */background-color:#f5f5f5}.container[data-v-44c4daa5]{display:flex;flex-direction:column;height:100vh;width:100%;max-width:600px; /* 限制最大宽度 */background-color:#f5f5f5;position:relative}.page-header[data-v-44c4daa5]{display:flex;align-items:center;justify-content:space-between;padding:0 16px;height:44px;background-color:#fff;border-bottom:1px solid #eee;width:100%;box-sizing:border-box;position:-webkit-sticky;position:sticky;top:0;z-index:10}.page-title[data-v-44c4daa5]{font-size:18px;font-weight:700;color:#333}.back-button[data-v-44c4daa5]{color:#666;display:flex;align-items:center}.save-button[data-v-44c4daa5]{background-color:#3a86ff;color:#fff;padding:6px 12px;border-radius:6px;font-size:14px;border:none}.form-scroll[data-v-44c4daa5]{flex:1;width:100%;box-sizing:border-box;overflow-x:hidden;overflow-y:auto;-webkit-overflow-scrolling:touch;padding:0 /* 移除内边距，让内部容器处理 */}.form-content-wrapper[data-v-44c4daa5]{width:100%;display:flex;flex-direction:column;align-items:center; /* 水平居中内容 */padding:16px 0 150px 0 /* 添加顶部和底部内边距 */}.form-section[data-v-44c4daa5]{background-color:#fff;margin-bottom:16px;padding:16px;border-radius:12px;border:1px solid #eee;box-shadow:0 1px 3px rgba(0,0,0,.05);width:calc(100% - 32px); /* 减去左右padding */box-sizing:border-box;max-width:540px /* 限制最大宽度 */}.section-title[data-v-44c4daa5]{font-size:16px;font-weight:600;margin-bottom:16px;color:#333}.section-header[data-v-44c4daa5]{display:flex;justify-content:space-between;align-items:center;margin-bottom:16px}.add-button[data-v-44c4daa5]{display:flex;align-items:center;gap:4px;background-color:#f5f5f5;border:1px solid #ddd;padding:6px 12px;border-radius:6px;font-size:14px;color:#333}.form-group[data-v-44c4daa5]{width:100%;margin-bottom:16px}.form-label[data-v-44c4daa5]{font-size:14px;margin-bottom:8px;display:block;color:#333;width:100%}.required[data-v-44c4daa5]{color:#dc2626}.form-input[data-v-44c4daa5], .form-textarea[data-v-44c4daa5], .form-picker[data-v-44c4daa5], .customer-select[data-v-44c4daa5], .opportunity-select[data-v-44c4daa5]{width:100%;box-sizing:border-box;padding:10px 12px;border:1px solid #ddd;border-radius:8px;font-size:14px;color:#333;background-color:#fff}\n/* 文本区域特殊样式 */.form-textarea[data-v-44c4daa5]{height:120px}.date-picker[data-v-44c4daa5]{position:relative;display:flex;align-items:center}.picker-value[data-v-44c4daa5]{padding-right:24px;color:#333}.date-picker svg-icon[data-v-44c4daa5]{position:absolute;right:12px;color:#666;pointer-events:none}.customer-name[data-v-44c4daa5], .opportunity-name[data-v-44c4daa5]{color:#333}.customer-details[data-v-44c4daa5]{background-color:#f9f9f9;padding:12px;border-radius:8px;margin:8px 0 16px;border:1px solid #eee}.customer-info-item[data-v-44c4daa5]{margin-bottom:12px;display:flex;flex-direction:row;justify-content:space-between;align-items:center}.customer-info-item[data-v-44c4daa5]:last-child{margin-bottom:0}.customer-info-item .info-label[data-v-44c4daa5]{font-size:13px;color:#666;width:60px}.customer-info-item .info-value[data-v-44c4daa5]{font-size:14px;color:#333;flex:1;text-align:right;word-break:break-all}.info-content[data-v-44c4daa5]{display:flex;flex-direction:column;gap:12px}.info-row[data-v-44c4daa5]{display:flex;flex-direction:column}.info-label[data-v-44c4daa5]{font-size:12px;color:#666;margin-bottom:4px}.info-value[data-v-44c4daa5]{font-size:14px;color:#333}.empty-products[data-v-44c4daa5]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:32px 0;color:#999}.empty-text[data-v-44c4daa5]{font-size:16px;margin:12px 0 8px}.empty-desc[data-v-44c4daa5]{font-size:14px;color:#999}.product-list[data-v-44c4daa5]{display:flex;flex-direction:column;margin-bottom:10px}.product-item[data-v-44c4daa5]{background-color:#f9f9f9;padding:12px;border-radius:8px;border:1px solid #eee;margin-bottom:10px}.product-item[data-v-44c4daa5]:last-child{margin-bottom:0}.product-header[data-v-44c4daa5]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:8px}.product-name[data-v-44c4daa5]{font-size:15px;font-weight:500;color:#333}.product-actions[data-v-44c4daa5]{display:flex;gap:8px}.action-icon[data-v-44c4daa5]{width:28px;height:28px;display:flex;align-items:center;justify-content:center;color:#666}.product-details[data-v-44c4daa5]{display:flex;flex-direction:column}.product-price[data-v-44c4daa5]{font-size:15px;font-weight:600;color:#333;margin-bottom:4px}.product-description[data-v-44c4daa5]{font-size:13px;color:#666;line-height:1.5}.product-total[data-v-44c4daa5]{display:flex;justify-content:space-between;padding:12px;margin-top:8px;background-color:#f5f5f5;border-radius:8px;border:1px solid #eee}.total-label[data-v-44c4daa5]{font-size:16px;font-weight:600;color:#333}.total-amount[data-v-44c4daa5]{font-size:16px;font-weight:600;color:#333}\n/* 底部占位区域，防止内容被TabBar遮挡 */.bottom-space[data-v-44c4daa5]{height:%?150?%;width:100%}",""]),t.exports=n},bf69:function(t,n,e){var a=e("c86c");n=a(!1),n.push([t.i,".custom-tab-bar[data-v-6a709636]{display:flex;justify-content:space-around;align-items:center;background-color:#fff;box-shadow:0 -1px 5px rgba(0,0,0,.1);height:%?100?%;position:fixed;bottom:0;left:0;right:0;z-index:999;padding-bottom:env(safe-area-inset-bottom)}.tab-item[data-v-6a709636]{display:flex;flex-direction:column;align-items:center;justify-content:center;flex:1;height:100%;padding:%?10?% 0}.tab-text[data-v-6a709636]{font-size:%?22?%;color:#333;margin-top:%?4?%}.active-text[data-v-6a709636]{color:#007aff}\n\n/* 更多菜单样式 */.more-menu[data-v-6a709636]{position:fixed;top:0;right:0;bottom:0;left:0;z-index:1000}.menu-overlay[data-v-6a709636]{position:absolute;top:0;right:0;bottom:0;left:0;background-color:rgba(0,0,0,.5)}.menu-content[data-v-6a709636]{position:absolute;bottom:%?100?%;left:0;right:0;background-color:#fff;border-top-left-radius:%?20?%;border-top-right-radius:%?20?%;overflow:hidden;-webkit-animation:slideUp-data-v-6a709636 .3s ease;animation:slideUp-data-v-6a709636 .3s ease;box-shadow:0 -2px 10px rgba(0,0,0,.1)}.menu-header[data-v-6a709636]{display:flex;justify-content:space-between;align-items:center;padding:%?30?%;border-bottom:1px solid #f0f0f0}.menu-title[data-v-6a709636]{font-size:%?32?%;font-weight:500;color:#333}.menu-close[data-v-6a709636]{padding:%?10?%}.menu-list[data-v-6a709636]{display:flex;flex-wrap:wrap;padding:%?20?%}.menu-item[data-v-6a709636]{width:25%;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?20?% 0}.menu-item-text[data-v-6a709636]{font-size:%?24?%;color:#333;margin-top:%?10?%;text-align:center}@-webkit-keyframes slideUp-data-v-6a709636{from{-webkit-transform:translateY(100%);transform:translateY(100%)}to{-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes slideUp-data-v-6a709636{from{-webkit-transform:translateY(100%);transform:translateY(100%)}to{-webkit-transform:translateY(0);transform:translateY(0)}}",""]),t.exports=n},bf94:function(t,n,e){var a=e("9085");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=e("967d").default;i("5eb4d916",a,!0,{sourceMap:!1,shadowMode:!1})},c9cf:function(t,n,e){"use strict";var a=e("e9f7"),i=e.n(a);i.a},c9f1:function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){return a}));var a={svgIcon:e("8a0f").default},i=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("v-uni-view",{staticClass:"outer-container"},[e("v-uni-view",{staticClass:"container"},[e("v-uni-view",{staticClass:"page-header"},[e("v-uni-view",{staticClass:"back-button",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.goBack.apply(void 0,arguments)}}},[e("svg-icon",{attrs:{name:"arrow-left",type:"svg",size:"24"}})],1),e("v-uni-text",{staticClass:"page-title"},[t._v("编辑报价单")]),e("v-uni-view",{staticClass:"header-actions"},[e("v-uni-button",{staticClass:"save-button",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.saveQuotation.apply(void 0,arguments)}}},[t._v("保存")])],1)],1),e("v-uni-scroll-view",{staticClass:"form-scroll",attrs:{"scroll-y":!0,"scroll-top":t.scrollTop},on:{scroll:function(n){arguments[0]=n=t.$handleEvent(n),t.onScroll.apply(void 0,arguments)}}},[e("v-uni-view",{staticClass:"form-content-wrapper"},[e("v-uni-view",{staticClass:"form-section"},[e("v-uni-text",{staticClass:"section-title"},[t._v("基本信息")]),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("报价单名称"),e("v-uni-text",{staticClass:"required"},[t._v("*")])],1),e("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入报价单名称"},model:{value:t.quotation.title,callback:function(n){t.$set(t.quotation,"title",n)},expression:"quotation.title"}})],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("报价单编号")]),e("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"自动生成",disabled:!0},model:{value:t.quotation.code,callback:function(n){t.$set(t.quotation,"code",n)},expression:"quotation.code"}})],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("有效期至"),e("v-uni-text",{staticClass:"required"},[t._v("*")])],1),e("v-uni-view",{staticClass:"date-picker"},[e("v-uni-picker",{staticClass:"form-picker",attrs:{mode:"date",value:t.quotation.validUntil},on:{change:function(n){arguments[0]=n=t.$handleEvent(n),t.onValidUntilChange.apply(void 0,arguments)}}},[e("v-uni-view",{staticClass:"picker-value"},[t._v(t._s(t.quotation.validUntil||"请选择日期"))])],1),e("svg-icon",{attrs:{name:"calendar",type:"svg",size:"20"}})],1)],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("负责人"),e("v-uni-text",{staticClass:"required"},[t._v("*")])],1),e("v-uni-picker",{staticClass:"form-picker",attrs:{mode:"selector",range:t.owners.map((function(t){return t.name}))},on:{change:function(n){arguments[0]=n=t.$handleEvent(n),t.onOwnerChange.apply(void 0,arguments)}}},[e("v-uni-view",{staticClass:"picker-value"},[t._v(t._s(t.selectedOwner?t.selectedOwner.name:"请选择负责人"))])],1)],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("状态")]),e("v-uni-picker",{staticClass:"form-picker",attrs:{mode:"selector",range:t.statuses.map((function(t){return t.name}))},on:{change:function(n){arguments[0]=n=t.$handleEvent(n),t.onStatusChange.apply(void 0,arguments)}}},[e("v-uni-view",{staticClass:"picker-value"},[t._v(t._s(t.selectedStatus?t.selectedStatus.name:"请选择状态"))])],1)],1)],1),e("v-uni-view",{staticClass:"form-section"},[e("v-uni-text",{staticClass:"section-title"},[t._v("客户信息")]),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("客户"),e("v-uni-text",{staticClass:"required"},[t._v("*")])],1),e("v-uni-view",{staticClass:"customer-select",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.selectCustomer.apply(void 0,arguments)}}},[e("v-uni-text",{staticClass:"customer-name"},[t._v(t._s(t.selectedCustomer&&t.selectedCustomer.name||"请选择客户"))]),e("svg-icon",{attrs:{name:"arrow-right",type:"svg",size:"18"}})],1)],1),t.selectedCustomer?e("v-uni-view",{staticClass:"customer-details"},[e("v-uni-view",{staticClass:"customer-info-item"},[e("v-uni-text",{staticClass:"info-label"},[t._v("联系人")]),e("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.selectedCustomer.contact||"未指定"))])],1),e("v-uni-view",{staticClass:"customer-info-item"},[e("v-uni-text",{staticClass:"info-label"},[t._v("电话")]),e("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.selectedCustomer.phone||"未提供"))])],1),e("v-uni-view",{staticClass:"customer-info-item"},[e("v-uni-text",{staticClass:"info-label"},[t._v("邮箱")]),e("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.selectedCustomer.email||"未提供"))])],1)],1):t._e(),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("关联商机")]),e("v-uni-view",{staticClass:"opportunity-select",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.selectOpportunity.apply(void 0,arguments)}}},[e("v-uni-text",{staticClass:"opportunity-name"},[t._v(t._s(t.selectedOpportunity&&t.selectedOpportunity.name||"选择关联商机（可选）"))]),e("svg-icon",{attrs:{name:"arrow-right",type:"svg",size:"18"}})],1)],1)],1),e("v-uni-view",{staticClass:"form-section"},[e("v-uni-view",{staticClass:"section-header"},[e("v-uni-text",{staticClass:"section-title"},[t._v("产品与服务")]),e("v-uni-button",{staticClass:"add-button",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.addProduct.apply(void 0,arguments)}}},[e("svg-icon",{attrs:{name:"add",type:"svg",size:"18"}}),e("v-uni-text",[t._v("添加产品")])],1)],1),0===t.quotation.items.length?e("v-uni-view",{staticClass:"empty-products"},[e("svg-icon",{attrs:{name:"shopping",type:"svg",size:"36"}}),e("v-uni-text",{staticClass:"empty-text"},[t._v("暂无产品")]),e("v-uni-text",{staticClass:"empty-desc"},[t._v('点击"添加产品"按钮添加产品或服务项目')])],1):e("v-uni-view",{staticClass:"product-list"},[t._l(t.quotation.items,(function(n,a){return e("v-uni-view",{key:a,staticClass:"product-item"},[e("v-uni-view",{staticClass:"product-header"},[e("v-uni-text",{staticClass:"product-name"},[t._v(t._s(n.name))]),e("v-uni-view",{staticClass:"product-actions"},[e("v-uni-view",{staticClass:"action-icon",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.editProduct(a)}}},[e("svg-icon",{attrs:{name:"edit",type:"svg",size:"18"}})],1),e("v-uni-view",{staticClass:"action-icon",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.removeProduct(a)}}},[e("svg-icon",{attrs:{name:"delete-bin",type:"svg",size:"18"}})],1)],1)],1),e("v-uni-view",{staticClass:"product-details"},[e("v-uni-view",{staticClass:"product-price"},[t._v("¥"+t._s(t.formatPrice(n.amount)))]),n.description?e("v-uni-view",{staticClass:"product-description"},[t._v(t._s(n.description))]):t._e()],1)],1)})),e("v-uni-view",{staticClass:"product-total"},[e("v-uni-text",{staticClass:"total-label"},[t._v("总计")]),e("v-uni-text",{staticClass:"total-amount"},[t._v("¥"+t._s(t.formatPrice(t.calculateTotal())))])],1)],2)],1),e("v-uni-view",{staticClass:"form-section"},[e("v-uni-text",{staticClass:"section-title"},[t._v("备注说明")]),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-textarea",{staticClass:"form-textarea",attrs:{placeholder:"请输入备注信息"},model:{value:t.quotation.remark,callback:function(n){t.$set(t.quotation,"remark",n)},expression:"quotation.remark"}})],1)],1),e("v-uni-view",{staticClass:"form-section"},[e("v-uni-text",{staticClass:"section-title"},[t._v("附加信息")]),e("v-uni-view",{staticClass:"info-content"},[e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"info-label"},[t._v("创建人")]),e("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.quotation.creator||"系统"))])],1),e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"info-label"},[t._v("创建时间")]),e("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.quotation.createTime||t.quotation.createDate))])],1),e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"info-label"},[t._v("最后更新")]),e("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.quotation.updateTime||"无更新记录"))])],1)],1)],1),e("v-uni-view",{staticClass:"bottom-space"})],1)],1),e("custom-tab-bar",{ref:"customTabBar"})],1)],1)},o=[]},e9f7:function(t,n,e){var a=e("bf69");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=e("967d").default;i("9e21a296",a,!0,{sourceMap:!1,shadowMode:!1})},eab4:function(t,n,e){"use strict";e.r(n);var a=e("7775"),i=e("1880");for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);e("c9cf");var s=e("828b"),c=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,"6a709636",null,!1,a["a"],void 0);n["default"]=c.exports},f4bc:function(t,n,e){"use strict";e("6a54");var a=e("f5bd").default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,e("bd06"),e("473f"),e("bf0f"),e("aa9c"),e("01a2"),e("e39c"),e("dd2b"),e("0c26"),e("5c47"),e("a1c1");var i=a(e("9b1b")),o=a(e("eab4")),s=a(e("8a0f")),c={components:{CustomTabBar:o.default,SvgIcon:s.default},data:function(){return{id:"",scrollTop:0,quotation:{id:"",title:"",code:"",validUntil:"",owner:"",status:"",customer:"",opportunity:"",items:[],remark:"",creator:"",createDate:"",createTime:"",updateTime:""},selectedCustomer:null,selectedOpportunity:null,selectedOwner:null,selectedStatus:null,owners:[{id:"1",name:"李销售"},{id:"2",name:"王经理"},{id:"3",name:"张总监"}],statuses:[{code:"draft",name:"草稿"},{code:"sent",name:"已发送"},{code:"accepted",name:"已接受"},{code:"rejected",name:"已拒绝"}]}},onLoad:function(t){t.id&&(this.id=t.id,this.loadQuotationData())},methods:{onScroll:function(t){this.scrollTop=t.detail.scrollTop},loadQuotationData:function(){var t={id:"1",title:"云数据分析平台解决方案",code:"QT-2023-10-001",customer:"北京科技有限公司",customerId:"1",opportunity:"云平台建设项目",opportunityId:"2",contact:"张经理",phone:"13800138000",email:"<EMAIL>",createDate:"2023-10-10",createTime:"2023-10-10 14:30",validUntil:"2023-11-10",owner:"1",creator:"李销售",updateTime:"2023-10-12 09:45",remark:"1. 本报价单有效期为30天；\n2. 不包含硬件设备费用；\n3. 可根据客户需求调整配置和价格。",items:[{id:"101",name:"产品费用",amount:28e4,description:"包含基础模块、数据分析模块、报表模块的授权费用"},{id:"102",name:"实施费用",amount:5e4,description:"包含需求调研、系统部署、数据迁移和系统测试"},{id:"103",name:"年度维护",amount:2e4,description:"包含系统升级、故障处理和技术支持"}],status:"sent"};this.quotation=(0,i.default)({},t),this.selectedCustomer={id:t.customerId,name:t.customer,contact:t.contact,phone:t.phone,email:t.email},t.opportunityId&&(this.selectedOpportunity={id:t.opportunityId,name:t.opportunity});var n=this.owners.findIndex((function(n){return n.id===t.owner}));-1!==n&&(this.selectedOwner=this.owners[n]);var e=this.statuses.findIndex((function(n){return n.code===t.status}));-1!==e&&(this.selectedStatus=this.statuses[e])},formatPrice:function(t){return t.toLocaleString("zh-CN")},calculateTotal:function(){return this.quotation.items.reduce((function(t,n){return t+n.amount}),0)},goBack:function(){uni.navigateBack()},onValidUntilChange:function(t){this.quotation.validUntil=t.detail.value},onOwnerChange:function(t){var n=t.detail.value;this.selectedOwner=this.owners[n],this.quotation.owner=this.owners[n].id},onStatusChange:function(t){var n=t.detail.value;this.selectedStatus=this.statuses[n],this.quotation.status=this.statuses[n].code},selectCustomer:function(){var t=this;uni.navigateTo({url:"/pages/sales/customer-select?mode=select",events:{selectCustomer:function(n){t.selectedCustomer=n,t.quotation.customer=n.id}}})},selectOpportunity:function(){var t=this;this.selectedCustomer?uni.navigateTo({url:"/pages/sales/opportunity-select?customerId=".concat(this.selectedCustomer.id),events:{selectOpportunity:function(n){t.selectedOpportunity=n,t.quotation.opportunity=n.id}}}):uni.showToast({title:"请先选择客户",icon:"none"})},addProduct:function(){var t=this;uni.navigateTo({url:"/pages/sales/product-select?mode=select",events:{selectProduct:function(n){t.quotation.items.push({id:n.id,name:n.name,amount:n.price,description:n.description})}}})},editProduct:function(t){var n=this,e=this.quotation.items[t];uni.navigateTo({url:"/pages/sales/product-edit?mode=edit&index=".concat(t),events:{updateProduct:function(t,e){n.quotation.items[e]=t}},success:function(n){n.eventChannel.emit("productData",{product:e,index:t})}})},removeProduct:function(t){var n=this;uni.showModal({title:"确认删除",content:"确定要删除该产品吗？",success:function(e){e.confirm&&n.quotation.items.splice(t,1)}})},validateForm:function(){return this.quotation.title.trim()?this.quotation.validUntil?this.selectedCustomer?0!==this.quotation.items.length||(uni.showToast({title:"请至少添加一个产品或服务",icon:"none"}),!1):(uni.showToast({title:"请选择客户",icon:"none"}),!1):(uni.showToast({title:"请选择有效期",icon:"none"}),!1):(uni.showToast({title:"请输入报价单名称",icon:"none"}),!1)},saveQuotation:function(){this.validateForm()&&(this.quotation.updateTime=(new Date).toISOString().replace("T"," ").substring(0,19),uni.showLoading({title:"保存中..."}),setTimeout((function(){uni.hideLoading(),uni.showToast({title:"保存成功",icon:"success",success:function(){setTimeout((function(){uni.navigateBack()}),1500)}})}),1e3))}},onShow:function(){"undefined"!==typeof this.$refs.customTabBar&&(this.$refs.customTabBar.current=2)}};n.default=c}}]);
<template>
  <view class="page-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @tap="goBack">
        <svg-icon name="arrow-left" type="svg" size="24"></svg-icon>
      </view>
      <text class="page-title">{{ isNew ? '创建任务' : isEdit ? '编辑任务' : '任务详情' }}</text>
      <view class="header-actions">
        <view class="header-icon" v-if="!isNew && !isEdit" @tap="startEdit">
          <svg-icon name="edit" type="svg" size="24"></svg-icon>
        </view>
        <view class="header-icon" v-if="isEdit || isNew" @tap="saveTask">
          <svg-icon name="check" type="svg" size="24"></svg-icon>
        </view>
      </view>
    </view>
    
    <!-- 任务表单/详情 -->
    <scroll-view scroll-y class="task-detail-container">
      <view class="task-form">
        <!-- 任务标题 -->
        <view class="form-group">
          <text class="form-label">任务标题</text>
          <input 
            class="form-input" 
            type="text" 
            v-model="task.title" 
            placeholder="请输入任务标题" 
            :disabled="!isEdit && !isNew"
          />
        </view>
        
        <!-- 任务优先级 -->
        <view class="form-group">
          <text class="form-label">优先级</text>
          <view class="priority-group">
            <view 
              v-for="(priority, index) in priorities" 
              :key="index"
              :class="['priority-item', priority.value, { active: task.priority === priority.value }]"
              @tap="(isEdit || isNew) && (task.priority = priority.value)"
            >
              <text>{{ priority.label }}</text>
            </view>
          </view>
        </view>
        
        <!-- 任务日期和时间 -->
        <view class="form-group">
          <text class="form-label">日期和时间</text>
          <view class="date-time-group">
            <view class="date-picker">
              <picker 
                mode="date" 
                :value="task.date" 
                start="2020-01-01" 
                end="2030-12-31" 
                @change="onDateChange" 
                :disabled="!isEdit && !isNew"
              >
                <view class="picker-value">
                  <svg-icon name="calendar" type="svg" size="20"></svg-icon>
                  <text>{{ task.date }}</text>
                </view>
              </picker>
            </view>
            <view class="time-picker">
              <picker 
                mode="time" 
                :value="task.time" 
                @change="onTimeChange" 
                :disabled="!isEdit && !isNew"
              >
                <view class="picker-value">
                  <svg-icon name="clock" type="svg" size="20"></svg-icon>
                  <text>{{ task.time }}</text>
                </view>
              </picker>
            </view>
          </view>
        </view>
        
        <!-- 相关内容 -->
        <view class="form-group">
          <text class="form-label">相关内容</text>
          <view class="related-picker">
            <picker 
              :range="relatedTypes" 
              range-key="label" 
              :value="getRelatedTypeIndex(task.relatedType)" 
              @change="onRelatedTypeChange" 
              :disabled="!isEdit && !isNew"
            >
              <view class="picker-value">
                <svg-icon :name="getRelatedIconName(task)" type="svg" size="20"></svg-icon>
                <text>{{ getRelatedTypeLabel(task.relatedType) }}</text>
              </view>
            </picker>
          </view>
          <input 
            class="form-input" 
            type="text" 
            v-model="task.related" 
            placeholder="输入相关内容" 
            :disabled="!isEdit && !isNew"
          />
        </view>
        
        <!-- 任务描述 -->
        <view class="form-group">
          <text class="form-label">任务描述</text>
          <textarea 
            class="form-textarea" 
            v-model="task.description" 
            placeholder="请输入任务描述" 
            :disabled="!isEdit && !isNew"
          />
        </view>
      </view>
      
      <!-- 详情页面的附加信息 -->
      <block v-if="!isNew && !isEdit">
        <!-- 最后更新时间 -->
        <view class="info-group">
          <text class="info-label">创建时间</text>
          <text class="info-value">2023-05-15 09:30</text>
        </view>
        <view class="info-group">
          <text class="info-label">最后更新</text>
          <text class="info-value">2023-05-18 15:45</text>
        </view>
        
        <!-- 相关记录 -->
        <view class="section-title">相关记录</view>
        <view class="related-records">
          <view class="record-item">
            <svg-icon name="file-list" type="svg" size="20"></svg-icon>
            <text>销售记录：项目A合同签署</text>
          </view>
          <view class="record-item">
            <svg-icon name="user" type="svg" size="20"></svg-icon>
            <text>客户：创新科技有限公司</text>
          </view>
        </view>
        
        <!-- 操作记录 -->
        <view class="section-title">操作记录</view>
        <view class="operation-records">
          <view class="operation-item">
            <view class="operation-icon">
              <svg-icon name="user" type="svg" size="20"></svg-icon>
            </view>
            <view class="operation-content">
              <view class="operation-title">李明 创建了任务</view>
              <view class="operation-time">2023-05-15 09:30</view>
            </view>
          </view>
          <view class="operation-item">
            <view class="operation-icon">
              <svg-icon name="edit" type="svg" size="20"></svg-icon>
            </view>
            <view class="operation-content">
              <view class="operation-title">李明 修改了任务描述</view>
              <view class="operation-time">2023-05-18 15:45</view>
            </view>
          </view>
        </view>
      </block>
      
      <!-- 添加底部空间，防止底部按钮遮挡内容 -->
      <view class="bottom-space"></view>
    </scroll-view>
    
    <!-- 底部按钮 -->
    <view class="action-buttons" v-if="!isNew && !isEdit">
      <view class="action-button" @tap="completeTask">
        <svg-icon name="check-double" type="svg" size="28" color="#52c41a"></svg-icon>
        <text class="button-text">完成任务</text>
      </view>
      <view class="action-button" @tap="setReminder">
        <svg-icon name="bell" type="svg" size="28" color="#faad14"></svg-icon>
        <text class="button-text">设置提醒</text>
      </view>
      <view class="action-button" @tap="deleteTask">
        <svg-icon name="delete" type="svg" size="28" color="#ff4d4f"></svg-icon>
        <text class="button-text">删除任务</text>
      </view>
    </view>
    
    <!-- 自定义TabBar组件 -->
    <custom-tab-bar></custom-tab-bar>
  </view>
</template>

<script>
import SvgIcon from '@/components/svg-icon.vue';
import CustomTabBar from '@/components/CustomTabBar.vue';

export default {
  components: {
    SvgIcon,
    CustomTabBar
  },
  data() {
    return {
      isNew: false,
      isEdit: false,
      taskId: '',
      task: {
        id: '',
        title: '',
        priority: 'medium',
        date: this.formatDate(new Date()),
        time: this.formatTime(new Date()),
        related: '',
        relatedType: 'company',
        description: '',
        completed: false
      },
      priorities: [
        { label: '低', value: 'low' },
        { label: '中', value: 'medium' },
        { label: '高', value: 'high' }
      ],
      relatedTypes: [
        { label: '公司', value: 'company' },
        { label: '联系人', value: 'contact' },
        { label: '商机', value: 'opportunity' },
        { label: '合同', value: 'contract' },
        { label: '会议', value: 'meeting' },
        { label: '报表', value: 'report' }
      ]
    };
  },
  onLoad(options) {
    // 判断页面模式
    this.isNew = options.new === 'true';
    this.isEdit = options.edit === 'true';
    this.taskId = options.id;
    
    // 如果是查看或编辑模式，加载任务数据
    if (this.taskId) {
      this.loadTaskData(this.taskId);
    }
  },
  onShow() {
    // 设置TabBar选中项
    if (typeof this.$refs.customTabBar !== 'undefined') {
      this.$refs.customTabBar.current = 3;
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 开始编辑
    startEdit() {
      this.isEdit = true;
    },
    
    // 保存任务
    saveTask() {
      // 验证表单
      if (!this.task.title) {
        uni.showToast({
          title: '请输入任务标题',
          icon: 'none'
        });
        return;
      }
      
      // 模拟保存任务
      setTimeout(() => {
        uni.showToast({
          title: this.isNew ? '任务创建成功' : '任务更新成功',
          icon: 'success'
        });
        
        // 延迟后返回任务列表
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }, 500);
    },
    
    // 完成任务
    completeTask() {
      uni.showModal({
        title: '完成任务',
        content: '确认将任务标记为已完成吗？',
        success: (res) => {
          if (res.confirm) {
            this.task.completed = true;
            uni.showToast({
              title: '任务已完成',
              icon: 'success'
            });
            
            // 延迟后返回任务列表
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          }
        }
      });
    },
    
    // 设置提醒
    setReminder() {
      uni.showToast({
        title: '提醒功能开发中',
        icon: 'none'
      });
    },
    
    // 删除任务
    deleteTask() {
      uni.showModal({
        title: '删除任务',
        content: '确认删除此任务吗？此操作不可撤销。',
        success: (res) => {
          if (res.confirm) {
            // 模拟删除任务
            setTimeout(() => {
              uni.showToast({
                title: '任务已删除',
                icon: 'success'
              });
              
              // 延迟后返回任务列表
              setTimeout(() => {
                uni.navigateBack();
              }, 1500);
            }, 500);
          }
        }
      });
    },
    
    // 修改日期
    onDateChange(e) {
      this.task.date = e.detail.value;
    },
    
    // 修改时间
    onTimeChange(e) {
      this.task.time = e.detail.value;
    },
    
    // 修改相关内容类型
    onRelatedTypeChange(e) {
      const index = e.detail.value;
      this.task.relatedType = this.relatedTypes[index].value;
    },
    
    // 获取相关内容类型索引
    getRelatedTypeIndex(type) {
      return this.relatedTypes.findIndex(item => item.value === type);
    },
    
    // 获取相关内容类型标签
    getRelatedTypeLabel(type) {
      const found = this.relatedTypes.find(item => item.value === type);
      return found ? found.label : '公司';
    },
    
    // 获取相关图标名称
    getRelatedIconName(task) {
      const iconMap = {
        'company': 'building',
        'contact': 'user',
        'opportunity': 'briefcase',
        'contract': 'file-list',
        'meeting': 'presentation',
        'report': 'chart'
      };
      
      return iconMap[task.relatedType] || 'link';
    },
    
    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      
      return `${year}-${month}-${day}`;
    },
    
    // 格式化时间
    formatTime(date) {
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      
      return `${hours}:${minutes}`;
    },
    
    // 加载任务数据（模拟）
    loadTaskData(id) {
      // 模拟加载数据，实际应用中应该通过API获取
      setTimeout(() => {
        if (id === '1') {
          this.task = {
            id: '1',
            title: '与王总进行项目方案讨论',
            priority: 'high',
            date: '2023-05-20',
            time: '15:00',
            related: '创新科技有限公司',
            relatedType: 'company',
            description: '准备项目A的方案，讨论预算和实施计划等细节内容。需要准备的资料：\n1. 项目介绍PPT\n2. 预算表\n3. 实施计划甘特图',
            completed: false
          };
        } else if (id === '2') {
          this.task = {
            id: '2',
            title: '回电广州未来科技公司',
            priority: 'medium',
            date: '2023-05-20',
            time: '16:30',
            related: '未来科技有限公司',
            relatedType: 'company',
            description: '回复关于产品功能和价格的咨询，重点强调产品的安全性和易用性，为后续约见做准备。',
            completed: false
          };
        } else {
          // 默认任务数据
        }
      }, 300);
    }
  }
};
</script>

<style>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  padding-bottom: 120rpx; /* 为TabBar预留空间 */
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background-color: white;
  border-bottom: 1rpx solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 100;
  width: 100%;
  box-sizing: border-box;
}

.back-button {
  padding: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  flex: 1;
  text-align: center;
}

.header-actions {
  display: flex;
  gap: 30rpx;
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3370ff;
}

.task-detail-container {
  flex: 1;
  padding: 30rpx;
  width: 100%;
  box-sizing: border-box;
}

.task-form {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
  width: 100%;
  box-sizing: border-box;
}

.form-group {
  margin-bottom: 30rpx;
  width: 100%;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.form-input {
  width: 100%;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e0e0e0;
  font-size: 32rpx;
  box-sizing: border-box;
  height: 80rpx; /* 增加输入框高度 */
}

.form-textarea {
  width: 100%;
  height: 240rpx; /* 增加文本框高度 */
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.priority-group {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
  width: 100%;
}

.priority-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 8rpx;
  background-color: #f9f9f9;
  border: 2rpx solid transparent;
  font-size: 28rpx;
}

.priority-item.active {
  font-weight: bold;
}

.priority-item.low.active {
  background-color: rgba(51, 112, 255, 0.1);
  border-color: #3370ff;
  color: #3370ff;
}

.priority-item.medium.active {
  background-color: rgba(250, 173, 20, 0.1);
  border-color: #faad14;
  color: #faad14;
}

.priority-item.high.active {
  background-color: rgba(255, 77, 79, 0.1);
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.date-time-group {
  display: flex;
  gap: 20rpx;
  width: 100%;
}

.date-picker, .time-picker {
  flex: 1;
}

.picker-value {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  font-size: 28rpx;
  height: 80rpx; /* 确保选择器高度一致 */
  box-sizing: border-box;
}

.related-picker {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin: 30rpx 0 20rpx;
}

.info-group {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e0e0e0;
}

.info-label {
  color: #666;
}

.related-records, .operation-records {
  background-color: white;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

.record-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.operation-item {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.operation-item:last-child {
  border-bottom: none;
}

.operation-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.operation-content {
  flex: 1;
}

.operation-time {
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
}

.bottom-space {
  height: 140rpx; /* 添加底部空间防止内容被底部按钮遮挡 */
}

.action-buttons {
  display: flex;
  justify-content: space-around;
  background-color: white;
  padding: 20rpx 0;
  border-top: 1rpx solid #e0e0e0;
  width: 100%;
  box-sizing: border-box;
  position: fixed;
  bottom: 100rpx; /* 位于TabBar上方 */
  left: 0;
  z-index: 50;
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 10rpx 30rpx;
}

.button-text {
  font-size: 24rpx;
  color: #333;
  margin-top: 6rpx;
}
</style> 
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-customers-customer-groups"],{"003c":function(t,e,a){"use strict";var i=a("7029"),o=a.n(i);o.a},5033:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"container"},[a("v-uni-view",{staticClass:"page-header"},[a("v-uni-navigator",{staticClass:"back-button",attrs:{"open-type":"navigateBack"}},[a("v-uni-text",{staticClass:"iconfont icon-arrow-left"})],1),a("v-uni-view",{staticClass:"page-title"},[t._v("客户分组")]),a("v-uni-view",{staticClass:"header-actions"},[a("v-uni-button",{staticClass:"action-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showAddGroupModal.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"iconfont icon-add"})],1)],1)],1),a("v-uni-view",{staticClass:"search-container"},[a("v-uni-view",{staticClass:"search-box"},[a("v-uni-view",{staticClass:"search-icon"},[a("v-uni-text",{staticClass:"iconfont icon-search"})],1),a("v-uni-input",{staticClass:"search-input",attrs:{type:"text",placeholder:"搜索分组名称"},model:{value:t.searchKeyword,callback:function(e){t.searchKeyword=e},expression:"searchKeyword"}})],1)],1),a("v-uni-view",{staticClass:"groups-list"},[0===t.filteredGroups.length?a("v-uni-view",{staticClass:"empty-state"},[a("v-uni-view",{staticClass:"empty-icon"},[a("v-uni-text",{staticClass:"iconfont icon-folder"})],1),a("v-uni-view",{staticClass:"empty-title"},[t._v("暂无客户分组")]),a("v-uni-view",{staticClass:"empty-description"},[t._v("您还没有创建任何客户分组，点击右上角的加号创建新分组。")]),a("v-uni-button",{staticClass:"btn btn-primary",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showAddGroupModal.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"iconfont icon-add"}),t._v("创建分组")],1)],1):t._e(),t._l(t.filteredGroups,(function(e,i){return a("v-uni-view",{key:i,staticClass:"group-card"},[a("v-uni-view",{staticClass:"group-header"},[a("v-uni-view",{staticClass:"group-name"},[a("v-uni-view",{staticClass:"group-color",style:{backgroundColor:e.color}}),a("v-uni-text",[t._v(t._s(e.name))]),e.isSystem?a("v-uni-text",{staticClass:"system-tag"},[t._v("系统")]):t._e()],1),a("v-uni-view",{staticClass:"group-count"},[t._v(t._s(e.count)+"个客户")])],1),a("v-uni-view",{staticClass:"group-content"},[a("v-uni-view",{staticClass:"group-description"},[t._v(t._s(e.description))]),a("v-uni-view",{staticClass:"group-meta"},[a("v-uni-view",[t._v("创建于 "+t._s(e.createDate))]),a("v-uni-view",[t._v("最近更新 "+t._s(e.updateDate))])],1)],1),a("v-uni-view",{staticClass:"group-actions"},[a("v-uni-view",{staticClass:"group-action",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.viewGroup(e)}}},[a("v-uni-text",{staticClass:"iconfont icon-eye"}),t._v("查看")],1),e.isSystem?t._e():a("v-uni-view",{staticClass:"group-action",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.editGroup(e)}}},[a("v-uni-text",{staticClass:"iconfont icon-edit"}),t._v("编辑")],1),e.isSystem?t._e():a("v-uni-view",{staticClass:"group-action",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.confirmDeleteGroup(e)}}},[a("v-uni-text",{staticClass:"iconfont icon-delete"}),t._v("删除")],1)],1)],1)}))],2),a("v-uni-view",{staticClass:"fab",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showAddGroupModal.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"iconfont icon-add"})],1)],1)},o=[]},"603b":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("8f71"),a("bf0f"),a("4626"),a("5ac7"),a("01a2"),a("e39c"),a("c223"),a("bd06"),a("dd2b");var i={data:function(){return{searchKeyword:"",groups:[{id:1,name:"所有客户",color:"#4a6fff",count:112,description:"系统默认分组，包含所有客户",isSystem:!0,createDate:"2023-01-01",updateDate:"2023-01-01"},{id:2,name:"新增客户",color:"#00c16e",count:28,description:"最近30天新增的客户",isSystem:!0,createDate:"2023-01-01",updateDate:"2023-10-20"},{id:3,name:"重点关注",color:"#f59e0b",count:15,description:"需要重点关注和跟进的客户",isSystem:!1,createDate:"2023-08-15",updateDate:"2023-10-18"},{id:4,name:"上海区域",color:"#3b82f6",count:32,description:"上海区域的所有客户",isSystem:!1,createDate:"2023-09-05",updateDate:"2023-10-10"},{id:5,name:"年度目标",color:"#ec4899",count:24,description:"今年的销售目标客户",isSystem:!1,createDate:"2023-01-10",updateDate:"2023-10-15"}]}},computed:{filteredGroups:function(){if(!this.searchKeyword)return this.groups;var t=this.searchKeyword.toLowerCase();return this.groups.filter((function(e){return e.name.toLowerCase().includes(t)||e.description.toLowerCase().includes(t)}))}},methods:{showAddGroupModal:function(){uni.navigateTo({url:"/pages/customers/customer-group-create"})},viewGroup:function(t){uni.navigateTo({url:"/pages/customers/customer-list?groupId=".concat(t.id,"&groupName=").concat(t.name)})},editGroup:function(t){t.isSystem?uni.showToast({title:"系统分组不可编辑",icon:"none"}):uni.navigateTo({url:"/pages/customers/customer-group-edit?id=".concat(t.id)})},confirmDeleteGroup:function(t){var e=this;t.isSystem?uni.showToast({title:"系统分组不可删除",icon:"none"}):uni.showModal({title:"删除分组",content:'确定要删除"'.concat(t.name,'"分组吗？分组内的客户不会被删除。'),success:function(a){a.confirm&&e.deleteGroup(t.id)}})},deleteGroup:function(t){var e=this.groups.findIndex((function(e){return e.id===t}));-1!==e&&(this.groups.splice(e,1),uni.showToast({title:"删除成功",icon:"success"}))}},onLoad:function(){}};e.default=i},7029:function(t,e,a){var i=a("e125");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("56967d05",i,!0,{sourceMap:!1,shadowMode:!1})},9108:function(t,e,a){"use strict";a.r(e);var i=a("603b"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=o.a},c332:function(t,e,a){"use strict";a.r(e);var i=a("5033"),o=a("9108");for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(n);a("003c");var r=a("828b"),s=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,"d8d8b8fe",null,!1,i["a"],void 0);e["default"]=s.exports},e125:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,".page-header[data-v-d8d8b8fe]{display:flex;align-items:center;justify-content:space-between;padding:var(--spacing-md) var(--spacing-lg);border-bottom:%?1?% solid var(--border-color);background-color:#fff;position:-webkit-sticky;position:sticky;top:0;z-index:10}.page-title[data-v-d8d8b8fe]{font-size:%?32?%;font-weight:700;color:var(--text-primary)}.back-button[data-v-d8d8b8fe]{color:var(--text-secondary);display:flex;align-items:center}.header-actions[data-v-d8d8b8fe]{display:flex;gap:var(--spacing-sm)}.action-button[data-v-d8d8b8fe]{width:%?72?%;height:%?72?%;display:flex;align-items:center;justify-content:center;border-radius:var(--radius-full);color:var(--text-secondary);background-color:var(--light-color);border:%?1?% solid var(--border-color);padding:0;line-height:1}\n\n/* 搜索栏样式 */.search-container[data-v-d8d8b8fe]{padding:var(--spacing-md) var(--spacing-lg);background-color:#fff;border-bottom:%?1?% solid var(--border-color)}.search-box[data-v-d8d8b8fe]{display:flex;align-items:center;background-color:var(--light-color);border:%?1?% solid var(--border-color);border-radius:var(--radius-md);padding:0 var(--spacing-sm);overflow:hidden}.search-icon[data-v-d8d8b8fe]{color:var(--text-secondary);padding:var(--spacing-xs)}.search-input[data-v-d8d8b8fe]{flex:1;border:none;padding:var(--spacing-sm);background-color:initial;color:var(--text-primary);font-size:%?28?%}\n\n/* 分组列表样式 */.groups-list[data-v-d8d8b8fe]{display:flex;flex-direction:column;gap:%?32?%;padding:%?32?%;padding-bottom:%?160?% /* 添加底部填充，确保内容不被底部导航栏遮挡 */}.group-card[data-v-d8d8b8fe]{background-color:#fff;border-radius:var(--radius-md);box-shadow:0 %?2?% %?8?% rgba(0,0,0,.05);margin-bottom:var(--spacing-sm);overflow:hidden;border:%?1?% solid var(--border-color)}.group-header[data-v-d8d8b8fe]{padding:var(--spacing-md);border-bottom:%?1?% solid var(--border-color-light);display:flex;justify-content:space-between;align-items:center}.group-name[data-v-d8d8b8fe]{font-size:%?30?%;font-weight:500;color:var(--text-primary);display:flex;align-items:center}.group-color[data-v-d8d8b8fe]{width:%?24?%;height:%?24?%;border-radius:50%;margin-right:var(--spacing-xs)}.group-count[data-v-d8d8b8fe]{font-size:%?26?%;color:var(--text-secondary);background-color:var(--light-color);border-radius:var(--radius-full);padding:%?4?% %?16?%}.group-content[data-v-d8d8b8fe]{padding:var(--spacing-md)}.group-description[data-v-d8d8b8fe]{font-size:%?28?%;color:var(--text-secondary);margin-bottom:var(--spacing-md)}.group-meta[data-v-d8d8b8fe]{display:flex;justify-content:space-between;font-size:%?24?%;color:var(--text-tertiary)}.group-actions[data-v-d8d8b8fe]{display:flex;border-top:%?1?% solid var(--border-color)}.group-action[data-v-d8d8b8fe]{flex:1;display:flex;align-items:center;justify-content:center;padding:%?24?% 0;color:var(--text-secondary);font-size:%?26?%}.group-action uni-text[data-v-d8d8b8fe]{margin-right:%?8?%}.group-action[data-v-d8d8b8fe]:not(:last-child){border-right:%?1?% solid var(--border-color)}\n\n/* 系统分组标识 */.system-tag[data-v-d8d8b8fe]{font-size:%?22?%;background-color:#f3f4f6;color:#6b7280;padding:%?2?% %?16?%;border-radius:var(--radius-full);margin-left:var(--spacing-xs)}\n\n/* 浮动操作按钮 */.fab[data-v-d8d8b8fe]{position:fixed;bottom:var(--spacing-lg);right:var(--spacing-lg);width:%?112?%;height:%?112?%;border-radius:50%;background-color:var(--primary-color);color:#fff;display:flex;align-items:center;justify-content:center;box-shadow:0 %?8?% %?16?% rgba(0,0,0,.1);z-index:100;font-size:%?48?%}\n\n/* 空状态样式 */.empty-state[data-v-d8d8b8fe]{padding:var(--spacing-xl) var(--spacing-lg);text-align:center}.empty-icon[data-v-d8d8b8fe]{font-size:%?96?%;color:var(--border-color);margin-bottom:var(--spacing-md)}.empty-title[data-v-d8d8b8fe]{font-size:%?32?%;font-weight:500;color:var(--text-primary);margin-bottom:var(--spacing-sm)}.empty-description[data-v-d8d8b8fe]{color:var(--text-secondary);margin-bottom:var(--spacing-lg)}",""]),t.exports=e}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-products-product-select"],{"02c6":function(t,e,r){"use strict";r.r(e);var n=r("cb20"),a=r("4927");for(var o in a)["default"].indexOf(o)<0&&function(t){r.d(e,t,(function(){return a[t]}))}(o);r("b3ea");var i=r("828b"),c=Object(i["a"])(a["default"],n["b"],n["c"],!1,null,"6ee7b19e",null,!1,n["a"],void 0);e["default"]=c.exports},"04f1":function(t,e,r){var n=r("c86c");e=n(!1),e.push([t.i,".product-select-page[data-v-6ee7b19e]{display:flex;flex-direction:column;height:100vh;background-color:#f5f5f5}.product-select-page .header[data-v-6ee7b19e]{display:flex;justify-content:space-between;align-items:center;height:%?90?%;background-color:#3a86ff;color:#fff;padding:0 %?30?%;position:-webkit-sticky;position:sticky;top:0;z-index:100}.product-select-page .header .left[data-v-6ee7b19e]{display:flex;align-items:center}.product-select-page .header .title[data-v-6ee7b19e]{font-size:%?34?%;font-weight:700}.product-select-page .header .right[data-v-6ee7b19e]{font-size:%?30?%}.product-select-page .search-area[data-v-6ee7b19e]{background-color:#fff;padding:%?20?% %?30?%;border-bottom:%?1?% solid #eee}.product-select-page .search-area .search-box[data-v-6ee7b19e]{display:flex;align-items:center;background-color:#f5f5f5;border-radius:%?36?%;padding:0 %?20?%;height:%?72?%;margin-bottom:%?20?%}.product-select-page .search-area .search-box uni-input[data-v-6ee7b19e]{flex:1;height:%?72?%;padding:0 %?20?%;font-size:%?28?%}.product-select-page .search-area .search-box .clear-icon[data-v-6ee7b19e]{padding:%?10?%}.product-select-page .search-area .category-scroll[data-v-6ee7b19e]{white-space:nowrap;height:%?80?%}.product-select-page .search-area .category-scroll .category-tabs[data-v-6ee7b19e]{display:inline-flex;padding:0 %?10?%}.product-select-page .search-area .category-scroll .category-tabs .category-item[data-v-6ee7b19e]{padding:0 %?30?%;height:%?64?%;line-height:%?64?%;margin-right:%?20?%;border-radius:%?32?%;font-size:%?28?%;color:#666;background-color:#f5f5f5}.product-select-page .search-area .category-scroll .category-tabs .category-item.active[data-v-6ee7b19e]{color:#fff;background-color:#3a86ff}.product-select-page .product-list-container[data-v-6ee7b19e]{flex:1;padding:%?20?%;display:flex;flex-direction:column;align-items:center}.product-select-page .product-grid[data-v-6ee7b19e]{display:flex;flex-direction:column;padding-bottom:%?120?%;width:88%;max-width:%?720?%}.product-select-page .product-list-item[data-v-6ee7b19e]{display:flex;flex-direction:column;background-color:#fff;border-radius:%?12?%;padding:%?30?%;margin-bottom:%?20?%;box-shadow:0 %?2?% %?6?% rgba(0,0,0,.05);width:100%}.product-select-page .product-list-info[data-v-6ee7b19e]{display:flex;flex-direction:column;flex:1}.product-select-page .product-name[data-v-6ee7b19e]{font-size:%?32?%;font-weight:700;color:#333;margin-bottom:%?10?%}.product-select-page .product-desc[data-v-6ee7b19e]{font-size:%?26?%;color:#666;margin-bottom:%?20?%;display:-webkit-box;-webkit-line-clamp:2;line-clamp:2;-webkit-box-orient:vertical;overflow:hidden;text-overflow:ellipsis}.product-select-page .product-meta[data-v-6ee7b19e]{display:flex;font-size:%?24?%;color:#999;margin-bottom:%?16?%}.product-select-page .product-code[data-v-6ee7b19e]{margin-right:%?20?%}.product-select-page .product-price[data-v-6ee7b19e]{display:flex;align-items:baseline;padding-right:%?10?%}.product-select-page .price-value[data-v-6ee7b19e]{font-size:%?36?%;color:#ff6b18;font-weight:700}.product-select-page .price-unit[data-v-6ee7b19e]{font-size:%?24?%;color:#999;margin-left:%?4?%}.product-select-page .action-bar[data-v-6ee7b19e]{position:fixed;bottom:0;left:0;right:0;background-color:#fff;padding:%?20?% %?30?%;display:flex;justify-content:center;border-top:%?1?% solid #eee}.product-select-page .confirm-button[data-v-6ee7b19e]{width:88%;max-width:%?720?%;height:%?88?%;line-height:%?88?%;background-color:#3a86ff;color:#fff;font-size:%?32?%;border-radius:%?44?%;text-align:center}.product-select-page .empty-state[data-v-6ee7b19e]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?100?% 0;color:#999;font-size:%?28?%}.product-select-page .empty-state uni-text[data-v-6ee7b19e]{margin-top:%?20?%}",""]),t.exports=e},2634:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
e.default=function(){return t};var t={},r=Object.prototype,a=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},c=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(j){l=function(t,e,r){return t[e]=r}}function d(t,e,r,n){var a=e&&e.prototype instanceof h?e:h,i=Object.create(a.prototype),c=new O(n||[]);return o(i,"_invoke",{value:L(t,r,c)}),i}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(j){return{type:"throw",arg:j}}}t.wrap=d;var p={};function h(){}function v(){}function g(){}var y={};l(y,c,(function(){return this}));var m=Object.getPrototypeOf,b=m&&m(m(E([])));b&&b!==r&&a.call(b,c)&&(y=b);var w=g.prototype=h.prototype=Object.create(y);function x(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function _(t,e){var r;o(this,"_invoke",{value:function(o,i){function c(){return new e((function(r,c){(function r(o,i,c,s){var u=f(t[o],t,i);if("throw"!==u.type){var l=u.arg,d=l.value;return d&&"object"==(0,n.default)(d)&&a.call(d,"__await")?e.resolve(d.__await).then((function(t){r("next",t,c,s)}),(function(t){r("throw",t,c,s)})):e.resolve(d).then((function(t){l.value=t,c(l)}),(function(t){return r("throw",t,c,s)}))}s(u.arg)})(o,i,r,c)}))}return r=r?r.then(c,c):c()}})}function L(t,e,r){var n="suspendedStart";return function(a,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===a)throw o;return T()}for(r.method=a,r.arg=o;;){var i=r.delegate;if(i){var c=k(i,r);if(c){if(c===p)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var s=f(t,e,r);if("normal"===s.type){if(n=r.done?"completed":"suspendedYield",s.arg===p)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n="completed",r.method="throw",r.arg=s.arg)}}}function k(t,e){var r=e.method,n=t.iterator[r];if(void 0===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=void 0,k(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),p;var a=f(n,t.iterator,e.arg);if("throw"===a.type)return e.method="throw",e.arg=a.arg,e.delegate=null,p;var o=a.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,p):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function E(t){if(t){var e=t[c];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,n=function e(){for(;++r<t.length;)if(a.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return n.next=n}}return{next:T}}function T(){return{value:void 0,done:!0}}return v.prototype=g,o(w,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:v,configurable:!0}),v.displayName=l(g,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,l(t,u,"GeneratorFunction")),t.prototype=Object.create(w),t},t.awrap=function(t){return{__await:t}},x(_.prototype),l(_.prototype,s,(function(){return this})),t.AsyncIterator=_,t.async=function(e,r,n,a,o){void 0===o&&(o=Promise);var i=new _(d(e,r,n,a),o);return t.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},x(w),l(w,u,"Generator"),l(w,c,(function(){return this})),l(w,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=E,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(P),!t)for(var e in this)"t"===e.charAt(0)&&a.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(r,n){return i.type="throw",i.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],i=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var c=a.call(o,"catchLoc"),s=a.call(o,"finallyLoc");if(c&&s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,p):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),P(r),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;P(r)}return a}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:E(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),p}},t},r("6a54"),r("01a2"),r("e39c"),r("bf0f"),r("844d"),r("18f7"),r("de6c"),r("3872e"),r("4e9b"),r("114e"),r("c240"),r("926e"),r("7a76"),r("c9b5"),r("aa9c"),r("2797"),r("8a8d"),r("dc69"),r("f7a5");var n=function(t){return t&&t.__esModule?t:{default:t}}(r("fcf3"))},"2fdc":function(t,e,r){"use strict";function n(t,e,r,n,a,o,i){try{var c=t[o](i),s=c.value}catch(u){return void r(u)}c.done?e(s):Promise.resolve(s).then(n,a)}r("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return function(){var e=this,r=arguments;return new Promise((function(a,o){var i=t.apply(e,r);function c(t){n(i,a,o,c,s,"next",t)}function s(t){n(i,a,o,c,s,"throw",t)}c(void 0)}))}},r("bf0f")},4927:function(t,e,r){"use strict";r.r(e);var n=r("4b02"),a=r.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){r.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},"4b02":function(t,e,r){"use strict";r("6a54");var n=r("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(r("2634")),o=n(r("2fdc")),i=n(r("8a0f")),c=r("d86f"),s={components:{SvgIcon:i.default},data:function(){return{searchText:"",currentCategory:"",categories:[{name:"全部",value:""},{name:"生产",value:"生产"},{name:"外采",value:"外采"}],products:[]}},methods:{getProductList:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var r;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,r={pageIndex:1,pageSize:999,filter:{likeString:t.searchText,productType:t.currentCategory}},e.next=4,(0,c.getProductList)(r).then((function(e){t.products=e.items}));case 4:e.next=8;break;case 6:e.prev=6,e.t0=e["catch"](0);case 8:case"end":return e.stop()}}),e,null,[[0,6]])})))()},goBack:function(){uni.navigateBack()},goToCreate:function(){uni.navigateTo({url:"/pages/products/product-create"})},clearSearch:function(){this.searchText="",this.getProductList()},selectCategory:function(t){this.currentCategory=t,this.getProductList()},selectProduct:function(t){var e=this.getOpenerEventChannel&&this.getOpenerEventChannel();e&&e.emit("selectProduct",t),uni.navigateBack()}},onLoad:function(){this.getProductList()}};e.default=s},"4f88":function(t,e,r){var n=r("04f1");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=r("967d").default;a("2fb02be4",n,!0,{sourceMap:!1,shadowMode:!1})},b3ea:function(t,e,r){"use strict";var n=r("4f88"),a=r.n(n);a.a},c475:function(t,e,r){"use strict";r("6a54");var n=r("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.request=e.getTenantInfo=void 0;var a=n(r("9b1b"));r("bf0f"),r("4626"),r("5ac7");var o=null;e.getTenantInfo=function(t){return new Promise((function(e,r){uni.request({url:"/api/Tenants/getTenantsByLogin",method:"POST",data:t,header:{"Content-Type":"application/json"},success:function(t){200===t.statusCode?(o=t.data,e(t.data)):r(t.data)},fail:function(t){r(t)}})}))};e.request=function(t){return t.url.includes("/login")&&o&&(t.header=(0,a.default)((0,a.default)({},t.header),{},{__tenant:o[0].id})),new Promise((function(e,r){uni.request({url:t.url,method:t.method||"GET",data:t.data||{},header:(0,a.default)({Authorization:"Bearer "+uni.getStorageSync("token"),"Content-Type":"application/json"},t.header),success:function(t){200===t.statusCode?e(t.data):r(t.data)},fail:function(t){r(t)}})}))}},cb20:function(t,e,r){"use strict";r.d(e,"b",(function(){return a})),r.d(e,"c",(function(){return o})),r.d(e,"a",(function(){return n}));var n={svgIcon:r("8a0f").default},a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("v-uni-view",{staticClass:"product-select-page"},[r("v-uni-view",{staticClass:"search-area"},[r("v-uni-view",{staticClass:"search-box"},[r("svg-icon",{attrs:{name:"search",type:"svg",size:"28",color:"#999999"}}),r("v-uni-input",{attrs:{type:"text",placeholder:"搜索产品名称、编号等"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.getProductList.apply(void 0,arguments)}},model:{value:t.searchText,callback:function(e){t.searchText=e},expression:"searchText"}}),t.searchText?r("v-uni-view",{staticClass:"clear-icon",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clearSearch.apply(void 0,arguments)}}},[r("svg-icon",{attrs:{name:"close",type:"svg",size:"24",color:"#999999"}})],1):t._e()],1),r("v-uni-scroll-view",{staticClass:"category-scroll",attrs:{"scroll-x":!0,"show-scrollbar":"false"}},[r("v-uni-view",{staticClass:"category-tabs"},t._l(t.categories,(function(e,n){return r("v-uni-view",{key:n,staticClass:"category-item",class:{active:t.currentCategory===e.value},on:{click:function(r){arguments[0]=r=t.$handleEvent(r),t.selectCategory(e.value)}}},[t._v(t._s(e.name))])})),1)],1)],1),t.products.length>0?r("v-uni-scroll-view",{staticClass:"product-list-container",attrs:{"scroll-y":!0}},[r("v-uni-view",{staticClass:"product-grid"},t._l(t.products,(function(e,n){return r("v-uni-view",{key:n,staticClass:"product-list-item",on:{click:function(r){arguments[0]=r=t.$handleEvent(r),t.selectProduct(e)}}},[r("v-uni-view",{staticClass:"product-list-info"},[r("v-uni-view",{staticClass:"product-name"},[t._v(t._s(e.name))]),r("v-uni-view",{staticClass:"product-desc"},[t._v(t._s(e.description))]),r("v-uni-view",{staticClass:"product-meta"},[r("v-uni-text",{staticClass:"product-code"},[t._v("产品编号: "+t._s(e.code))]),r("v-uni-text",[t._v("产品类型："+t._s(e.productTypeText))])],1),r("v-uni-view",{staticClass:"product-meta"},[r("v-uni-text",{staticClass:"product-code"},[t._v("产品规格: "+t._s(e.spec))]),r("v-uni-text",[t._v("产品单位："+t._s(e.productUnit))])],1)],1)],1)})),1)],1):t._e(),0===t.products.length?r("v-uni-view",{staticClass:"empty-state"},[r("v-uni-view",{staticClass:"empty-icon"},[r("svg-icon",{attrs:{name:"search",type:"svg",size:"48"}})],1),r("v-uni-text",{staticClass:"empty-text"},[t._v("没有找到匹配的产品")])],1):t._e()],1)},o=[]},d86f:function(t,e,r){"use strict";r("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.getUserList=e.getProductList=e.getOpportunityList=e.getContactList=e.getCompanyList=e.getAgreementList=void 0;var n=r("c475");e.getContactList=function(t){return(0,n.request)({url:"/api/crm/contract/getList",method:"POST",data:t})};e.getCompanyList=function(){return(0,n.request)({url:"/api/crm/contract/getAllCompanys",method:"GET"})};e.getUserList=function(t){return(0,n.request)({url:"/api/Users/<USER>",method:"POST",data:t})};e.getOpportunityList=function(t){return(0,n.request)({url:"/api/crm/business/getKanbanList",method:"POST",data:t})};e.getAgreementList=function(t){return(0,n.request)({url:"/api/crm/contract/getAgreementList",method:"POST",data:t})};e.getProductList=function(t){return(0,n.request)({url:"/api/crm/product/getList",method:"POST",data:t})}}}]);
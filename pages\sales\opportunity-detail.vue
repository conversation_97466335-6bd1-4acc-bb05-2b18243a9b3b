<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <navigator url="./opportunity-list" open-type="navigateBack" class="back-button">
        <svg-icon name="arrow-left" type="svg" size="40"></svg-icon>
      </navigator>
      <view class="page-title">商机详情</view>
      <view class="header-actions">
<!--        <view class="action-button" @tap="shareOpportunity">
          <svg-icon name="share" type="svg" size="24"></svg-icon>
        </view>-->
        <view class="action-button" @tap="showMoreOptions">
          <svg-icon name="more" type="svg" size="24"></svg-icon>
        </view>
      </view>
    </view>
    <!-- 商机头部信息 -->
    <view class="opportunity-header">
      <view class="opportunity-title">{{ opportunityData.name }}</view>
      <view class="opportunity-company">
        <svg-icon name="building" type="svg" size="24" class="prev-icon"></svg-icon>
        <text>{{ opportunityData.customName }}</text>
      </view>
      <view class="opportunity-meta">
        <view class="meta-item">
          <svg-icon name="money" type="svg" size="24" class="prev-icon"></svg-icon>
          <text>{{ opportunityData.expectedTransAmount | price }}</text>
        </view>
        <view class="meta-item">
          <svg-icon name="calendar" type="svg" size="24" class="prev-icon"></svg-icon>
          <text>预计成单: {{ opportunityData.expectedCompleteDate | formatDateFilter }}</text>
        </view>
      </view>
      <view
        v-if="opportunityData.businessProcessId"
        class="opportunity-stage"
        :class="opportunityData.businessProcess === null ? 'stage-default' : 'stage-' + opportunityData.businessProcess.code"
      >
        {{ opportunityData.businessProcess.displayText }}
      </view>
      <view class="stage-progress">
        <view class="progress-bar" :style="{ width: opportunityData.expectedTransProbability }"></view>
      </view>
      <view class="stage-labels">
        <text v-for="item in stageOptions" :key="item.id">{{ item.displayText }}</text>
      </view>
    </view>
    <!-- 商机信息 -->
    <view class="info-section">
      <view class="section-title">
        <text>商机信息</text>
        <text class="action" @tap="editOpportunity">编辑</text>
      </view>
      <view class="detail-list">
        <view class="detail-label">商机来源</view>
        <view class="detail-value">{{ opportunityData.businessSource && opportunityData.businessSource.displayText | formatEmptyFilter }}</view>
        <view class="detail-label">商机类型</view>
        <view class="detail-value">{{ opportunityData.businessType && opportunityData.businessType.displayText | formatEmptyFilter }}</view>
        <view class="detail-label">优先级</view>
        <view class="detail-value">{{ opportunityData.businessPriority && opportunityData.businessPriority.displayText | formatEmptyFilter }}</view>
        <view class="detail-label">成交概率</view>
        <view class="detail-value">{{ opportunityData.expectedTransProbability | formatEmptyFilter }}</view>
        <view class="detail-label">负责人</view>
        <view class="detail-value">{{ opportunityData.owner }}</view>
        <view class="detail-label">创建时间</view>
        <view class="detail-value">{{ opportunityData.creationTime | formatDateFilter }}</view>
        <view class="detail-label">最近更新</view>
        <view class="detail-value">{{ opportunityData.lastModificationTime | formatDateFilter }}</view>
      </view>
    </view>
    <!-- 商机描述 -->
    <view class="info-section">
      <view class="section-title">
        <text>商机描述</text>
      </view>
      <text class="requirements-text">{{ opportunityData.description }}</text>
    </view>
    <!-- 联系人 -->
    <view class="info-section" v-if="opportunityData.contacts && opportunityData.contacts.length">
      <view class="section-title">
        <text>联系人</text>
<!--        <text class="action" @tap="addContact">添加</text>-->
      </view>
      <view class="contact-list">
        <view 
          class="contact-item" 
          v-for="(item, index) in opportunityData.contacts"
          :key="index"
        >
          <view class="contact-avatar">{{ item.contact.name.charAt(0) }}</view>
          <view class="contact-info">
            <view class="contact-name">{{ item.contact.name }}</view>
            <view class="contact-role">{{ item.contact.position }}</view>
          </view>
          <view class="contact-actions">
            <view class="contact-action" @tap="callContact(item.contact.telephone)">
              <svg-icon name="phone" type="svg" size="16"></svg-icon>
            </view>
<!--            <view class="contact-action" @tap="emailContact(contact.email)">
              <svg-icon name="mail" type="svg" size="16"></svg-icon>
            </view>-->
          </view>
        </view>
      </view>
    </view>
    <!-- 活动记录 -->
<!--    <view class="info-section notes-container">
      <view class="section-title">
        <text>活动记录</text>
        <text class="action" @tap="addActivity">添加</text>
      </view>
      <view class="activity-timeline">
        <view 
          class="activity-item" 
          v-for="(activity, index) in opportunityData.activities" 
          :key="index"
        >
          <view class="activity-icon" :class="activity.type">
            <svg-icon v-if="activity.type === 'call'" name="phone" type="svg" size="16"></svg-icon>
            <svg-icon v-else-if="activity.type === 'email'" name="mail" type="svg" size="16"></svg-icon>
            <svg-icon v-else-if="activity.type === 'note'" name="file-list" type="svg" size="16"></svg-icon>
            <svg-icon v-else name="time" type="svg" size="16"></svg-icon>
          </view>
          <view class="activity-content">
            <view class="activity-title">{{ activity.title }}</view>
            <view class="activity-meta">
              {{ activity.creator }} · {{ activity.time }}
            </view>
            <view class="activity-description" v-if="activity.description">
              {{ activity.description }}
            </view>
          </view>
        </view>
      </view>
    </view>-->
    <!-- 沟通记录 -->
<!--    <view class="info-section communication-container">
      <view class="section-title">
        <text>沟通记录</text>
        <text class="action" @tap="addCommunication">添加</text>
      </view>
      <view class="communication-timeline" v-if="communicationRecords.length > 0">
        <view 
          class="communication-item" 
          v-for="(record, index) in communicationRecords" 
          :key="index"
          @tap="viewCommunicationDetail(record.id)"
          hover-class="item-hover"
        >
          <view class="communication-icon" :class="record.type">
            <svg-icon v-if="record.type === 'call'" name="phone" type="svg" size="16"></svg-icon>
            <svg-icon v-else-if="record.type === 'meeting'" name="team" type="svg" size="16"></svg-icon>
            <svg-icon v-else-if="record.type === 'email'" name="mail" type="svg" size="16"></svg-icon>
            <svg-icon v-else-if="record.type === 'visit'" name="navigation" type="svg" size="16"></svg-icon>
            <svg-icon v-else name="message" type="svg" size="16"></svg-icon>
          </view>
          <view class="communication-content">
            <view class="communication-header">
              <view class="communication-title">{{ record.subject }}</view>
              <view class="communication-type">{{ getTypeName(record.type) }}</view>
            </view>
            <view class="communication-meta">
              {{ record.creator }} · {{ record.time }}
            </view>
            <view class="communication-description" v-if="record.content">
              {{ record.content }}
            </view>
            <view class="tag-container" v-if="record.tags && record.tags.length > 0">
              <text class="tag" v-for="(tag, tagIndex) in record.tags" :key="tagIndex">{{ tag }}</text>
            </view>
          </view>
        </view>
      </view>
      <view class="empty-state" v-else>
        <svg-icon name="message" type="svg" size="32"></svg-icon>
        <text class="empty-text">暂无沟通记录</text>
        <view class="empty-action" @tap="addCommunication">
          <text>添加沟通记录</text>
        </view>
      </view>
    </view>-->
    <!-- 底部操作栏 -->
    <view class="action-bar">
<!--      <button class="btn btn-outline" @tap="arrangeActivity">
        <svg-icon name="calendar" type="svg" size="16"></svg-icon>
        <text>安排活动</text>
      </button>-->
      <button class="btn btn-primary" @tap="editOpportunity">
        <svg-icon name="edit" type="svg" color="#FFFFFF" size="20"></svg-icon>
        <text>编辑商机</text>
      </button>
    </view>
  </view>
</template>

<script>
import { getBusinessDetail, deleteBusiness } from '@/api/business.api';
import getSelectOptions from "@/utils/dictionary";

export default {
  data() {
    return {
      opportunityId: '',
      stageOptions: [],
      opportunityData: {
        businessProcess: {},
        businessSource: {},
        businessType: {},
        businessPriority: {},
        // activities: [
        //   {
        //     id: '201',
        //     type: 'call',
        //     icon: 'icon-phone',
        //     title: '电话沟通',
        //     creator: '李销售',
        //     time: '2023-10-15 14:30',
        //     description: '与张经理通话，讨论了方案细节和价格调整可能性。客户表示将在内部讨论后给出反馈。'
        //   },
        //   {
        //     id: '202',
        //     type: 'email',
        //     icon: 'icon-mail',
        //     title: '邮件沟通',
        //     creator: '李销售',
        //     time: '2023-10-10 09:15',
        //     description: '发送详细解决方案文档给王总监，包含技术架构、实施时间表和价格明细。'
        //   },
        //   {
        //     id: '203',
        //     type: 'note',
        //     icon: 'icon-file-list',
        //     title: '会议记录',
        //     creator: '李销售',
        //     time: '2023-10-05 10:00',
        //     description: '线下拜访客户，进行了详细的需求调研。客户对数据安全和系统稳定性有较高要求，需要在方案中重点体现这两部分。'
        //   },
        //   {
        //     id: '204',
        //     type: '',
        //     icon: 'icon-time',
        //     title: '创建商机',
        //     creator: '系统',
        //     time: '2023-09-28 16:45'
        //   }
        // ]
      },
      // 沟通记录数据
      communicationRecords: [
        {
          id: '1001',
          type: 'call',
          subject: '产品功能确认电话',
          creator: '李销售',
          time: '2023-10-20 15:30',
          content: '与张经理通话30分钟，讨论了产品功能细节和交付时间。客户关心数据安全问题，需要准备详细的技术方案。',
          tags: ['跟进中', '重要']
        },
        {
          id: '1002',
          type: 'meeting',
          subject: '需求沟通会议',
          creator: '李销售',
          time: '2023-10-15 10:00',
          content: '与客户IT团队进行了详细的需求沟通，明确了系统架构和主要功能模块。客户对交付时间表有明确要求，需在12月前完成初版。',
          tags: ['需求确认']
        },
        {
          id: '1003',
          type: 'email',
          subject: '方案文档发送',
          creator: '王产品经理',
          time: '2023-10-10 09:30',
          content: '向客户发送了完整的解决方案文档，包含技术架构、实施计划和报价。',
          tags: ['方案阶段']
        }
      ]
    }
  },
  methods: {
    // 加载商机数据
    loadOpportunityData(id) {
      getBusinessDetail(id).then(res => {
        Object.assign(this.opportunityData, res)
      })
    },
    // 获取数据字典
    async loadDictionaryOptions() {
      try {
        this.stageOptions = await getSelectOptions('BusinessProcess')
        this.stageOptions.pop()
        this.stageOptions.pop()
      } catch (error) {
        this.$message.error('加载字典数据失败');
      }
    },
    // 显示更多选项
    showMoreOptions() {
      uni.showActionSheet({
        itemList: ['删除商机'],
        success: (res) => {
          switch(res.tapIndex) {
            case 0:
              this.confirmDeleteOpportunity()
              break
            // default:
            //   uni.showToast({
            //     title: '功能开发中',
            //     icon: 'none'
            //   })
          }
        }
      })
    },
    // 确认删除商机
    confirmDeleteOpportunity() {
      uni.showModal({
        title: '删除商机',
        content: '确定要删除此商机吗？删除后无法恢复！',
        confirmColor: '#f56c6c',
        success: (res) => {
          if (res.confirm) {
            deleteBusiness(this.opportunityId).then(res => {
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
              uni.navigateBack();
            })
          }
        }
      })
    },
    // 编辑商机
    editOpportunity() {
      uni.navigateTo({
        url: `./opportunity-edit?id=${this.opportunityId}`
      })
    },
    // 添加联系人
    addContact() {
      uni.showToast({
        title: '添加联系人功能开发中',
        icon: 'none'
      })
    },
    // 拨打电话
    callContact(phone) {
      if (!phone) return
      uni.makePhoneCall({
        phoneNumber: phone,
        fail: () => {
          uni.showToast({
            title: '拨号取消',
            icon: 'none'
          })
        }
      })
    },
    // 发送邮件
    emailContact(email) {
      if (!email) return
      uni.showToast({
        title: '邮件功能开发中',
        icon: 'none'
      })
    },
    // 分享商机
    shareOpportunity() {
      uni.showActionSheet({
        itemList: ['分享给同事', '导出PDF', '发送邮件'],
        success: (res) => {
          uni.showToast({
            title: '分享功能开发中',
            icon: 'none'
          })
        }
      })
    },
    // 添加活动记录
    addActivity() {
      uni.navigateTo({
        url: `/pages/actions/action-create?type=activity&relatedId=${this.opportunityId}&relatedType=opportunity&relatedName=${encodeURIComponent(this.opportunityData.title)}`
      })
    },
    // 安排活动
    arrangeActivity() {
      uni.navigateTo({
        url: `/pages/actions/action-create?type=activity&relatedId=${this.opportunityId}&relatedType=opportunity&relatedName=${encodeURIComponent(this.opportunityData.title)}`
      })
    },
    // 添加沟通记录
    addCommunication() {
      uni.navigateTo({
        url: `/pages/interactions/interaction-create?type=communication&relatedId=${this.opportunityId}&relatedType=opportunity&relatedName=${encodeURIComponent(this.opportunityData.title)}`
      })
    },
    // 查看沟通记录详情
    viewCommunicationDetail(id) {
      uni.navigateTo({
        url: `/pages/interactions/interaction-detail?id=${id}`
      })
    },
    // 加载与商机相关的沟通记录
    loadCommunicationRecords() {
      console.log('加载商机相关的沟通记录')
    },
    // 获取沟通记录类型名称
    getTypeName(type) {
      const typeMap = {
        call: '电话',
        meeting: '会议',
        email: '邮件',
        visit: '拜访',
        message: '消息'
      }
      return typeMap[type] || '未知'
    }
  },
  onLoad(options) {
    if (options.id) {
      this.opportunityId = options.id
      this.loadDictionaryOptions()
      this.loadOpportunityData(this.opportunityId)
      // this.loadCommunicationRecords()
    }
  }
}
</script>

<style lang="scss">
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1rpx solid var(--border-color);
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.page-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
}

.back-button {
  color: var(--text-secondary);
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.action-button {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  color: var(--text-secondary);
  background-color: var(--light-color);
  border: 1rpx solid var(--border-color);
}

.opportunity-header {
  background-color: #ffffff;
  padding: var(--spacing-lg);
  border-bottom: 1rpx solid var(--border-color);
}

.opportunity-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}
.opportunity-company {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
}

.prev-icon {
  margin-right: 8rpx;
}

.opportunity-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-right: 20rpx;
}

.opportunity-stage {
  display: inline-block;
  padding: 8rpx 24rpx;
  border-radius: var(--radius-full);
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: var(--spacing-md);
  &.stage-Survey {
    background-color: #e0e7ff;
    color: #4f46e5;
  }
  &.stage-Proposal {
    background-color: #fef3c7;
    color: #d97706;
  }
  &.stage-Quote {
    background-color: #dbeafe;
    color: #2563eb;
  }
  &.stage-Negotiate {
    background-color: #fee2e2;
    color: #dc2626;
  }
  &.stage-Win {
    background-color: #d1fae5;
    color: #059669;
  }
  &.stage-Lose, &.stage-Cancel, &.stage-default {
    background-color: #e5e7eb;
    color: #6b7280;
  }
}

.stage-progress {
  width: 100%;
  height: 12rpx;
  background-color: var(--border-color);
  border-radius: var(--radius-full);
  margin-bottom: var(--spacing-sm);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: var(--primary-color);
  border-radius: var(--radius-full);
}

.stage-labels {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: var(--text-secondary);
}

.info-section {
  background-color: #ffffff;
  margin: var(--spacing-md) 0;
  padding: var(--spacing-lg);
  border-top: 1rpx solid var(--border-color);
  border-bottom: 1rpx solid var(--border-color);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-title .action {
  font-size: 28rpx;
  font-weight: normal;
  color: var(--primary-color);
}

.detail-list {
  display: grid;
  grid-template-columns: 200rpx 1fr;
  row-gap: var(--spacing-sm);
}

.detail-label {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 500;
}

.requirements-text {
  font-size: 28rpx;
  line-height: 1.5;
  color: var(--text-secondary);
}

.activity-item {
  display: flex;
  margin-bottom: var(--spacing-md);
  position: relative;
}

.activity-item:not(:last-child)::before {
  content: '';
  position: absolute;
  top: 48rpx;
  left: 24rpx;
  width: 2rpx;
  height: calc(100% + var(--spacing-md));
  background-color: var(--border-color);
  z-index: 1;
}

.activity-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: var(--radius-full);
  background-color: var(--primary-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  position: relative;
  z-index: 2;
  flex-shrink: 0;
}

.activity-icon.call {
  background-color: #fef3c7;
  color: #d97706;
}

.activity-icon.note {
  background-color: #e0e7ff;
  color: #4f46e5;
}

.activity-icon.email {
  background-color: #dbeafe;
  color: #2563eb;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 4rpx;
  color: var(--text-primary);
}

.activity-meta {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
}

.activity-description {
  font-size: 28rpx;
  color: var(--text-secondary);
  background-color: var(--light-color);
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  margin-top: var(--spacing-xs);
}

.contact-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1rpx solid var(--border-color);
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: var(--radius-full);
  background-color: var(--primary-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  font-weight: bold;
  flex-shrink: 0;
}

.contact-info {
  flex: 1;
}

.contact-name {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
}

.contact-role {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.contact-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.contact-action {
  width: 64rpx;
  height: 64rpx;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  background-color: var(--light-color);
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: var(--spacing-md);
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-md);
  border-top: 1rpx solid var(--border-color);
  z-index: 10;
}

.action-bar .btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
}

.action-bar .btn text:first-child {
  margin-right: 8rpx;
}

.notes-container {
  margin-bottom: 180rpx;
}

.communication-container {
  margin-bottom: 180rpx;
}

.communication-timeline {
  width: 100%;
}

.communication-item {
  display: flex;
  align-items: flex-start;
  padding: var(--spacing-md) 0;
  border-bottom: 1rpx solid var(--border-color);
  transition: background-color 0.2s;
}

.communication-item:last-child {
  border-bottom: none;
}

.communication-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: var(--radius-full);
  background-color: var(--primary-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  position: relative;
  z-index: 2;
  flex-shrink: 0;
}

.communication-icon.call {
  background-color: #dbeafe;
  color: #2563eb;
}

.communication-icon.meeting {
  background-color: #e0e7ff;
  color: #4f46e5;
}

.communication-icon.email {
  background-color: #fef3c7;
  color: #d97706;
}

.communication-icon.visit {
  background-color: #d1fae5;
  color: #059669;
}

.communication-content {
  flex: 1;
}

.communication-header {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  margin-bottom: 4rpx;
}

.communication-title {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
}

.communication-type {
  font-size: 24rpx;
  color: var(--text-secondary);
  background-color: var(--light-color);
  padding: 2rpx 12rpx;
  border-radius: var(--radius-full);
}

.communication-meta {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-bottom: 12rpx;
}

.communication-description {
  font-size: 28rpx;
  color: var(--text-secondary);
  background-color: var(--light-color);
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-sm);
  line-height: 1.5;
}

.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.tag {
  padding: 4rpx 12rpx;
  border-radius: var(--radius-full);
  background-color: var(--primary-light);
  color: var(--primary-color);
  font-size: 22rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-md);
  margin-top: var(--spacing-md);
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

.empty-action {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-full);
  background-color: var(--primary-color);
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 500;
}

.item-hover {
  background-color: var(--primary-light);
}
</style> 
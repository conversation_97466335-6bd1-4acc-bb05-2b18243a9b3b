<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="goBack">
        <svg-icon name="arrow-left" type="svg" size="24"></svg-icon>
      </view>
      <text class="page-title">创建报价单</text>
      <view class="header-actions">
        <button class="save-button" @click="saveQuotation">保存</button>
      </view>
    </view>

    <scroll-view scroll-y class="form-scroll">
      <!-- 基本信息 -->
      <view class="form-section">
        <text class="section-title">基本信息</text>
        
        <view class="form-group">
          <text class="form-label">报价单名称 <text class="required">*</text></text>
          <input 
            type="text" 
            class="form-input" 
            placeholder="请输入报价单名称" 
            v-model="quotation.title"
          />
        </view>
        
        <view class="form-group">
          <text class="form-label">报价单编号</text>
          <input 
            type="text" 
            class="form-input" 
            placeholder="自动生成" 
            disabled
            v-model="quotation.code"
          />
        </view>
        
        <view class="form-group">
          <text class="form-label">有效期至 <text class="required">*</text></text>
          <view class="date-picker">
            <picker 
              mode="date" 
              :value="quotation.validUntil" 
              @change="onValidUntilChange"
              class="form-picker"
            >
              <view class="picker-value">
                {{ quotation.validUntil || '请选择日期' }}
              </view>
            </picker>
            <svg-icon name="calendar" type="svg" size="20"></svg-icon>
          </view>
        </view>
        
        <view class="form-group">
          <text class="form-label">负责人 <text class="required">*</text></text>
          <picker 
            mode="selector" 
            :range="owners.map(item => item.name)" 
            @change="onOwnerChange"
            class="form-picker"
          >
            <view class="picker-value">
              {{ selectedOwner ? selectedOwner.name : '请选择负责人' }}
            </view>
          </picker>
        </view>
        
        <view class="form-group">
          <text class="form-label">状态</text>
          <picker 
            mode="selector" 
            :range="statuses.map(item => item.name)" 
            @change="onStatusChange"
            class="form-picker"
          >
            <view class="picker-value">
              {{ selectedStatus ? selectedStatus.name : '请选择状态' }}
            </view>
          </picker>
        </view>
      </view>
      
      <!-- 客户信息 -->
      <view class="form-section">
        <text class="section-title">客户信息</text>
        
        <view class="form-group">
          <text class="form-label">客户 <text class="required">*</text></text>
          <view class="customer-select" @click="selectCustomer">
            <text class="customer-name">{{ selectedCustomer && selectedCustomer.name || '请选择客户' }}</text>
            <svg-icon name="arrow-right" type="svg" size="18"></svg-icon>
          </view>
        </view>
        
        <view class="customer-details" v-if="selectedCustomer">
          <view class="customer-info-item">
            <text class="info-label">联系人</text>
            <text class="info-value">{{ selectedCustomer.contact || '未指定' }}</text>
          </view>
          <view class="customer-info-item">
            <text class="info-label">电话</text>
            <text class="info-value">{{ selectedCustomer.phone || '未提供' }}</text>
          </view>
          <view class="customer-info-item">
            <text class="info-label">邮箱</text>
            <text class="info-value">{{ selectedCustomer.email || '未提供' }}</text>
          </view>
        </view>
        
        <view class="form-group">
          <text class="form-label">关联商机</text>
          <view class="opportunity-select" @click="selectOpportunity">
            <text class="opportunity-name">{{ selectedOpportunity && selectedOpportunity.name || '选择关联商机（可选）' }}</text>
            <svg-icon name="arrow-right" type="svg" size="18"></svg-icon>
          </view>
        </view>
      </view>
      
      <!-- 产品与服务 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">产品与服务</text>
          <button class="add-button" @click="addProduct">
            <svg-icon name="add" type="svg" size="18"></svg-icon>
            <text>添加产品</text>
          </button>
        </view>
        
        <view class="empty-products" v-if="quotation.items.length === 0">
          <svg-icon name="shopping" type="svg" size="36"></svg-icon>
          <text class="empty-text">暂无产品</text>
          <text class="empty-desc">点击"添加产品"按钮添加产品或服务项目</text>
        </view>
        
        <view class="product-list" v-else>
          <view 
            class="product-item" 
            v-for="(item, index) in quotation.items" 
            :key="index"
          >
            <view class="product-header">
              <text class="product-name">{{ item.name }}</text>
              <view class="product-actions">
                <view class="action-icon" @click="editProduct(index)">
                  <svg-icon name="edit" type="svg" size="18"></svg-icon>
                </view>
                <view class="action-icon" @click="removeProduct(index)">
                  <svg-icon name="delete-bin" type="svg" size="18"></svg-icon>
                </view>
              </view>
            </view>
            
            <view class="product-details">
              <view class="product-price">¥{{ formatPrice(item.amount) }}</view>
              <view class="product-description" v-if="item.description">{{ item.description }}</view>
            </view>
          </view>
          
          <view class="product-total">
            <text class="total-label">总计</text>
            <text class="total-amount">¥{{ formatPrice(calculateTotal()) }}</text>
          </view>
        </view>
      </view>
      
      <!-- 备注信息 -->
      <view class="form-section">
        <text class="section-title">备注说明</text>
        <view class="form-group">
          <textarea 
            class="form-textarea" 
            placeholder="请输入备注信息" 
            v-model="quotation.remark"
          ></textarea>
        </view>
      </view>
    </scroll-view>
    
    <!-- 自定义TabBar组件 -->
    <custom-tab-bar ref="customTabBar"></custom-tab-bar>

    <view class="submit-bar">
      <button class="save-button" @click="saveQuotation">保存</button>
    </view>
  </view>
</template>

<script>
import CustomTabBar from '@/components/CustomTabBar.vue';
import SvgIcon from '@/components/svg-icon.vue';

export default {
  components: {
    CustomTabBar,
    SvgIcon
  },
  data() {
    return {
      quotation: {
        title: '',
        code: '自动生成',
        validUntil: this.getDefaultValidUntil(),
        owner: '',
        status: 'draft',
        customer: '',
        opportunity: '',
        items: [],
        remark: ''
      },
      selectedCustomer: null,
      selectedOpportunity: null,
      selectedOwner: null,
      selectedStatus: null,
      
      owners: [
        { id: '1', name: '李销售' },
        { id: '2', name: '王经理' },
        { id: '3', name: '张总监' }
      ],
      statuses: [
        { code: 'draft', name: '草稿' },
        { code: 'sent', name: '已发送' },
        { code: 'accepted', name: '已接受' },
        { code: 'rejected', name: '已拒绝' }
      ]
    }
  },
  created() {
    // 设置默认值
    this.selectedOwner = this.owners[0];
    this.quotation.owner = this.owners[0].id;
    
    this.selectedStatus = this.statuses[0];
    this.quotation.status = this.statuses[0].code;
  },
  onLoad(options) {
    // 如果有传入商机参数，自动填充相关信息
    if (options.opportunityId && options.opportunityName) {
      // 设置商机信息
      this.selectedOpportunity = {
        id: options.opportunityId,
        name: decodeURIComponent(options.opportunityName)
      };
      this.quotation.opportunity = options.opportunityId;
      
      // 设置报价单默认标题（基于商机名称）
      this.quotation.title = decodeURIComponent(options.opportunityName) + ' - 报价方案';
      
      // 如果有客户信息，也一并设置
      if (options.customerId && options.customerName) {
        this.selectedCustomer = {
          id: options.customerId,
          name: decodeURIComponent(options.customerName)
        };
        this.quotation.customer = options.customerId;
        
        // 模拟获取客户详细信息
        setTimeout(() => {
          // 实际项目中应该通过API获取客户详情
          this.selectedCustomer = {
            ...this.selectedCustomer,
            contact: '联系人', // 示例数据
            phone: '13800138000', // 示例数据
            email: '<EMAIL>' // 示例数据
          };
        }, 300);
      }
    }
  },
  methods: {
    formatPrice(price) {
      return price.toLocaleString('zh-CN');
    },
    getDefaultValidUntil() {
      // 默认有效期为当前日期后30天
      const date = new Date();
      date.setDate(date.getDate() + 30);
      return date.toISOString().split('T')[0]; // 格式化为 YYYY-MM-DD
    },
    calculateTotal() {
      return this.quotation.items.reduce((total, item) => total + item.amount, 0);
    },
    goBack() {
      uni.navigateBack();
    },
    onValidUntilChange(e) {
      this.quotation.validUntil = e.detail.value;
    },
    onOwnerChange(e) {
      const index = e.detail.value;
      this.selectedOwner = this.owners[index];
      this.quotation.owner = this.owners[index].id;
    },
    onStatusChange(e) {
      const index = e.detail.value;
      this.selectedStatus = this.statuses[index];
      this.quotation.status = this.statuses[index].code;
    },
    selectCustomer() {
      uni.navigateTo({
        url: '/pages/sales/customer-select?mode=select',
        events: {
          // 获取被选择的客户
          selectCustomer: (customer) => {
            this.selectedCustomer = customer;
            this.quotation.customer = customer.id;
          }
        }
      });
    },
    selectOpportunity() {
      if (!this.selectedCustomer) {
        uni.showToast({
          title: '请先选择客户',
          icon: 'none'
        });
        return;
      }
      
      uni.navigateTo({
        url: `/pages/sales/opportunity-select?customerId=${this.selectedCustomer.id}`,
        events: {
          // 获取被选择的商机
          selectOpportunity: (opportunity) => {
            this.selectedOpportunity = opportunity;
            this.quotation.opportunity = opportunity.id;
          }
        }
      });
    },
    addProduct() {
      uni.navigateTo({
        url: '/pages/sales/product-select?mode=select',
        events: {
          // 获取被选择的产品
          selectProduct: (product) => {
            // 添加产品到列表
            this.quotation.items.push({
              id: product.id,
              name: product.name,
              amount: product.price,
              description: product.description
            });
          }
        }
      });
    },
    editProduct(index) {
      // 存储当前产品数据到临时变量，用于产品编辑页面
      const product = this.quotation.items[index];
      uni.navigateTo({
        url: `/pages/sales/product-edit?mode=edit&index=${index}`,
        events: {
          // 获取编辑后的产品
          updateProduct: (updatedProduct, index) => {
            this.quotation.items[index] = updatedProduct;
          }
        },
        success: (res) => {
          // 传递当前产品数据到编辑页面
          res.eventChannel.emit('productData', { product, index });
        }
      });
    },
    removeProduct(index) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除该产品吗？',
        success: (res) => {
          if (res.confirm) {
            this.quotation.items.splice(index, 1);
          }
        }
      });
    },
    validateForm() {
      if (!this.quotation.title.trim()) {
        uni.showToast({
          title: '请输入报价单名称',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.quotation.validUntil) {
        uni.showToast({
          title: '请选择有效期',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.selectedCustomer) {
        uni.showToast({
          title: '请选择客户',
          icon: 'none'
        });
        return false;
      }
      
      if (this.quotation.items.length === 0) {
        uni.showToast({
          title: '请至少添加一个产品或服务',
          icon: 'none'
        });
        return false;
      }
      
      return true;
    },
    saveQuotation() {
      if (!this.validateForm()) return;
      
      // 实际项目中应该调用API保存报价单
      uni.showLoading({
        title: '保存中...'
      });
      
      // 模拟保存延迟
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '保存成功',
          icon: 'success',
          success: () => {
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          }
        });
      }, 1000);
    }
  },
  onShow() {
    // 设置TabBar当前选中项
    if (typeof this.$refs.customTabBar !== 'undefined') {
      this.$refs.customTabBar.current = 2; // 对应"销售"菜单
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  height: 44px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  width: 100%;
  box-sizing: border-box;
}

.page-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.back-button {
  color: #666;
  display: flex;
  align-items: center;
}

.save-button {
  background-color: #3a86ff;
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  border: none;
}

.form-scroll {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  padding-bottom: 100px;
}

.form-section {
  margin: 16px;
  padding: 12px;
  background-color: #fff;
  border-radius: 8px;
  position: relative;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.add-button {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  color: #333;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  font-size: 14px;
  margin-bottom: 8px;
  display: block;
  color: #333;
}

.required {
  color: #dc2626;
}

.form-input {
  width: 100%;
  box-sizing: border-box;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  color: #333;
  background-color: #fff;
}

.form-textarea {
  width: 100%;
  box-sizing: border-box;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  color: #333;
  height: 120px;
  background-color: #fff;
}

.date-picker {
  position: relative;
  display: flex;
  align-items: center;
}

.form-picker {
  width: 100%;
  box-sizing: border-box;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  color: #333;
  position: relative;
  background-color: #fff;
}

.picker-value {
  padding-right: 24px;
  color: #333;
}

.date-picker svg-icon {
  position: absolute;
  right: 12px;
  color: #666;
  pointer-events: none;
}

.customer-select, .opportunity-select {
  width: 100%;
  box-sizing: border-box;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
}

.customer-name, .opportunity-name {
  color: #333;
}

.customer-details {
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 8px;
  margin: 8px 0 16px;
  border: 1px solid #eee;
}

.customer-info-item {
  margin-bottom: 8px;
  display: flex;
  flex-direction: column;
}

.customer-info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.info-value {
  font-size: 14px;
  color: #333;
}

.empty-products {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 0;
  color: #999;
}

.empty-text {
  font-size: 16px;
  margin: 12px 0 8px;
}

.empty-desc {
  font-size: 14px;
  color: #999;
}

.product-list {
  margin: 16px 0;
}

.product-item {
  margin-bottom: 12px;
  padding: 12px;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.product-name {
  font-size: 15px;
  font-weight: 500;
  color: #333;
}

.product-actions {
  display: flex;
  gap: 8px;
}

.action-icon {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.product-details {
  display: flex;
  flex-direction: column;
}

.product-price {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.product-description {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}

.product-total {
  display: flex;
  justify-content: space-between;
  padding: 12px;
  margin-top: 8px;
  background-color: #f5f5f5;
  border-radius: 8px;
  border: 1px solid #eee;
}

.total-label {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.total-amount {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.submit-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10px 16px;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
  z-index: 100;
}
</style> 
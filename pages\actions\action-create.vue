<template>
  <view class="page">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @tap="navBack">
        <svg-icon name="arrow-left" type="svg" size="24"></svg-icon>
      </view>
      <text class="page-title">创建行动计划</text>
      <view class="header-spacer"></view>
    </view>

    <scroll-view scroll-y class="page-container">
      <!-- 行动类型选择区域 -->
      <view class="form-section">
        <view class="section-header">
          <svg-icon name="information" type="svg" size="20"></svg-icon>
          <text class="section-title">行动类型</text>
        </view>
        <view class="section-content">
          <view class="type-selector">
            <view 
              v-for="(type, index) in actionTypes" 
              :key="index" 
              class="type-option" 
              :class="{ selected: formData.type === type.value }"
              @tap="formData.type = type.value"
            >
              <svg-icon 
                :name="type.icon" 
                :type="type.iconType || 'svg'" 
                size="24"
                :color="formData.type === type.value ? '#3370ff' : ''"
              ></svg-icon>
              <text>{{type.label}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 基本信息区域 -->
      <view class="form-section">
        <view class="section-header">
          <svg-icon name="file-list" type="svg" size="20"></svg-icon>
          <text class="section-title">基本信息</text>
        </view>
        <view class="section-content">
          <view class="form-group">
            <text class="form-label required">标题</text>
            <input 
              type="text" 
              class="form-control" 
              v-model="formData.title" 
              placeholder="请输入标题"
            />
          </view>
          
          <view class="form-group" v-if="formData.type === 'task'">
            <text class="form-label">优先级</text>
            <view class="priority-group">
              <view 
                v-for="(priority, index) in priorities" 
                :key="index"
                :class="['priority-item', priority.value, { active: formData.priority === priority.value }]"
                @tap="formData.priority = priority.value"
              >
                <text>{{ priority.label }}</text>
              </view>
            </view>
          </view>
          
          <view class="form-group">
            <text class="form-label required">日期</text>
            <view class="date-picker">
              <picker 
                mode="date" 
                :value="formData.date" 
                @change="onDateChange"
                :start="startDate"
              >
                <view class="uni-input">
                  {{formData.date}}
                  <view class="input-icon">
                    <svg-icon name="calendar" type="svg" size="20"></svg-icon>
                  </view>
                </view>
              </picker>
            </view>
          </view>
          
          <view class="form-group">
            <text class="form-label">时间</text>
            <view class="date-picker">
              <picker 
                mode="time" 
                :value="formData.time" 
                @change="onTimeChange"
              >
                <view class="uni-input">
                  {{formData.time}}
                  <view class="input-icon">
                    <svg-icon name="clock" type="svg" size="20"></svg-icon>
                  </view>
                </view>
              </picker>
            </view>
          </view>
          
          <view class="toggle-switch" v-if="formData.type !== 'task'">
            <view class="toggle-label">设置提醒</view>
            <switch 
              :checked="formData.hasReminder" 
              @change="onReminderChange" 
              color="#3370ff"
            />
          </view>
          
          <view class="form-group" v-if="formData.hasReminder && formData.type !== 'task'">
            <picker 
              :value="reminderIndex" 
              :range="reminderOptions.map(item => item.label)" 
              @change="onReminderTimeChange"
              mode="selector"
            >
              <view class="form-select">
                <view class="uni-input">{{reminderOptions[reminderIndex].label}}
                  <view class="input-icon">
                    <svg-icon name="notification" type="svg" size="20"></svg-icon>
                  </view>
                </view>
              </view>
            </picker>
          </view>
        </view>
      </view>
      
      <!-- 关联信息区域 -->
      <view class="form-section">
        <view class="section-header">
          <svg-icon name="link" type="svg" size="20"></svg-icon>
          <text class="section-title">关联信息</text>
        </view>
        <view class="section-content">
          <view class="form-group">
            <text class="form-label">关联对象</text>
            <picker @change="onRelatedTypeChange" :value="relatedTypeIndex" :range="relatedTypes" range-key="label">
              <view class="uni-input">
                {{relatedTypes[relatedTypeIndex].label}}
                <view class="input-icon">
                  <svg-icon name="down" type="svg" size="20"></svg-icon>
                </view>
              </view>
            </picker>
          </view>
          
          <view class="form-group">
            <text class="form-label">关联内容</text>
            <input 
              type="text" 
              class="form-control" 
              v-model="formData.related" 
              placeholder="输入或选择关联内容"
            />
          </view>
        </view>
      </view>
      
      <!-- 详情描述区域 -->
      <view class="form-section">
        <view class="section-header">
          <svg-icon name="align-left" type="svg" size="20"></svg-icon>
          <text class="section-title">详情描述</text>
        </view>
        <view class="section-content">
          <view class="form-group">
            <textarea 
              class="form-control" 
              v-model="formData.description" 
              placeholder="请输入详情描述..."
            />
          </view>
        </view>
      </view>
      
      <!-- 参与人区域 (仅非任务类型) -->
      <view class="form-section" v-if="formData.type !== 'task'">
        <view class="section-header">
          <svg-icon name="user-group" type="svg" size="20"></svg-icon>
          <text class="section-title">参与人</text>
        </view>
        <view class="section-content">
          <view class="add-participant" @tap="showParticipantModal">
            <svg-icon name="user-add" type="svg" size="20"></svg-icon>
            <text>添加参与人</text>
          </view>
        </view>
      </view>
      
      <!-- 底部占位 -->
      <view class="bottom-spacer"></view>
    </scroll-view>
    
    <!-- 底部操作栏 -->
    <view class="action-bar">
      <button class="btn btn-outline" @tap="navBack">取消</button>
      <button class="btn btn-primary" @tap="saveAction">保存</button>
    </view>
  </view>
</template>

<script>
import SvgIcon from '@/components/svg-icon.vue';

export default {
  components: {
    SvgIcon
  },
  data() {
    const now = new Date();
    
    return {
      formData: {
        type: 'task', // 默认为任务类型
        title: '',
        priority: 'medium',
        date: this.formatDate(now),
        time: this.formatTime(now),
        related: '',
        relatedType: 'company',
        description: '',
        hasReminder: false,
        reminderTime: 15
      },
      actionTypes: [
        { label: '任务', value: 'task', icon: 'check-circle' },
        { label: '电话', value: 'call', icon: 'phone' },
        { label: '会议', value: 'meeting', icon: 'team' },
        { label: '拜访', value: 'visit', icon: 'navigation' },
        { label: '邮件', value: 'email', icon: 'mail' }
      ],
      priorities: [
        { label: '低', value: 'low' },
        { label: '中', value: 'medium' },
        { label: '高', value: 'high' }
      ],
      reminderOptions: [
        { label: '活动开始前5分钟', value: 5 },
        { label: '活动开始前15分钟', value: 15 },
        { label: '活动开始前30分钟', value: 30 },
        { label: '活动开始前1小时', value: 60 },
        { label: '活动开始前2小时', value: 120 },
        { label: '活动开始前1天', value: 1440 }
      ],
      reminderIndex: 1, // 默认选择15分钟
      relatedTypes: [
        { label: '公司', value: 'company' },
        { label: '联系人', value: 'contact' },
        { label: '商机', value: 'opportunity' },
        { label: '合同', value: 'contract' },
        { label: '会议', value: 'meeting' },
        { label: '报表', value: 'report' }
      ],
      relatedTypeIndex: 0, // 默认选择公司
      startDate: this.formatDate(now)
    }
  },
  methods: {
    navBack() {
      uni.navigateBack();
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    formatTime(date) {
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${hours}:${minutes}`;
    },
    onDateChange(e) {
      this.formData.date = e.detail.value;
    },
    onTimeChange(e) {
      this.formData.time = e.detail.value;
    },
    onReminderChange(e) {
      this.formData.hasReminder = e.detail.value;
    },
    onReminderTimeChange(e) {
      this.reminderIndex = e.detail.value;
      this.formData.reminderTime = this.reminderOptions[this.reminderIndex].value;
    },
    onRelatedTypeChange(e) {
      this.relatedTypeIndex = e.detail.value;
      this.formData.relatedType = this.relatedTypes[this.relatedTypeIndex].value;
    },
    showParticipantModal() {
      uni.showToast({
        title: '添加参与人功能开发中',
        icon: 'none'
      });
    },
    saveAction() {
      // 表单验证
      if (!this.formData.title) {
        uni.showToast({
          title: '请输入标题',
          icon: 'none'
        });
        return;
      }
      
      // 模拟保存
      uni.showLoading({
        title: '保存中...'
      });
      
      // 模拟API请求延迟
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '创建成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            // 返回列表页
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          }
        });
      }, 1000);
    }
  },
  onLoad(options) {
    // 处理传入参数
    if (options.type) {
      this.formData.type = options.type;
    }
    
    if (options.relatedId && options.relatedType) {
      // 处理关联信息
      this.formData.relatedType = options.relatedType;
      
      // 查找关联类型索引
      const typeIndex = this.relatedTypes.findIndex(item => item.value === options.relatedType);
      if (typeIndex !== -1) {
        this.relatedTypeIndex = typeIndex;
      }
      
      // 这里应该根据relatedId获取关联对象的详细信息
      // ...
    }
  }
}
</script>

<style>
.page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #e0e0e0;
  background-color: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

.back-button {
  padding: 10rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-spacer {
  width: 44rpx;
}

.page-container {
  flex: 1;
  padding: 30rpx;
}

.form-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-left: 10rpx;
  color: #333;
}

.section-content {
  padding: 10rpx;
}

.type-selector {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.type-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
  border-radius: 12rpx;
  border: 1rpx solid #e0e0e0;
  transition: all 0.3s;
}

.type-option.selected {
  border-color: #3370ff;
  background-color: #f0f7ff;
}

.type-option text {
  margin-top: 16rpx;
  font-size: 28rpx;
  color: #666;
}

.type-option.selected text {
  color: #3370ff;
  font-weight: 500;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.form-label.required:after {
  content: '*';
  color: #f5222d;
  margin-left: 8rpx;
}

.form-control {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  box-sizing: border-box;
}

textarea.form-control {
  height: 240rpx;
  padding: 20rpx;
}

.priority-group {
  display: flex;
  gap: 20rpx;
}

.priority-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
}

.priority-item.active {
  border-color: currentColor;
  background-color: rgba(0, 0, 0, 0.05);
  font-weight: 500;
}

.priority-item.low.active {
  color: #52c41a;
}

.priority-item.medium.active {
  color: #faad14;
}

.priority-item.high.active {
  color: #f5222d;
}

.uni-input {
  position: relative;
  height: 80rpx;
  padding: 0 20rpx;
  line-height: 80rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
}

.input-icon {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.toggle-switch {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.toggle-label {
  font-size: 28rpx;
  color: #666;
}

.form-select {
  width: 100%;
}

.add-participant {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border: 1rpx dashed #d9d9d9;
  border-radius: 8rpx;
  color: #3370ff;
}

.add-participant text {
  margin-left: 10rpx;
}

.bottom-spacer {
  height: 120rpx;
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background-color: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  gap: 30rpx;
}

.btn {
  flex: 1;
  height: 88rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}

.btn-outline {
  border: 1rpx solid #d9d9d9;
  color: #666;
}

.btn-primary {
  background-color: #3370ff;
  color: white;
}
</style> 
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-marketing-lead-detail"],{1092:function(t,e,i){"use strict";var a=i("6122"),n=i.n(a);n.a},"17dd":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return c})),i.d(e,"a",(function(){return a}));var a={svgIcon:i("8a0f").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"container"},[i("v-uni-view",{staticClass:"page-header"},[i("v-uni-view",{staticClass:"back-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[i("svg-icon",{attrs:{name:"arrow-left",type:"svg",size:"40"}})],1),i("v-uni-text",{staticClass:"page-title"},[t._v("线索详情")]),i("v-uni-view",{staticClass:"header-actions"},[i("v-uni-view",{staticClass:"menu-container"},[i("v-uni-view",{staticClass:"action-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showMenu=!t.showMenu}}},[i("svg-icon",{attrs:{name:"more",type:"svg",size:"32"}})],1),t.showMenu?i("v-uni-view",{staticClass:"dropdown-menu"},[i("v-uni-view",{staticClass:"dropdown-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.editLead.apply(void 0,arguments)}}},[i("svg-icon",{attrs:{name:"edit",type:"svg",size:"20"}}),i("v-uni-text",[t._v("编辑线索")])],1),i("v-uni-view",{staticClass:"dropdown-divider"}),i("v-uni-view",{staticClass:"dropdown-item delete",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.deleteLead.apply(void 0,arguments)}}},[i("svg-icon",{attrs:{name:"delete-bin",type:"svg",size:"20"}}),i("v-uni-text",[t._v("删除线索")])],1)],1):t._e()],1)],1)],1),t.connectionTimeout?i("v-uni-view",{staticClass:"timeout-state",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.retryConnection.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"timeout-text"},[t._v("连接服务器超时，点击屏幕重试")])],1):i("v-uni-scroll-view",{staticClass:"content-scroll",attrs:{"scroll-y":!0}},[i("v-uni-view",{staticClass:"lead-detail-header"},[i("v-uni-text",{staticClass:"lead-name"},[t._v(t._s(t.lead.name))]),i("v-uni-text",{staticClass:"lead-company"},[t._v(t._s(t.lead.customName))]),i("v-uni-view",{staticClass:"lead-status-wrapper"},[t.lead.clueStatus?i("v-uni-text",{staticClass:"lead-status",style:t.computedStatusTag(t.lead.clueStatus.order||0)},[t._v(t._s(t.lead.clueStatus.displayText))]):t._e(),i("v-uni-view",{staticClass:"lead-source"},[i("v-uni-text",[t._v("来源：")]),i("v-uni-text",{staticClass:"source-value"},[t._v(t._s(t.lead.clueSource.displayText))])],1)],1),i("v-uni-view",{staticClass:"action-buttons"},[i("v-uni-button",{staticClass:"btn btn-primary",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.createFollowup.apply(void 0,arguments)}}},[i("svg-icon",{attrs:{name:"add",type:"svg",size:"20"}}),i("v-uni-text",[t._v("添加跟进")])],1)],1)],1),i("v-uni-view",{staticClass:"detail-section"},[i("v-uni-view",{staticClass:"section-title"},[i("svg-icon",{attrs:{name:"contacts",type:"svg",size:"20"}}),i("v-uni-text",[t._v("联系方式")])],1),i("v-uni-view",{staticClass:"info-grid"},[i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("固定电话")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t._f("formatEmptyFilter")(t.lead.fixPhone)))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("手机号码")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t._f("formatEmptyFilter")(t.lead.telephone)))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("微信号")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.lead.wechat||"未提供"))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("电子邮箱")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t._f("formatEmptyFilter")(t.lead.email)))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("地址")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t._f("formatEmptyFilter")(t.lead.address)))])],1)],1)],1),i("v-uni-view",{staticClass:"detail-section"},[i("v-uni-view",{staticClass:"section-title"},[i("v-uni-text",{staticClass:"ri-file-text-line"}),i("v-uni-text",[t._v("线索备注")])],1),i("v-uni-view",{staticClass:"description-block"},[i("v-uni-text",{staticClass:"description-content"},[t._v(t._s(t.lead.remark))])],1)],1),i("v-uni-view",{staticClass:"detail-section"},[i("v-uni-view",{staticClass:"section-title"},[i("v-uni-text",{staticClass:"ri-history-line"}),i("v-uni-text",[t._v("跟进历史")])],1),t.lead.activities&&t.lead.activities.length>0?i("v-uni-view",{staticClass:"activity-list"},t._l(t.lead.activities,(function(e,a){return i("v-uni-view",{key:a,staticClass:"activity-item"},[i("v-uni-view",{staticClass:"activity-icon",class:"icon-"+e.type}),i("v-uni-view",{staticClass:"activity-content"},[i("v-uni-view",{staticClass:"activity-header"},[i("v-uni-text",{staticClass:"activity-title"},[t._v(t._s(e.title))]),i("v-uni-text",{staticClass:"activity-time"},[t._v(t._s(e.time))])],1),i("v-uni-text",{staticClass:"activity-description"},[t._v(t._s(e.description))]),i("v-uni-view",{staticClass:"activity-user"},[i("v-uni-text",{staticClass:"user-avatar"},[t._v(t._s(e.user.substring(0,1)))]),i("v-uni-text",{staticClass:"user-name"},[t._v(t._s(e.user))])],1)],1)],1)})),1):i("v-uni-view",{staticClass:"empty-activities"},[i("v-uni-text",{staticClass:"ri-calendar-line empty-icon"}),i("v-uni-text",{staticClass:"empty-text"},[t._v("暂无跟进记录")]),i("v-uni-text",{staticClass:"empty-description"},[t._v('点击"添加跟进"按钮记录与线索的沟通')])],1)],1),i("v-uni-view",{staticClass:"detail-section"},[i("v-uni-view",{staticClass:"section-title"},[i("v-uni-text",{staticClass:"ri-information-line"}),i("v-uni-text",[t._v("附加信息")])],1),i("v-uni-view",{staticClass:"info-grid"},[i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("创建日期")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t._f("formatDateFilter")(t.lead.creationTime)))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("创建人")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.lead.creatorName))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("负责人")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.lead.owner||"未分配"))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("最后更新")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t._f("formatDateFilter")(t.lead.lastModificationTime)))])],1)],1)],1)],1)],1)},c=[]},"2afd":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("8a0f")),c=i("6c8b"),s={components:{SvgIcon:n.default},data:function(){return{leadId:"",showMenu:!1,connectionTimeout:!1,lead:{clueStatus:{displayText:""},clueSource:{displayText:""},activities:[{type:"note",title:"初次联系",time:"2023-10-26 14:30",description:"已电话联系客户，对方表示有意向，但需要更多产品信息。已通过邮件发送产品手册。",user:"李销售"},{type:"task",title:"安排演示",time:"2023-10-25 10:15",description:"客户希望能看到产品演示，计划下周三安排线上演示会议。",user:"李销售"},{type:"system",title:"线索创建",time:"2023-10-25 09:30",description:"通过官网表单创建的线索",user:"系统"}]}}},computed:{computedStatusTag:function(){var t=["#e0f2fe","#dbeafe","#d1fae5","#e5e7eb"],e=["#0284c7","#2563eb","#059669","#6b7280"];return function(i){return{backgroundColor:t[i],color:e[i]}}}},onLoad:function(t){t.id&&(this.leadId=t.id,this.loadLeadData(t.id))},methods:{retryConnection:function(){this.loadLeadData(this.leadId)},loadLeadData:function(t){var e=this;this.connectionTimeout=!1,(0,c.getClueDetail)(t).then((function(t){Object.assign(e.lead,t)}))},goBack:function(){uni.navigateBack()},editLead:function(){uni.navigateTo({url:"/pages/marketing/lead-edit?id=".concat(this.lead.id)}),this.showMenu=!1},convertToCustomer:function(){uni.showToast({title:"转为客户功能开发中...",icon:"none"}),this.showMenu=!1},deleteLead:function(){var t=this;uni.showModal({title:"确认删除",content:"删除后无法恢复，确定要删除该线索吗？",success:function(e){e.confirm&&(0,c.deleteClue)(t.leadId).then((function(t){uni.showToast({title:"删除成功",icon:"success"}),uni.navigateBack()}))}}),this.showMenu=!1},callLead:function(){uni.showToast({title:"电话联系功能开发中...",icon:"none"})},createFollowup:function(){uni.showToast({title:"添加跟进功能开发中...",icon:"none"})}}};e.default=s},"5f59":function(t,e,i){"use strict";i.r(e);var a=i("17dd"),n=i("9c00");for(var c in n)["default"].indexOf(c)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(c);i("1092");var s=i("828b"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"c76cc8f4",null,!1,a["a"],void 0);e["default"]=o.exports},6122:function(t,e,i){var a=i("c93b");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("59afb2f8",a,!0,{sourceMap:!1,shadowMode:!1})},"6c8b":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.getClueList=e.getClueDetail=e.getAllOwnerList=e.deleteClue=e.UpdateClue=e.AddNewClue=void 0;var a=i("c475");e.getClueList=function(t){return(0,a.request)({url:"/api/crm/clue/getList",method:"POST",data:t})};e.AddNewClue=function(t){return(0,a.request)({url:"/api/crm/clue/create",method:"POST",data:t})};e.deleteClue=function(t){return(0,a.request)({url:"/api/crm/clue/delete?id=".concat(t),method:"POST"})};e.getClueDetail=function(t){return(0,a.request)({url:"/api/crm/clue/getClueById?id=".concat(t),method:"GET"})};e.UpdateClue=function(t,e){return(0,a.request)({url:"/api/crm/clue/update?id=".concat(t),method:"POST",data:e})};e.getAllOwnerList=function(t){return(0,a.request)({url:"/api/Users/<USER>",method:"POST",data:t})}},"9c00":function(t,e,i){"use strict";i.r(e);var a=i("2afd"),n=i.n(a);for(var c in a)["default"].indexOf(c)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(c);e["default"]=n.a},c475:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.request=e.getTenantInfo=void 0;var n=a(i("9b1b"));i("bf0f"),i("4626"),i("5ac7");var c=null;e.getTenantInfo=function(t){return new Promise((function(e,i){uni.request({url:"/api/Tenants/getTenantsByLogin",method:"POST",data:t,header:{"Content-Type":"application/json"},success:function(t){200===t.statusCode?(c=t.data,e(t.data)):i(t.data)},fail:function(t){i(t)}})}))};e.request=function(t){return t.url.includes("/login")&&c&&(t.header=(0,n.default)((0,n.default)({},t.header),{},{__tenant:c[0].id})),new Promise((function(e,i){uni.request({url:t.url,method:t.method||"GET",data:t.data||{},header:(0,n.default)({Authorization:"Bearer "+uni.getStorageSync("token"),"Content-Type":"application/json"},t.header),success:function(t){200===t.statusCode?e(t.data):i(t.data)},fail:function(t){i(t)}})}))}},c93b:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";.container[data-v-c76cc8f4]{display:flex;flex-direction:column;height:100vh;background-color:#f5f7fa}.page-header[data-v-c76cc8f4]{display:flex;align-items:center;justify-content:space-between;padding:12px 16px;border-bottom:1px solid #eee;background-color:#fff;z-index:10}.page-header .page-title[data-v-c76cc8f4]{font-size:18px;font-weight:700;color:#333;flex:1;text-align:center}.page-header .back-button[data-v-c76cc8f4]{color:#666;font-size:24px;width:40px;height:40px;display:flex;align-items:center;justify-content:center}.page-header .header-actions[data-v-c76cc8f4]{width:40px;display:flex;justify-content:flex-end}.page-header .action-button[data-v-c76cc8f4]{width:36px;height:36px;display:flex;align-items:center;justify-content:center;border-radius:50%;color:#666}.content-scroll[data-v-c76cc8f4]{flex:1;padding-bottom:%?40?%}.lead-detail-header[data-v-c76cc8f4]{background-color:#fff;padding:20px 16px;border-bottom:1px solid #eee}.lead-detail-header .lead-name[data-v-c76cc8f4]{font-size:20px;font-weight:700;color:#333;margin-bottom:4px;display:block}.lead-detail-header .lead-company[data-v-c76cc8f4]{font-size:14px;color:#666;margin-bottom:16px;display:block}.lead-detail-header .lead-status-wrapper[data-v-c76cc8f4]{display:flex;align-items:center;margin-bottom:20px}.lead-detail-header .lead-status-wrapper .lead-status[data-v-c76cc8f4]{padding:4px 12px;border-radius:100px;font-size:12px;font-weight:500;margin-right:12px}.lead-detail-header .lead-status-wrapper .lead-source[data-v-c76cc8f4]{font-size:14px;color:#666}.lead-detail-header .lead-status-wrapper .lead-source .source-value[data-v-c76cc8f4]{font-weight:500;color:#333}.lead-detail-header .action-buttons[data-v-c76cc8f4]{display:flex;gap:10px}.lead-detail-header .action-buttons .btn[data-v-c76cc8f4]{flex:1;height:40px;display:flex;align-items:center;justify-content:center;border-radius:8px;font-size:14px;white-space:nowrap;padding:0 8px}.lead-detail-header .action-buttons .btn uni-text[data-v-c76cc8f4]{margin-left:4px}.lead-detail-header .action-buttons .btn-outline[data-v-c76cc8f4]{border:1px solid #ddd;color:#666;background-color:#fff}.lead-detail-header .action-buttons .btn-primary[data-v-c76cc8f4]{background-color:#3a86ff;color:#fff;border:none}.detail-section[data-v-c76cc8f4]{background-color:#fff;padding:16px;margin-top:12px;border-top:1px solid #eee;border-bottom:1px solid #eee}.detail-section .section-title[data-v-c76cc8f4]{font-size:16px;font-weight:600;color:#333;margin-bottom:16px;display:flex;align-items:center}.detail-section .section-title uni-text[data-v-c76cc8f4]:first-child{color:#3a86ff;margin-right:6px;font-size:18px}.info-grid[data-v-c76cc8f4]{display:grid;grid-template-columns:repeat(2,1fr);gap:16px}.info-grid .info-item[data-v-c76cc8f4]{display:flex;flex-direction:column;min-width:0}.info-grid .info-item .info-label[data-v-c76cc8f4]{font-size:12px;color:#999;margin-bottom:4px}.info-grid .info-item .info-value[data-v-c76cc8f4]{font-size:14px;color:#333;font-weight:500;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.description-block[data-v-c76cc8f4]{margin-top:10px}.description-block .description-content[data-v-c76cc8f4]{font-size:14px;color:#333;line-height:1.6}.activity-list[data-v-c76cc8f4]{margin-top:10px}.activity-item[data-v-c76cc8f4]{position:relative;padding-left:28px;padding-bottom:20px}.activity-item[data-v-c76cc8f4]:not(:last-child)::before{content:"";position:absolute;top:24px;left:8px;bottom:0;width:2px;background-color:#eee}.activity-icon[data-v-c76cc8f4]{position:absolute;left:0;top:2px;width:18px;height:18px;border-radius:50%;background-color:#3a86ff}.icon-note[data-v-c76cc8f4]{background-color:#3a86ff}.icon-task[data-v-c76cc8f4]{background-color:#ff9500}.icon-system[data-v-c76cc8f4]{background-color:#999}.activity-content[data-v-c76cc8f4]{padding-bottom:10px}.activity-header[data-v-c76cc8f4]{display:flex;justify-content:space-between;margin-bottom:6px}.activity-title[data-v-c76cc8f4]{font-weight:500;color:#333;font-size:14px}.activity-time[data-v-c76cc8f4]{color:#999;font-size:12px}.activity-description[data-v-c76cc8f4]{color:#666;font-size:14px;line-height:1.5;margin-bottom:8px}.activity-user[data-v-c76cc8f4]{display:flex;align-items:center}.user-avatar[data-v-c76cc8f4]{width:20px;height:20px;border-radius:50%;background-color:#f0f0f0;color:#666;display:flex;align-items:center;justify-content:center;font-size:12px;margin-right:6px}.user-name[data-v-c76cc8f4]{font-size:12px;color:#666}.empty-activities[data-v-c76cc8f4]{display:flex;flex-direction:column;align-items:center;padding:20px 0}.empty-icon[data-v-c76cc8f4]{font-size:36px;color:#ddd;margin-bottom:12px}.empty-text[data-v-c76cc8f4]{font-size:16px;color:#666;margin-bottom:6px}.empty-description[data-v-c76cc8f4]{font-size:13px;color:#999}.menu-container[data-v-c76cc8f4]{position:relative}.dropdown-menu[data-v-c76cc8f4]{position:absolute;top:100%;right:0;background-color:#fff;border-radius:8px;box-shadow:0 4px 12px rgba(0,0,0,.15);border:1px solid #eee;padding:4px 0;z-index:1000;min-width:180px}.dropdown-item[data-v-c76cc8f4]{padding:10px 16px;display:flex;align-items:center;gap:8px;font-size:14px;color:#333}.dropdown-item.delete[data-v-c76cc8f4]{color:#ff4d4f}.dropdown-divider[data-v-c76cc8f4]{height:1px;background-color:#eee;margin:4px 0}\n/* 添加连接超时状态样式 */.timeout-state[data-v-c76cc8f4]{display:flex;justify-content:center;align-items:center;height:calc(100vh - %?120?%);text-align:center;padding:%?40?%}.timeout-text[data-v-c76cc8f4]{font-size:%?32?%;color:#6b7280}\n/* 确保最后一个detail-section有足够的底部边距 */.detail-section[data-v-c76cc8f4]:last-child{margin-bottom:%?30?%}',""]),t.exports=e}}]);
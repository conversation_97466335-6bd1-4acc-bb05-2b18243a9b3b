<template>
  <view class="container">
    <uni-nav-bar left-icon="left" title="图标展示" @clickLeft="goBack" fixed />
    
    <view class="content">
      <view class="title">
        <text>图标使用示例</text>
      </view>

      <view class="card">
        <view class="card-title">SVG图标使用方式</view>
        <view class="code-block">
          <text>1. 使用名称引用: &lt;svg-icon name="dashboard" type="svg" size="32"&gt;&lt;/svg-icon&gt;</text>
          <text>2. 注意: type属性为"svg"，name属性为图标名称(不含.svg后缀)</text>
        </view>
      </view>

      <view class="card">
        <view class="card-title">SVG图标示例</view>
        <view class="icon-grid">
          <view class="icon-item" v-for="icon in svgIcons" :key="icon">
            <svg-icon :name="icon" type="svg" size="48" class="icon-image"></svg-icon>
            <text class="icon-name">{{icon}}</text>
          </view>
        </view>
      </view>

      <view class="card">
        <view class="card-title">Remix Icon使用方式</view>
        <view class="code-block">
          <text>1. 类名方式: &lt;text class="ri-home-line"&gt;&lt;/text&gt;</text>
          <text>2. 组件方式: &lt;svg-icon name="home-line" type="remix" size="32"&gt;&lt;/svg-icon&gt;</text>
          <text>3. 注意: type属性为"remix"，name属性为图标名称(不含ri-前缀)</text>
        </view>
      </view>

      <view class="card">
        <view class="card-title">Remix Icon示例</view>
        <view class="icon-grid">
          <view class="icon-item" v-for="icon in remixIcons" :key="icon">
            <svg-icon :name="icon" type="remix" size="48" class="icon-image"></svg-icon>
            <text class="icon-name">{{icon}}</text>
          </view>
        </view>
      </view>

      <view class="card">
        <view class="card-title">Iconfont使用方式</view>
        <view class="code-block">
          <text>1. 类名方式: &lt;text class="iconfont icon-user"&gt;&lt;/text&gt;</text>
          <text>2. 组件方式: &lt;svg-icon name="user" type="iconfont" size="32"&gt;&lt;/svg-icon&gt;</text>
          <text>3. 注意: type属性为"iconfont"，name属性为图标名称(不含icon-前缀)</text>
        </view>
      </view>

      <view class="card">
        <view class="card-title">Iconfont示例</view>
        <view class="icon-grid">
          <view class="icon-item" v-for="icon in iconfontIcons" :key="icon">
            <svg-icon :name="icon" type="iconfont" size="48" class="icon-image"></svg-icon>
            <text class="icon-name">{{icon}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      svgIcons: [
        'auth', 'password', 'login', 'register', 'logout',
        'dashboard', 'statistics', 'customer', 'contract',
        'invoice', 'payment', 'report', 'analysis',
        'call', 'email', 'meeting', 'communication',
        'marketing', 'campaign', 'lead',
        'settings', 'team', 'workflow',
        'tasks', 'calendar', 'payment-progress',
        'employee', 'department', 'performance',
        'attendance', 'payroll', 'recruitment',
        'training', 'analytics', 'hiring'
      ],
      remixIcons: [
        'home-line', 'user-line', 'team-line', 'chat-1-line',
        'file-list-line', 'search-line', 'settings-line',
        'notification-line', 'calendar-line', 'briefcase-line'
      ],
      iconfontIcons: [
        'user', 'arrow-left', 'arrow-right', 'arrow-up', 'arrow-down',
        'add', 'minus', 'close', 'check', 'search', 'filter'
      ]
    };
  },
  methods: {
    goBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.content {
  padding: 88rpx 30rpx 30rpx;
}

.title {
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 30rpx;
}

.card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  color: #4a6fff;
}

.code-block {
  background-color: #f5f7fa;
  padding: 16rpx;
  border-radius: 8rpx;
  font-family: monospace;
  margin-bottom: 16rpx;
}

.code-block text {
  display: block;
  font-size: 24rpx;
  line-height: 1.6;
  color: #333;
}

.icon-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.icon-item {
  width: 20%;
  padding: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}

.icon-image {
  margin-bottom: 10rpx;
}

.icon-name {
  font-size: 22rpx;
  text-align: center;
  color: #666;
  word-break: break-all;
}

@media screen and (max-width: 768rpx) {
  .icon-item {
    width: 25%;
  }
}

@media screen and (max-width: 500rpx) {
  .icon-item {
    width: 33.33%;
  }
}
</style> 
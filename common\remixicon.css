/*
* Remix Icon Font CSS - https://remixicon.com
* 简化版，仅包含基本图标定义
*/

@font-face {
  font-family: 'remixicon';
  src: url('https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.woff') format('woff');
  font-display: swap;
}

[class^="ri-"],
[class*=" ri-"] {
  font-family: 'remixicon' !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 常用图标定义 */
.ri-eye-line:before { content: "\ec95"; }
.ri-eye-off-line:before { content: "\ec97"; }
.ri-wechat-line:before { content: "\f252"; }
.ri-message-2-line:before { content: "\eef9"; }
.ri-user-line:before { content: "\f25b"; }
.ri-home-line:before { content: "\edd1"; }
.ri-customer-service-line:before { content: "\eb94"; }
.ri-briefcase-line:before { content: "\ea64"; }
.ri-calendar-line:before { content: "\ea6a"; }
.ri-settings-line:before { content: "\f0f6"; }
.ri-file-list-line:before { content: "\ec5a"; }
.ri-check-line:before { content: "\ea9a"; }
.ri-add-line:before { content: "\ea12"; }
.ri-arrow-right-s-line:before { content: "\ea6e"; }
.ri-arrow-left-s-line:before { content: "\ea64"; }
.ri-more-2-line:before { content: "\ef0b"; }
.ri-chat-1-line:before { content: "\ea95"; }
.ri-team-line:before { content: "\f1a9"; }
.ri-notification-line:before { content: "\ef5e"; }
.ri-lock-line:before { content: "\eeac"; }
.ri-search-line:before { content: "\f0d1"; }
.ri-filter-line:before { content: "\ec65"; }
.ri-download-line:before { content: "\ebfa"; }
.ri-upload-line:before { content: "\f232"; }
.ri-delete-bin-line:before { content: "\ebac"; }
.ri-delete-bin-fill:before { content: "\ebab"; }
.ri-edit-line:before { content: "\ebfd"; }
.ri-save-line:before { content: "\f0da"; }
.ri-close-line:before { content: "\eb99"; }
.ri-share-line:before { content: "\f0fa"; }
.ri-phone-line:before { content: "\efa9"; }
.ri-mail-line:before { content: "\eee2"; }
.ri-map-pin-line:before { content: "\eeef"; }
.ri-attachment-line:before { content: "\ea78"; }
.ri-price-tag-3-line:before { content: "\efbc"; }
.ri-star-line:before { content: "\f18b"; }
.ri-star-fill:before { content: "\f18a"; }
.ri-file-copy-line:before { content: "\ec50"; }
.ri-information-line:before { content: "\ee36"; }
.ri-arrow-left-line:before { content: "\ea6c"; }
.ri-filter-3-line:before { content: "\ec63"; }
.ri-sort-desc:before { content: "\f179"; }
.ri-mail-send-line:before { content: "\eee7"; }
.ri-delete-bin-6-line:before { content: "\ebad"; }
.ri-time-line:before { content: "\f1be"; }
.ri-building-line:before { content: "\ea67"; }
.ri-file-list-3-line:before { content: "\ec5c"; } 
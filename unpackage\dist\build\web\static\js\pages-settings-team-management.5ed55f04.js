(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-settings-team-management"],{"00a7":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return s})),a.d(t,"a",(function(){return i}));var i={svgIcon:a("8a0f").default},n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"container"},[a("v-uni-view",{staticClass:"page-header"},[a("v-uni-view",{staticClass:"flex-row items-center gap-sm"},[a("v-uni-text",{staticClass:"page-title"},[e._v("团队管理")])],1),a("v-uni-view",{staticClass:"header-actions"},[a("v-uni-button",{staticClass:"action-button",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.addMember.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"add",type:"svg",size:"36",color:"#666666"}})],1)],1)],1),a("v-uni-scroll-view",{staticClass:"page-content",attrs:{"scroll-y":!0}},[a("v-uni-view",{staticClass:"stats-container"},[a("v-uni-view",{staticClass:"stat-card"},[a("v-uni-text",{staticClass:"stat-value"},[e._v("8")]),a("v-uni-text",{staticClass:"stat-label"},[e._v("团队成员")])],1),a("v-uni-view",{staticClass:"stat-card"},[a("v-uni-text",{staticClass:"stat-value"},[e._v("3")]),a("v-uni-text",{staticClass:"stat-label"},[e._v("管理人员")])],1),a("v-uni-view",{staticClass:"stat-card"},[a("v-uni-text",{staticClass:"stat-value"},[e._v("5")]),a("v-uni-text",{staticClass:"stat-label"},[e._v("销售人员")])],1)],1),a("v-uni-view",{staticClass:"filter-container"},[a("v-uni-view",{staticClass:"search-box"},[a("svg-icon",{attrs:{name:"search",type:"svg",size:"32",color:"#999999"}}),a("v-uni-input",{attrs:{type:"text",placeholder:"搜索成员"},model:{value:e.searchText,callback:function(t){e.searchText=t},expression:"searchText"}})],1),a("v-uni-view",{staticClass:"filter-pills"},[a("v-uni-view",{staticClass:"filter-pill",class:{active:"all"===e.currentFilter},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.setFilter("all")}}},[a("v-uni-text",[e._v("全部")])],1),a("v-uni-view",{staticClass:"filter-pill",class:{active:"manager"===e.currentFilter},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.setFilter("manager")}}},[a("v-uni-text",[e._v("管理人员")])],1),a("v-uni-view",{staticClass:"filter-pill",class:{active:"sales"===e.currentFilter},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.setFilter("sales")}}},[a("v-uni-text",[e._v("销售人员")])],1)],1)],1),a("v-uni-view",{staticClass:"members-list"},e._l(e.filteredMembers,(function(t,i){return a("v-uni-view",{key:i,staticClass:"member-card",on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.viewMember(t)}}},[a("v-uni-view",{staticClass:"member-avatar",style:{"background-color":t.avatarBg}},[a("v-uni-text",[e._v(e._s(t.avatar))])],1),a("v-uni-view",{staticClass:"member-info"},[a("v-uni-view",{staticClass:"member-name-row"},[a("v-uni-text",{staticClass:"member-name"},[e._v(e._s(t.name))]),a("v-uni-text",{staticClass:"member-role",class:{"role-manager":"管理员"===t.role}},[e._v(e._s(t.role))])],1),a("v-uni-text",{staticClass:"member-title"},[e._v(e._s(t.title))]),a("v-uni-view",{staticClass:"member-stats"},[a("v-uni-view",{staticClass:"member-stat"},[a("svg-icon",{attrs:{name:"user",type:"svg",size:"28",color:"#4a6fff"}}),a("v-uni-text",[e._v(e._s(t.customers)+" 客户")])],1),a("v-uni-view",{staticClass:"member-stat"},[a("svg-icon",{attrs:{name:"medal",type:"svg",size:"28",color:"#4a6fff"}}),a("v-uni-text",[e._v(e._s(t.deals)+" 成交")])],1)],1)],1),a("v-uni-view",{staticClass:"member-actions"},[a("v-uni-button",{staticClass:"action-btn",on:{click:function(a){a.stopPropagation(),arguments[0]=a=e.$handleEvent(a),e.callMember(t)}}},[a("svg-icon",{attrs:{name:"phone",type:"svg",size:"28",color:"#666666"}})],1),a("v-uni-button",{staticClass:"action-btn",on:{click:function(a){a.stopPropagation(),arguments[0]=a=e.$handleEvent(a),e.messageMember(t)}}},[a("svg-icon",{attrs:{name:"message",type:"svg",size:"28",color:"#666666"}})],1),a("v-uni-button",{staticClass:"action-btn",on:{click:function(a){a.stopPropagation(),arguments[0]=a=e.$handleEvent(a),e.showMemberOptions(t)}}},[a("svg-icon",{attrs:{name:"more",type:"svg",size:"28",color:"#666666"}})],1)],1)],1)})),1),a("v-uni-view",{staticClass:"invite-card"},[a("v-uni-text",{staticClass:"invite-title"},[e._v("邀请新团队成员")]),a("v-uni-text",{staticClass:"invite-desc"},[e._v("发送邀请链接，让新同事加入团队")]),a("v-uni-button",{staticClass:"invite-btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showInviteOptions.apply(void 0,arguments)}}},[a("v-uni-text",[e._v("生成邀请链接")])],1)],1)],1)],1)},s=[]},"1a0f":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,".container[data-v-23f38994]{display:flex;flex-direction:column;height:100vh;background-color:#f5f7fa}.page-header[data-v-23f38994]{display:flex;flex-direction:row;align-items:center;justify-content:space-between;padding:%?30?% %?40?%;background-color:#fff;border-bottom:1px solid #e0e0e0;position:relative;z-index:10}.page-title[data-v-23f38994]{font-size:%?36?%;font-weight:700;color:#333}.header-actions[data-v-23f38994]{display:flex;flex-direction:row;gap:%?20?%}.action-button[data-v-23f38994]{width:%?72?%;height:%?72?%;display:flex;align-items:center;justify-content:center;border-radius:50%;background-color:#f5f5f5;border:1px solid #e0e0e0;color:#666;padding:0;margin:0;line-height:1}.flex-row[data-v-23f38994]{display:flex;flex-direction:row}.items-center[data-v-23f38994]{align-items:center}.gap-sm[data-v-23f38994]{gap:%?20?%}.page-content[data-v-23f38994]{flex:1;padding:%?30?%}.stats-container[data-v-23f38994]{display:flex;justify-content:space-between;margin-bottom:%?30?%}.stat-card[data-v-23f38994]{background-color:#fff;border-radius:%?16?%;padding:%?20?%;flex:1;margin:0 %?10?%;text-align:center;box-shadow:0 %?2?% %?8?% rgba(0,0,0,.05);border:%?1?% solid #e0e0e0}.stat-value[data-v-23f38994]{font-size:%?40?%;font-weight:700;color:#4a6fff;display:block;margin-bottom:%?8?%}.stat-label[data-v-23f38994]{font-size:%?24?%;color:#666}.filter-container[data-v-23f38994]{margin-bottom:%?30?%}.search-box[data-v-23f38994]{display:flex;align-items:center;background-color:#fff;border-radius:%?8?%;padding:%?20?%;margin-bottom:%?20?%;border:%?1?% solid #e0e0e0}.search-box svg-icon[data-v-23f38994]{margin-right:%?20?%;flex-shrink:0}.search-box uni-input[data-v-23f38994]{flex:1;height:%?40?%;font-size:%?28?%}.filter-pills[data-v-23f38994]{display:flex;flex-wrap:wrap;gap:%?20?%;margin-bottom:%?10?%}.filter-pill[data-v-23f38994]{padding:%?12?% %?24?%;border-radius:%?8?%;background-color:#fff;border:%?1?% solid #e0e0e0;font-size:%?24?%;color:#666}.filter-pill.active[data-v-23f38994]{background-color:#4a6fff;color:#fff;border-color:#4a6fff}.members-list[data-v-23f38994]{margin-bottom:%?30?%}.member-card[data-v-23f38994]{display:flex;align-items:center;background-color:#fff;border-radius:%?16?%;padding:%?30?%;margin-bottom:%?20?%;box-shadow:0 %?2?% %?8?% rgba(0,0,0,.05);border:%?1?% solid #e0e0e0}.member-avatar[data-v-23f38994]{width:%?100?%;height:%?100?%;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:%?40?%;font-weight:700;color:#4a6fff;margin-right:%?30?%}.member-info[data-v-23f38994]{flex:1}.member-name-row[data-v-23f38994]{display:flex;align-items:center;margin-bottom:%?10?%}.member-name[data-v-23f38994]{font-size:%?32?%;font-weight:500;color:#333;margin-right:%?20?%}.member-role[data-v-23f38994]{font-size:%?22?%;background-color:#e0e0e0;color:#666;padding:%?6?% %?16?%;border-radius:%?30?%}.role-manager[data-v-23f38994]{background-color:rgba(74,111,255,.12549019607843137);color:#4a6fff}.member-title[data-v-23f38994]{font-size:%?26?%;color:#666;margin-bottom:%?16?%}.member-stats[data-v-23f38994]{display:flex;gap:%?30?%}.member-stat[data-v-23f38994]{display:flex;align-items:center;font-size:%?24?%;color:#999}.member-stat svg-icon[data-v-23f38994]{margin-right:%?8?%;flex-shrink:0}.member-actions[data-v-23f38994]{display:flex;gap:%?10?%}.action-btn[data-v-23f38994]{width:%?60?%;height:%?60?%;border-radius:%?8?%;display:flex;align-items:center;justify-content:center;background-color:#f5f5f5;border:%?1?% solid #e0e0e0;color:#666;padding:0;margin:0;line-height:1;flex-shrink:0}.invite-card[data-v-23f38994]{background-color:#fff;border-radius:%?16?%;padding:%?30?%;text-align:center;box-shadow:0 %?2?% %?8?% rgba(0,0,0,.05);border:%?1?% solid #e0e0e0;margin-bottom:%?30?%}.invite-title[data-v-23f38994]{font-size:%?32?%;font-weight:500;color:#333;margin-bottom:%?10?%}.invite-desc[data-v-23f38994]{font-size:%?26?%;color:#666;margin-bottom:%?30?%}.invite-btn[data-v-23f38994]{background-color:#4a6fff;color:#fff;border-radius:%?8?%;padding:%?20?% %?40?%;font-size:%?28?%;display:inline-block}",""]),e.exports=t},"3bc4":function(e,t,a){"use strict";a.r(t);var i=a("00a7"),n=a("9e42");for(var s in n)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(s);a("893c");var o=a("828b"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"23f38994",null,!1,i["a"],void 0);t["default"]=r.exports},"893c":function(e,t,a){"use strict";var i=a("e894"),n=a.n(i);n.a},"9e42":function(e,t,a){"use strict";a.r(t);var i=a("e09e"),n=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(s);t["default"]=n.a},e09e:function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("8f71"),a("bf0f"),a("4626"),a("5ac7");var n=i(a("8a0f")),s={components:{SvgIcon:n.default},data:function(){return{searchText:"",currentFilter:"all",members:[{id:1,name:"李经理",avatar:"LJ",avatarBg:"#4a6fff20",role:"管理员",title:"销售经理",customers:42,deals:18,phone:"13812345678",email:"<EMAIL>"},{id:2,name:"张小明",avatar:"ZM",avatarBg:"#34d39920",role:"管理员",title:"市场总监",customers:35,deals:15,phone:"13812345679",email:"<EMAIL>"},{id:3,name:"王丽",avatar:"WL",avatarBg:"#f59e0b20",role:"管理员",title:"客户经理",customers:56,deals:24,phone:"13812345680",email:"<EMAIL>"},{id:4,name:"赵强",avatar:"ZQ",avatarBg:"#ef444420",role:"销售员",title:"资深销售顾问",customers:38,deals:16,phone:"13812345681",email:"<EMAIL>"},{id:5,name:"陈静",avatar:"CJ",avatarBg:"#8b5cf620",role:"销售员",title:"销售顾问",customers:27,deals:9,phone:"13812345682",email:"<EMAIL>"},{id:6,name:"刘洋",avatar:"LY",avatarBg:"#ec489920",role:"销售员",title:"销售顾问",customers:24,deals:8,phone:"13812345683",email:"<EMAIL>"},{id:7,name:"孙亮",avatar:"SL",avatarBg:"#10b98120",role:"销售员",title:"初级销售顾问",customers:18,deals:4,phone:"13812345684",email:"<EMAIL>"},{id:8,name:"林小华",avatar:"LH",avatarBg:"#64748b20",role:"销售员",title:"初级销售顾问",customers:15,deals:3,phone:"13812345685",email:"<EMAIL>"}]}},computed:{filteredMembers:function(){var e=this.members;if("manager"===this.currentFilter?e=e.filter((function(e){return"管理员"===e.role})):"sales"===this.currentFilter&&(e=e.filter((function(e){return"销售员"===e.role}))),this.searchText){var t=this.searchText.toLowerCase();e=e.filter((function(e){return e.name.toLowerCase().includes(t)||e.title.toLowerCase().includes(t)}))}return e}},methods:{setFilter:function(e){this.currentFilter=e},viewMember:function(e){uni.showToast({title:"查看".concat(e.name,"的详细信息"),icon:"none"})},addMember:function(){uni.navigateTo({url:"/pages/settings/add-team-member"})},callMember:function(e){uni.makePhoneCall({phoneNumber:e.phone,success:function(){console.log("拨打电话成功")},fail:function(e){console.log("拨打电话失败",e),uni.showToast({title:"拨号失败",icon:"none"})}})},messageMember:function(e){uni.showToast({title:"发消息给".concat(e.name),icon:"none"})},showMemberOptions:function(e){var t=this;uni.showActionSheet({itemList:["查看详情","编辑信息","设置权限","调整部门","停用账号"],success:function(a){switch(a.tapIndex){case 0:t.viewMember(e);break;case 1:uni.showToast({title:"编辑信息功能开发中",icon:"none"});break;case 2:uni.showToast({title:"权限设置功能开发中",icon:"none"});break;case 3:uni.showToast({title:"部门调整功能开发中",icon:"none"});break;case 4:t.confirmDisableMember(e);break}}})},confirmDisableMember:function(e){uni.showModal({title:"停用账号",content:"确定要停用".concat(e.name,"的账号吗？停用后该成员将无法登录系统。"),confirmText:"停用",confirmColor:"#ef4444",success:function(e){e.confirm&&uni.showToast({title:"账号已停用",icon:"success"})}})},showInviteOptions:function(){uni.showActionSheet({itemList:["生成链接","发送邮件邀请","分享到微信"],success:function(e){uni.showToast({title:"邀请功能开发中",icon:"none"})}})}}};t.default=s},e894:function(e,t,a){var i=a("1a0f");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var n=a("967d").default;n("27598f9e",i,!0,{sourceMap:!1,shadowMode:!1})}}]);
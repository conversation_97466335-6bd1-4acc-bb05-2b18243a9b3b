<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="goBack">
        <text class="ri-arrow-left-line"></text>
      </view>
      <text class="page-title">添加员工</text>
      <view class="header-actions">
        <button type="button" class="action-button submit-button" @click="submitForm">
          保存
        </button>
      </view>
    </view>
    
    <!-- 表单 -->
    <scroll-view scroll-y class="form-container">
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">基本信息</text>
          <text class="required-hint">* 为必填项</text>
        </view>
        
        <view class="form-group">
          <view class="avatar-upload">
            <view class="avatar-preview" @click="chooseAvatar">
              <image v-if="formData.avatar" :src="formData.avatar" mode="aspectFill"></image>
              <text v-else class="ri-user-line avatar-placeholder"></text>
            </view>
            <text class="upload-hint">点击上传头像</text>
          </view>
        </view>
        
        <view class="form-group">
          <text class="form-label required">姓名</text>
          <input 
            type="text" 
            class="form-input" 
            v-model="formData.name" 
            placeholder="请输入员工姓名"
            :class="{ 'error': errors.name }"
          />
          <text v-if="errors.name" class="error-message">{{errors.name}}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label required">工号</text>
          <input 
            type="text" 
            class="form-input" 
            v-model="formData.employeeId" 
            placeholder="请输入员工工号"
            :class="{ 'error': errors.employeeId }"
          />
          <text v-if="errors.employeeId" class="error-message">{{errors.employeeId}}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label required">性别</text>
          <view class="radio-group">
            <view class="radio-item" @click="formData.gender = '男'">
              <view class="radio-button" :class="{ active: formData.gender === '男' }"></view>
              <text class="radio-label">男</text>
            </view>
            <view class="radio-item" @click="formData.gender = '女'">
              <view class="radio-button" :class="{ active: formData.gender === '女' }"></view>
              <text class="radio-label">女</text>
            </view>
          </view>
        </view>
        
        <view class="form-group">
          <text class="form-label">出生日期</text>
          <picker 
            mode="date" 
            :value="formData.birthday" 
            @change="onBirthdayChange"
            class="form-picker"
          >
            <view class="picker-value">
              <text v-if="formData.birthday">{{formData.birthday}}</text>
              <text v-else class="placeholder">请选择出生日期</text>
              <text class="ri-calendar-line picker-icon"></text>
            </view>
          </picker>
        </view>
        
        <view class="form-group">
          <text class="form-label">身份证号</text>
          <input 
            type="idcard" 
            class="form-input" 
            v-model="formData.idNumber" 
            placeholder="请输入身份证号"
            :class="{ 'error': errors.idNumber }"
          />
          <text v-if="errors.idNumber" class="error-message">{{errors.idNumber}}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label required">手机号码</text>
          <input 
            type="number" 
            class="form-input" 
            v-model="formData.phone" 
            placeholder="请输入手机号码"
            :class="{ 'error': errors.phone }"
          />
          <text v-if="errors.phone" class="error-message">{{errors.phone}}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label">邮箱</text>
          <input 
            type="text" 
            class="form-input" 
            v-model="formData.email" 
            placeholder="请输入邮箱地址"
            :class="{ 'error': errors.email }"
          />
          <text v-if="errors.email" class="error-message">{{errors.email}}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label">居住地址</text>
          <textarea 
            class="form-textarea" 
            v-model="formData.address" 
            placeholder="请输入详细地址"
          ></textarea>
        </view>
      </view>
      
      <!-- 工作信息 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">工作信息</text>
        </view>
        
        <view class="form-group">
          <text class="form-label required">部门</text>
          <picker 
            mode="selector" 
            :range="departments" 
            range-key="name"
            @change="onDepartmentChange"
            class="form-picker"
          >
            <view class="picker-value">
              <text v-if="formData.department">{{formData.department}}</text>
              <text v-else class="placeholder">请选择部门</text>
              <text class="ri-arrow-down-s-line picker-icon"></text>
            </view>
          </picker>
          <text v-if="errors.department" class="error-message">{{errors.department}}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label required">职位</text>
          <input 
            type="text" 
            class="form-input" 
            v-model="formData.position" 
            placeholder="请输入职位名称"
            :class="{ 'error': errors.position }"
          />
          <text v-if="errors.position" class="error-message">{{errors.position}}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label">直属上级</text>
          <picker 
            mode="selector" 
            :range="managers" 
            range-key="name"
            @change="onManagerChange"
            class="form-picker"
          >
            <view class="picker-value">
              <text v-if="formData.manager">{{formData.manager}}</text>
              <text v-else class="placeholder">请选择直属上级</text>
              <text class="ri-arrow-down-s-line picker-icon"></text>
            </view>
          </picker>
        </view>
        
        <view class="form-group">
          <text class="form-label required">入职日期</text>
          <picker 
            mode="date" 
            :value="formData.hireDate" 
            @change="onHireDateChange"
            class="form-picker"
          >
            <view class="picker-value">
              <text v-if="formData.hireDate">{{formData.hireDate}}</text>
              <text v-else class="placeholder">请选择入职日期</text>
              <text class="ri-calendar-line picker-icon"></text>
            </view>
          </picker>
          <text v-if="errors.hireDate" class="error-message">{{errors.hireDate}}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label">工作地点</text>
          <input 
            type="text" 
            class="form-input" 
            v-model="formData.workLocation" 
            placeholder="请输入工作地点"
          />
        </view>
        
        <view class="form-group">
          <text class="form-label required">员工类型</text>
          <picker 
            mode="selector" 
            :range="employeeTypes" 
            @change="onEmployeeTypeChange"
            class="form-picker"
          >
            <view class="picker-value">
              <text v-if="formData.employeeType">{{formData.employeeType}}</text>
              <text v-else class="placeholder">请选择员工类型</text>
              <text class="ri-arrow-down-s-line picker-icon"></text>
            </view>
          </picker>
          <text v-if="errors.employeeType" class="error-message">{{errors.employeeType}}</text>
        </view>
      </view>
      
      <!-- 合同信息 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">合同信息</text>
        </view>
        
        <view class="form-group">
          <text class="form-label required">合同类型</text>
          <picker 
            mode="selector" 
            :range="contractTypes" 
            @change="onContractTypeChange"
            class="form-picker"
          >
            <view class="picker-value">
              <text v-if="formData.contract.type">{{formData.contract.type}}</text>
              <text v-else class="placeholder">请选择合同类型</text>
              <text class="ri-arrow-down-s-line picker-icon"></text>
            </view>
          </picker>
          <text v-if="errors.contractType" class="error-message">{{errors.contractType}}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label required">合同起始日期</text>
          <picker 
            mode="date" 
            :value="formData.contract.startDate" 
            @change="onContractStartDateChange"
            class="form-picker"
          >
            <view class="picker-value">
              <text v-if="formData.contract.startDate">{{formData.contract.startDate}}</text>
              <text v-else class="placeholder">请选择合同起始日期</text>
              <text class="ri-calendar-line picker-icon"></text>
            </view>
          </picker>
          <text v-if="errors.contractStartDate" class="error-message">{{errors.contractStartDate}}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label required">合同结束日期</text>
          <picker 
            mode="date" 
            :value="formData.contract.endDate" 
            @change="onContractEndDateChange"
            class="form-picker"
          >
            <view class="picker-value">
              <text v-if="formData.contract.endDate">{{formData.contract.endDate}}</text>
              <text v-else class="placeholder">请选择合同结束日期</text>
              <text class="ri-calendar-line picker-icon"></text>
            </view>
          </picker>
          <text v-if="errors.contractEndDate" class="error-message">{{errors.contractEndDate}}</text>
        </view>
      </view>
      
      <!-- 薪资信息 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">薪资信息</text>
        </view>
        
        <view class="form-group">
          <text class="form-label required">基本工资</text>
          <input 
            type="digit" 
            class="form-input" 
            v-model="formData.salary.base" 
            placeholder="请输入基本工资"
            :class="{ 'error': errors.salaryBase }"
          />
          <text v-if="errors.salaryBase" class="error-message">{{errors.salaryBase}}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label">社保基数</text>
          <input 
            type="digit" 
            class="form-input" 
            v-model="formData.salary.socialSecurity" 
            placeholder="请输入社保基数"
          />
        </view>
        
        <view class="form-group">
          <text class="form-label">公积金基数</text>
          <input 
            type="digit" 
            class="form-input" 
            v-model="formData.salary.housingFund" 
            placeholder="请输入公积金基数"
          />
        </view>
      </view>
      
      <!-- 备注 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">备注</text>
        </view>
        
        <view class="form-group">
          <textarea 
            class="form-textarea" 
            v-model="formData.notes" 
            placeholder="添加员工相关备注信息"
          ></textarea>
        </view>
      </view>
      
      <!-- 提交按钮 -->
      <view class="form-actions">
        <button class="btn-cancel" @click="goBack">取消</button>
        <button class="btn-submit" @click="submitForm">保存</button>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        name: '',
        employeeId: '',
        gender: '男',
        birthday: '',
        idNumber: '',
        phone: '',
        email: '',
        address: '',
        department: '',
        position: '',
        manager: '',
        hireDate: '',
        workLocation: '',
        employeeType: '',
        status: 'active',
        avatar: '',
        contract: {
          type: '',
          startDate: '',
          endDate: ''
        },
        salary: {
          base: '',
          socialSecurity: '',
          housingFund: ''
        },
        notes: ''
      },
      errors: {},
      departments: [
        { id: '1', name: '销售部' },
        { id: '2', name: '技术部' },
        { id: '3', name: '市场部' },
        { id: '4', name: '财务部' },
        { id: '5', name: '人事部' }
      ],
      managers: [
        { id: '1', name: '王经理' },
        { id: '2', name: '李总监' },
        { id: '3', name: '张主管' }
      ],
      employeeTypes: ['全职', '兼职', '实习', '劳务派遣', '外包'],
      contractTypes: ['固定期限合同', '无固定期限合同', '以完成一定工作任务为期限的合同', '实习协议']
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    chooseAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.formData.avatar = res.tempFilePaths[0];
        }
      });
    },
    onBirthdayChange(e) {
      this.formData.birthday = e.detail.value;
    },
    onDepartmentChange(e) {
      const index = e.detail.value;
      this.formData.department = this.departments[index].name;
    },
    onManagerChange(e) {
      const index = e.detail.value;
      this.formData.manager = this.managers[index].name;
    },
    onHireDateChange(e) {
      this.formData.hireDate = e.detail.value;
    },
    onEmployeeTypeChange(e) {
      const index = e.detail.value;
      this.formData.employeeType = this.employeeTypes[index];
    },
    onContractTypeChange(e) {
      const index = e.detail.value;
      this.formData.contract.type = this.contractTypes[index];
    },
    onContractStartDateChange(e) {
      this.formData.contract.startDate = e.detail.value;
    },
    onContractEndDateChange(e) {
      this.formData.contract.endDate = e.detail.value;
    },
    validateForm() {
      this.errors = {};
      let isValid = true;
      
      // 基本信息验证
      if (!this.formData.name) {
        this.errors.name = '请输入员工姓名';
        isValid = false;
      }
      
      if (!this.formData.employeeId) {
        this.errors.employeeId = '请输入员工工号';
        isValid = false;
      }
      
      if (!this.formData.phone) {
        this.errors.phone = '请输入手机号码';
        isValid = false;
      } else if (!/^1\d{10}$/.test(this.formData.phone)) {
        this.errors.phone = '请输入有效的手机号码';
        isValid = false;
      }
      
      if (this.formData.email && !/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(this.formData.email)) {
        this.errors.email = '请输入有效的邮箱地址';
        isValid = false;
      }
      
      if (this.formData.idNumber && !/^\d{17}[\dXx]$/.test(this.formData.idNumber)) {
        this.errors.idNumber = '请输入有效的身份证号';
        isValid = false;
      }
      
      // 工作信息验证
      if (!this.formData.department) {
        this.errors.department = '请选择部门';
        isValid = false;
      }
      
      if (!this.formData.position) {
        this.errors.position = '请输入职位名称';
        isValid = false;
      }
      
      if (!this.formData.hireDate) {
        this.errors.hireDate = '请选择入职日期';
        isValid = false;
      }
      
      if (!this.formData.employeeType) {
        this.errors.employeeType = '请选择员工类型';
        isValid = false;
      }
      
      // 合同信息验证
      if (!this.formData.contract.type) {
        this.errors.contractType = '请选择合同类型';
        isValid = false;
      }
      
      if (!this.formData.contract.startDate) {
        this.errors.contractStartDate = '请选择合同起始日期';
        isValid = false;
      }
      
      if (!this.formData.contract.endDate) {
        this.errors.contractEndDate = '请选择合同结束日期';
        isValid = false;
      }
      
      // 薪资信息验证
      if (!this.formData.salary.base) {
        this.errors.salaryBase = '请输入基本工资';
        isValid = false;
      }
      
      return isValid;
    },
    submitForm() {
      if (this.validateForm()) {
        // 这里应该调用API保存员工信息
        console.log('提交的表单数据:', this.formData);
        
        // 显示提交成功提示
        uni.showToast({
          title: '员工添加成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            // 延迟返回列表页
            setTimeout(() => {
              uni.navigateBack();
            }, 2000);
          }
        });
      } else {
        // 表单验证失败，滚动到第一个错误字段
        uni.showToast({
          title: '请填写必填项',
          icon: 'none'
        });
      }
    }
  }
};
</script>

<style>
.container {
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  position: relative;
  border-bottom: 1rpx solid #eaeaea;
}

.back-button {
  font-size: 40rpx;
  color: #333;
  padding: 10rpx;
}

.page-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
}

.action-button {
  background: none;
  border: none;
  font-size: 32rpx;
  color: #4a6fff;
  padding: 10rpx;
}

.form-container {
  padding-bottom: 120rpx;
  height: calc(100vh - 100rpx);
}

.form-section {
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eaeaea;
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.required-hint {
  font-size: 24rpx;
  color: #ff4d4f;
}

.form-group {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-group:last-child {
  border-bottom: none;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.required:after {
  content: ' *';
  color: #ff4d4f;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #dcdfe6;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #ffffff;
}

.form-input.error {
  border-color: #ff4d4f;
}

.form-textarea {
  width: 100%;
  height: 180rpx;
  border: 1rpx solid #dcdfe6;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #ffffff;
}

.error-message {
  display: block;
  font-size: 24rpx;
  color: #ff4d4f;
  margin-top: 10rpx;
}

.avatar-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 10rpx 0 30rpx;
}

.avatar-preview {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  overflow: hidden;
  background-color: #f0f2f5;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15rpx;
}

.avatar-preview image {
  width: 100%;
  height: 100%;
}

.avatar-placeholder {
  font-size: 80rpx;
  color: #bbbec4;
}

.upload-hint {
  font-size: 24rpx;
  color: #999;
}

.radio-group {
  display: flex;
  align-items: center;
}

.radio-item {
  display: flex;
  align-items: center;
  margin-right: 60rpx;
}

.radio-button {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 1rpx solid #dcdfe6;
  margin-right: 10rpx;
  position: relative;
  background-color: #ffffff;
}

.radio-button.active {
  border-color: #4a6fff;
}

.radio-button.active:after {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #4a6fff;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.radio-label {
  font-size: 28rpx;
  color: #333;
}

.form-picker {
  width: 100%;
}

.picker-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  border: 1rpx solid #dcdfe6;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #ffffff;
}

.placeholder {
  color: #999;
}

.picker-icon {
  font-size: 32rpx;
  color: #999;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.btn-cancel {
  width: 48%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  color: #666;
  background-color: #f5f7fa;
  border: 1rpx solid #dcdfe6;
  font-size: 30rpx;
}

.btn-submit {
  width: 48%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  color: #ffffff;
  background-color: #4a6fff;
  font-size: 30rpx;
}
</style> 
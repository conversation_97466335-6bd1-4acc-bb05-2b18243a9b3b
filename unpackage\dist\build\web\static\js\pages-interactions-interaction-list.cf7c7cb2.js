(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-interactions-interaction-list"],{"06d6":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={svgIcon:a("8a0f").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"page-container"},[a("v-uni-view",{staticClass:"page-header"},[a("v-uni-view",{staticClass:"page-title"},[t._v("沟通记录")]),a("v-uni-view",{staticClass:"header-actions"},[a("v-uni-view",{staticClass:"header-icon",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showFilterPanel.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"filter",type:"svg",size:"28"}})],1),a("v-uni-view",{staticClass:"header-icon",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.navigateToSearch.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"search",type:"svg",size:"28"}})],1),a("v-uni-view",{staticClass:"header-icon",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.navigateToCreate.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"add",type:"svg",size:"28"}})],1)],1)],1),a("v-uni-view",{staticClass:"tabs-container"},t._l(t.tabs,(function(e,i){return a("v-uni-view",{key:i,staticClass:"tab-item",class:{active:t.currentTab===e.value},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.changeTab(e.value)}}},[a("v-uni-text",[t._v(t._s(e.label))])],1)})),1),a("v-uni-scroll-view",{staticClass:"interaction-list",attrs:{"scroll-y":!0},on:{scrolltolower:function(e){arguments[0]=e=t.$handleEvent(e),t.loadMore.apply(void 0,arguments)}}},[t.filteredInteractions.length>0?t._l(t.filteredInteractions,(function(e,i){return a("v-uni-view",{key:i,staticClass:"interaction-card",attrs:{"data-type":e.type,"hover-class":"card-hover"},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.navigateToDetail(e.id)}}},[a("v-uni-view",{staticClass:"interaction-header"},[a("v-uni-view",{staticClass:"interaction-icon",class:"icon-"+e.type},[a("svg-icon",{attrs:{name:t.getIconName(e.type),type:"svg",size:"28"}})],1),a("v-uni-view",{staticClass:"interaction-title"},[a("v-uni-text",{staticClass:"interaction-type"},[t._v(t._s(t.getTypeName(e.type)))]),a("v-uni-text",{staticClass:"interaction-subject"},[t._v(t._s(e.subject))])],1),a("v-uni-text",{staticClass:"interaction-time"},[t._v(t._s(e.time))])],1),e.relatedObject?a("v-uni-view",{staticClass:"interaction-detail"},[a("v-uni-text",{staticClass:"detail-label"},[t._v("关联对象")]),a("v-uni-text",{staticClass:"detail-value"},[t._v(t._s(e.relatedObject))])],1):t._e(),e.contacts?a("v-uni-view",{staticClass:"interaction-detail"},[a("v-uni-text",{staticClass:"detail-label"},[t._v(t._s(t.getContactLabel(e.type)))]),a("v-uni-text",{staticClass:"detail-value"},[t._v(t._s(e.contacts))])],1):t._e(),e.owner?a("v-uni-view",{staticClass:"interaction-detail"},[a("v-uni-text",{staticClass:"detail-label"},[t._v("负责人")]),a("v-uni-text",{staticClass:"detail-value"},[t._v(t._s(e.owner))])],1):t._e(),a("v-uni-view",{staticClass:"interaction-content"},[t._v(t._s(e.content))]),e.nextSteps&&e.nextSteps.length>0?a("v-uni-view",{staticClass:"next-steps"},t._l(e.nextSteps,(function(e,i){return a("v-uni-view",{key:i,staticClass:"next-step-item"},[t._v(t._s(e))])})),1):t._e(),e.tags&&e.tags.length>0?a("v-uni-view",{staticClass:"tag-container"},t._l(e.tags,(function(e,i){return a("v-uni-text",{key:i,staticClass:"tag"},[t._v(t._s(e))])})),1):t._e()],1)})):a("v-uni-view",{staticClass:"empty-state"},[a("svg-icon",{attrs:{name:"empty-box",type:"svg",size:"100"}}),a("v-uni-text",{staticClass:"empty-title"},[t._v("暂无沟通记录")]),a("v-uni-text",{staticClass:"empty-desc"},[t._v('点击右上角"+"按钮添加沟通记录')])],1),t.showLoadMore?a("v-uni-view",{staticClass:"loading-more"},[a("v-uni-text",[t._v("加载中...")])],1):t._e()],2),a("v-uni-view",{staticClass:"fab",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.navigateToCreate.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"add",type:"svg",size:"60",color:"#FFFFFF"}})],1),t.showFilter?a("v-uni-view",{staticClass:"modal-filter"},[a("v-uni-view",{staticClass:"modal-mask",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.hideFilterPanel.apply(void 0,arguments)}}}),a("v-uni-view",{staticClass:"modal-dialog"},[a("v-uni-view",{staticClass:"modal-header"},[a("v-uni-view",{staticClass:"modal-title"},[t._v("筛选条件")]),a("v-uni-view",{staticClass:"modal-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.hideFilterPanel.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"close",type:"svg",size:"32"}})],1)],1),a("v-uni-view",{staticClass:"modal-body"},[a("v-uni-view",{staticClass:"filter-section"},[a("v-uni-text",{staticClass:"filter-title"},[t._v("记录类型")]),a("v-uni-view",{staticClass:"filter-options"},t._l(t.typeOptions,(function(e,i){return a("v-uni-view",{key:i,staticClass:"filter-tag",class:{active:t.selectedTypes.includes(e.value)},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.toggleTypeFilter(e.value)}}},[t._v(t._s(e.label))])})),1)],1),a("v-uni-view",{staticClass:"filter-section"},[a("v-uni-text",{staticClass:"filter-title"},[t._v("关联对象")]),a("v-uni-view",{staticClass:"filter-options"},t._l(t.relatedOptions,(function(e,i){return a("v-uni-view",{key:i,staticClass:"filter-tag",class:{active:t.selectedRelatedTypes.includes(e.value)},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.toggleRelatedFilter(e.value)}}},[t._v(t._s(e.label))])})),1)],1),a("v-uni-view",{staticClass:"filter-section"},[a("v-uni-text",{staticClass:"filter-title"},[t._v("时间范围")]),a("v-uni-view",{staticClass:"filter-options"},t._l(t.timeOptions,(function(e,i){return a("v-uni-view",{key:i,staticClass:"filter-tag",class:{active:t.selectedTimeRange===e.value},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.setTimeRangeFilter(e.value)}}},[t._v(t._s(e.label))])})),1)],1)],1),a("v-uni-view",{staticClass:"modal-footer"},[a("v-uni-view",{staticClass:"filter-actions"},[a("v-uni-button",{staticClass:"btn btn-outline",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.resetFilters.apply(void 0,arguments)}}},[t._v("重置")]),a("v-uni-button",{staticClass:"btn btn-primary",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.applyFilters.apply(void 0,arguments)}}},[t._v("应用")])],1)],1)],1)],1):t._e(),a("custom-tab-bar",{ref:"customTabBar"})],1)},o=[]},1880:function(t,e,a){"use strict";a.r(e);var i=a("1a37"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},"1a37":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4626"),a("5ac7"),a("bf0f"),a("bd06");var i={name:"CustomTabBar",data:function(){return{current:0,color:"#333333",activeColor:"#007AFF",showMoreMenu:!1,tabList:[{pagePath:"/pages/dashboard/main-dashboard",text:"首页",iconPath:"dashboard",selectedIconPath:"dashboard"},{pagePath:"/pages/customers/customer-list",text:"客户",iconPath:"customer",selectedIconPath:"customer"},{pagePath:"/pages/sales/opportunity-list",text:"销售",iconPath:"sales",selectedIconPath:"sales"},{type:"more",text:"更多",iconPath:"more",selectedIconPath:"more"},{pagePath:"/pages/settings/profile",text:"我的",iconPath:"user",selectedIconPath:"user"}],moreMenuList:[{pagePath:"/pages/marketing/leads",text:"线索",iconPath:"lead"},{pagePath:"/pages/interactions/interaction-list",text:"沟通",iconPath:"communication"},{pagePath:"/pages/sales/quotation-list",text:"报价",iconPath:"quotation"},{pagePath:"/pages/contracts/contract-list",text:"合同",iconPath:"contract"},{pagePath:"/pages/contracts/invoice-list",text:"发票",iconPath:"file-text"},{pagePath:"/pages/contracts/payment-list",text:"收款",iconPath:"money"},{pagePath:"/pages/reports/report-list",text:"报表",iconPath:"report"}]}},created:function(){this.updateCurrentTab()},onLoad:function(){this.updateCurrentTab()},onShow:function(){var t=this;setTimeout((function(){t.updateCurrentTab()}),100)},methods:{updateCurrentTab:function(){try{var t=getCurrentPages(),e=t[t.length-1];if(!e||!e.route)return;var a=e.route;console.log("当前路由:",a),a.includes("/pages/dashboard/")?this.current=0:a.includes("/pages/customers/")?this.current=1:a.includes("/pages/sales/")?this.current=2:a.includes("/pages/actions/")?this.current=3:a.includes("/pages/settings/")&&(this.current=5)}catch(i){console.error("更新Tab出错:",i)}},handleTabClick:function(t,e){"more"===t.type?(this.toggleMoreMenu(),this.current=e):this.switchTab(t.pagePath,e)},switchTab:function(t,e){this.current!==e&&(this.current=e,uni.switchTab({url:t}))},toggleMoreMenu:function(){this.showMoreMenu=!this.showMoreMenu},closeMoreMenu:function(){this.showMoreMenu=!1},navigateToPage:function(t){var e=this.tabList.some((function(e){return e.pagePath===t}));if(e){uni.switchTab({url:t});var a=this.tabList.findIndex((function(e){return e.pagePath===t}));-1!==a&&(this.current=a)}else uni.navigateTo({url:t});this.closeMoreMenu()}},watch:{$route:{handler:function(){this.updateCurrentTab()},immediate:!0}}};e.default=i},"2c37":function(t,e,a){"use strict";var i=a("a918"),n=a.n(i);n.a},"30f7":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},a("7a76"),a("c9b5")},4467:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'.page-container[data-v-8d885e16]{display:flex;flex-direction:column;height:100vh;background-color:#f5f7fa;width:100%;overflow-x:hidden;box-sizing:border-box}.page-header[data-v-8d885e16]{display:flex;align-items:center;justify-content:space-between;padding:%?30?% %?40?%;border-bottom:%?1?% solid #e0e0e0;background-color:#fff;position:-webkit-sticky;position:sticky;top:0;z-index:10}.page-title[data-v-8d885e16]{font-size:%?36?%;font-weight:700;color:#333}.header-actions[data-v-8d885e16]{display:flex;gap:%?30?%}.header-icon[data-v-8d885e16]{color:#666;font-size:%?40?%;display:flex;align-items:center;justify-content:center}.tabs-container[data-v-8d885e16]{display:flex;padding:0 %?20?%;border-bottom:%?1?% solid #e0e0e0;background-color:#fff;position:-webkit-sticky;position:sticky;top:%?120?%;z-index:9}.tab-item[data-v-8d885e16]{padding:%?24?% %?40?%;font-size:%?28?%;color:#666;position:relative}.tab-item.active[data-v-8d885e16]{color:#3370ff;font-weight:500}.tab-item.active[data-v-8d885e16]:after{content:"";position:absolute;bottom:0;left:%?40?%;right:%?40?%;height:%?4?%;background-color:#3370ff;border-radius:%?4?%}.interaction-list[data-v-8d885e16]{flex:1;padding:%?20?%}.interaction-card[data-v-8d885e16]{background-color:#fff;border-radius:%?16?%;margin-bottom:%?20?%;padding:%?30?%;box-shadow:0 %?2?% %?10?% rgba(0,0,0,.05)}.interaction-header[data-v-8d885e16]{display:flex;align-items:center;margin-bottom:%?20?%}.interaction-icon[data-v-8d885e16]{width:%?80?%;height:%?80?%;display:flex;justify-content:center;align-items:center;border-radius:50%;margin-right:%?20?%;color:#fff}.icon-call[data-v-8d885e16]{background-color:#3370ff}.icon-meeting[data-v-8d885e16]{background-color:#722ed1}.icon-email[data-v-8d885e16]{background-color:#13c2c2}.icon-visit[data-v-8d885e16]{background-color:#fa8c16}.icon-note[data-v-8d885e16], .icon-other[data-v-8d885e16]{background-color:#52c41a}.interaction-title[data-v-8d885e16]{flex:1;display:flex;flex-direction:column}.interaction-type[data-v-8d885e16]{font-size:%?24?%;color:#999;margin-bottom:%?8?%}.interaction-subject[data-v-8d885e16]{font-size:%?32?%;font-weight:500;color:#333}.interaction-time[data-v-8d885e16]{font-size:%?24?%;color:#999}.interaction-detail[data-v-8d885e16]{display:flex;align-items:center;margin-bottom:%?16?%;font-size:%?26?%}.detail-label[data-v-8d885e16]{color:#999;width:%?160?%}.detail-value[data-v-8d885e16]{color:#666;flex:1}.interaction-content[data-v-8d885e16]{font-size:%?28?%;line-height:1.6;color:#333;margin-bottom:%?20?%;word-break:break-all}.next-steps[data-v-8d885e16]{display:flex;flex-wrap:wrap;gap:%?16?%;margin-bottom:%?20?%}.next-step-item[data-v-8d885e16]{font-size:%?24?%;padding:%?8?% %?20?%;background-color:#e6f7ff;color:#1890ff;border-radius:%?100?%}.tag-container[data-v-8d885e16]{display:flex;flex-wrap:wrap;gap:%?16?%}.tag[data-v-8d885e16]{font-size:%?24?%;padding:%?8?% %?20?%;background-color:#f5f5f5;color:#666;border-radius:%?100?%}.empty-state[data-v-8d885e16]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?100?% 0;color:#a0a0a0}.empty-title[data-v-8d885e16]{font-size:%?32?%;margin:%?40?% 0 %?20?%}.empty-desc[data-v-8d885e16]{font-size:%?28?%}.loading-more[data-v-8d885e16]{text-align:center;padding:%?30?% 0;color:#999;font-size:%?28?%}.fab[data-v-8d885e16]{position:fixed;right:%?40?%;bottom:%?140?%;width:%?120?%;height:%?120?%;background-color:#3370ff;border-radius:50%;display:flex;align-items:center;justify-content:center;box-shadow:0 %?6?% %?16?% rgba(51,112,255,.3);z-index:100}.modal-filter[data-v-8d885e16]{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1000}.modal-mask[data-v-8d885e16]{position:absolute;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.5)}.modal-dialog[data-v-8d885e16]{position:absolute;bottom:0;left:0;right:0;background-color:#fff;border-top-left-radius:%?24?%;border-top-right-radius:%?24?%;padding-bottom:%?40?%;max-height:80vh;overflow:auto}.modal-header[data-v-8d885e16]{display:flex;justify-content:space-between;align-items:center;padding:%?30?% %?40?%;border-bottom:%?1?% solid #f0f0f0}.modal-title[data-v-8d885e16]{font-size:%?32?%;font-weight:700}.modal-close[data-v-8d885e16]{color:#999}.modal-body[data-v-8d885e16]{padding:%?30?%}.filter-section[data-v-8d885e16]{margin-bottom:%?40?%}.filter-title[data-v-8d885e16]{font-size:%?28?%;font-weight:500;margin-bottom:%?20?%}.filter-options[data-v-8d885e16]{display:flex;flex-wrap:wrap;gap:%?20?%}.filter-tag[data-v-8d885e16]{padding:%?16?% %?30?%;border-radius:%?100?%;font-size:%?26?%;background-color:#f5f7fa;color:#666}.filter-tag.active[data-v-8d885e16]{background-color:#e6f0ff;color:#3370ff;font-weight:500}.modal-footer[data-v-8d885e16]{padding:0 %?30?%}.filter-actions[data-v-8d885e16]{display:flex;gap:%?30?%}.btn[data-v-8d885e16]{flex:1;height:%?88?%;border-radius:%?8?%;display:flex;align-items:center;justify-content:center;font-size:%?30?%}.btn-outline[data-v-8d885e16]{border:%?1?% solid #d9d9d9;color:#666}.btn-primary[data-v-8d885e16]{background-color:#3370ff;color:#fff}.card-hover[data-v-8d885e16]{opacity:.8}',""]),t.exports=e},"44be":function(t,e,a){"use strict";a.r(e);var i=a("06d6"),n=a("e505");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("2c37");var s=a("828b"),r=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"8d885e16",null,!1,i["a"],void 0);e["default"]=r.exports},4733:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if(Array.isArray(t))return(0,i.default)(t)};var i=function(t){return t&&t.__esModule?t:{default:t}}(a("8d0b"))},7775:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={svgIcon:a("8a0f").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"custom-tab-bar"},[t._l(t.tabList,(function(e,i){return a("v-uni-view",{key:i,staticClass:"tab-item",class:{active:t.current===i},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.handleTabClick(e,i)}}},[a("svg-icon",{attrs:{name:t.current===i?e.selectedIconPath:e.iconPath,type:"svg",size:24,color:t.current===i?t.activeColor:t.color}}),a("v-uni-text",{staticClass:"tab-text",class:{"active-text":t.current===i}},[t._v(t._s(e.text))])],1)})),t.showMoreMenu?a("v-uni-view",{staticClass:"more-menu"},[a("v-uni-view",{staticClass:"menu-overlay",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeMoreMenu.apply(void 0,arguments)}}}),a("v-uni-view",{staticClass:"menu-content"},[a("v-uni-view",{staticClass:"menu-header"},[a("v-uni-text",{staticClass:"menu-title"},[t._v("更多功能")]),a("v-uni-view",{staticClass:"menu-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeMoreMenu.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"close",type:"svg",size:32,color:"#666"}})],1)],1),a("v-uni-view",{staticClass:"menu-list"},t._l(t.moreMenuList,(function(e,i){return a("v-uni-view",{key:i,staticClass:"menu-item",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.navigateToPage(e.pagePath)}}},[a("svg-icon",{attrs:{name:e.iconPath,type:"svg",size:24,color:"#333333"}}),a("v-uni-text",{staticClass:"menu-item-text"},[t._v(t._s(e.text))])],1)})),1)],1)],1):t._e()],2)},o=[]},a918:function(t,e,a){var i=a("4467");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("172231c1",i,!0,{sourceMap:!1,shadowMode:!1})},b7c7:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,i.default)(t)||(0,n.default)(t)||(0,o.default)(t)||(0,s.default)()};var i=r(a("4733")),n=r(a("d14d")),o=r(a("5d6b")),s=r(a("30f7"));function r(t){return t&&t.__esModule?t:{default:t}}},bf69:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,".custom-tab-bar[data-v-6a709636]{display:flex;justify-content:space-around;align-items:center;background-color:#fff;box-shadow:0 -1px 5px rgba(0,0,0,.1);height:%?100?%;position:fixed;bottom:0;left:0;right:0;z-index:999;padding-bottom:env(safe-area-inset-bottom)}.tab-item[data-v-6a709636]{display:flex;flex-direction:column;align-items:center;justify-content:center;flex:1;height:100%;padding:%?10?% 0}.tab-text[data-v-6a709636]{font-size:%?22?%;color:#333;margin-top:%?4?%}.active-text[data-v-6a709636]{color:#007aff}\n\n/* 更多菜单样式 */.more-menu[data-v-6a709636]{position:fixed;top:0;right:0;bottom:0;left:0;z-index:1000}.menu-overlay[data-v-6a709636]{position:absolute;top:0;right:0;bottom:0;left:0;background-color:rgba(0,0,0,.5)}.menu-content[data-v-6a709636]{position:absolute;bottom:%?100?%;left:0;right:0;background-color:#fff;border-top-left-radius:%?20?%;border-top-right-radius:%?20?%;overflow:hidden;-webkit-animation:slideUp-data-v-6a709636 .3s ease;animation:slideUp-data-v-6a709636 .3s ease;box-shadow:0 -2px 10px rgba(0,0,0,.1)}.menu-header[data-v-6a709636]{display:flex;justify-content:space-between;align-items:center;padding:%?30?%;border-bottom:1px solid #f0f0f0}.menu-title[data-v-6a709636]{font-size:%?32?%;font-weight:500;color:#333}.menu-close[data-v-6a709636]{padding:%?10?%}.menu-list[data-v-6a709636]{display:flex;flex-wrap:wrap;padding:%?20?%}.menu-item[data-v-6a709636]{width:25%;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?20?% 0}.menu-item-text[data-v-6a709636]{font-size:%?24?%;color:#333;margin-top:%?10?%;text-align:center}@-webkit-keyframes slideUp-data-v-6a709636{from{-webkit-transform:translateY(100%);transform:translateY(100%)}to{-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes slideUp-data-v-6a709636{from{-webkit-transform:translateY(100%);transform:translateY(100%)}to{-webkit-transform:translateY(0);transform:translateY(0)}}",""]),t.exports=e},c9cf:function(t,e,a){"use strict";var i=a("e9f7"),n=a.n(i);n.a},d14d:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},a("01a2"),a("e39c"),a("bf0f"),a("844d"),a("18f7"),a("de6c"),a("08eb")},e505:function(t,e,a){"use strict";a.r(e);var i=a("ef53"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},e9f7:function(t,e,a){var i=a("bf69");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("9e21a296",i,!0,{sourceMap:!1,shadowMode:!1})},eab4:function(t,e,a){"use strict";a.r(e);var i=a("7775"),n=a("1880");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("c9cf");var s=a("828b"),r=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"6a709636",null,!1,i["a"],void 0);e["default"]=r.exports},ef53:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("8f71"),a("bf0f"),a("4626"),a("5ac7"),a("aa77"),a("c223"),a("5ef2"),a("dd2b"),a("aa9c");var n=i(a("b7c7")),o=i(a("8a0f")),s=i(a("eab4")),r={components:{SvgIcon:o.default,CustomTabBar:s.default},data:function(){return{currentTab:"all",showFilter:!1,showLoadMore:!1,searchText:"",tabs:[{label:"全部",value:"all"},{label:"客户跟进",value:"follow-up"},{label:"详细沟通",value:"communication"},{label:"我的",value:"mine"}],typeOptions:[{label:"电话",value:"call"},{label:"会议",value:"meeting"},{label:"邮件",value:"email"},{label:"拜访",value:"visit"},{label:"其他",value:"other"}],relatedOptions:[{label:"客户",value:"customer"},{label:"商机",value:"opportunity"},{label:"合同",value:"contract"},{label:"联系人",value:"contact"}],timeOptions:[{label:"今天",value:"today"},{label:"昨天",value:"yesterday"},{label:"本周",value:"thisWeek"},{label:"本月",value:"thisMonth"},{label:"全部",value:"all"}],selectedTypes:[],selectedRelatedTypes:[],selectedTimeRange:"all",interactionItems:[{id:"1",type:"call",recordType:"follow-up",subject:"电话沟通产品需求",time:"今天 15:30",relatedObject:"北京科技有限公司",contacts:"张经理",owner:"李销售",content:"与张经理沟通了产品功能和交付时间需求，客户希望在下周一前收到详细方案。",nextSteps:["发送方案","安排面谈"],tags:[]},{id:"2",type:"visit",recordType:"follow-up",subject:"客户现场拜访",time:"昨天 14:00",relatedObject:"上海电子科技有限公司",contacts:"王总监",owner:"赵销售",content:"拜访了客户总部，与技术和采购部门进行了深入交流，了解了客户核心需求。",nextSteps:["制定详细方案","跟进技术需求"],tags:["重要客户"]},{id:"3",type:"meeting",recordType:"communication",subject:"项目需求讨论会议",time:"2023-10-20 10:00",relatedObject:"上海电子科技有限公司 - 企业ERP升级项目",contacts:"李总, 王经理, 郑工程师, 赵销售",owner:"赵销售",content:"与客户进行了2小时的需求讨论，确定了项目范围和主要功能模块。客户希望3个月内完成第一阶段上线，预算有限，需要优化方案。",tags:["需求确认","进行中"]},{id:"4",type:"email",recordType:"communication",subject:"发送产品规格说明书",time:"2023-10-18 16:30",relatedObject:"广州未来科技有限公司 - 数据中心升级项目",contacts:"陈经理",owner:"刘销售",content:"根据客户要求，发送了产品详细规格说明书和初步报价单，等待客户确认。",tags:["跟进中"]}]}},computed:{filteredInteractions:function(){if(this.showFilter)return this.interactionItems;var t=(0,n.default)(this.interactionItems);if("follow-up"===this.currentTab?t=t.filter((function(t){return"follow-up"===t.recordType})):"communication"===this.currentTab?t=t.filter((function(t){return"communication"===t.recordType})):"mine"===this.currentTab&&(t=t.filter((function(t){return"李销售"===t.owner}))),this.searchText){var e=this.searchText.toLowerCase();t=t.filter((function(t){return t.subject.toLowerCase().includes(e)||t.content&&t.content.toLowerCase().includes(e)||t.relatedObject&&t.relatedObject.toLowerCase().includes(e)}))}return t}},methods:{changeTab:function(t){this.currentTab=t},navigateToSearch:function(){uni.navigateTo({url:"/pages/common/search?type=interaction"})},navigateToCreate:function(){var t="/pages/interactions/interaction-create";"follow-up"===this.currentTab?t+="?recordType=follow-up":"communication"===this.currentTab&&(t+="?recordType=communication"),uni.navigateTo({url:t})},navigateToDetail:function(t){var e=this.interactionItems.find((function(e){return e.id===t}));e&&uni.navigateTo({url:"/pages/interactions/interaction-detail?id=".concat(t,"&recordType=").concat(e.recordType)})},loadMore:function(){var t=this;this.showLoadMore||(this.showLoadMore=!0,setTimeout((function(){t.showLoadMore=!1}),1500))},showFilterPanel:function(){this.showFilter=!0},hideFilterPanel:function(){this.showFilter=!1},toggleTypeFilter:function(t){var e=this.selectedTypes.indexOf(t);e>-1?this.selectedTypes.splice(e,1):this.selectedTypes.push(t)},toggleRelatedFilter:function(t){var e=this.selectedRelatedTypes.indexOf(t);e>-1?this.selectedRelatedTypes.splice(e,1):this.selectedRelatedTypes.push(t)},setTimeRangeFilter:function(t){this.selectedTimeRange=t},resetFilters:function(){this.selectedTypes=[],this.selectedRelatedTypes=[],this.selectedTimeRange="all"},applyFilters:function(){this.hideFilterPanel(),uni.showToast({title:"筛选已应用",icon:"none"})},getIconName:function(t){return{call:"phone",meeting:"team",email:"mail",visit:"navigation",note:"file-list",other:"message"}[t]||"message"},getTypeName:function(t){return{call:"电话",meeting:"会议",email:"邮件",visit:"拜访",note:"记录",other:"其他"}[t]||"沟通"},getContactLabel:function(t){return"meeting"===t?"参与人":"email"===t?"收件人":"联系人"}},onLoad:function(){console.log("加载沟通记录列表")},onShow:function(){"undefined"!==typeof this.$refs.customTabBar&&(this.$refs.customTabBar.current=2)},onPullDownRefresh:function(){setTimeout((function(){uni.stopPullDownRefresh()}),1e3)}};e.default=r}}]);
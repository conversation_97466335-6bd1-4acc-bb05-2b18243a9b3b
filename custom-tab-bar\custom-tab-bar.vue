<template>
  <view>
    <index v-if="show"></index>
  </view>
</template>

<script>
import Index from './index.vue'

export default {
  components: {
    Index
  },
  data() {
    return {
      show: true
    }
  },
  created() {
    const app = getApp()
    app.globalData.customTabBar = this
  },
  methods: {
    setTabIndex(index) {
      if (this.$children && this.$children[0]) {
        this.$children[0].selected = index
      }
    }
  }
}
</script>

<style>
/* 无需额外样式 */
</style> 
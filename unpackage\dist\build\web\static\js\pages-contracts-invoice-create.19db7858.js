(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-contracts-invoice-create"],{"0205":function(t,e,i){"use strict";i.r(e);var a=i("3ef5"),n=i("4aa7");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("6e80");var s=i("828b"),c=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"777c92eb",null,!1,a["a"],void 0);e["default"]=c.exports},"3ef5":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"invoice-create-container"},[i("v-uni-view",{staticClass:"page-header"},[i("v-uni-view",{staticClass:"back-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"ri-arrow-left-line"})],1),i("v-uni-text",{staticClass:"page-title"},[t._v("创建发票")]),i("v-uni-view",{staticClass:"header-actions"})],1),i("v-uni-scroll-view",{staticClass:"invoice-container",attrs:{"scroll-y":!0}},[i("v-uni-view",{staticClass:"section-card"},[i("v-uni-view",{staticClass:"section-title"},[t._v("基本信息")]),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-view",{staticClass:"form-label required"},[t._v("标题")]),i("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入发票标题"},model:{value:t.invoice.title,callback:function(e){t.$set(t.invoice,"title",e)},expression:"invoice.title"}})],1),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-view",{staticClass:"form-label required"},[t._v("客户")]),i("v-uni-view",{staticClass:"selector",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showCustomerSelector.apply(void 0,arguments)}}},[t.invoice.customer?i("v-uni-text",[t._v(t._s(t.invoice.customer.name))]):i("v-uni-text",{staticClass:"placeholder"},[t._v("请选择客户")]),i("v-uni-text",{staticClass:"ri-arrow-right-s-line"})],1)],1),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-view",{staticClass:"form-label"},[t._v("关联合同")]),i("v-uni-view",{staticClass:"selector",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showContractSelector.apply(void 0,arguments)}}},[t.invoice.contract?i("v-uni-text",[t._v(t._s(t.invoice.contract.name))]):i("v-uni-text",{staticClass:"placeholder"},[t._v("请选择合同（可选）")]),i("v-uni-text",{staticClass:"ri-arrow-right-s-line"})],1)],1),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-view",{staticClass:"form-label required"},[t._v("发票类型")]),i("v-uni-picker",{attrs:{value:t.invoiceTypeIndex,range:t.invoiceTypes},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onInvoiceTypeChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"selector"},[t.invoice.invoiceType?i("v-uni-text",[t._v(t._s(t.invoice.invoiceType))]):i("v-uni-text",{staticClass:"placeholder"},[t._v("请选择发票类型")]),i("v-uni-text",{staticClass:"ri-arrow-down-s-line"})],1)],1)],1),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-view",{staticClass:"form-label required"},[t._v("开票日期")]),i("v-uni-picker",{attrs:{mode:"date",value:t.invoice.invoiceDate},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onInvoiceDateChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"selector"},[t.invoice.invoiceDate?i("v-uni-text",[t._v(t._s(t.invoice.invoiceDate))]):i("v-uni-text",{staticClass:"placeholder"},[t._v("请选择开票日期")]),i("v-uni-text",{staticClass:"ri-calendar-line"})],1)],1)],1),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-view",{staticClass:"form-label required"},[t._v("付款期限")]),i("v-uni-picker",{attrs:{mode:"date",value:t.invoice.dueDate},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onDueDateChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"selector"},[t.invoice.dueDate?i("v-uni-text",[t._v(t._s(t.invoice.dueDate))]):i("v-uni-text",{staticClass:"placeholder"},[t._v("请选择付款期限")]),i("v-uni-text",{staticClass:"ri-calendar-line"})],1)],1)],1)],1),i("v-uni-view",{staticClass:"section-card"},[i("v-uni-view",{staticClass:"section-title"},[t._v("发票金额")]),i("v-uni-view",{staticClass:"invoice-items"},[i("v-uni-view",{staticClass:"invoice-items-header"},[i("v-uni-text",{staticClass:"item-name"},[t._v("项目名称")]),i("v-uni-text",{staticClass:"item-price"},[t._v("单价")]),i("v-uni-text",{staticClass:"item-quantity"},[t._v("数量")]),i("v-uni-text",{staticClass:"item-total"},[t._v("小计")]),i("v-uni-text",{staticClass:"item-action"})],1),t._l(t.invoice.items,(function(e,a){return i("v-uni-view",{key:a,staticClass:"invoice-item"},[i("v-uni-input",{staticClass:"item-name",attrs:{type:"text",placeholder:"项目名称"},model:{value:e.name,callback:function(i){t.$set(e,"name",i)},expression:"item.name"}}),i("v-uni-input",{staticClass:"item-price",attrs:{type:"digit",placeholder:"单价"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.calculateItemTotal(a)}},model:{value:e.price,callback:function(i){t.$set(e,"price",i)},expression:"item.price"}}),i("v-uni-input",{staticClass:"item-quantity",attrs:{type:"number",placeholder:"数量"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.calculateItemTotal(a)}},model:{value:e.quantity,callback:function(i){t.$set(e,"quantity",i)},expression:"item.quantity"}}),i("v-uni-text",{staticClass:"item-total"},[t._v("¥"+t._s(t.formatMoney(e.total)))]),i("v-uni-button",{staticClass:"delete-btn",attrs:{type:"button"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.removeItem(a)}}},[i("v-uni-text",{staticClass:"ri-delete-bin-line"})],1)],1)})),i("v-uni-button",{staticClass:"add-item-btn",attrs:{type:"button"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.addItem.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"ri-add-line"}),i("v-uni-text",[t._v("添加项目")])],1),i("v-uni-view",{staticClass:"invoice-summary"},[i("v-uni-view",{staticClass:"summary-row"},[i("v-uni-text",{staticClass:"summary-label"},[t._v("不含税金额:")]),i("v-uni-text",{staticClass:"summary-value"},[t._v("¥"+t._s(t.formatMoney(t.invoice.amountBeforeTax)))])],1),i("v-uni-view",{staticClass:"summary-row"},[i("v-uni-text",{staticClass:"summary-label"},[t._v("税额:"),i("v-uni-picker",{attrs:{value:t.taxRateIndex,range:t.taxRates},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onTaxRateChange.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"tax-rate-selector"},[t._v(t._s(t.invoice.taxRate)+"%")])],1)],1),i("v-uni-text",{staticClass:"summary-value"},[t._v("¥"+t._s(t.formatMoney(t.invoice.taxAmount)))])],1),i("v-uni-view",{staticClass:"summary-row total-row"},[i("v-uni-text",{staticClass:"summary-label"},[t._v("总计:")]),i("v-uni-text",{staticClass:"summary-value"},[t._v("¥"+t._s(t.formatMoney(t.invoice.totalAmount)))])],1)],1)],2)],1),t.invoice.customer?i("v-uni-view",{staticClass:"section-card"},[i("v-uni-view",{staticClass:"section-title"},[t._v("发票接收信息")]),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-view",{staticClass:"form-label"},[t._v("税号")]),i("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入税号"},model:{value:t.invoice.customer.taxNumber,callback:function(e){t.$set(t.invoice.customer,"taxNumber",e)},expression:"invoice.customer.taxNumber"}})],1),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-view",{staticClass:"form-label"},[t._v("开户行")]),i("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入开户行"},model:{value:t.invoice.customer.bank,callback:function(e){t.$set(t.invoice.customer,"bank",e)},expression:"invoice.customer.bank"}})],1),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-view",{staticClass:"form-label"},[t._v("账号")]),i("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入账号"},model:{value:t.invoice.customer.accountNumber,callback:function(e){t.$set(t.invoice.customer,"accountNumber",e)},expression:"invoice.customer.accountNumber"}})],1),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-view",{staticClass:"form-label"},[t._v("地址电话")]),i("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入地址电话"},model:{value:t.invoice.customer.addressAndPhone,callback:function(e){t.$set(t.invoice.customer,"addressAndPhone",e)},expression:"invoice.customer.addressAndPhone"}})],1)],1):t._e(),i("v-uni-view",{staticClass:"section-card"},[i("v-uni-view",{staticClass:"section-title"},[t._v("备注信息")]),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-textarea",{staticClass:"form-textarea",attrs:{placeholder:"请输入备注信息"},model:{value:t.invoice.notes,callback:function(e){t.$set(t.invoice,"notes",e)},expression:"invoice.notes"}})],1)],1)],1),i("v-uni-view",{staticClass:"float-actions"},[i("v-uni-button",{staticClass:"action-btn secondary-action",attrs:{type:"button"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveDraft.apply(void 0,arguments)}}},[i("v-uni-text",[t._v("保存草稿")])],1),i("v-uni-button",{staticClass:"action-btn primary-action",attrs:{type:"button"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.submitInvoice.apply(void 0,arguments)}}},[i("v-uni-text",[t._v("创建发票")])],1)],1)],1)},n=[]},"4aa7":function(t,e,i){"use strict";i.r(e);var a=i("7e48"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"6e80":function(t,e,i){"use strict";var a=i("b64f"),n=i.n(a);n.a},"7e48":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("795c"),i("c223"),i("e838"),i("aa9c"),i("dd2b"),i("473f"),i("bf0f"),i("2797");var a={data:function(){return{invoice:{title:"",customer:null,contract:null,invoiceType:"",invoiceDate:this.formatDate(new Date),dueDate:this.formatDate(new Date(Date.now()+1296e6)),items:[{name:"",price:"",quantity:"",total:0}],amountBeforeTax:0,taxRate:13,taxAmount:0,totalAmount:0,notes:""},invoiceTypes:["增值税专用发票","增值税普通发票","电子发票"],invoiceTypeIndex:0,taxRates:[0,3,6,9,13],taxRateIndex:4,isLoading:!1}},onLoad:function(t){t.contractId&&this.loadContractData(t.contractId)},methods:{loadContractData:function(t){console.log("加载合同ID:",t),this.invoice.contract={id:t,name:"系统集成服务合同",number:"CT-2023-09-001"},this.invoice.title="系统集成项目 - 第一期发票",this.invoice.items=[{name:"软件系统集成服务",price:"170000",quantity:"1",total:17e4},{name:"数据迁移服务",price:"87500",quantity:"1",total:87500}],this.invoice.customer={id:"1001",name:"上海智能科技",taxNumber:"91310000MA1FL4CT3X",bank:"中国建设银行上海张江支行",accountNumber:"31050161393600000123",addressAndPhone:"上海市浦东新区张江高科技园区科苑路88号 021-********"},this.calculateTotalAmount()},formatDate:function(t){var e=t.getFullYear(),i=String(t.getMonth()+1).padStart(2,"0"),a=String(t.getDate()).padStart(2,"0");return"".concat(e,"-").concat(i,"-").concat(a)},formatMoney:function(t){return parseFloat(t||0).toLocaleString("zh-CN")},goBack:function(){uni.navigateBack()},addItem:function(){this.invoice.items.push({name:"",price:"",quantity:"",total:0})},removeItem:function(t){this.invoice.items.length>1?(this.invoice.items.splice(t,1),this.calculateTotalAmount()):uni.showToast({title:"至少需要一个项目",icon:"none"})},calculateItemTotal:function(t){var e=this.invoice.items[t],i=parseFloat(e.price)||0,a=parseFloat(e.quantity)||0;e.total=i*a,this.calculateTotalAmount()},calculateTotalAmount:function(){this.invoice.amountBeforeTax=this.invoice.items.reduce((function(t,e){return t+(e.total||0)}),0),this.invoice.taxAmount=this.invoice.amountBeforeTax*(this.invoice.taxRate/100),this.invoice.totalAmount=this.invoice.amountBeforeTax+this.invoice.taxAmount},onInvoiceTypeChange:function(t){this.invoiceTypeIndex=t.detail.value,this.invoice.invoiceType=this.invoiceTypes[this.invoiceTypeIndex]},onInvoiceDateChange:function(t){this.invoice.invoiceDate=t.detail.value},onDueDateChange:function(t){this.invoice.dueDate=t.detail.value},onTaxRateChange:function(t){this.taxRateIndex=t.detail.value,this.invoice.taxRate=this.taxRates[this.taxRateIndex],this.calculateTotalAmount()},showCustomerSelector:function(){var t=this;uni.navigateTo({url:"/pages/customers/customer-selector?multiple=false",events:{customerSelected:function(e){t.invoice.customer=e}}})},showContractSelector:function(){var t=this;uni.navigateTo({url:"/pages/contracts/contract-selector?multiple=false&customerId="+(this.invoice.customer?this.invoice.customer.id:""),events:{contractSelected:function(e){t.invoice.contract=e}}})},validateForm:function(){if(!this.invoice.title)return uni.showToast({title:"请输入发票标题",icon:"none"}),!1;if(!this.invoice.customer)return uni.showToast({title:"请选择客户",icon:"none"}),!1;if(!this.invoice.invoiceType)return uni.showToast({title:"请选择发票类型",icon:"none"}),!1;if(!this.invoice.invoiceDate)return uni.showToast({title:"请选择开票日期",icon:"none"}),!1;if(!this.invoice.dueDate)return uni.showToast({title:"请选择付款期限",icon:"none"}),!1;var t=!0;return this.invoice.items.forEach((function(e,i){e.name||(uni.showToast({title:"第".concat(i+1,"项未填写名称"),icon:"none"}),t=!1),e.price||(uni.showToast({title:"第".concat(i+1,"项未填写价格"),icon:"none"}),t=!1),e.quantity||(uni.showToast({title:"第".concat(i+1,"项未填写数量"),icon:"none"}),t=!1)})),t},saveDraft:function(){var t=this;this.invoice.title?(this.isLoading=!0,setTimeout((function(){t.isLoading=!1,uni.showToast({title:"草稿保存成功",icon:"success"}),setTimeout((function(){uni.navigateBack()}),1500)}),1e3)):uni.showToast({title:"请至少输入发票标题",icon:"none"})},submitInvoice:function(){var t=this;this.validateForm()&&(this.isLoading=!0,setTimeout((function(){t.isLoading=!1,uni.showToast({title:"发票创建成功",icon:"success"}),setTimeout((function(){uni.navigateBack()}),1500)}),1e3))}}};e.default=a},b64f:function(t,e,i){var a=i("d114");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("9e0486bc",a,!0,{sourceMap:!1,shadowMode:!1})},d114:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'.invoice-create-container[data-v-777c92eb]{display:flex;flex-direction:column;height:100vh;background-color:#f5f6fa}.page-header[data-v-777c92eb]{display:flex;align-items:center;padding:%?20?% %?30?%;background-color:#fff;box-shadow:0 %?2?% %?10?% rgba(0,0,0,.05);position:relative;z-index:10}.back-button[data-v-777c92eb]{width:%?60?%;height:%?60?%;display:flex;align-items:center;justify-content:center}.page-title[data-v-777c92eb]{flex:1;font-size:%?36?%;font-weight:600;text-align:center;margin-right:%?60?%}.header-actions[data-v-777c92eb]{width:%?60?%}.invoice-container[data-v-777c92eb]{flex:1;padding:%?30?%}.section-card[data-v-777c92eb]{background-color:#fff;border-radius:%?16?%;padding:%?30?%;margin-bottom:%?30?%;box-shadow:0 %?2?% %?8?% rgba(0,0,0,.03)}.section-title[data-v-777c92eb]{font-size:%?32?%;font-weight:600;margin-bottom:%?30?%}.form-item[data-v-777c92eb]{margin-bottom:%?24?%}.form-label[data-v-777c92eb]{font-size:%?28?%;margin-bottom:%?12?%;color:#333}.form-label.required[data-v-777c92eb]:after{content:"*";color:#ff4d4f;margin-left:%?8?%}.form-input[data-v-777c92eb]{width:100%;height:%?80?%;padding:0 %?24?%;border:%?2?% solid #e8e8e8;border-radius:%?12?%;font-size:%?28?%;background-color:#fff}.form-textarea[data-v-777c92eb]{width:100%;height:%?200?%;padding:%?20?% %?24?%;border:%?2?% solid #e8e8e8;border-radius:%?12?%;font-size:%?28?%;background-color:#fff}.selector[data-v-777c92eb]{display:flex;justify-content:space-between;align-items:center;width:100%;height:%?80?%;padding:0 %?24?%;border:%?2?% solid #e8e8e8;border-radius:%?12?%;font-size:%?28?%;background-color:#fff}.placeholder[data-v-777c92eb]{color:#bbb}.invoice-items[data-v-777c92eb]{margin-top:%?20?%}.invoice-items-header[data-v-777c92eb]{display:flex;padding:%?16?% 0;border-bottom:%?2?% solid #e8e8e8;font-size:%?24?%;color:#8c8c8c}.invoice-item[data-v-777c92eb]{display:flex;padding:%?16?% 0;border-bottom:%?2?% solid #f0f0f0;align-items:center}.item-name[data-v-777c92eb]{flex:2;padding:0 %?8?%;font-size:%?28?%}.item-price[data-v-777c92eb], .item-quantity[data-v-777c92eb]{flex:1;padding:0 %?8?%;font-size:%?28?%;text-align:right}.item-total[data-v-777c92eb]{flex:1;padding:0 %?8?%;font-size:%?28?%;text-align:right}.item-action[data-v-777c92eb]{width:%?60?%;text-align:center}.delete-btn[data-v-777c92eb]{width:%?60?%;height:%?60?%;display:flex;align-items:center;justify-content:center;border:none;background:none;color:#ff4d4f;padding:0;font-size:%?32?%}.add-item-btn[data-v-777c92eb]{display:flex;align-items:center;justify-content:center;gap:%?10?%;margin-top:%?30?%;width:100%;padding:%?20?%;background-color:#f5f5f5;border:%?2?% dashed #e8e8e8;border-radius:%?16?%;color:#8c8c8c;font-size:%?28?%}.invoice-summary[data-v-777c92eb]{margin-top:%?40?%;border-top:%?2?% solid #e8e8e8;padding-top:%?30?%}.summary-row[data-v-777c92eb]{display:flex;justify-content:space-between;margin-bottom:%?16?%}.tax-rate-selector[data-v-777c92eb]{display:inline-block;padding:%?4?% %?16?%;background-color:#f5f5f5;border-radius:%?8?%;color:#1890ff}.total-row[data-v-777c92eb]{margin-top:%?20?%;font-weight:600;font-size:%?32?%}.float-actions[data-v-777c92eb]{position:fixed;bottom:0;left:0;right:0;display:flex;padding:%?30?%;background-color:#fff;border-top:%?2?% solid #f0f0f0;z-index:100}.action-btn[data-v-777c92eb]{flex:1;display:flex;align-items:center;justify-content:center;gap:%?10?%;padding:%?24?%;border-radius:%?16?%;font-size:%?28?%;font-weight:500}.primary-action[data-v-777c92eb]{background-color:#2979ff;color:#fff;border:none}.secondary-action[data-v-777c92eb]{background-color:#f5f5f5;color:#333;border:%?2?% solid #e8e8e8;margin-right:%?20?%}',""]),t.exports=e}}]);
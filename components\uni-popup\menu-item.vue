<template>
	<view :class="['menu-item', { 'menu-item-danger': danger }]" hover-class="menu-item-hover" @click="onClick">
		<view class="menu-item-content">
			<svg-icon v-if="icon" :name="icon" type="svg" :size="20" :color="computedIconColor"></svg-icon>
			<text :class="['menu-item-text', { 'with-icon': icon }]">{{ text }}</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'menu-item',
	props: {
		text: {
			type: String,
			default: ''
		},
		icon: {
			type: String,
			default: ''
		},
		iconColor: {
			type: String,
			default: ''
		},
		danger: {
			type: Boolean,
			default: false
		}
	},
	computed: {
		computedIconColor() {
			if (this.iconColor) return this.iconColor
			return this.danger ? '#ff5252' : '#333'
		}
	},
	methods: {
		onClick() {
			this.$emit('click')
		}
	}
}
</script>

<style>
.menu-item {
	padding: 15px 20px;
	position: relative;
}

.menu-item:after {
	content: '';
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	height: 1px;
	background-color: #eaeaea;
	transform: scaleY(0.5);
}

.menu-item:last-child:after {
	display: none;
}

.menu-item-hover {
	background-color: #f5f5f5;
}

.menu-item-content {
	display: flex;
	align-items: center;
}

.menu-item-text {
	font-size: 16px;
	color: #333;
}

.menu-item-text.with-icon {
	margin-left: 12px;
}

.menu-item-danger .menu-item-text {
	color: #ff5252;
}
</style> 
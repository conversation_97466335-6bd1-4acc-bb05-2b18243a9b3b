<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="goBack">
        <text class="ri-arrow-left-line"></text>
      </view>
      <text class="page-title">合同历史记录</text>
      <view class="header-actions">
        <button type="button" class="action-button" @click="showAdvancedFilter">
          <text class="ri-filter-3-line"></text>
        </button>
      </view>
    </view>
    
    <scroll-view scroll-y class="page-content">
      <view class="history-container">
        <!-- 合同信息 -->
        <view class="contract-info">
          <view class="contract-title">{{contract.title}}</view>
          <view class="contract-meta">
            <text>合同编号: {{contract.number}}</text>
            <text>当前版本: {{contract.version}}</text>
          </view>
          <view class="contract-meta">
            <text>创建日期: {{contract.createDate}}</text>
            <text>最后更新: {{contract.updateDate}}</text>
          </view>
        </view>
        
        <!-- 筛选栏 -->
        <scroll-view scroll-x class="filter-scroll">
          <view class="filter-bar">
            <view 
              v-for="(filter, index) in filters" 
              :key="index" 
              :class="['filter-button', currentFilter === filter.value ? 'active' : '']"
              @click="setFilter(filter.value)"
            >
              <text>{{filter.label}}</text>
            </view>
          </view>
        </scroll-view>
        
        <!-- 时间线 -->
        <view class="timeline">
          <view 
            v-for="(item, index) in filteredHistoryItems" 
            :key="index" 
            class="timeline-item"
          >
            <view :class="['timeline-dot', 'dot-' + item.type]"></view>
            <view class="timeline-content">
              <view class="timeline-header">
                <view>
                  <view class="timeline-title">
                    {{item.title}}
                    <text v-if="item.version" class="version-badge">{{item.version}}</text>
                  </view>
                  <view class="timeline-user">
                    <text class="avatar">{{item.user.initial}}</text>
                    <text>{{item.user.name}}（{{item.user.department}}）</text>
                  </view>
                </view>
                <view class="timeline-timestamp">{{item.timestamp}}</view>
              </view>
              
              <view class="timeline-details">
                <text>{{item.details}}</text>
                <view v-if="item.tags && item.tags.length">
                  <view 
                    v-for="(tag, tagIndex) in item.tags" 
                    :key="tagIndex" 
                    :class="['timeline-tag', tag.type]"
                  >
                    {{tag.label}}
                  </view>
                </view>
              </view>
              
              <!-- 变更详情 -->
              <view class="change-detail" v-if="item.changeDetail && item.showChangeDetail">
                <view 
                  v-for="(change, changeIndex) in item.changeDetail" 
                  :key="changeIndex" 
                  class="change-row"
                >
                  <view class="change-label">{{change.label}}：</view>
                  <view class="change-value">
                    <text v-if="change.oldValue" class="old">{{change.oldValue}}</text>
                    <text v-if="change.newValue" class="new">{{change.newValue}}</text>
                    <text v-if="!change.oldValue && !change.newValue">{{change.value}}</text>
                  </view>
                </view>
              </view>
              
              <!-- 操作按钮 -->
              <view class="timeline-actions" v-if="item.actions && item.actions.length">
                <view 
                  v-for="(action, actionIndex) in item.actions" 
                  :key="actionIndex" 
                  class="timeline-action"
                  @click="handleAction(action, item, index)"
                >
                  <text :class="action.icon"></text>
                  <text>{{action.label}}</text>
                </view>
              </view>
              
              <!-- 版本比较按钮 -->
              <button 
                v-if="item.type === 'version' && item.version !== '草稿'" 
                class="compare-button"
                @click="compareVersions(item)"
              >
                <text class="ri-file-copy-line"></text>
                <text>与上一版本比较</text>
              </button>
            </view>
          </view>
          
          <!-- 空状态 -->
          <view v-if="filteredHistoryItems.length === 0" class="empty-state">
            <text class="ri-history-line empty-icon"></text>
            <text class="empty-text">没有找到相关历史记录</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentFilter: 'all',
      filters: [
        { label: '全部', value: 'all' },
        { label: '版本更新', value: 'version' },
        { label: '编辑记录', value: 'edit' },
        { label: '审批记录', value: 'approval' },
        { label: '签署记录', value: 'signature' },
        { label: '备注', value: 'comment' }
      ],
      contract: {
        id: 'CT-2023-09-001',
        title: '企业系统集成项目合同',
        number: 'CT-2023-09-001',
        version: 'V1.0',
        createDate: '2023-09-15',
        updateDate: '2023-10-25'
      },
      historyItems: [
        {
          type: 'signature',
          title: '合同签署完成',
          user: {
            initial: '张',
            name: '张总监',
            department: '客户'
          },
          timestamp: '2023-10-25 14:30',
          details: '合同已由双方代表签署完成，正式生效。',
          actions: [
            {
              icon: 'ri-eye-line',
              label: '查看签署信息',
              type: 'view'
            }
          ]
        },
        {
          type: 'signature',
          title: '合同签署',
          user: {
            initial: '王',
            name: '王经理',
            department: '我方'
          },
          timestamp: '2023-10-25 10:15',
          details: '我方代表已完成合同签署，等待客户方签署。',
          actions: [
            {
              icon: 'ri-eye-line',
              label: '查看签署信息',
              type: 'view'
            }
          ]
        },
        {
          type: 'approval',
          title: '合同审批通过',
          user: {
            initial: '刘',
            name: '刘总监',
            department: '法务部'
          },
          timestamp: '2023-10-20 16:45',
          details: '合同条款已审核通过，可进入签署流程。',
          actions: [
            {
              icon: 'ri-eye-line',
              label: '查看审批意见',
              type: 'view'
            }
          ]
        },
        {
          type: 'approval',
          title: '合同提交审批',
          user: {
            initial: '王',
            name: '王经理',
            department: '销售部'
          },
          timestamp: '2023-10-18 09:30',
          details: '合同草案已提交法务部审批。'
        },
        {
          type: 'version',
          title: '创建合同版本',
          version: 'V1.0',
          user: {
            initial: '王',
            name: '王经理',
            department: '销售部'
          },
          timestamp: '2023-10-15 11:20',
          details: '根据客户需求和报价单，创建了合同正式版本。',
          tags: [
            { type: 'added', label: '新增' },
            { type: 'changed', label: '修改' }
          ],
          actions: [
            {
              icon: 'ri-list-check',
              label: '查看变更明细',
              type: 'toggle',
              target: 'changeDetail'
            }
          ],
          showChangeDetail: false,
          changeDetail: [
            {
              label: '合同金额',
              oldValue: '¥550,000',
              newValue: '¥581,950（含税）'
            },
            {
              label: '项目周期',
              oldValue: '120个工作日',
              newValue: '90个工作日'
            },
            {
              label: '增加条款',
              value: '知识产权保护条款、保密条款'
            }
          ]
        },
        {
          type: 'edit',
          title: '修改合同条款',
          user: {
            initial: '王',
            name: '王经理',
            department: '销售部'
          },
          timestamp: '2023-10-12 15:40',
          details: '根据客户反馈，调整了项目周期和付款方式。',
          tags: [
            { type: 'changed', label: '修改' }
          ],
          actions: [
            {
              icon: 'ri-eye-line',
              label: '查看详情',
              type: 'view'
            }
          ]
        },
        {
          type: 'comment',
          title: '客户反馈',
          user: {
            initial: '张',
            name: '张总监',
            department: '客户'
          },
          timestamp: '2023-10-10 11:05',
          details: '希望缩短项目周期至90工作日，并调整付款方式为"首付50%、中期30%、尾款20%"。'
        },
        {
          type: 'version',
          title: '创建合同草稿',
          version: '草稿',
          user: {
            initial: '王',
            name: '王经理',
            department: '销售部'
          },
          timestamp: '2023-09-25 14:30',
          details: '基于客户需求和初步沟通，创建了合同草稿。',
          actions: [
            {
              icon: 'ri-eye-line',
              label: '查看草稿',
              type: 'view'
            }
          ]
        },
        {
          type: 'create',
          title: '创建合同',
          user: {
            initial: '王',
            name: '王经理',
            department: '销售部'
          },
          timestamp: '2023-09-15 10:00',
          details: '基于报价单 QT-2023-09-005 创建合同。'
        }
      ]
    }
  },
  computed: {
    filteredHistoryItems() {
      if (this.currentFilter === 'all') {
        return this.historyItems;
      } else {
        // 处理特殊情况：审批记录
        if (this.currentFilter === 'approval') {
          return this.historyItems.filter(item => 
            item.type === 'approval' || 
            (item.title && item.title.includes('审批'))
          );
        }
        
        return this.historyItems.filter(item => item.type === this.currentFilter);
      }
    }
  },
  onLoad(options) {
    if (options.id) {
      this.loadContractHistory(options.id);
    }
    
    if (options.filter) {
      this.setFilter(options.filter);
    }
  },
  methods: {
    loadContractHistory(id) {
      // 实际应用中，这里应该从API获取合同历史数据
      console.log(`加载合同ID: ${id} 的历史记录`);
      // 本示例使用模拟数据，无需再次加载
    },
    goBack() {
      uni.navigateBack();
    },
    setFilter(filter) {
      this.currentFilter = filter;
    },
    handleAction(action, item, index) {
      if (action.type === 'toggle') {
        if (action.target === 'changeDetail') {
          // 显示/隐藏变更明细
          this.$set(this.historyItems[index], 'showChangeDetail', !item.showChangeDetail);
          if (item.showChangeDetail) {
            action.label = '收起变更明细';
            action.icon = 'ri-list-unordered';
          } else {
            action.label = '查看变更明细';
            action.icon = 'ri-list-check';
          }
        }
      } else if (action.type === 'view') {
        // 查看详情逻辑
        uni.showToast({
          title: '查看详情功能开发中...',
          icon: 'none'
        });
      }
    },
    compareVersions(item) {
      uni.showToast({
        title: '版本比较功能开发中...',
        icon: 'none'
      });
    },
    showAdvancedFilter() {
      uni.showToast({
        title: '高级筛选功能开发中...',
        icon: 'none'
      });
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  background-color: #fff;
  z-index: 10;
}

.page-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.back-button {
  color: #666;
  font-size: 24px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #666;
  background-color: #f5f5f5;
  border: 1px solid #eee;
  padding: 0;
  margin: 0;
  font-size: 20px;
}

.page-content {
  flex: 1;
  overflow-y: auto;
}

.history-container {
  padding: 16px;
  padding-bottom: 32px;
}

.contract-info {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #eee;
}

.contract-title {
  font-size: 17px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.contract-meta {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
  display: flex;
  justify-content: space-between;
}

.filter-scroll {
  margin-bottom: 20px;
  white-space: nowrap;
}

.filter-bar {
  display: flex;
  padding: 4px 0;
}

.filter-button {
  white-space: nowrap;
  background-color: #f5f5f5;
  border: 1px solid #eee;
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 14px;
  color: #666;
  margin-right: 10px;
  min-width: 60px;
  text-align: center;
}

.filter-button.active {
  background-color: #3a86ff;
  color: white;
  border-color: #3a86ff;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(58, 134, 255, 0.2);
}

.timeline {
  position: relative;
  margin-left: 12px;
  padding-left: 24px;
  border-left: 2px solid #e8e8e8;
}

.timeline-item {
  position: relative;
  margin-bottom: 24px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: -29px;
  top: 12px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: #3a86ff;
  border: 2px solid #fff;
  z-index: 2;
}

.dot-signature {
  background-color: #00b578;
}

.dot-approval, .dot-edit {
  background-color: #ff9a2a;
}

.dot-version {
  background-color: #45a6fc;
}

.dot-comment {
  background-color: #999;
}

.dot-create {
  background-color: #3a86ff;
}

.timeline-content {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #eee;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.timeline-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.timeline-timestamp {
  font-size: 13px;
  color: #999;
  white-space: nowrap;
  margin-left: 8px;
}

.timeline-user {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #e6f3ff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #3a86ff;
  font-weight: bold;
}

.timeline-details {
  margin-top: 8px;
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

.timeline-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 16px;
  font-size: 12px;
  margin-right: 6px;
  margin-top: 6px;
  background-color: #f5f5f5;
  color: #666;
}

.timeline-tag.added {
  background-color: #e6f7ee;
  color: #00b578;
}

.timeline-tag.removed {
  background-color: #ffece8;
  color: #ff4d4f;
}

.timeline-tag.changed {
  background-color: #fff5e6;
  color: #ff9a2a;
}

.timeline-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 12px;
}

.timeline-action {
  font-size: 14px;
  color: #3a86ff;
  display: flex;
  align-items: center;
  gap: 4px;
}

.change-detail {
  margin-top: 12px;
  padding: 12px;
  background-color: #f9f9f9;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.5;
}

.change-row {
  display: flex;
  margin-bottom: 6px;
}

.change-row:last-child {
  margin-bottom: 0;
}

.change-label {
  flex: 0 0 80px;
  color: #666;
}

.change-value {
  flex: 1;
  color: #333;
}

.old {
  text-decoration: line-through;
  color: #ff4d4f;
  margin-right: 6px;
}

.new {
  color: #00b578;
}

.version-badge {
  display: inline-block;
  padding: 2px 8px;
  background-color: #e6f3ff;
  color: #3a86ff;
  border-radius: 16px;
  font-size: 12px;
  margin-left: 6px;
}

.compare-button {
  margin-top: 12px;
  width: 100%;
  padding: 10px;
  background-color: #e6f3ff;
  color: #3a86ff;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
}

.empty-icon {
  font-size: 48px;
  color: #ddd;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 15px;
  color: #999;
}
</style> 
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-contracts-payment-history"],{"384e":function(e,t,a){"use strict";a.r(t);var i=a("acf4"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);t["default"]=n.a},41965:function(e,t,a){"use strict";var i=a("8741"),n=a.n(i);n.a},8741:function(e,t,a){var i=a("d4d4");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var n=a("967d").default;n("f050c0ae",i,!0,{sourceMap:!1,shadowMode:!1})},acf4:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("8f71"),a("bf0f");var i={data:function(){return{paymentInfo:{title:"系统集成项目 - 第一期",code:"PAY-2023-11-001",status:"已完成",statusCode:"completed",customer:"上海智能科技",amount:"290,975.00"},currentFilter:"all",filters:[{type:"all",name:"全部",icon:"ri-filter-line"},{type:"create",name:"创建",icon:"ri-add-line"},{type:"edit",name:"编辑",icon:"ri-edit-line"},{type:"status",name:"状态变更",icon:"ri-exchange-line"},{type:"payment",name:"收款记录",icon:"ri-bank-card-line"},{type:"comment",name:"备注",icon:"ri-chat-1-line"}],timeline:[{type:"payment",title:"确认收款",date:"2023-11-12",operator:"李财务",time:"14:30",message:"已确认收到客户全额付款，金额¥290,975.00，付款方式为银行转账。",changes:[{label:"收款状态",oldValue:"待收款",newValue:"已完成"},{label:"实收金额",oldValue:"¥0.00",newValue:"¥290,975.00"},{label:"交易参考号",oldValue:"-",newValue:"REF2023111201254895"}]},{type:"edit",title:"编辑收款信息",date:"2023-11-12",operator:"王销售",time:"11:25",message:"更新了收款信息，修改了客户银行账号信息。",changes:[{label:"开户行",oldValue:"中国建设银行上海分行",newValue:"中国建设银行上海张江支行"},{label:"客户账号",oldValue:"31050161393600000789",newValue:"31050161393600000123"}]},{type:"comment",title:"添加备注",date:"2023-11-12",operator:"李财务",time:"10:45",message:"客户已确认收款金额，将于今日下午通过银行转账支付。",changes:[]},{type:"status",title:"状态变更",date:"2023-11-11",operator:"系统",time:"00:00",message:"系统自动将收款状态从【待收款】变更为【逾期未收】。",changes:[{label:"收款状态",oldValue:"待收款",newValue:"逾期未收"}]},{type:"create",title:"创建收款记录",date:"2023-11-05",operator:"李财务",time:"15:20",message:"基于发票 INV-2023-11-001 创建了收款记录。",changes:[{label:"收款编号",newValue:"PAY-2023-11-001"},{label:"收款标题",newValue:"系统集成项目 - 第一期"},{label:"应收金额",newValue:"¥290,975.00"},{label:"收款状态",newValue:"待收款"}]}]}},computed:{filteredTimeline:function(){var e=this;return"all"===this.currentFilter?this.timeline:this.timeline.filter((function(t){return t.type===e.currentFilter}))}},methods:{goBack:function(){uni.navigateBack()},setFilter:function(e){this.currentFilter=e},getIconClass:function(e){return{create:"ri-add-line",edit:"ri-edit-line",status:"ri-exchange-line",payment:"ri-bank-card-line",comment:"ri-chat-1-line"}[e]||"ri-question-line"}}};t.default=i},b1e1:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"container"},[a("v-uni-view",{staticClass:"page-header"},[a("v-uni-view",{staticClass:"back-button",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.goBack.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-arrow-left-line"})],1),a("v-uni-text",{staticClass:"page-title"},[e._v("收款历史")])],1),a("v-uni-scroll-view",{staticClass:"history-container",attrs:{"scroll-y":!0}},[a("v-uni-view",{staticClass:"payment-info"},[a("v-uni-text",{staticClass:"payment-title"},[e._v(e._s(e.paymentInfo.title))]),a("v-uni-view",{staticClass:"payment-meta"},[a("v-uni-text",{staticClass:"payment-id"},[e._v("收款编号: "+e._s(e.paymentInfo.code))]),a("v-uni-text",{staticClass:"payment-status",class:"status-"+e.paymentInfo.statusCode},[e._v(e._s(e.paymentInfo.status))])],1),a("v-uni-view",{staticClass:"payment-details"},[a("v-uni-text",{staticClass:"payment-customer"},[e._v("客户: "+e._s(e.paymentInfo.customer))]),a("v-uni-text",{staticClass:"payment-amount"},[e._v("¥"+e._s(e.paymentInfo.amount))])],1)],1),a("v-uni-scroll-view",{staticClass:"filter-bar",attrs:{"scroll-x":!0}},e._l(e.filters,(function(t,i){return a("v-uni-view",{key:i,class:["filter-button",e.currentFilter===t.type?"active":""],on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.setFilter(t.type)}}},[a("v-uni-text",{class:t.icon}),a("v-uni-text",[e._v(e._s(t.name))])],1)})),1),a("v-uni-view",{staticClass:"timeline"},e._l(e.filteredTimeline,(function(t,i){return a("v-uni-view",{key:i,staticClass:"timeline-item"},[a("v-uni-view",{staticClass:"timeline-icon",class:"icon-"+t.type},[a("v-uni-text",{class:e.getIconClass(t.type)})],1),a("v-uni-view",{staticClass:"timeline-content"},[a("v-uni-view",{staticClass:"timeline-header"},[a("v-uni-text",{staticClass:"timeline-title"},[e._v(e._s(t.title))]),a("v-uni-text",{staticClass:"timeline-datetime"},[e._v(e._s(t.date))])],1),a("v-uni-text",{staticClass:"timeline-operator"},[e._v("操作人: "+e._s(t.operator)+" | "+e._s(t.time))]),a("v-uni-text",{staticClass:"timeline-message"},[e._v(e._s(t.message))]),t.changes&&t.changes.length>0?a("v-uni-view",{staticClass:"timeline-details"},e._l(t.changes,(function(t,i){return a("v-uni-view",{key:i,staticClass:"change-item"},[a("v-uni-text",{staticClass:"change-label"},[e._v(e._s(t.label))]),a("v-uni-view",{staticClass:"change-values"},[t.oldValue?a("v-uni-text",{staticClass:"old-value"},[e._v(e._s(t.oldValue))]):e._e(),t.oldValue&&t.newValue?a("v-uni-text",{staticClass:"arrow-icon ri-arrow-right-line"}):e._e(),a("v-uni-text",{staticClass:"new-value"},[e._v(e._s(t.newValue))])],1)],1)})),1):e._e()],1)],1)})),1),0===e.filteredTimeline.length?a("v-uni-view",{staticClass:"empty-state"},[a("v-uni-text",{staticClass:"ri-history-line empty-icon"}),a("v-uni-text",{staticClass:"empty-title"},[e._v("暂无历史记录")]),a("v-uni-text",{staticClass:"empty-description"},[e._v("该收款记录尚未产生任何操作历史，修改记录将在此处显示。")])],1):e._e()],1)],1)},n=[]},d4d4:function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'.container[data-v-063287e6]{display:flex;flex-direction:column;height:100vh}.page-header[data-v-063287e6]{display:flex;align-items:center;justify-content:space-between;padding:10px 15px;border-bottom:1px solid #e6e6e6;background-color:#fff}.page-title[data-v-063287e6]{font-size:18px;font-weight:700;color:#333}.back-button[data-v-063287e6]{color:#666;font-size:24px}.history-container[data-v-063287e6]{flex:1;padding:15px;margin-bottom:60px}.payment-info[data-v-063287e6]{background-color:#fff;border-radius:8px;padding:15px;margin-bottom:15px;border:1px solid #e6e6e6;box-shadow:0 2px 4px rgba(0,0,0,.05)}.payment-title[data-v-063287e6]{font-size:17px;font-weight:600;margin-bottom:8px;color:#333}.payment-meta[data-v-063287e6]{display:flex;justify-content:space-between;align-items:center;margin-bottom:8px}.payment-id[data-v-063287e6]{font-size:14px;color:#666}.payment-status[data-v-063287e6]{padding:4px 12px;border-radius:20px;font-size:12px;font-weight:500}.status-completed[data-v-063287e6]{background-color:rgba(0,200,81,.1);color:#00c851}.payment-details[data-v-063287e6]{display:flex;justify-content:space-between;margin-top:15px;padding-top:8px;border-top:1px solid #f0f0f0}.payment-customer[data-v-063287e6]{font-size:14px;color:#666}.payment-amount[data-v-063287e6]{font-size:16px;font-weight:600;color:#1890ff}.filter-bar[data-v-063287e6]{display:flex;white-space:nowrap;margin-bottom:15px}.filter-button[data-v-063287e6]{display:inline-flex;align-items:center;gap:5px;padding:6px 12px;border-radius:20px;background-color:#f5f5f5;color:#666;font-size:13px;border:1px solid #e6e6e6;margin-right:8px}.filter-button.active[data-v-063287e6]{background-color:rgba(24,144,255,.1);color:#1890ff;border-color:#1890ff;font-weight:500}.timeline[data-v-063287e6]{position:relative;margin-bottom:15px}.timeline[data-v-063287e6]::before{content:"";position:absolute;left:16px;top:0;height:100%;width:2px;background-color:#e6e6e6}.timeline-item[data-v-063287e6]{position:relative;margin-bottom:25px;padding-left:52px}.timeline-item[data-v-063287e6]:last-child{margin-bottom:0}.timeline-icon[data-v-063287e6]{position:absolute;left:0;top:0;width:34px;height:34px;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#fff;font-size:18px;z-index:2}.icon-create[data-v-063287e6]{background-color:#1890ff}.icon-edit[data-v-063287e6]{background-color:#faad14}.icon-status[data-v-063287e6]{background-color:#13c2c2}.icon-payment[data-v-063287e6]{background-color:#52c41a}.icon-comment[data-v-063287e6]{background-color:#722ed1}.timeline-content[data-v-063287e6]{background-color:#fff;border-radius:8px;padding:15px;border:1px solid #e6e6e6;box-shadow:0 2px 4px rgba(0,0,0,.05)}.timeline-header[data-v-063287e6]{display:flex;justify-content:space-between;margin-bottom:8px}.timeline-title[data-v-063287e6]{font-weight:600;color:#333}.timeline-datetime[data-v-063287e6]{font-size:13px;color:#666}.timeline-operator[data-v-063287e6]{font-size:14px;color:#666;margin-bottom:8px}.timeline-message[data-v-063287e6]{line-height:1.5;color:#333}.timeline-details[data-v-063287e6]{background-color:#f5f5f5;padding:10px;border-radius:8px;font-size:14px;margin-top:10px}.change-item[data-v-063287e6]{display:flex;justify-content:space-between;margin-bottom:5px;padding-bottom:5px;border-bottom:1px dashed #e6e6e6}.change-item[data-v-063287e6]:last-child{margin-bottom:0;padding-bottom:0;border-bottom:none}.change-label[data-v-063287e6]{color:#666}.change-values[data-v-063287e6]{display:flex;align-items:center;gap:15px}.old-value[data-v-063287e6]{color:#f5222d;text-decoration:line-through}.new-value[data-v-063287e6]{color:#52c41a;font-weight:500}.arrow-icon[data-v-063287e6]{color:#666;font-size:13px}.empty-state[data-v-063287e6]{text-align:center;padding:30px 15px;color:#666}.empty-icon[data-v-063287e6]{font-size:48px;margin-bottom:15px;color:#e6e6e6}.empty-title[data-v-063287e6]{font-size:17px;font-weight:600;margin-bottom:8px;color:#333}.empty-description[data-v-063287e6]{font-size:14px;max-width:280px;margin:0 auto;line-height:1.5}',""]),e.exports=t},ea1e:function(e,t,a){"use strict";a.r(t);var i=a("b1e1"),n=a("384e");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("41965");var l=a("828b"),s=Object(l["a"])(n["default"],i["b"],i["c"],!1,null,"063287e6",null,!1,i["a"],void 0);t["default"]=s.exports}}]);
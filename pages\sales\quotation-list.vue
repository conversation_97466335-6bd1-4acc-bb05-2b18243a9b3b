<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="goBack">
        <svg-icon name="arrow-left" type="svg" size="24"></svg-icon>
      </view>
      <text class="page-title">{{ pageTitle }}</text>
      <view class="header-actions">
        <view class="action-button" @click="navigateToCreate">
          <svg-icon name="add" type="svg" size="24"></svg-icon>
        </view>
        <view class="menu-container">
          <view class="action-button" @click="toggleMenu">
            <svg-icon name="more" type="svg" size="24"></svg-icon>
          </view>
          <view class="dropdown-menu" v-if="showMenu">
            <view class="dropdown-item" @click="exportExcel">
              <svg-icon name="file-excel" type="svg" size="20"></svg-icon>
              <text>导出Excel</text>
            </view>
            <view class="dropdown-item" @click="printBatch">
              <svg-icon name="printer" type="svg" size="20"></svg-icon>
              <text>批量打印</text>
            </view>
            <view class="dropdown-divider"></view>
            <view class="dropdown-item" @click="goToSettings">
              <svg-icon name="settings" type="svg" size="20"></svg-icon>
              <text>报价单设置</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 标签页 -->
    <scroll-view scroll-x class="tabs-container" :show-scrollbar="false">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="['tab', { active: currentTab === tab.status }]"
        @click="changeTab(tab.status)"
      >
        {{ tab.name }}
      </view>
    </scroll-view>

    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-button" @click="showFilterPanel">
        <svg-icon name="filter-3" type="svg" size="20"></svg-icon>
        <text>筛选</text>
      </view>
      <view class="sort-button" @click="showSortOptions">
        <svg-icon name="sort-desc" type="svg" size="20"></svg-icon>
        <text>排序</text>
      </view>
    </view>

    <!-- 报价单列表 -->
    <scroll-view 
      scroll-y 
      class="quotation-list"
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="loadMore"
    >
      <!-- 空状态 -->
      <view class="empty-state" v-if="filteredQuotations.length === 0">
        <view class="empty-icon">
          <svg-icon name="file-list-3" type="svg" size="64"></svg-icon>
        </view>
        <view class="empty-title">暂无报价单</view>
        <view class="empty-description">您还没有创建任何报价单，点击右上角的加号创建新报价单。</view>
        <button class="btn btn-primary" @click="navigateToCreate">
          <svg-icon name="add" type="svg" size="20"></svg-icon> 创建报价单
        </button>
      </view>
      
      <!-- 报价单卡片列表 -->
      <view 
        class="quotation-card" 
        v-for="(quotation, index) in filteredQuotations" 
        :key="index"
        :data-status="quotation.status.code"
      >
        <view class="quotation-header">
          <view>
            <view class="quotation-title">{{ quotation.title }}</view>
            <view class="quotation-id">报价单号: {{ quotation.code }}</view>
          </view>
          <view :class="['quotation-status', 'status-' + quotation.status.code]">{{ quotation.status.name }}</view>
        </view>
        <view class="quotation-content">
          <view class="quotation-details">
            <view class="detail-group">
              <view class="detail-label">客户</view>
              <view class="detail-value">{{ quotation.customer }}</view>
            </view>
            <view class="detail-group">
              <view class="detail-label">创建日期</view>
              <view class="detail-value">{{ quotation.createDate }}</view>
            </view>
            <view class="detail-group">
              <view class="detail-label">有效期至</view>
              <view class="detail-value">{{ quotation.validUntil }}</view>
            </view>
            <view class="detail-group">
              <view class="detail-label">负责人</view>
              <view class="detail-value">{{ quotation.owner }}</view>
            </view>
          </view>
          
          <view class="quotation-amount">
            <view class="amount-row" v-for="(item, itemIndex) in quotation.items" :key="itemIndex">
              <view>{{ item.name }}</view>
              <view>¥{{ formatPrice(item.amount) }}</view>
            </view>
            <view class="amount-row total">
              <view>总计</view>
              <view>¥{{ formatPrice(quotation.totalAmount) }}</view>
            </view>
          </view>
        </view>
        <view class="quotation-footer">
          <view class="quotation-action" @click="viewQuotation(quotation)">
            <svg-icon name="eye" type="svg" size="20"></svg-icon> 查看
          </view>
          
          <template v-if="quotation.status.code === 'sent'">
            <view class="quotation-action" @click="sendQuotation(quotation)">
              <svg-icon name="mail" type="svg" size="20"></svg-icon> 发送
            </view>
            <view class="quotation-action" @click="convertToContract(quotation)">
              <svg-icon name="contract" type="svg" size="20"></svg-icon> 转为合同
            </view>
          </template>
          
          <template v-if="quotation.status.code === 'accepted'">
            <view class="quotation-action" @click="downloadQuotation(quotation)">
              <svg-icon name="file-pdf" type="svg" size="20"></svg-icon> 下载
            </view>
            <view class="quotation-action" @click="convertToContract(quotation)">
              <svg-icon name="contract" type="svg" size="20"></svg-icon> 转为合同
            </view>
          </template>
          
          <template v-if="quotation.status.code === 'draft'">
            <view class="quotation-action" @click="editQuotation(quotation)">
              <svg-icon name="edit" type="svg" size="20"></svg-icon> 编辑
            </view>
            <view class="quotation-action" @click="sendQuotation(quotation)">
              <svg-icon name="mail" type="svg" size="20"></svg-icon> 发送
            </view>
            <view class="quotation-action" @click="deleteQuotation(quotation)">
              <svg-icon name="delete-bin" type="svg" size="20"></svg-icon> 删除
            </view>
          </template>
          
          <template v-if="quotation.status.code === 'rejected'">
            <view class="quotation-action" @click="downloadQuotation(quotation)">
              <svg-icon name="file-pdf" type="svg" size="20"></svg-icon> 下载
            </view>
            <view class="quotation-action" @click="sendQuotation(quotation)">
              <svg-icon name="mail" type="svg" size="20"></svg-icon> 发送
            </view>
          </template>
        </view>
      </view>
      
      <!-- 加载更多提示 -->
      <view v-if="loadingMore" class="loading-more">
        <text>加载更多...</text>
      </view>
    </scroll-view>
    
    <!-- 浮动添加按钮 -->
    <view class="fab" @click="navigateToCreate">
      <svg-icon name="add" type="svg" size="60" color="#FFFFFF"></svg-icon>
    </view>

    <!-- 自定义TabBar组件 -->
    <custom-tab-bar ref="customTabBar"></custom-tab-bar>
  </view>
</template>

<script>
import CustomTabBar from '@/components/CustomTabBar.vue';
import SvgIcon from '@/components/svg-icon.vue';

export default {
  components: {
    CustomTabBar,
    SvgIcon
  },
  data() {
    return {
      showMenu: false,
      isRefreshing: false,
      loadingMore: false,
      currentTab: 'all',
      opportunityId: '',
      companyName: '',
      tabs: [
        { name: '全部', status: 'all' },
        { name: '草稿', status: 'draft' },
        { name: '已发送', status: 'sent' },
        { name: '已接受', status: 'accepted' },
        { name: '已拒绝', status: 'rejected' }
      ],
      quotations: [
        {
          id: '1',
          title: '云数据分析平台解决方案',
          code: 'QT-2023-10-001',
          customer: '北京科技有限公司',
          createDate: '2023-10-10',
          validUntil: '2023-11-10',
          owner: '李销售',
          items: [
            { name: '产品费用', amount: 280000 },
            { name: '实施费用', amount: 50000 },
            { name: '年度维护', amount: 20000 }
          ],
          totalAmount: 350000,
          status: {
            code: 'sent',
            name: '已发送'
          }
        },
        {
          id: '2',
          title: '企业系统集成项目',
          code: 'QT-2023-09-005',
          customer: '上海智能科技',
          createDate: '2023-09-20',
          validUntil: '2023-10-20',
          owner: '王销售',
          items: [
            { name: '系统集成', amount: 320000 },
            { name: '定制开发', amount: 150000 },
            { name: '培训与支持', amount: 45000 }
          ],
          totalAmount: 515000,
          status: {
            code: 'accepted',
            name: '已接受'
          }
        },
        {
          id: '3',
          title: 'IT咨询服务方案',
          code: 'QT-2023-09-004',
          customer: '广州贸易有限公司',
          createDate: '2023-10-16',
          validUntil: '2023-11-16',
          owner: '赵销售',
          items: [
            { name: '咨询费用', amount: 120000 },
            { name: '系统评估', amount: 80000 },
            { name: '优化建议', amount: 60000 }
          ],
          totalAmount: 260000,
          status: {
            code: 'draft',
            name: '草稿'
          }
        },
        {
          id: '4',
          title: '企业系统集成项目',
          code: 'QT-2023-09-003',
          customer: '上海智能科技',
          createDate: '2023-09-20',
          validUntil: '2023-10-20',
          owner: '王销售',
          items: [
            { name: '系统集成', amount: 320000 },
            { name: '定制开发', amount: 150000 },
            { name: '培训与支持', amount: 45000 }
          ],
          totalAmount: 515000,
          status: {
            code: 'rejected',
            name: '已拒绝'
          }
        }
      ]
    }
  },
  computed: {
    filteredQuotations() {
      let result = this.quotations;
      
      if (this.currentTab !== 'all') {
        result = result.filter(item => item.status.code === this.currentTab);
      }
      
      if (this.opportunityId) {
        result = result.filter(item => {
          return item.opportunityId === this.opportunityId;
        });
      }
      
      return result;
    },
    
    pageTitle() {
      if (this.companyName) {
        return `${this.companyName}的报价单`;
      }
      return '报价单';
    }
  },
  methods: {
    formatPrice(price) {
      return price.toLocaleString('zh-CN');
    },
    goBack() {
      uni.navigateBack();
    },
    toggleMenu() {
      this.showMenu = !this.showMenu;
    },
    changeTab(status) {
      this.currentTab = status;
    },
    navigateToCreate() {
      uni.navigateTo({
        url: '/pages/sales/quotation-create'
      });
    },
    viewQuotation(quotation) {
      uni.navigateTo({
        url: `/pages/sales/quotation-detail?id=${quotation.id}`
      });
    },
    editQuotation(quotation) {
      uni.navigateTo({
        url: `/pages/sales/quotation-edit?id=${quotation.id}`
      });
    },
    sendQuotation(quotation) {
      uni.showToast({
        title: '发送报价单功能开发中...',
        icon: 'none'
      });
    },
    downloadQuotation(quotation) {
      uni.showToast({
        title: '下载报价单功能开发中...',
        icon: 'none'
      });
    },
    deleteQuotation(quotation) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除报价单"${quotation.title}"吗？`,
        success: (res) => {
          if (res.confirm) {
            const index = this.quotations.findIndex(item => item.id === quotation.id);
            if (index !== -1) {
              this.quotations.splice(index, 1);
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
            }
          }
        }
      });
    },
    convertToContract(quotation) {
      uni.navigateTo({
        url: `/pages/sales/contract-create?quotationId=${quotation.id}`
      });
    },
    exportExcel() {
      uni.showToast({
        title: '导出Excel功能开发中...',
        icon: 'none'
      });
      this.showMenu = false;
    },
    printBatch() {
      uni.showToast({
        title: '批量打印功能开发中...',
        icon: 'none'
      });
      this.showMenu = false;
    },
    goToSettings() {
      uni.showToast({
        title: '报价单设置功能开发中...',
        icon: 'none'
      });
      this.showMenu = false;
    },
    showFilterPanel() {
      uni.showToast({
        title: '筛选功能开发中...',
        icon: 'none'
      });
    },
    showSortOptions() {
      uni.showActionSheet({
        itemList: ['创建日期', '有效期至', '金额', '客户名称'],
        success: (res) => {
          const sortTypes = ['date', 'validUntil', 'amount', 'customer'];
          const selectedSort = sortTypes[res.tapIndex];
          console.log('选择的排序方式:', selectedSort);
          
          uni.showToast({
            title: '排序功能开发中...',
            icon: 'none'
          });
        }
      });
    },
    onRefresh() {
      this.isRefreshing = true;
      
      setTimeout(() => {
        this.isRefreshing = false;
        uni.showToast({
          title: '刷新成功',
          icon: 'success'
        });
      }, 1000);
    },
    loadMore() {
      if (this.loadingMore) return;
      
      this.loadingMore = true;
      
      setTimeout(() => {
        this.loadingMore = false;
      }, 1000);
    },
    onLoad(options) {
      if (options.opportunityId) {
        this.opportunityId = options.opportunityId;
        
        if (options.company) {
          this.companyName = decodeURIComponent(options.company);
        }
        
        this.mockAddRelatedQuotations(options.opportunityId);
      }
    },
    mockAddRelatedQuotations(opportunityId) {
      const hasRelated = this.quotations.some(q => q.opportunityId === opportunityId);
      
      if (!hasRelated) {
        this.quotations.push({
          id: 'q-' + opportunityId + '-1',
          opportunityId: opportunityId,
          title: '解决方案初步报价',
          code: 'QT-' + opportunityId + '-001',
          customer: this.companyName || '客户公司',
          createDate: '2023-10-05',
          validUntil: '2023-11-05',
          owner: '李销售',
          items: [
            { name: '基础服务', amount: 120000 },
            { name: '增值服务', amount: 50000 }
          ],
          totalAmount: 170000,
          status: {
            code: 'sent',
            name: '已发送'
          }
        });
        
        this.quotations.push({
          id: 'q-' + opportunityId + '-2',
          opportunityId: opportunityId,
          title: '解决方案详细报价',
          code: 'QT-' + opportunityId + '-002',
          customer: this.companyName || '客户公司',
          createDate: '2023-10-20',
          validUntil: '2023-11-20',
          owner: '李销售',
          items: [
            { name: '基础服务', amount: 120000 },
            { name: '增值服务', amount: 50000 },
            { name: '定制开发', amount: 80000 }
          ],
          totalAmount: 250000,
          status: {
            code: 'draft',
            name: '草稿'
          }
        });
      }
    }
  },
  onShow() {
    if (typeof this.$refs.customTabBar !== 'undefined') {
      this.$refs.customTabBar.current = 2;
    }
  }
}
</script>

<style>
.container {
  background-color: #f8fafc;
  min-height: 100vh;
}

/* 页面头部样式 */
.page-header {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  position: relative;
}

.back-button {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  padding: 0 60rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.header-actions {
  display: flex;
  align-items: center;
}

.action-button {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-container {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  right: 0;
  top: 70rpx;
  width: 280rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.dropdown-item {
  padding: 20rpx 24rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
}

.dropdown-item text {
  margin-left: 16rpx;
}

.dropdown-divider {
  height: 1rpx;
  background-color: rgba(0, 0, 0, 0.05);
  margin: 8rpx 0;
}

/* 标签页 */
.tabs-container {
  display: flex;
  white-space: nowrap;
  background-color: #ffffff;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.tab {
  display: inline-block;
  padding: 24rpx 32rpx;
  color: #6b7280;
  font-size: 28rpx;
  position: relative;
  transition: all 0.2s ease;
}

.tab.active {
  color: #0057ff;
  font-weight: 600;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 32rpx;
  right: 32rpx;
  height: 4rpx;
  background-color: #0057ff;
  border-radius: 4rpx;
}

/* 筛选栏样式 */
.filter-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.filter-button, .sort-button {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #6b7280;
  padding: 16rpx 24rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 8rpx;
  background-color: #f5f7fa;
  transition: all 0.2s ease;
}

.filter-button:active, .sort-button:active {
  background-color: rgba(0, 87, 255, 0.1);
  color: #0057ff;
  border-color: #3a80ff;
}

.filter-button text, .sort-button text {
  margin-left: 8rpx;
}

/* 报价单列表样式 */
.quotation-list {
  padding: 24rpx;
  height: calc(100vh - 240rpx);
  box-sizing: border-box;
  padding-bottom: calc(24rpx + 128rpx); /* 增加底部填充空间，避开TabBar */
}

.quotation-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 24rpx;
  overflow: hidden;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  position: relative;
  transition: all 0.3s ease;
}

.quotation-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 报价单状态标识条 */
.quotation-card::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.quotation-card[data-status="draft"]::before {
  background-color: #9ca3af;
}

.quotation-card[data-status="sent"]::before {
  background-color: #f59e0b;
}

.quotation-card[data-status="accepted"]::before {
  background-color: #10b981;
}

.quotation-card[data-status="rejected"]::before {
  background-color: #ef4444;
}

.quotation-header {
  padding: 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: linear-gradient(to right, rgba(249, 250, 251, 0.5), rgba(255, 255, 255, 0.8));
}

.quotation-title {
  font-size: 32rpx;
  font-weight: 600;
  margin: 0 0 12rpx 0;
  color: #111827;
}

.quotation-id {
  font-size: 24rpx;
  color: #6b7280;
}

.quotation-status {
  font-size: 24rpx;
  font-weight: 500;
  padding: 6rpx 16rpx;
  border-radius: 100rpx;
}

.status-draft {
  background-color: #e5e7eb;
  color: #6b7280;
}

.status-sent {
  background-color: #fef3c7;
  color: #d97706;
}

.status-accepted {
  background-color: #d1fae5;
  color: #059669;
}

.status-rejected {
  background-color: #fee2e2;
  color: #dc2626;
}

.quotation-content {
  padding: 24rpx;
}

.quotation-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  background-color: rgba(245, 247, 250, 0.3);
  padding: 24rpx 16rpx;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.detail-group {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 4rpx;
}

.detail-value {
  font-size: 26rpx;
  color: #374151;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.quotation-amount {
  background-color: rgba(245, 247, 250, 0.5);
  padding: 16rpx;
  border-radius: 12rpx;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  font-size: 26rpx;
  color: #4b5563;
  padding: 8rpx 0;
}

.amount-row.total {
  margin-top: 8rpx;
  padding-top: 8rpx;
  border-top: 1rpx dashed rgba(0, 0, 0, 0.1);
  font-weight: 700;
  color: #111827;
}

.quotation-footer {
  display: flex;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  background: linear-gradient(to bottom, #ffffff, #f9fafb);
}

.quotation-action {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 0;
  color: #6b7280;
  font-size: 26rpx;
  transition: all 0.2s ease;
}

.quotation-action:active {
  background-color: rgba(0, 87, 255, 0.1);
  color: #0057ff;
}

.quotation-action:not(:last-child) {
  border-right: 1rpx solid rgba(0, 0, 0, 0.05);
}

/* 浮动操作按钮 */
.fab {
  position: fixed;
  bottom: 128rpx;
  right: 40rpx;
  width: 110rpx;
  height: 110rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #0a6bff, #0057ff);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6), 0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  z-index: 100;
  transition: all 0.3s ease;
  border: 4rpx solid rgba(255, 255, 255, 0.7);
  animation: pulse 2s infinite;
}

.fab:active {
  transform: scale(0.95);
  box-shadow: 0 5rpx 10rpx rgba(0, 87, 255, 0.5), 0 3rpx 3rpx rgba(0, 87, 255, 0.3);
  animation: none;
}

/* 添加脉动动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6), 0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 15rpx 25rpx rgba(0, 87, 255, 0.7), 0 8rpx 10rpx rgba(0, 87, 255, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6), 0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  }
}

/* 空状态样式 */
.empty-state {
  padding: 40rpx 32rpx;
  text-align: center;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  margin: 32rpx 0;
}

.empty-icon {
  margin-bottom: 24rpx;
  color: rgba(0, 0, 0, 0.1);
  display: inline-block;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #111827;
  margin-bottom: 12rpx;
}

.empty-description {
  color: #6b7280;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.btn {
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
}

.btn-primary {
  background-color: #0057ff;
  color: #ffffff;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* 列表加载更多样式 */
.loading-more {
  padding: 24rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 24rpx;
}
</style> 
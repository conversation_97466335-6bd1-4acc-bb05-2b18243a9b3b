(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-contracts-agreement-select"],{2086:function(t,e,r){"use strict";r.r(e);var n=r("e7ee"),a=r("d21b");for(var o in a)["default"].indexOf(o)<0&&function(t){r.d(e,t,(function(){return a[t]}))}(o);r("3627");var i=r("828b"),c=Object(i["a"])(a["default"],n["b"],n["c"],!1,null,"2409432c",null,!1,n["a"],void 0);e["default"]=c.exports},2634:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
e.default=function(){return t};var t={},r=Object.prototype,a=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},c=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(P){l=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var a=e&&e.prototype instanceof h?e:h,i=Object.create(a.prototype),c=new E(n||[]);return o(i,"_invoke",{value:C(t,r,c)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(P){return{type:"throw",arg:P}}}t.wrap=f;var v={};function h(){}function p(){}function g(){}var m={};l(m,c,(function(){return this}));var y=Object.getPrototypeOf,b=y&&y(y(T([])));b&&b!==r&&a.call(b,c)&&(m=b);var w=g.prototype=h.prototype=Object.create(m);function x(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function L(t,e){var r;o(this,"_invoke",{value:function(o,i){function c(){return new e((function(r,c){(function r(o,i,c,s){var u=d(t[o],t,i);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==(0,n.default)(f)&&a.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,c,s)}),(function(t){r("throw",t,c,s)})):e.resolve(f).then((function(t){l.value=t,c(l)}),(function(t){return r("throw",t,c,s)}))}s(u.arg)})(o,i,r,c)}))}return r=r?r.then(c,c):c()}})}function C(t,e,r){var n="suspendedStart";return function(a,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===a)throw o;return j()}for(r.method=a,r.arg=o;;){var i=r.delegate;if(i){var c=_(i,r);if(c){if(c===v)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var s=d(t,e,r);if("normal"===s.type){if(n=r.done?"completed":"suspendedYield",s.arg===v)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n="completed",r.method="throw",r.arg=s.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(void 0===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=void 0,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var a=d(n,t.iterator,e.arg);if("throw"===a.type)return e.method="throw",e.arg=a.arg,e.delegate=null,v;var o=a.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,v):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,v)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function T(t){if(t){var e=t[c];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,n=function e(){for(;++r<t.length;)if(a.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return n.next=n}}return{next:j}}function j(){return{value:void 0,done:!0}}return p.prototype=g,o(w,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:p,configurable:!0}),p.displayName=l(g,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,l(t,u,"GeneratorFunction")),t.prototype=Object.create(w),t},t.awrap=function(t){return{__await:t}},x(L.prototype),l(L.prototype,s,(function(){return this})),t.AsyncIterator=L,t.async=function(e,r,n,a,o){void 0===o&&(o=Promise);var i=new L(f(e,r,n,a),o);return t.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},x(w),l(w,u,"Generator"),l(w,c,(function(){return this})),l(w,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=T,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(O),!t)for(var e in this)"t"===e.charAt(0)&&a.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(r,n){return i.type="throw",i.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],i=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var c=a.call(o,"catchLoc"),s=a.call(o,"finallyLoc");if(c&&s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),O(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;O(r)}return a}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:T(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),v}},t},r("6a54"),r("01a2"),r("e39c"),r("bf0f"),r("844d"),r("18f7"),r("de6c"),r("3872e"),r("4e9b"),r("114e"),r("c240"),r("926e"),r("7a76"),r("c9b5"),r("aa9c"),r("2797"),r("8a8d"),r("dc69"),r("f7a5");var n=function(t){return t&&t.__esModule?t:{default:t}}(r("fcf3"))},"2c8a":function(t,e,r){var n=r("b19f");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=r("967d").default;a("f10657a2",n,!0,{sourceMap:!1,shadowMode:!1})},"2fdc":function(t,e,r){"use strict";function n(t,e,r,n,a,o,i){try{var c=t[o](i),s=c.value}catch(u){return void r(u)}c.done?e(s):Promise.resolve(s).then(n,a)}r("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return function(){var e=this,r=arguments;return new Promise((function(a,o){var i=t.apply(e,r);function c(t){n(i,a,o,c,s,"next",t)}function s(t){n(i,a,o,c,s,"throw",t)}c(void 0)}))}},r("bf0f")},3627:function(t,e,r){"use strict";var n=r("2c8a"),a=r.n(n);a.a},"8de9":function(t,e,r){"use strict";r("6a54");var n=r("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(r("2634")),o=n(r("2fdc"));r("0c26"),r("8f71"),r("bf0f"),r("4626"),r("5ac7");var i=n(r("8a0f")),c=r("d86f"),s={components:{SvgIcon:i.default},data:function(){return{searchText:"",currentFilter:"all",filterTabs:[{label:"全部",value:"all"},{label:"重点客户",value:"A"},{label:"普通客户",value:"B"},{label:"潜在客户",value:"C"}],recentCustomers:[],allCustomers:[]}},onLoad:function(){this.getList()},computed:{filteredCustomers:function(){var t=this,e=this.searchText.toLowerCase().trim(),r=this.allCustomers;return"all"!==this.currentFilter&&(r=r.filter((function(e){return e.customLevelName===t.currentFilter}))),e&&(r=r.filter((function(t){var r=t.name?t.name.toLowerCase():"";return!!r.includes(e)}))),r}},methods:{getList:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var r,n;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,r={pageIndex:1,pageSize:10,filter:{name:""}},e.next=4,(0,c.getAgreementList)(r);case 4:n=e.sent,t.allCustomers=n.items,e.next=11;break;case 8:e.prev=8,e.t0=e["catch"](0),console.log(e.t0);case 11:case"end":return e.stop()}}),e,null,[[0,8]])})))()},navigateBack:function(){uni.navigateBack()},getCustomerTypeLabel:function(t){return{A:"重点客户",B:"普通客户",C:"潜在客户",D:"非活跃客户"}[t]||"未知类型"},filterCustomers:function(t){this.currentFilter=t},searchCustomers:function(){},selectCustomer:function(t){var e=this.getOpenerEventChannel&&this.getOpenerEventChannel();e&&(e.emit("agreementSelected",t),console.log("agreementSelected",t)),uni.navigateBack()}}};e.default=s},b19f:function(t,e,r){var n=r("c86c");e=n(!1),e.push([t.i,'.page-container[data-v-2409432c]{min-height:100vh;background-color:#f5f7fa;display:flex;flex-direction:column;width:100%;overflow-x:hidden;box-sizing:border-box}.page-header[data-v-2409432c]{display:flex;align-items:center;justify-content:space-between;padding:%?30?% %?40?%;border-bottom:%?1?% solid #e0e0e0;background-color:#fff;position:-webkit-sticky;position:sticky;top:0;z-index:10}.page-title[data-v-2409432c]{font-size:%?36?%;font-weight:700;color:#333}.back-button[data-v-2409432c]{padding:%?10?%}.header-spacer[data-v-2409432c]{width:%?44?%}.search-container[data-v-2409432c]{position:relative;padding:%?20?% %?30?%;background-color:#fff}.search-box[data-v-2409432c]{display:flex;align-items:center;background-color:#f5f7fa;border-radius:%?8?%;padding:0 %?20?%}.search-icon[data-v-2409432c]{position:absolute;left:%?40?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);z-index:1;color:#999}.search-input[data-v-2409432c]{width:100%;height:%?80?%;padding:0 %?20?% 0 %?60?%;font-size:%?28?%;background-color:#f5f7fa;border-radius:%?8?%}.filter-tabs[data-v-2409432c]{display:flex;padding:0 %?30?%;background-color:#fff;border-bottom:%?1?% solid #e0e0e0;overflow-x:auto}.filter-tab[data-v-2409432c]{padding:%?20?% %?30?%;font-size:%?28?%;color:#666;position:relative;white-space:nowrap}.filter-tab.active[data-v-2409432c]{color:#3370ff;font-weight:500}.filter-tab.active[data-v-2409432c]::after{content:"";position:absolute;bottom:0;left:0;right:0;height:%?4?%;background-color:#3370ff}.customer-list[data-v-2409432c]{flex:1;padding:%?20?% %?30?%}.section-header[data-v-2409432c]{font-size:%?28?%;font-weight:500;color:#999;margin:%?20?% 0}.customer-item[data-v-2409432c]{display:flex;align-items:center;padding:%?30?%;background-color:#fff;border-radius:%?12?%;margin-bottom:%?20?%}.customer-avatar[data-v-2409432c]{width:%?80?%;height:%?80?%;border-radius:50%;background-color:#f0f7ff;display:flex;align-items:center;justify-content:center;margin-right:%?20?%}.customer-info[data-v-2409432c]{flex:1}.customer-name[data-v-2409432c]{font-size:%?32?%;font-weight:500;color:#333;margin-bottom:%?10?%}.customer-detail[data-v-2409432c]{font-size:%?26?%;color:#666;margin-bottom:%?10?%}.customer-tags[data-v-2409432c]{display:flex;gap:%?10?%}.tag[data-v-2409432c]{padding:%?4?% %?16?%;border-radius:%?100?%;font-size:%?24?%}.tag-A[data-v-2409432c]{background-color:#fff1f0;color:#f5222d}.tag-B[data-v-2409432c]{background-color:#f6ffed;color:#52c41a}.tag-C[data-v-2409432c]{background-color:#e6f7ff;color:#1890ff}.tag-D[data-v-2409432c]{background-color:#f5f5f5;color:#666}.tag-industry[data-v-2409432c]{background-color:#f5f5f5;color:#666}.empty-state[data-v-2409432c]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?100?% 0}.empty-icon[data-v-2409432c]{margin-bottom:%?20?%;color:#999}.empty-text[data-v-2409432c]{font-size:%?28?%;color:#999}.detail-item[data-v-2409432c]{display:flex;align-items:baseline;margin-bottom:var(--spacing-xs)}.detail-label[data-v-2409432c]{font-size:%?24?%;color:var(--text-tertiary);margin-right:%?8?%;white-space:nowrap}.detail-value[data-v-2409432c]{font-size:%?26?%;color:var(--text-secondary);font-weight:500;flex:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}',""]),t.exports=e},c475:function(t,e,r){"use strict";r("6a54");var n=r("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.request=e.getTenantInfo=void 0;var a=n(r("9b1b"));r("bf0f"),r("4626"),r("5ac7");var o=null;e.getTenantInfo=function(t){return new Promise((function(e,r){uni.request({url:"/api/Tenants/getTenantsByLogin",method:"POST",data:t,header:{"Content-Type":"application/json"},success:function(t){200===t.statusCode?(o=t.data,e(t.data)):r(t.data)},fail:function(t){r(t)}})}))};e.request=function(t){return t.url.includes("/login")&&o&&(t.header=(0,a.default)((0,a.default)({},t.header),{},{__tenant:o[0].id})),new Promise((function(e,r){uni.request({url:t.url,method:t.method||"GET",data:t.data||{},header:(0,a.default)({Authorization:"Bearer "+uni.getStorageSync("token"),"Content-Type":"application/json"},t.header),success:function(t){200===t.statusCode?e(t.data):r(t.data)},fail:function(t){r(t)}})}))}},d21b:function(t,e,r){"use strict";r.r(e);var n=r("8de9"),a=r.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){r.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},d86f:function(t,e,r){"use strict";r("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.getUserList=e.getProductList=e.getOpportunityList=e.getContactList=e.getCompanyList=e.getAgreementList=void 0;var n=r("c475");e.getContactList=function(t){return(0,n.request)({url:"/api/crm/contract/getList",method:"POST",data:t})};e.getCompanyList=function(){return(0,n.request)({url:"/api/crm/contract/getAllCompanys",method:"GET"})};e.getUserList=function(t){return(0,n.request)({url:"/api/Users/<USER>",method:"POST",data:t})};e.getOpportunityList=function(t){return(0,n.request)({url:"/api/crm/business/getKanbanList",method:"POST",data:t})};e.getAgreementList=function(t){return(0,n.request)({url:"/api/crm/contract/getAgreementList",method:"POST",data:t})};e.getProductList=function(t){return(0,n.request)({url:"/api/crm/product/getList",method:"POST",data:t})}},e7ee:function(t,e,r){"use strict";r.d(e,"b",(function(){return a})),r.d(e,"c",(function(){return o})),r.d(e,"a",(function(){return n}));var n={svgIcon:r("8a0f").default},a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("v-uni-view",{staticClass:"page-container"},[r("v-uni-view",{staticClass:"page-header"},[r("v-uni-view",{staticClass:"back-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.navigateBack.apply(void 0,arguments)}}},[r("svg-icon",{attrs:{name:"arrow-left",type:"svg",size:"24"}})],1),r("v-uni-text",{staticClass:"page-title"},[t._v("选择协议")]),r("v-uni-view",{staticClass:"header-spacer"})],1),r("v-uni-view",{staticClass:"search-container"},[r("v-uni-view",{staticClass:"search-icon"},[r("svg-icon",{attrs:{name:"search",type:"svg",size:"24"}})],1),r("v-uni-input",{staticClass:"search-input",attrs:{type:"text",placeholder:"搜索协议"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.searchCustomers.apply(void 0,arguments)}},model:{value:t.searchText,callback:function(e){t.searchText=e},expression:"searchText"}})],1),r("v-uni-scroll-view",{staticClass:"customer-list",attrs:{"scroll-y":!0}},[t._l(t.filteredCustomers,(function(e){return r("v-uni-view",{key:e.id,staticClass:"customer-item",on:{click:function(r){arguments[0]=r=t.$handleEvent(r),t.selectCustomer(e)}}},[r("v-uni-view",{staticClass:"customer-info"},[r("v-uni-text",{staticClass:"customer-name"},[t._v(t._s(e.name))]),r("v-uni-text",{staticClass:"customer-detail"},[t._v(t._s(e.contact))]),r("v-uni-view",{staticClass:"detail-item"},[r("v-uni-view",{staticClass:"detail-label"},[t._v("关联商机：")]),r("v-uni-view",{staticClass:"detail-value"},[t._v(t._s(e.businessName))])],1),r("v-uni-view",{staticClass:"detail-item"},[r("v-uni-view",{staticClass:"detail-label"},[t._v("客户名称：")]),r("v-uni-view",{staticClass:"detail-value"},[t._v(t._s(e.customName))])],1),r("v-uni-view",{staticClass:"detail-item"},[r("v-uni-view",{staticClass:"detail-label"},[t._v("付款比例：")]),r("v-uni-view",{staticClass:"detail-value"},[t._v(t._s(e.paidTypeName))])],1)],1)],1)}))],2)],1)},o=[]}}]);
<template>
  <view class="page-container">
<!--    <view class="page-header">
      <view class="back-button" @click="navigateBack">
        <svg-icon name="arrow-left" type="svg" size="24"></svg-icon>
      </view>
      <text class="page-title">选择客户</text>
      <view class="header-spacer"></view>
    </view>-->
    <view class="search-container">
      <view class="search-icon">
        <svg-icon name="search" type="svg" size="24"></svg-icon>
      </view>
      <input
        type="text"
        class="search-input"
        v-model="searchText"
        placeholder="搜索客户名称..."
        @input="getCustomList"
      />
    </view>
    <view class="filter-tabs">
      <view
        v-for="(tab) in filterTabs"
        :key="tab.id"
        class="filter-tab"
        :class="{ active: currentFilter === tab.id }"
        @click="switchTab(tab.id)"
      >
        {{ tab.displayText }}
      </view>
    </view>

    <view class="customer-list">
      <!-- <block v-if="filteredRecentCustomers.length > 0">
        <text class="section-header">最近联系</text>
        <view 
          v-for="customer in filteredRecentCustomers" 
          :key="customer.id"
          class="customer-item" 
          @click="selectCustomer(customer)"
        >
          <view class="customer-avatar">
            <svg-icon name="building" type="svg" size="24"></svg-icon>
          </view>
          <view class="customer-info">
            <text class="customer-name">{{customer.name}}</text>
            <text class="customer-detail">{{customer.contact}} | {{customer.phone}}</text>
            <view class="customer-tags">
              <text class="tag" :class="'tag-' + customer.type">{{getCustomerTypeLabel(customer.type)}}</text>
              <text class="tag tag-industry">{{customer.industry}}</text>
            </view>
          </view>
        </view>
      </block> -->
      <block>
        <view
          v-for="customer in allCustomers"
          :key="customer.id"
          class="customer-item"
          @click="selectCustomer(customer)"
        >
          <view class="customer-avatar">
            <svg-icon name="building" type="svg" size="24"></svg-icon>
          </view>
          <view class="customer-info">
            <text class="customer-name">{{ customer.name }}</text>
<!--            <text class="customer-detail">
              {{ customer.contact }} | {{ customer.phone }}
            </text>-->
            <view class="customer-tags">
              <text
                v-if="customer.customLevelName"
                class="tag"
                :class="customer.customLevel === null ? 'tag-default' : 'tag-' + customer.customLevel.code"
              >
                {{ customer.customLevelName }}
              </text>
              <text v-if="customer.industry" class="tag tag-industry">{{ customer.industry }}</text>
            </view>
          </view>
        </view>
      </block>
      <view class="empty-state" v-if="allCustomers.length === 0">
        <view class="empty-icon">
          <svg-icon name="search" type="svg" size="48"></svg-icon>
        </view>
        <text class="empty-text">没有找到匹配的客户</text>
      </view>
    </view>
  </view>
</template>

<script>
import SvgIcon from "@/components/svg-icon.vue";
import getSelectOptions from "@/utils/dictionary";
import { getCustomerList } from "@/api/customer.api";

export default {
  components: {
    SvgIcon,
  },
  data() {
    return {
      searchText: '',
      currentFilter: undefined,
      filterTabs: [],
      recentCustomers: [],
      allCustomers: [],
    };
  },
  onLoad() {
    this.loadDictionaryOptions();
    this.getCustomList();
  },
  methods: {
    // 获取数据字典
    async loadDictionaryOptions() {
      try {
        this.filterTabs = await getSelectOptions('CustomLevel');
        this.filterTabs.unshift({
          displayText: '全部客户',
          id: undefined,
        })
      } catch (error) {
        this.$message.error('加载字典数据失败');
      }
    },
    // 获得客户数据
    async getCustomList() {
      try {
        const params = {
          pageIndex: 1,
          pageSize: 999,
          filter: {
            customLevelId: this.currentFilter,
            likeString: this.searchText
          },
        };
        const res = await getCustomerList(params);
        this.allCustomers = res.items;
      } catch (error) {
        console.log(error);
      }
    },
    // 返回上一页
    navigateBack() {
      uni.navigateBack();
    },
    // 根据类型筛选客户
    switchTab(tabId) {
      this.currentFilter = tabId;
      this.getCustomList();
    },
    // 选择客户并返回
    selectCustomer(customer) {
      uni.setStorageSync("selected_customer", customer);
      const eventChannel = this.getOpenerEventChannel && this.getOpenerEventChannel();
      if (eventChannel) {
        eventChannel.emit("selectCustomer", {
          id: customer.id,
          name: customer.name,
        });
        // 兼容合同页面的 updateSelectedCustomer 事件
        eventChannel.emit("updateSelectedCustomer", customer);
        console.log("selectCustomer", customer);
        eventChannel.emit("updateCurrency", customer.capitalTypeName,customer.capitalTypeId);
      }
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss">
.page-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #e0e0e0;
  background-color: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.back-button {
  padding: 10rpx;
}

.header-spacer {
  width: 44rpx;
}

.search-container {
  position: relative;
  padding: 20rpx 30rpx;
  background-color: white;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  padding: 0 20rpx;
}

.search-icon {
  position: absolute;
  left: 40rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  color: #999;
}

.search-input {
  /*width: 100%;*/
  height: 80rpx;
  padding: 0 20rpx 0 60rpx;
  font-size: 28rpx;
  background-color: #f5f7fa;
  border-radius: 8rpx;
}

.filter-tabs {
  display: flex;
  padding: 0 30rpx;
  background-color: white;
  border-bottom: 1rpx solid #e0e0e0;
  overflow-x: auto;
}

.filter-tab {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
  white-space: nowrap;
}

.filter-tab.active {
  color: #3370ff;
  font-weight: 500;
}

.filter-tab.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background-color: #3370ff;
}

.customer-list {
  flex: 1;
  padding: 20rpx 30rpx;
}

.section-header {
  font-size: 28rpx;
  font-weight: 500;
  color: #999;
  margin: 20rpx 0;
}

.customer-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.customer-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f0f7ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.customer-info {
  flex: 1;
}

.customer-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.customer-detail {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.customer-tags {
  display: flex;
  gap: 10rpx;
  margin-top: var(--spacing-xs);
}

.tag {
  padding: 4rpx 16rpx;
  border-radius: 100rpx;
  font-size: 24rpx;
  &.tag-A {
    background-color: #fef3c7;
    color: #d97706;
  }
  &.tag-B {
    background-color: #e0f2fe;
    color: #0284c7;
  }
  &.tag-C {
    background-color: #dbeafe;
    color: #2563eb;
  }
  &.tag-default {
    background-color: #9ca3af;
    color: #000;
  }
  &.tag-industry {
    background-color: #f5f5f5;
    color: #666;
  }
}


.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  margin-bottom: 20rpx;
  color: #999;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style>

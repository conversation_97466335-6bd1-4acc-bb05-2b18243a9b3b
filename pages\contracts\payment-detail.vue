<template>
  <view class="page-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="goBack">
        <svg-icon name="arrow-left" type="svg" size="24"></svg-icon>
      </view>
      <text class="page-title">收款详情</text>
      <view class="header-actions">
        <view class="action-button" @tap="showMenu">
          <svg-icon name="more" type="svg" size="24"></svg-icon>
        </view>
      </view>
    </view>
    
    <!-- 内容区域 -->
    <scroll-view scroll-y class="payment-container">
      <!-- 收款基本信息 -->
      <view class="section-card">
        <view class="section-title">
          <text>{{payment.title}}</text>
          <text :class="['status-badge', 'status-'+payment.statusClass]">{{payment.statusText}}</text>
        </view>
        
        <view class="info-grid">
          <view class="info-item">
            <text class="info-label">收款编号</text>
            <text class="info-value">{{payment.code}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">关联发票</text>
            <text class="info-value">{{payment.invoice}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">关联合同</text>
            <text class="info-value">{{payment.contract}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">收款日期</text>
            <text class="info-value">{{payment.date}}</text>
          </view>
        </view>
      </view>
      
      <!-- 收款金额 -->
      <view class="section-card">
        <text class="section-title">收款金额</text>
        
        <view class="amount-container">
          <view class="amount-row">
            <text class="amount-label">应收金额</text>
            <text class="amount-value">¥{{payment.amountDue}}</text>
          </view>
          <view class="amount-row">
            <text class="amount-label">实收金额</text>
            <text class="amount-value">¥{{payment.amountReceived}}</text>
          </view>
          <view class="amount-row total-row">
            <text class="amount-label">差额</text>
            <text class="amount-value">¥{{payment.difference}}</text>
          </view>
        </view>
        
        <view class="payment-method">
          <view class="payment-method-icon">
            <svg-icon name="bank" type="svg" size="24"></svg-icon>
          </view>
          <view class="payment-method-details">
            <text class="payment-method-name">{{payment.method}}</text>
            <text class="payment-method-info">{{payment.methodDetails}}</text>
          </view>
        </view>
      </view>
      
      <!-- 客户信息 -->
      <view class="section-card">
        <text class="section-title">客户信息</text>
        
        <view class="info-item">
          <text class="info-label">客户名称</text>
          <text class="info-value">{{payment.customer.name}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">联系人</text>
          <text class="info-value">{{payment.customer.contact}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">开户行</text>
          <text class="info-value">{{payment.customer.bank}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">账号</text>
          <text class="info-value">{{payment.customer.account}}</text>
        </view>
      </view>
      
      <!-- 关联单据 -->
      <view class="section-card">
        <text class="section-title">关联单据</text>
        
        <view class="linked-docs">
          <view class="linked-doc-item" v-for="(doc, index) in payment.linkedDocs" :key="index" @tap="viewLinkedDoc(doc)">
            <view :class="['doc-icon', 'doc-'+doc.type]">
              <svg-icon :name="doc.type === 'invoice' ? 'file-text' : 'contract'" type="svg" size="24"></svg-icon>
            </view>
            <view class="doc-details">
              <text class="doc-title">{{doc.title}}</text>
              <text class="doc-meta">{{doc.meta}}</text>
            </view>
            <svg-icon name="arrow-right" type="svg" size="20"></svg-icon>
          </view>
        </view>
      </view>
      
      <!-- 收款凭证 -->
      <view class="section-card">
        <text class="section-title">收款凭证</text>
        
        <view class="file-list">
          <view class="file-item" v-for="(file, index) in payment.files" :key="index">
            <svg-icon :name="file.type === 'pdf' ? 'file-pdf' : 'image'" type="svg" size="24"></svg-icon>
            <text>{{file.name}}</text>
            <view class="download-button" @tap="downloadFile(file)">
              <svg-icon name="download" type="svg" size="20"></svg-icon>
            </view>
          </view>
        </view>
        
        <view class="add-note-button" @tap="uploadFile">
          <svg-icon name="upload" type="svg" size="20"></svg-icon>
          <text>上传凭证</text>
        </view>
      </view>
      
      <!-- 合同收款情况 -->
      <view class="section-card">
        <text class="section-title">合同收款情况</text>
        
        <view class="progress-section">
          <view class="progress-title">
            <text>收款进度</text>
            <text class="progress-ratio">{{payment.progressPercentage}}%</text>
          </view>
          <view class="progress-container">
            <view class="progress-bar" :style="{width: payment.progressPercentage+'%'}"></view>
          </view>
        </view>
        
        <view class="amount-container" style="margin-top: 12px;">
          <view class="amount-row">
            <text class="amount-label">合同总金额</text>
            <text class="amount-value">¥{{payment.contractAmount}}</text>
          </view>
          <view class="amount-row">
            <text class="amount-label">已收款金额</text>
            <text class="amount-value">¥{{payment.paidAmount}}</text>
          </view>
          <view class="amount-row total-row">
            <text class="amount-label">收款比例</text>
            <text class="amount-value">{{payment.progressPercentage}}%</text>
          </view>
        </view>
      </view>
      
      <!-- 操作记录 -->
      <view class="section-card">
        <text class="section-title">操作记录</text>
        
        <view class="timeline">
          <view class="timeline-item" v-for="(record, index) in payment.records" :key="index">
            <view :class="['timeline-icon', record.type]">
              <svg-icon :name="record.type === 'payment' ? 'bank-card' : 'add'" type="svg" size="20"></svg-icon>
            </view>
            <view class="timeline-content">
              <text class="timeline-title">{{record.title}}</text>
              <text class="timeline-date">{{record.date}} | {{record.operator}}</text>
            </view>
          </view>
        </view>
        
        <view class="add-note-button" @tap="viewHistory">
          <svg-icon name="history" type="svg" size="20"></svg-icon>
          <text>查看详细历史记录</text>
        </view>
      </view>
      
      <!-- 备注信息 -->
      <view class="section-card">
        <text class="section-title">备注信息</text>
        
        <view class="notes-content">
          <text>{{payment.notes || '暂无备注信息'}}</text>
        </view>
        
        <view class="add-note-button" @tap="addNote">
          <svg-icon name="add" type="svg" size="20"></svg-icon>
          <text>添加备注</text>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部操作栏 -->
    <view class="float-actions">
      <view class="action-btn secondary-action" @tap="printReceipt">
        <svg-icon name="printer" type="svg" size="20"></svg-icon>
        <text>打印回执</text>
      </view>
      <view class="action-btn primary-action" @tap="sendReceipt">
        <svg-icon name="mail-send" type="svg" size="20"></svg-icon>
        <text>发送回执</text>
      </view>
    </view>
  </view>
</template>

<script>
import SvgIcon from '@/components/svg-icon.vue';

export default {
  components: {
    SvgIcon
  },
  data() {
    return {
      payment: {
        title: '系统集成项目 - 第一期',
        statusText: '已完成',
        statusClass: 'completed',
        code: 'PAY-2023-11-001',
        invoice: 'INV-2023-11-001',
        contract: 'CT-2023-09-001',
        date: '2023-11-12',
        
        amountDue: '290,975.00',
        amountReceived: '290,975.00',
        difference: '0.00',
        
        method: '银行转账',
        methodDetails: '中国建设银行 | 到账时间：2023-11-12',
        
        customer: {
          name: '上海智能科技',
          contact: '张总经理 | ***********',
          bank: '中国建设银行上海张江支行',
          account: '31050161393600000123'
        },
        
        linkedDocs: [
          {
            type: 'invoice',
            title: '发票 #INV-2023-11-001',
            meta: '金额: ¥290,975.00 | 状态: 已付款',
            path: '/pages/contracts/invoice-detail'
          },
          {
            type: 'contract',
            title: '合同 #CT-2023-09-001',
            meta: '企业系统集成项目合同',
            path: '/pages/contracts/contract-detail'
          }
        ],
        
        files: [
          {
            type: 'pdf',
            name: '收款回执单-上海智能科技.pdf',
            url: '#'
          },
          {
            type: 'image',
            name: '银行转账凭证.jpg',
            url: '#'
          }
        ],
        
        progressPercentage: 100,
        contractAmount: '580,000.00',
        paidAmount: '580,000.00',
        
        records: [
          {
            type: 'payment',
            title: '确认收款',
            date: '2023-11-12 14:30',
            operator: '李财务'
          },
          {
            type: 'create',
            title: '创建收款记录',
            date: '2023-11-12 10:15',
            operator: '李财务'
          }
        ],
        
        notes: '客户已通过银行转账方式支付全款，交易参考号: REF2023111201254895。'
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    showMenu() {
      uni.showActionSheet({
        itemList: ['查看历史记录', '编辑收款信息', '导出收款凭证', '删除收款记录'],
        success: (res) => {
          switch(res.tapIndex) {
            case 0:
              this.viewHistory();
              break;
            case 1:
              uni.navigateTo({
                url: '/pages/contracts/payment-edit'
              });
              break;
            default:
              uni.showToast({
                title: '该功能开发中...',
                icon: 'none'
              });
          }
        }
      });
    },
    
    viewLinkedDoc(doc) {
      uni.navigateTo({
        url: doc.path
      });
    },
    
    downloadFile(file) {
      uni.showToast({
        title: '下载功能开发中...',
        icon: 'none'
      });
    },
    
    uploadFile() {
      uni.showToast({
        title: '上传凭证功能开发中...',
        icon: 'none'
      });
    },
    
    viewHistory() {
      uni.navigateTo({
        url: '/pages/contracts/payment-history'
      });
    },
    
    addNote() {
      uni.showModal({
        title: '添加备注',
        content: '',
        editable: true,
        placeholderText: '请输入备注信息',
        success: (res) => {
          if (res.confirm && res.content) {
            this.payment.notes = this.payment.notes ? 
              (this.payment.notes + '\n' + res.content) : res.content;
          }
        }
      });
    },
    
    printReceipt() {
      uni.showToast({
        title: '打印收款回执功能开发中...',
        icon: 'none'
      });
    },
    
    sendReceipt() {
      uni.showToast({
        title: '发送收款回执功能开发中...',
        icon: 'none'
      });
    }
  }
}
</script>

<style>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.page-title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

.back-button {
  color: #666666;
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #666666;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
}

.payment-container {
  flex: 1;
  padding: 12px;
  padding-bottom: 140px;
  width: 100%;
  box-sizing: border-box;
}

.section-card {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333333;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.status-completed {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34c759;
}

.status-partial {
  background-color: rgba(255, 204, 0, 0.1);
  color: #ffcc00;
}

.status-pending {
  background-color: rgba(0, 122, 255, 0.1);
  color: #007aff;
}

.status-overdue {
  background-color: rgba(255, 59, 48, 0.1);
  color: #ff3b30;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  width: 100%;
  box-sizing: border-box;
}

.info-item {
  margin-bottom: 8px;
  min-width: 0;
  overflow: hidden;
}

.info-label {
  font-size: 13px;
  color: #666666;
  margin-bottom: 4px;
}

.info-value {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.amount-container {
  background-color: rgba(0, 122, 255, 0.05);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  width: 100%;
  box-sizing: border-box;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  padding: 4px 0;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.amount-label {
  font-size: 14px;
  color: #666666;
  flex-shrink: 0;
}

.amount-value {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
  text-align: right;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.total-row {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  margin-top: 4px;
  padding-top: 8px;
}

.total-row .amount-label {
  font-size: 15px;
  color: #333333;
  font-weight: 500;
}

.total-row .amount-value {
  font-size: 17px;
  color: #007aff;
  font-weight: 600;
}

.payment-method {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 12px;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.payment-method-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background-color: #ffffff;
  color: #007aff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.payment-method-details {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.payment-method-name {
  font-weight: 600;
  color: #333333;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.payment-method-info {
  font-size: 13px;
  color: #666666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.linked-docs {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  box-sizing: border-box;
}

.linked-doc-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #ffffff;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.doc-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.doc-invoice {
  background-color: rgba(255, 204, 0, 0.1);
  color: #ffcc00;
}

.doc-contract {
  background-color: rgba(0, 122, 255, 0.1);
  color: #007aff;
}

.doc-details {
  flex: 1;
  overflow: hidden;
  min-width: 0;
}

.doc-title {
  font-weight: 500;
  color: #333333;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.doc-meta {
  font-size: 12px;
  color: #666666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-list {
  margin-top: 12px;
  width: 100%;
  box-sizing: border-box;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item text {
  flex: 1;
  font-size: 14px;
  color: #333333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

.download-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f5f5f5;
}

.progress-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  box-sizing: border-box;
}

.progress-title {
  font-weight: 500;
  color: #333333;
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  width: 100%;
  box-sizing: border-box;
}

.progress-ratio {
  font-weight: 600;
  color: #007aff;
}

.progress-container {
  height: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
}

.progress-bar {
  height: 100%;
  background-color: #34c759;
  border-radius: 4px;
}

.notes-content {
  font-size: 14px;
  line-height: 1.5;
  color: #333333;
  width: 100%;
  box-sizing: border-box;
  word-break: break-all;
  white-space: pre-wrap;
}

.add-note-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  margin-top: 8px;
  padding: 8px;
  width: 100%;
  box-sizing: border-box;
  background-color: #f5f5f5;
  border: 1px dashed #e0e0e0;
  border-radius: 8px;
  color: #666666;
  font-size: 14px;
}

.timeline {
  margin-top: 12px;
  width: 100%;
  box-sizing: border-box;
}

.timeline-item {
  display: flex;
  margin-bottom: 12px;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.timeline-icon {
  flex: 0 0 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: #666666;
}

.timeline-icon.payment {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34c759;
}

.timeline-icon.create {
  background-color: rgba(0, 122, 255, 0.1);
  color: #007aff;
}

.timeline-content {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.timeline-title {
  font-weight: 500;
  margin-bottom: 2px;
  color: #333333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.timeline-date {
  font-size: 13px;
  color: #666666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.float-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 12px 16px;
  gap: 12px;
  background-color: #ffffff;
  box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
  padding-bottom: calc(12px + env(safe-area-inset-bottom));
  width: 100%;
  box-sizing: border-box;
}

.action-btn {
  flex: 1;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 22px;
  font-size: 16px;
  font-weight: 500;
  gap: 8px;
}

.primary-action {
  background-color: #3a86ff;
  color: #ffffff;
}

.secondary-action {
  background-color: #f5f5f5;
  color: #333333;
  border: 1px solid #e0e0e0;
}
</style> 
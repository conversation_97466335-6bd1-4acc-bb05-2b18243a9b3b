<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <navigator
        url="./customer-list"
        open-type="navigateBack"
        class="back-button"
      >
        <svg-icon name="arrow-left" type="svg" size="32"></svg-icon>
      </navigator>
      <view class="page-title">客户详情</view>
      <view class="header-actions">
        <view class="action-button" @tap="editCustomer">
          <svg-icon name="edit" type="svg" size="28"></svg-icon>
        </view>
        <view class="action-button" @tap="showHeaderMenu">
          <svg-icon name="more" type="svg" size="28"></svg-icon>
        </view>
      </view>
    </view>

    <view class="page-container">
      <!-- 客户基础信息 -->
      <view class="detail-card">
        <view class="detail-header">
          <view>
            <view class="customer-title">{{ customerData.name }}</view>
            <view class="customer-tags">
              <text
                v-if="customerData && customerData.customLevel"
                class="tag"
                :class=" customerData.customLevel.code ? 'tag-' + customerData.customLevel.code : 'tag-default'"
              >
                {{ customerData.customLevel.displayText }}
              </text>
              <text class="tag tag-industry">{{ customerData.industry }}</text>
              <text class="tag tag-custom" v-if="customerData.customTag">{{ customerData.customTag }}</text>
            </view>
          </view>
          <view class="customer-value">¥{{ formatNumber(customerData.registeredCapital) }}</view>
        </view>

        <view class="detail-content">
          <view class="info-section">
            <view class="section-title">
              <svg-icon name="building" type="svg" size="28"></svg-icon>
              <text>基本信息</text>
            </view>
            <view class="info-grid">
              <view class="info-item">
                <view class="info-label">地区</view>
                <view class="info-value">{{ customerData.province + customerData.city | formatEmptyFilter }}</view>
              </view>
              <view class="info-item">
                <view class="info-label">客户类型</view>
                <view class="info-value">{{ customerData.customType }}</view>
              </view>
            </view>
            <view class="info-item">
              <view class="info-label">详细地址</view>
              <view class="info-value">{{ customerData.address | formatEmptyFilter }}</view>
            </view>
            <view class="info-item">
              <view class="info-label">公司介绍</view>
              <view class="info-value">{{ customerData.introduction | formatEmptyFilter }}</view>
            </view>
          </view>

          <view class="info-section">
            <view class="section-title">
              <svg-icon name="user" type="svg" size="28"></svg-icon>
              <text>联系人信息</text>
            </view>
            <view class="contact-list">
              <view
                class="contact-item"
                v-for="(contact) in customerData.contacts"
                :key="contact.contactId"
              >
                <view class="contact-avatar">
                  <svg-icon name="user" type="svg" size="32"></svg-icon>
                </view>
                <view class="contact-info">
                  <view class="contact-name">{{ contact.contact.name }}</view>
                  <view class="contact-position">{{contact.contact.position || "暂无职位"}}</view>
                </view>
                <view class="contact-actions">
                  <view
                    class="contact-action"
                    @tap="makePhoneCall(contact.contact.telephone)"
                  >
                    <svg-icon name="phone" type="svg" size="28"></svg-icon>
                  </view>
                  <view
                    class="contact-action"
                    @tap="sendSMS(contact.contact.telephone)"
                  >
                    <svg-icon name="message" type="svg" size="28"></svg-icon>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view class="info-section">
            <view class="section-title">
              <svg-icon name="info" type="svg" size="28"></svg-icon>
              <text>附加信息</text>
            </view>
            <view class="info-grid">
              <view class="info-item">
                <view class="info-label">创建日期</view>
                <view class="info-value">{{ customerData.creationTime | formatDateFilter }}</view>
              </view>
              <view class="info-item">
                <view class="info-label">创建人</view>
                <view class="info-value">{{ customerData.creatorName }}</view>
              </view>
              <view class="info-item">
                <view class="info-label">负责人</view>
                <view class="info-value">{{ customerData.owner || "暂无" }}</view>
              </view>
              <view class="info-item">
                <view class="info-label">最近联系</view>
                <view class="info-value">{{ customerData.lastContactTime || "暂无" }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 跟进记录 -->
      <view class="detail-card">
        <view class="detail-header">
          <view class="section-title" style="margin-bottom: 0">
            <svg-icon name="history" type="svg" size="28"></svg-icon>
            <text>任务记录</text>
          </view>
          <view class="action-button" @tap="createFollowUp">
            <svg-icon name="add" type="svg" size="28"></svg-icon>
          </view>
        </view>

        <view class="detail-content">
          <view class="activity-list">
            <view
              class="activity-item"
              v-for="(activity, index) in followUpData"
              :key="index"
              @tap="goToFollowUpDetail(activity.id)"
              hover-class="item-hover"
            >
              <view class="activity-icon">
                <svg-icon
                  :name="getIconNameForActivity(activity.icon)"
                  type="svg"
                  size="28"
                ></svg-icon>
              </view>
              <view class="activity-content">
                <view class="activity-header">
                  <view class="activity-title">{{ activity.title }}</view>
                  <view class="activity-time">{{ activity.relatedName }}</view>
                </view>
                <view class="activity-description">
                  {{ activity.description }}
                </view>
                <view class="activity-meta">
                  <view>{{ activity.creationTime | formatDateFilter }}</view>
                  <view>{{ activity.lastModificationTime | formatDateFilter }}</view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 跟进中商机 -->
      <!-- <view class="detail-card">
        <view class="detail-header">
          <view class="section-title" style="margin-bottom: 0">
            <svg-icon name="opportunity" type="svg" size="28"></svg-icon>
            <text>跟进中商机</text>
          </view>
          <view class="action-button" @tap="createOpportunity">
            <svg-icon name="add" type="svg" size="28"></svg-icon>
          </view>
        </view>

        <view class="detail-content">
          <view class="activity-list" v-if="opportunityData.length > 0">
            <view
              class="activity-item"
              v-for="(opportunity, index) in opportunityData"
              :key="index"
              @tap="goToOpportunityDetail(opportunity.id)"
              hover-class="item-hover"
            >
              <view
                class="activity-icon"
                :style="{
                  color:
                    opportunity.probability > 70 ? 'var(--success-color)' : '',
                }"
              >
                <svg-icon name="target" type="svg" size="28"></svg-icon>
              </view>
              <view class="activity-content">
                <view class="activity-header">
                  <view class="activity-title">{{ opportunity.title }}</view>
                  <view class="activity-time">{{
                    opportunity.lastUpdateTime
                  }}</view>
                </view>
                <view class="activity-description">
                  {{ opportunity.description }}
                </view>
                <view class="activity-meta">
                  <view
                    >预计金额：¥{{
                      formatNumber(opportunity.expectedValue)
                    }}</view
                  >
                  <view class="opportunity-progress">
                    <text>成功率：{{ opportunity.probability }}%</text>
                    <text>{{ opportunity.stage }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view class="empty-list" v-else>
            <view class="empty-icon">
              <svg-icon name="target" type="svg" size="64"></svg-icon>
            </view>
            <view class="empty-text">暂无跟进中商机</view>
            <view class="empty-action" @tap="createOpportunity">
              <svg-icon name="add" type="svg" size="24"></svg-icon>
              <text>创建商机</text>
            </view>
          </view>
        </view>
      </view> -->

      <!-- 交易记录 -->
      <!-- <view class="detail-card">
        <view class="detail-header">
          <view class="section-title" style="margin-bottom: 0">
            <svg-icon name="money" type="svg" size="28"></svg-icon>
            <text>交易记录</text>
          </view>
          <view class="action-button" @tap="createTransaction">
            <svg-icon name="add" type="svg" size="28"></svg-icon>
          </view>
        </view>

        <view class="detail-content">
          <view class="activity-list">
            <view
              class="activity-item"
              v-for="(transaction, index) in transactionData"
              :key="index"
              @tap="goToContractDetail(transaction.id)"
              hover-class="item-hover"
            >
              <view
                class="activity-icon"
                :style="{
                  color:
                    transaction.status === 'success'
                      ? 'var(--success-color)'
                      : '',
                }"
              >
                <svg-icon
                  :name="getIconNameForActivity(transaction.icon)"
                  type="svg"
                  size="28"
                ></svg-icon>
              </view>
              <view class="activity-content">
                <view class="activity-header">
                  <view class="activity-title">{{ transaction.title }}</view>
                  <view class="activity-time">{{ transaction.time }}</view>
                </view>
                <view class="activity-description">
                  {{ transaction.description }}
                </view>
                <view class="activity-meta">
                  <view>合同金额：¥{{ formatNumber(transaction.amount) }}</view>
                  <view
                    >已付款：¥{{ formatNumber(transaction.paidAmount) }}</view
                  >
                </view>
              </view>
            </view>
          </view>
        </view>
      </view> -->
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view
        class="action-btn"
        @tap="makePhoneCall(getPrimaryContact().telephone)"
      >
        <svg-icon name="phone" type="svg" size="28"></svg-icon>
        <text class="action-label">电话</text>
      </view>
      <view class="action-btn" @tap="sendEmail(getPrimaryContact().email)">
        <svg-icon name="mail" type="svg" size="28"></svg-icon>
        <text class="action-label">邮件</text>
      </view>
      <view class="action-btn" @tap="createFollowUp">
        <svg-icon name="history" type="svg" size="28"></svg-icon>
        <text class="action-label">跟进</text>
      </view>
      <view class="action-btn primary" @tap="createOpportunity">
        <svg-icon name="add-file" type="svg" size="28"></svg-icon>
        <text class="action-label">创建商机</text>
      </view>
    </view>
  </view>
</template>

<script>
import {
  getCustomerById,
  deleteCustomer,
  getTaskDetail,
} from "@/api/customer.api";
import getSelectOptions from "@/utils/dictionary.js";
export default {
  data() {
    return {
      customerId: "",
      customerTypeList: [],
      relatedId: "",
      customerData: {},
      followUpData: [],
      opportunityData: [
        {
          id: "1",
          title: "企业ERP系统实施项目",
          description:
            "客户需要实施一套完整的ERP系统，覆盖采购、生产、销售、财务等模块",
          expectedValue: 1500000,
          probability: 75,
          stage: "方案确认",
          lastUpdateTime: "2023-10-20",
        },
        {
          id: "2",
          title: "数据中心扩容方案",
          description:
            "客户计划扩大现有数据中心规模，需要采购服务器、存储和网络设备",
          expectedValue: 800000,
          probability: 60,
          stage: "需求分析",
          lastUpdateTime: "2023-10-12",
        },
      ],
      transactionData: [
        {
          id: "1",
          title: "年度维护合同",
          time: "2023-09-15",
          description: "签订了价值30万的年度系统维护合同。",
          amount: 300000,
          paidAmount: 300000,
          status: "success",
          icon: "icon-check",
        },
        {
          id: "2",
          title: "系统升级项目",
          time: "2023-05-20",
          description: "完成了系统升级项目，客户表示非常满意。",
          amount: 200000,
          paidAmount: 200000,
          status: "success",
          icon: "icon-check",
        },
      ],
    };
  },
  methods: {
    formatNumber(value) {
      if (!value) return "0";
      return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    async loadData() {
      await getSelectOptions("CustomType").then((res) => {
        this.customerTypeList = res;
      });
    },
    loadTaskData() {
      let params = {
        pageIndex: 1,
        pageSize: 10,
        filter: {
          fuzzySeach: "",
          relatedId: this.relatedId,
          sortAsc: true,
          sortProperty: "StatusSort",
        },
      };
      getTaskDetail(params).then((res) => {
        this.followUpData = res.items;
      });
    },

    // 显示更多菜单
    showHeaderMenu() {
      uni.showActionSheet({
        itemList: ["删除客户"],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.confirmDelete();
          }
        },
      });
    },

    // 确认删除客户
    confirmDelete() {
      uni.showModal({
        title: "确认删除",
        content: "确定要删除此客户吗？此操作无法撤销。",
        success: (res) => {
          if (res.confirm) {
            // 执行删除逻辑
            deleteCustomer(this.customerId).then((res) => {
              uni.showToast({
                title: "删除成功",
                icon: "success",
              });
              setTimeout(() => {
                uni.navigateBack();
              }, 1500);
            });
          }
        },
      });
    },

    // 编辑客户
    editCustomer() {
      uni.navigateTo({
        url: `./customer-edit?id=${this.customerId}`,
      });
    },

    // 拨打电话
    makePhoneCall(phoneNumber) {
      if (!phoneNumber) {
        phoneNumber = this.getPrimaryContact().telephone;
      }
      if (phoneNumber) {
        uni.makePhoneCall({
          phoneNumber: phoneNumber,
          fail: () => {
            uni.showToast({
              title: "拨打电话失败",
              icon: "none",
            });
          },
        });
      } else {
        uni.showToast({
          title: "无电话号码",
          icon: "none",
        });
      }
    },

    // 发送短信
    sendSMS(phoneNumber) {
      if (phoneNumber) {
        // 仅在手机平台支持发送短信
        uni.sendSMS({
          phoneNumber: phoneNumber,
          fail: () => {
            uni.showToast({
              title: "发送短信失败",
              icon: "none",
            });
          },
        });
      } else {
        uni.showToast({
          title: "无电话号码",
          icon: "none",
        });
      }
    },

    // 发送邮件
    sendEmail(email) {
      if (!email) {
        email = this.getPrimaryContact().email;
      }
      if (email) {
        // 打开邮箱应用或邮件编辑界面
        // 这里使用简单的URL Scheme，实际应用需根据平台调整
        window.location.href = `mailto:${email}`;
      } else {
        uni.showToast({
          title: "无邮箱地址",
          icon: "none",
        });
      }
    },

    // 创建跟进记录
    createFollowUp() {
      uni.navigateTo({
        url: `./follow-up-create?id=${
          this.customerId
        }&name=${encodeURIComponent(this.customerData.name)}`,
      });
    },

    // 创建交易记录
    createTransaction() {
      uni.navigateTo({
        url: `./transaction-create?id=${
          this.customerId
        }&name=${encodeURIComponent(this.customerData.name)}`,
      });
    },

    // 创建商机
    createOpportunity() {
      uni.navigateTo({
        url: `../sales/opportunity-create?customerId=${
          this.customerId
        }&customerName=${encodeURIComponent(this.customerData.name)}`,
      });
    },

    // 加载客户数据
    loadCustomerData(id) {
      this.customerId = id;
    },

    // 获取活动图标名称
    getIconNameForActivity(icon) {
      const iconMap = {
        call: "phone",
        visit: "user-check",
        email: "mail",
        proposal: "document",
        contract: "file-contract",
        payment: "money",
      };
      return iconMap[icon] || "history";
    },
    getPrimaryContact() {
      if (
        !this.customerData.contacts ||
        this.customerData.contacts.length === 0
      ) {
        return { name: "", phone: "", email: "" };
      }
      // 查找主要联系人
      const primary = this.customerData.contacts.find((contact) => contact.isPrimary);
      if (primary) {
        return primary;
      }
      // 如果没有标记为主要联系人，返回第一个
      return this.customerData.contacts[0].contact;
    },

    // 跳转到商机详情页
    goToOpportunityDetail(id) {
      uni.navigateTo({
        url: `../sales/opportunity-detail?id=${id}`,
      });
    },

    // 跳转到合同详情页
    goToContractDetail(id) {
      uni.navigateTo({
        url: `../contracts/contract-detail?id=${id}`,
      });
    },

    // 跳转到跟进详情页
    goToFollowUpDetail(id) {
      uni.navigateTo({
        url: `./customer-follow-up?id=${id}&mode=view`,
      });
    },
  },
  async onLoad(options) {
    if (options.id) {
      this.relatedId = options.id;
      this.loadCustomerData(options.id);
      this.loadTaskData();
      await this.loadData();
      await getCustomerById(options.id).then((res) => {
        this.customerData = res;
        let customeType = this.customerTypeList.find((item) => {
          if (item.id === res.customTypeId) {
            return item;
          }
        });
        if (customeType && customeType.displayText) {
          this.customerData.customType = customeType.displayText;
        } else {
          this.customerData.customType = "未知";
        }
      });
    }
  },
  onShow() {
    // 页面显示时可以刷新数据
  },
  onPullDownRefresh() {
    // 下拉刷新
    setTimeout(() => {
      uni.stopPullDownRefresh();
    }, 1000);
  },
};
</script>

<style lang="scss">
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1rpx solid var(--border-color);
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.page-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
}

.back-button {
  color: var(--text-secondary);
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.action-button {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}

.page-container {
  padding: var(--spacing-md) 0;
}

/* 详情卡片样式 */
.detail-card {
  background-color: #ffffff;
  border-radius: var(--radius-lg);
  overflow: hidden;
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-sm);
}

.detail-header {
  padding: var(--spacing-md);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: 1rpx solid var(--border-color-light);
}

.customer-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.customer-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: var(--spacing-xs);
}

.tag {
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  border-radius: var(--radius-full);
  background-color: var(--light-color);
  color: var(--text-secondary);
  &.tag-A {
    background-color: #fef3c7;
    color: #d97706;
  }
  &.tag-B {
    background-color: #e0f2fe;
    color: #0284c7;
  }
  &.tag-C {
    background-color: #dbeafe;
    color: #2563eb;
  }
  &.tag-default {
    background-color: #9ca3af;
    color: #000;
  }
  &.tag-industry {
    background-color: #f3f4f6;
    color: #4b5563;
  }
  &.tag-custom {
    background-color: var(--success-light);
    color: var(--success-color);
  }
}

.customer-value {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--primary-color);
}

.detail-content {
  padding: var(--spacing-md);
}

/* 信息区域样式 */
.info-section {
  margin-bottom: var(--spacing-lg);
}

.info-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.section-title text:first-child {
  margin-right: var(--spacing-xs);
  color: var(--primary-color);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md) var(--spacing-lg);
}

.info-item {
  margin-bottom: var(--spacing-sm);
  .info-label {
    font-size: 24rpx;
    color: var(--text-tertiary);
    margin-bottom: 4rpx;
  }
  .info-value {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
  }

}

/* 联系人列表样式 */
.contact-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.contact-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm);
  background-color: var(--light-color);
  border-radius: var(--radius-md);
}

.contact-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: var(--radius-full);
  background-color: var(--primary-light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  margin-right: var(--spacing-sm);
  flex-shrink: 0;
}

.contact-info {
  flex: 1;
}

.contact-name {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4rpx;
}

.contact-position {
  font-size: 24rpx;
  color: var(--text-tertiary);
}

.contact-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.contact-action {
  width: 64rpx;
  height: 64rpx;
  border-radius: var(--radius-full);
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  box-shadow: var(--shadow-xs);
}

/* 活动列表样式 */
.activity-list {
  display: flex;
  flex-direction: column;
}

.activity-item {
  display: flex;
  padding: var(--spacing-md);
  border-bottom: 1rpx solid var(--border-color-light);
}

.item-hover {
  background-color: var(--light-color);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: var(--radius-full);
  background-color: var(--light-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  margin-right: var(--spacing-sm);
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
  position: relative;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  margin-bottom: var(--spacing-xs);
}

.activity-title {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
}

.activity-time {
  font-size: 24rpx;
  color: var(--text-tertiary);
  white-space: nowrap;
}

.activity-description {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.5;
}

.activity-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-xs);
  font-size: 24rpx;
  color: var(--text-tertiary);
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: #ffffff;
  border-top: 1rpx solid var(--border-color);
  display: flex;
  gap: var(--spacing-md);
  z-index: 10;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  height: 128rpx;
  background-color: var(--light-color);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
}

.action-btn text:first-child {
  font-size: 40rpx;
}

.action-label {
  font-size: 24rpx;
}

.action-btn.primary {
  background-color: var(--primary-color);
  color: #ffffff;
  border-color: var(--primary-color);
}

/* 增加底部间距，避免内容被底部操作栏遮挡 */
.page-container {
  padding-bottom: calc(128rpx + var(--spacing-md) * 2 + var(--spacing-lg));
}

/* 空列表样式 */
.empty-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg) 0;
}

.empty-icon {
  color: var(--text-tertiary);
  margin-bottom: var(--spacing-md);
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-tertiary);
  margin-bottom: var(--spacing-md);
}

.empty-action {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--primary-light);
  color: var(--primary-color);
  border-radius: var(--radius-md);
  font-size: 26rpx;
}

.opportunity-progress {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4rpx;
}
</style>

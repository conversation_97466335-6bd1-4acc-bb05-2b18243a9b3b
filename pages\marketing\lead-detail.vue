<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="goBack">
        <svg-icon name="arrow-left" type="svg" size="40"></svg-icon>
      </view>
      <text class="page-title">线索详情</text>
      <view class="header-actions">
        <view class="menu-container">
          <view class="action-button" @click="showMenu = !showMenu">
            <svg-icon name="more" type="svg" size="32"></svg-icon>
          </view>
          <view class="dropdown-menu" v-if="showMenu">
            <view class="dropdown-item" @click="editLead">
              <svg-icon name="edit" type="svg" size="20"></svg-icon>
              <text>编辑线索</text>
            </view>
<!--            <view class="dropdown-item" @click="convertToCustomer">
              <svg-icon name="user-received" type="svg" size="20"></svg-icon>
              <text>转为客户</text>
            </view>
            <view class="dropdown-divider"></view>
            <view class="dropdown-item" @click="changLeadStatus">
              <svg-icon name="toggle" type="svg" size="20"></svg-icon>
              <text>修改状态</text>
            </view>-->
            <view class="dropdown-divider"></view>
            <view class="dropdown-item delete" @click="deleteLead">
              <svg-icon name="delete-bin" type="svg" size="20"></svg-icon>
              <text>删除线索</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 连接超时状态 -->
    <view class="timeout-state" v-if="connectionTimeout" @click="retryConnection">
      <text class="timeout-text">连接服务器超时，点击屏幕重试</text>
    </view>
    <!-- 线索详情内容 -->
    <scroll-view scroll-y class="content-scroll" v-else>
      <!-- 线索基本信息 -->
      <view class="lead-detail-header">
        <text class="lead-name">{{ lead.name }}</text>
        <text class="lead-company">{{ lead.customName }}</text>
        <view class="lead-status-wrapper">
          <text
            v-if="lead.clueStatus"
            class="lead-status"
            :style="computedStatusTag(lead.clueStatus.order || 0)"
          >
            {{ lead.clueStatus.displayText }}
          </text>
          <view class="lead-source">
            <text>来源：</text>
            <text class="source-value">{{ lead.clueSource.displayText }}</text>
          </view>
        </view>
        <view class="action-buttons">
<!--          <button class="btn btn-outline" @click="callLead">
            <svg-icon name="phone" type="svg" size="20"></svg-icon>
            <text>电话</text>
          </button>
          <button class="btn btn-outline" @click="mailLead">
            <svg-icon name="mail" type="svg" size="20"></svg-icon>
            <text>邮件</text>
          </button>-->
          <button class="btn btn-primary" @click="createFollowup">
            <svg-icon name="add" type="svg" size="20"></svg-icon>
            <text>添加跟进</text>
          </button>
        </view>
      </view>

      <!-- 联系方式 -->
      <view class="detail-section">
        <view class="section-title">
          <svg-icon name="contacts" type="svg" size="20"></svg-icon>
          <text>联系方式</text>
        </view>
        <view class="info-grid">
          <view class="info-item">
            <text class="info-label">固定电话</text>
            <text class="info-value">{{ lead.fixPhone | formatEmptyFilter }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">手机号码</text>
            <text class="info-value">{{ lead.telephone | formatEmptyFilter }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">微信号</text>
            <text class="info-value">{{ lead.wechat || '未提供' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">电子邮箱</text>
            <text class="info-value">{{ lead.email | formatEmptyFilter  }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">地址</text>
            <text class="info-value">{{ lead.address | formatEmptyFilter  }}</text>
          </view>
        </view>
      </view>
      <!-- 公司信息 -->
<!--      <view class="detail-section">
        <view class="section-title">
          <text class="ri-building-line"></text>
          <text>公司信息</text>
        </view>
        <view class="info-grid">
          <view class="info-item">
            <text class="info-label">网站</text>
            <text class="info-value">{{lead.website || '未提供'}}</text>
          </view>
        </view>
      </view>-->

      <!-- 线索备注 -->
      <view class="detail-section">
        <view class="section-title">
          <text class="ri-file-text-line"></text>
          <text>线索备注</text>
        </view>
        <view class="description-block">
          <text class="description-content">{{ lead.remark }}</text>
        </view>
      </view>
      <!-- 跟进历史 -->
      <view class="detail-section">
        <view class="section-title">
          <text class="ri-history-line"></text>
          <text>跟进历史</text>
        </view>
        <view class="activity-list" v-if="lead.activities && lead.activities.length > 0">
          <view v-for="(activity, index) in lead.activities" :key="index" class="activity-item">
            <view class="activity-icon" :class="'icon-' + activity.type"></view>
            <view class="activity-content">
              <view class="activity-header">
                <text class="activity-title">{{ activity.title }}</text>
                <text class="activity-time">{{ activity.time }}</text>
              </view>
              <text class="activity-description">{{ activity.description }}</text>
              <view class="activity-user">
                <text class="user-avatar">{{ activity.user.substring(0,1) }}</text>
                <text class="user-name">{{ activity.user }}</text>
              </view>
            </view>
          </view>
        </view>
        <view class="empty-activities" v-else>
          <text class="ri-calendar-line empty-icon"></text>
          <text class="empty-text">暂无跟进记录</text>
          <text class="empty-description">点击"添加跟进"按钮记录与线索的沟通</text>
        </view>
      </view>
      <!-- 附加信息 -->
      <view class="detail-section">
        <view class="section-title">
          <text class="ri-information-line"></text>
          <text>附加信息</text>
        </view>
        <view class="info-grid">
          <view class="info-item">
            <text class="info-label">创建日期</text>
            <text class="info-value">{{ lead.creationTime | formatDateFilter }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">创建人</text>
            <text class="info-value">{{ lead.creatorName }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">负责人</text>
            <text class="info-value">{{ lead.owner || '未分配' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">最后更新</text>
            <text class="info-value">{{ lead.lastModificationTime | formatDateFilter }}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import SvgIcon from '@/components/svg-icon.vue';
import { getClueDetail, deleteClue } from '@/api/clue.api';

export default {
  components: {
    SvgIcon
  },
  data() {
    return {
      leadId: '',
      showMenu: false,
      connectionTimeout: false,
      lead: {
        clueStatus: {
          displayText: ''
        },
        clueSource: {
          displayText: ''
        },
        activities: [
          {
            type: 'note',
            title: '初次联系',
            time: '2023-10-26 14:30',
            description: '已电话联系客户，对方表示有意向，但需要更多产品信息。已通过邮件发送产品手册。',
            user: '李销售'
          },
          {
            type: 'task',
            title: '安排演示',
            time: '2023-10-25 10:15',
            description: '客户希望能看到产品演示，计划下周三安排线上演示会议。',
            user: '李销售'
          },
          {
            type: 'system',
            title: '线索创建',
            time: '2023-10-25 09:30',
            description: '通过官网表单创建的线索',
            user: '系统'
          }
        ]
      }
    }
  },
  computed: {
    computedStatusTag() {
      let backgroundArr = ['#e0f2fe', '#dbeafe', '#d1fae5', '#e5e7eb'];
      let colorArr = ['#0284c7', '#2563eb', '#059669', '#6b7280'];
      return (order) => {
        return {
          backgroundColor: backgroundArr[order],
          color: colorArr[order]
        }
      }
    }
  },
  onLoad(options) {
    if (options.id) {
      this.leadId = options.id;
      this.loadLeadData(options.id);
    }
  },
  methods: {
    // 添加重试连接方法
    retryConnection() {
      this.loadLeadData(this.leadId);
    },
    // 修改加载数据方法，添加超时处理
    loadLeadData(id) {
      this.connectionTimeout = false;
      getClueDetail(id).then(res => {
        Object.assign(this.lead, res)
      })
      // // 模拟API请求超时
      // const timeout = setTimeout(() => {
      //   this.connectionTimeout = true;
      // }, 15000); // 15秒超时
      //
      // 模拟API请求
      // 注意：实际项目中，这里应该是真实的API调用
      // setTimeout(() => {
      //   clearTimeout(timeout);
      //   // 如果已经显示了超时状态，则不再更新数据
      //   if (this.connectionTimeout) return;
      //
      //   // 这里应该使用真实API返回的数据更新this.lead
      //   // 现在我们使用默认的测试数据
      // }, 1000);
    },
    goBack() {
      uni.navigateBack();
    },
    // 编辑线索
    editLead() {
      uni.navigateTo({
        url: `/pages/marketing/lead-edit?id=${this.lead.id}`
      });
      this.showMenu = false;
    },
    // 转为客户
    convertToCustomer() {
      uni.showToast({
        title: '转为客户功能开发中...',
        icon: 'none'
      });
      this.showMenu = false;
    },
    // 修改线索状态
    // changLeadStatus() {
    //   uni.showActionSheet({
    //     itemList: ['新线索', '跟进中', '已获客', '已失效'],
    //     success: (res) => {
    //       const statusMap = ['new', 'inProgress', 'qualified', 'disqualified'];
    //       const statusNameMap = ['新线索', '跟进中', '已获客', '已失效'];
    //       this.lead.status.code = statusMap[res.tapIndex];
    //       this.lead.status.name = statusNameMap[res.tapIndex];
    //       uni.showToast({
    //         title: '状态已更新',
    //         icon: 'success'
    //       });
    //     }
    //   });
    //   this.showMenu = false;
    // },
    // 删除线索
    deleteLead() {
      uni.showModal({
        title: '确认删除',
        content: '删除后无法恢复，确定要删除该线索吗？',
        success: (res) => {
          if (res.confirm) {
            deleteClue(this.leadId).then(res => {
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
              uni.navigateBack();
            })
          }
        }
      });
      this.showMenu = false;
    },
    // 电话联系
    callLead() {
      uni.showToast({
        title: '电话联系功能开发中...',
        icon: 'none'
      });
    },
    // 发送邮件
    // mailLead() {
    //   uni.showToast({
    //     title: '发送邮件功能开发中...',
    //     icon: 'none'
    //   });
    // },
    // 创建跟进
    createFollowup() {
      uni.showToast({
        title: '添加跟进功能开发中...',
        icon: 'none'
      });
    }
  }
}
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  background-color: #fff;
  z-index: 10;
  .page-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    flex: 1;
    text-align: center;
  }
  .back-button {
    color: #666;
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .header-actions {
    width: 40px;
    display: flex;
    justify-content: flex-end;
  }
  .action-button {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: #666;
  }
}

.content-scroll {
  flex: 1;
  padding-bottom: 40rpx;
}

.lead-detail-header {
  background-color: white;
  padding: 20px 16px;
  border-bottom: 1px solid #eee;
  .lead-name {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
    display: block;
  }
  .lead-company {
    font-size: 14px;
    color: #666;
    margin-bottom: 16px;
    display: block;
  }
  .lead-status-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .lead-status {
      padding: 4px 12px;
      border-radius: 100px;
      font-size: 12px;
      font-weight: 500;
      margin-right: 12px;
    }
    .lead-source {
      font-size: 14px;
      color: #666;
      .source-value {
        font-weight: 500;
        color: #333;
      }
    }
  }
  .action-buttons {
    display: flex;
    gap: 10px;
    .btn {
      flex: 1;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
      font-size: 14px;
      white-space: nowrap;
      padding: 0 8px;
    }
    .btn text {
      margin-left: 4px;
    }
    .btn-outline {
      border: 1px solid #ddd;
      color: #666;
      background-color: #fff;
    }
    .btn-primary {
      background-color: #3a86ff;
      color: white;
      border: none;
    }
  }
}
.detail-section {
  background-color: white;
  padding: 16px;
  margin-top: 12px;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
  }
  .section-title text:first-child {
    color: #3a86ff;
    margin-right: 6px;
    font-size: 18px;
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  .info-item {
    display: flex;
    flex-direction: column;
    min-width: 0;
    .info-label {
      font-size: 12px;
      color: #999;
      margin-bottom: 4px;
    }
    .info-value {
      font-size: 14px;
      color: #333;
      font-weight: 500;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.description-block {
  margin-top: 10px;
  .description-content {
    font-size: 14px;
    color: #333;
    line-height: 1.6;
  }
}

.activity-list {
  margin-top: 10px;
}

.activity-item {
  position: relative;
  padding-left: 28px;
  padding-bottom: 20px;
}
.activity-item:not(:last-child)::before {
  content: '';
  position: absolute;
  top: 24px;
  left: 8px;
  bottom: 0;
  width: 2px;
  background-color: #eee;
}
.activity-icon {
  position: absolute;
  left: 0;
  top: 2px;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: #3a86ff;
}

.icon-note {
  background-color: #3a86ff;
}

.icon-task {
  background-color: #ff9500;
}

.icon-system {
  background-color: #999;
}

.activity-content {
  padding-bottom: 10px;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
}

.activity-title {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.activity-time {
  color: #999;
  font-size: 12px;
}

.activity-description {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 8px;
}

.activity-user {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #f0f0f0;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 6px;
}

.user-name {
  font-size: 12px;
  color: #666;
}

.empty-activities {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.empty-icon {
  font-size: 36px;
  color: #ddd;
  margin-bottom: 12px;
}

.empty-text {
  font-size: 16px;
  color: #666;
  margin-bottom: 6px;
}

.empty-description {
  font-size: 13px;
  color: #999;
}

.menu-container {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  border: 1px solid #eee;
  padding: 4px 0;
  z-index: 1000;
  min-width: 180px;
}

.dropdown-item {
  padding: 10px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #333;
}

.dropdown-item.delete {
  color: #ff4d4f;
}

.dropdown-divider {
  height: 1px;
  background-color: #eee;
  margin: 4px 0;
}

/* 添加连接超时状态样式 */
.timeout-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 120rpx);
  text-align: center;
  padding: 40rpx;
}

.timeout-text {
  font-size: 32rpx;
  color: #6b7280;
}

/* 确保最后一个detail-section有足够的底部边距 */
.detail-section:last-child {
  margin-bottom: 30rpx;
}
</style> 
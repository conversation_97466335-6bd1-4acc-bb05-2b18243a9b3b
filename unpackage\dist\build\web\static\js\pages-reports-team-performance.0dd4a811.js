(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-reports-team-performance"],{"2e26":function(e,t,a){var i=a("7b56");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var s=a("967d").default;s("3b7f12aa",i,!0,{sourceMap:!1,shadowMode:!1})},3112:function(e,t,a){"use strict";var i=a("2e26"),s=a.n(i);s.a},"55a6":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return s})),a.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"container"},[a("v-uni-view",{staticClass:"page-header"},[a("v-uni-view",{staticClass:"flex-row items-center gap-sm"},[a("v-uni-text",{staticClass:"page-title"},[e._v("团队绩效")])],1),a("v-uni-view",{staticClass:"header-actions"},[a("v-uni-button",{staticClass:"action-button",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.shareReport.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-share-line"})],1),a("v-uni-view",{staticClass:"dropdown"},[a("v-uni-button",{staticClass:"action-button",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleReportMenu.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-file-list-3-line"})],1),a("v-uni-view",{staticClass:"dropdown-menu",class:{show:e.showReportMenu}},[a("v-uni-navigator",{staticClass:"dropdown-item",attrs:{url:"/pages/reports/sales-reports"}},[a("v-uni-text",{staticClass:"ri-bar-chart-2-line"}),a("v-uni-text",[e._v("销售报表")])],1),a("v-uni-navigator",{staticClass:"dropdown-item active",attrs:{url:"/pages/reports/team-performance"}},[a("v-uni-text",{staticClass:"ri-team-line"}),a("v-uni-text",[e._v("团队绩效")])],1),a("v-uni-navigator",{staticClass:"dropdown-item",attrs:{url:"/pages/reports/customer-analytics"}},[a("v-uni-text",{staticClass:"ri-user-search-line"}),a("v-uni-text",[e._v("客户分析")])],1),a("v-uni-navigator",{staticClass:"dropdown-item",attrs:{url:"/pages/reports/custom-reports"}},[a("v-uni-text",{staticClass:"ri-file-chart-line"}),a("v-uni-text",[e._v("自定义报表")])],1)],1)],1)],1)],1),a("v-uni-view",{staticClass:"team-selector"},[a("v-uni-view",{staticClass:"team-dropdown",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showTeamSelect.apply(void 0,arguments)}}},[a("v-uni-text",[e._v(e._s(e.selectedTeam))]),a("v-uni-text",{staticClass:"ri-arrow-down-s-line"})],1)],1),a("v-uni-scroll-view",{staticClass:"time-filter",attrs:{"scroll-x":"true","show-scrollbar":"false"}},e._l(e.timeOptions,(function(t,i){return a("v-uni-view",{key:i,staticClass:"time-option",class:{active:e.selectedTimeIndex===i},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectTimeOption(i)}}},[a("v-uni-text",[e._v(e._s(t))])],1)})),1),a("v-uni-scroll-view",{staticClass:"page-content",attrs:{"scroll-y":"true"}},[a("v-uni-view",{staticClass:"metrics-cards"},e._l(e.metricsData,(function(t,i){return a("v-uni-view",{key:i,staticClass:"metric-card"},[a("v-uni-text",{staticClass:"metric-label"},[e._v(e._s(t.label))]),a("v-uni-text",{staticClass:"metric-value"},[e._v(e._s(t.value))]),a("v-uni-view",{staticClass:"metric-change",class:t.changeClass},[t.change>0?a("v-uni-text",{staticClass:"ri-arrow-up-line"}):a("v-uni-text",{staticClass:"ri-arrow-down-line"}),a("v-uni-text",[e._v(e._s(t.changeText))])],1)],1)})),1),a("v-uni-view",{staticClass:"chart-container"},[a("v-uni-view",{staticClass:"chart-header"},[a("v-uni-text",{staticClass:"chart-title"},[e._v("团队目标达成进度")]),a("v-uni-view",{staticClass:"chart-actions"},[a("v-uni-button",{staticClass:"chart-action-btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.downloadChart("teamTarget")}}},[a("v-uni-text",{staticClass:"ri-download-line"})],1),a("v-uni-button",{staticClass:"chart-action-btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showChartOptions("teamTarget")}}},[a("v-uni-text",{staticClass:"ri-more-2-fill"})],1)],1)],1),a("v-uni-view",{staticClass:"chart-wrapper"},[a("qiun-data-charts",{attrs:{type:"column",opts:e.teamTargetOpts,chartData:e.teamTargetData}})],1)],1),a("v-uni-view",{staticClass:"chart-container"},[a("v-uni-view",{staticClass:"chart-header"},[a("v-uni-text",{staticClass:"chart-title"},[e._v("团队销售趋势")]),a("v-uni-view",{staticClass:"chart-actions"},[a("v-uni-button",{staticClass:"chart-action-btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.downloadChart("teamTrend")}}},[a("v-uni-text",{staticClass:"ri-download-line"})],1),a("v-uni-button",{staticClass:"chart-action-btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showChartOptions("teamTrend")}}},[a("v-uni-text",{staticClass:"ri-more-2-fill"})],1)],1)],1),a("v-uni-view",{staticClass:"chart-wrapper"},[a("qiun-data-charts",{attrs:{type:"line",opts:e.teamTrendOpts,chartData:e.teamTrendData}})],1)],1),a("v-uni-view",{staticClass:"performance-table"},[a("v-uni-view",{staticClass:"table-header"},[a("v-uni-text",{staticClass:"table-title"},[e._v("个人绩效排行")]),a("v-uni-button",{staticClass:"chart-action-btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.downloadPerformance.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-download-line"})],1)],1),a("v-uni-view",{staticClass:"table-wrapper"},[a("v-uni-view",{staticClass:"table-row table-header-row"},[a("v-uni-text",{staticClass:"table-cell table-head-cell"},[e._v("员工")]),a("v-uni-text",{staticClass:"table-cell table-head-cell"},[e._v("销售目标达成")]),a("v-uni-text",{staticClass:"table-cell table-head-cell"},[e._v("销售额")]),a("v-uni-text",{staticClass:"table-cell table-head-cell"},[e._v("成单数")])],1),e._l(e.performanceData,(function(t,i){return a("v-uni-view",{key:i,staticClass:"table-row"},[a("v-uni-view",{staticClass:"table-cell"},[a("v-uni-view",{staticClass:"employee-info"},[a("v-uni-view",{staticClass:"employee-avatar",style:{"background-color":t.avatarBg||"#4a6fff20",color:t.avatarColor||"#4a6fff"}},[a("v-uni-text",[e._v(e._s(t.avatar))])],1),a("v-uni-text",{staticClass:"employee-name"},[e._v(e._s(t.name))])],1)],1),a("v-uni-view",{staticClass:"table-cell"},[a("v-uni-view",{staticClass:"achievement-cell"},[a("v-uni-view",{staticClass:"achievement-label"},[a("v-uni-text",[e._v(e._s(t.percentage)+"%")]),a("v-uni-text",[e._v(e._s(t.achieved)+" / "+e._s(t.target))])],1),a("v-uni-view",{staticClass:"progress-bar"},[a("v-uni-view",{staticClass:"progress-fill",class:t.progressClass,style:{width:t.percentage+"%"}})],1)],1)],1),a("v-uni-text",{staticClass:"table-cell"},[e._v(e._s(t.sales))]),a("v-uni-text",{staticClass:"table-cell"},[e._v(e._s(t.deals))])],1)}))],2)],1)],1)],1)},s=[]},"7b56":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'.container[data-v-6584734a]{display:flex;flex-direction:column;height:100vh}.page-header[data-v-6584734a]{display:flex;flex-direction:row;align-items:center;justify-content:space-between;padding:%?30?% %?40?%;border-bottom:1px solid var(--border-color);background-color:#fff;position:relative;z-index:10}.flex-row[data-v-6584734a]{display:flex;flex-direction:row}.items-center[data-v-6584734a]{align-items:center}.gap-sm[data-v-6584734a]{gap:%?20?%}.page-title[data-v-6584734a]{font-size:%?36?%;font-weight:700;color:#333}.header-actions[data-v-6584734a]{display:flex;flex-direction:row;gap:%?20?%}.action-button[data-v-6584734a]{width:%?72?%;height:%?72?%;display:flex;align-items:center;justify-content:center;border-radius:50%;color:#666;background-color:#f5f5f5;border:1px solid #e0e0e0;padding:0;margin:0;line-height:1}.team-selector[data-v-6584734a]{display:flex;align-items:center;padding:%?30?% %?40?%;background-color:#fff;border-bottom:1px solid #e0e0e0}.team-dropdown[data-v-6584734a]{display:flex;align-items:center;gap:%?10?%;padding:%?20?% %?30?%;border:1px solid #e0e0e0;border-radius:%?8?%;font-size:%?28?%;color:#333;background-color:#f5f5f5}.time-filter[data-v-6584734a]{display:flex;flex-direction:row;white-space:nowrap;background-color:#fff;border-bottom:1px solid #e0e0e0;padding:0 %?30?%}.time-option[data-v-6584734a]{padding:%?30?%;color:#666;font-size:%?28?%;position:relative}.time-option.active[data-v-6584734a]{color:#4a6fff;font-weight:500}.time-option.active[data-v-6584734a]::after{content:"";position:absolute;bottom:0;left:0;right:0;height:%?4?%;background-color:#4a6fff;border-radius:%?4?%}.page-content[data-v-6584734a]{flex:1;background-color:#f5f7fa}.metrics-cards[data-v-6584734a]{display:grid;grid-template-columns:repeat(2,1fr);gap:%?20?%;padding:%?30?%}.metric-card[data-v-6584734a]{background-color:#fff;border-radius:%?16?%;border:1px solid #e0e0e0;padding:%?30?%;display:flex;flex-direction:column;box-shadow:0 %?2?% %?8?% rgba(0,0,0,.05)}.metric-label[data-v-6584734a]{font-size:%?24?%;color:#666;margin-bottom:%?10?%}.metric-value[data-v-6584734a]{font-size:%?36?%;font-weight:700;color:#333}.metric-change[data-v-6584734a]{font-size:%?24?%;margin-top:%?10?%;display:flex;flex-direction:row;align-items:center}.change-positive[data-v-6584734a]{color:#34d399}.change-negative[data-v-6584734a]{color:#ef4444}.chart-container[data-v-6584734a]{background-color:#fff;border-radius:%?16?%;border:1px solid #e0e0e0;margin:0 %?30?% %?30?%;padding:%?30?%;box-shadow:0 %?2?% %?8?% rgba(0,0,0,.05)}.chart-header[data-v-6584734a]{display:flex;flex-direction:row;justify-content:space-between;align-items:center;margin-bottom:%?30?%}.chart-title[data-v-6584734a]{font-size:%?30?%;font-weight:500;color:#333}.chart-actions[data-v-6584734a]{display:flex;flex-direction:row;gap:%?10?%}.chart-action-btn[data-v-6584734a]{width:%?56?%;height:%?56?%;display:flex;align-items:center;justify-content:center;border-radius:%?8?%;color:#666;background-color:#f5f5f5;border:1px solid #e0e0e0;padding:0;margin:0;line-height:1}.chart-wrapper[data-v-6584734a]{width:100%;height:%?500?%;position:relative}.performance-table[data-v-6584734a]{margin:0 %?30?% %?30?%;background-color:#fff;border-radius:%?16?%;border:1px solid #e0e0e0;overflow:hidden;box-shadow:0 %?2?% %?8?% rgba(0,0,0,.05)}.table-header[data-v-6584734a]{display:flex;flex-direction:row;justify-content:space-between;align-items:center;padding:%?30?%;border-bottom:1px solid #e0e0e0}.table-title[data-v-6584734a]{font-size:%?30?%;font-weight:500}.table-wrapper[data-v-6584734a]{width:100%}.table-row[data-v-6584734a]{display:flex;flex-direction:row;border-bottom:1px solid #e0e0e0}.table-row[data-v-6584734a]:last-child{border-bottom:none}.table-header-row[data-v-6584734a]{background-color:#f5f7fa}.table-cell[data-v-6584734a]{padding:%?20?% %?30?%;font-size:%?28?%;flex:1}.table-head-cell[data-v-6584734a]{font-weight:500;color:#666}.employee-info[data-v-6584734a]{display:flex;flex-direction:row;align-items:center;gap:%?20?%}.employee-avatar[data-v-6584734a]{width:%?60?%;height:%?60?%;border-radius:50%;display:flex;align-items:center;justify-content:center;font-weight:500;font-size:%?24?%}.employee-name[data-v-6584734a]{font-weight:500}.achievement-cell[data-v-6584734a]{display:flex;flex-direction:column;gap:%?10?%}.achievement-label[data-v-6584734a]{display:flex;justify-content:space-between;font-size:%?24?%}.progress-bar[data-v-6584734a]{height:%?16?%;background-color:#e0e0e0;border-radius:%?8?%;overflow:hidden;width:100%}.progress-fill[data-v-6584734a]{height:100%;border-radius:%?8?%}.progress-fill-primary[data-v-6584734a]{background-color:#4a6fff}.progress-fill-success[data-v-6584734a]{background-color:#34d399}.progress-fill-warning[data-v-6584734a]{background-color:#f59e0b}.progress-fill-danger[data-v-6584734a]{background-color:#ef4444}.dropdown[data-v-6584734a]{position:relative}.dropdown-menu[data-v-6584734a]{position:absolute;top:100%;right:0;background-color:#fff;border-radius:%?16?%;box-shadow:0 %?8?% %?20?% rgba(0,0,0,.1);border:1px solid #e0e0e0;width:%?360?%;z-index:1000;display:none;margin-top:%?20?%}.dropdown-menu.show[data-v-6584734a]{display:block}.dropdown-item[data-v-6584734a]{display:flex;flex-direction:row;align-items:center;padding:%?30?%;color:#333;font-size:%?28?%;border-bottom:1px solid #e0e0e0}.dropdown-item[data-v-6584734a]:last-child{border-bottom:none}.dropdown-item uni-text[data-v-6584734a]:first-child{margin-right:%?20?%;font-size:%?32?%;color:#4a6fff}.dropdown-item.active[data-v-6584734a]{font-weight:500;background-color:rgba(74,111,255,.1)}',""]),e.exports=t},"8bfb":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={data:function(){return{showReportMenu:!1,selectedTeam:"销售一部",timeOptions:["本月","上个月","本季度","上季度","本年度","自定义"],selectedTimeIndex:0,metricsData:[{label:"团队销售额",value:"¥1,284,562",change:15.2,changeText:"15.2%",changeClass:"change-positive"},{label:"目标完成率",value:"78.6%",change:5.3,changeText:"5.3%",changeClass:"change-positive"},{label:"平均成单周期",value:"18.5天",change:-2.3,changeText:"2.3天",changeClass:"change-positive"},{label:"平均客单价",value:"¥35,682",change:8.7,changeText:"8.7%",changeClass:"change-positive"}],teamTargetData:{categories:["销售额","新增客户","跟进次数","商机转化"],series:[{name:"目标",data:[15e5,100,500,50]},{name:"已完成",data:[1284562,86,472,38]}]},teamTargetOpts:{color:["#9ca3af","#4a6fff"],padding:[15,15,0,5],enableScroll:!1,legend:{position:"top",itemGap:20},xAxis:{disableGrid:!0},yAxis:{data:[{position:"left",axisLine:!0,formatter:function(e){return e>=1e6?"¥"+(e/1e6).toFixed(1)+"M":e>=1e3?"¥"+(e/1e3).toFixed(0)+"K":e}}]},extra:{column:{width:30,categoryGap:4}}},teamTrendData:{categories:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],series:[{name:"销售额",data:[85e4,92e4,88e4,95e4,98e4,105e4,112e4,109e4,118e4,123e4,126e4,128e4]}]},teamTrendOpts:{color:["#4a6fff"],padding:[15,15,0,5],enableScroll:!1,legend:{show:!1},xAxis:{disableGrid:!0},yAxis:{gridType:"dash",data:[{formatter:function(e){return"¥"+e/1e4+"万"}}]},extra:{line:{type:"curve",width:2,activeType:"hollow"},tooltip:{showCategory:!0}}},performanceData:[{avatar:"ZL",name:"张丽",percentage:95,achieved:"¥475K",target:"¥500K",sales:"¥475,230",deals:28,progressClass:"progress-fill-success"},{avatar:"WM",name:"王明",percentage:82,achieved:"¥410K",target:"¥500K",sales:"¥410,625",deals:25,progressClass:"progress-fill-primary"},{avatar:"LJ",name:"李军",percentage:72,achieved:"¥289K",target:"¥400K",sales:"¥289,125",deals:19,progressClass:"progress-fill-primary"},{avatar:"CY",name:"陈阳",percentage:63,achieved:"¥252K",target:"¥400K",sales:"¥252,350",deals:17,progressClass:"progress-fill-warning"},{avatar:"ZX",name:"赵小红",percentage:42,achieved:"¥168K",target:"¥400K",sales:"¥168,420",deals:11,progressClass:"progress-fill-danger"}]}},methods:{toggleReportMenu:function(){this.showReportMenu=!this.showReportMenu},selectTimeOption:function(e){this.selectedTimeIndex=e,this.loadReportData(this.timeOptions[e])},loadReportData:function(e){console.log("加载时间框架数据:",e)},showTeamSelect:function(){var e=this;uni.showActionSheet({itemList:["销售一部","销售二部","销售三部","销售四部"],success:function(t){e.selectedTeam=["销售一部","销售二部","销售三部","销售四部"][t.tapIndex],e.loadTeamData()}})},loadTeamData:function(){console.log("加载团队数据:",this.selectedTeam)},shareReport:function(){uni.showActionSheet({itemList:["分享到微信","发送邮件","导出PDF","导出Excel"],success:function(e){uni.showToast({title:"分享功能开发中",icon:"none"})}})},downloadChart:function(e){uni.showToast({title:"下载功能开发中",icon:"none"})},showChartOptions:function(e){uni.showActionSheet({itemList:["查看大图","下载数据","查看明细数据"],success:function(e){uni.showToast({title:"功能开发中",icon:"none"})}})},downloadPerformance:function(){uni.showToast({title:"下载功能开发中",icon:"none"})}},onLoad:function(){this.loadReportData(this.timeOptions[this.selectedTimeIndex]),this.loadTeamData()},onShow:function(){this.loadReportData(this.timeOptions[this.selectedTimeIndex])},onHide:function(){this.showReportMenu=!1}};t.default=i},f078:function(e,t,a){"use strict";a.r(t);var i=a("55a6"),s=a("fd88");for(var n in s)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return s[e]}))}(n);a("3112");var o=a("828b"),r=Object(o["a"])(s["default"],i["b"],i["c"],!1,null,"6584734a",null,!1,i["a"],void 0);t["default"]=r.exports},fd88:function(e,t,a){"use strict";a.r(t);var i=a("8bfb"),s=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=s.a}}]);
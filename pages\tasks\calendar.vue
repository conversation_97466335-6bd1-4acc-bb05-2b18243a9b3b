<template>
  <view class="container">
    <view class="page-header">
      <view class="left-button" @click="goBack">
        <text class="iconfont icon-back"></text>
      </view>
      <view class="title">日历</view>
      <view class="right-button" @click="addTask">
        <text class="iconfont icon-add"></text>
      </view>
    </view>
    
    <view class="calendar-header">
      <view class="calendar-nav">
        <view class="nav-button" @click="prevMonth">
          <text class="iconfont icon-left"></text>
        </view>
        <view class="current-month">{{currentMonth}}</view>
        <view class="nav-button" @click="nextMonth">
          <text class="iconfont icon-right"></text>
        </view>
      </view>
      <view class="view-toggle">
        <view :class="['toggle-button', { 'active': viewMode === 'month' }]" @click="switchView('month')">月</view>
        <view :class="['toggle-button', { 'active': viewMode === 'day' }]" @click="switchView('day')">日</view>
      </view>
    </view>
    
    <view class="calendar-body">
      <!-- 月视图 -->
      <view class="month-view" v-if="viewMode === 'month'">
        <view class="weekday-header">
          <view class="weekday" v-for="(day, index) in weekdays" :key="index">{{day}}</view>
        </view>
        <view class="calendar-grid">
          <view 
            v-for="(day, index) in calendarDays" 
            :key="index" 
            :class="['calendar-day', { 
              'other-month': day.isOtherMonth,
              'current-day': day.isCurrentDay
            }]"
            @click="selectDay(day)"
          >
            <view class="day-number">{{day.dayNumber}}</view>
            <view class="day-events">
              <view 
                v-for="(event, eventIndex) in day.events.slice(0, 2)" 
                :key="eventIndex" 
                class="event-item"
              >
                <text :class="['event-dot', event.priority]"></text>
                <text class="event-time">{{event.time}}</text>
                <text class="event-title">{{event.title}}</text>
              </view>
              <view v-if="day.events.length > 2" class="event-count">
                +{{day.events.length - 2}} 更多
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 日视图 -->
      <view class="day-view" v-if="viewMode === 'day'">
        <view class="current-date">{{currentDate}}</view>
        <scroll-view class="timeline" scroll-y :scroll-top="scrollPosition">
          <view 
            v-for="hour in 24" 
            :key="hour" 
            class="time-slot"
          >
            <view class="time-label">{{formatHour(hour - 1)}}</view>
            <view class="time-content" :data-hour="hour - 1">
              <view 
                v-for="(event, eventIndex) in getEventsForHour(hour - 1)" 
                :key="eventIndex"
                :class="['timeline-event', event.priority]"
                :style="{
                  top: `${calculateEventTop(event)}rpx`,
                  height: `${calculateEventHeight(event)}rpx`
                }"
                @click="navigateToTaskDetail(event.id)"
              >
                <view class="timeline-event-title">{{event.title}}</view>
                <view class="timeline-event-meta">{{event.location || event.customer || ''}}</view>
              </view>
            </view>
          </view>
          <view 
            v-if="isToday"
            class="current-time"
            :style="{top: `${calculateCurrentTimePosition()}rpx`}"
          ></view>
        </scroll-view>
      </view>
    </view>
    
    <view class="add-button" @click="addTask">
      <text class="iconfont icon-add"></text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      viewMode: 'month', // 'month' 或 'day'
      date: new Date(),
      selectedDate: new Date(),
      weekdays: ['日', '一', '二', '三', '四', '五', '六'],
      calendarDays: [],
      scrollPosition: 0,
      events: [] // 存储任务事件数据
    }
  },
  computed: {
    currentMonth() {
      return `${this.date.getFullYear()}年${this.date.getMonth() + 1}月`;
    },
    currentDate() {
      const dayOfWeek = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][this.selectedDate.getDay()];
      return `${this.selectedDate.getFullYear()}年${this.selectedDate.getMonth() + 1}月${this.selectedDate.getDate()}日（${dayOfWeek}）`;
    },
    isToday() {
      const today = new Date();
      return (
        this.selectedDate.getFullYear() === today.getFullYear() &&
        this.selectedDate.getMonth() === today.getMonth() &&
        this.selectedDate.getDate() === today.getDate()
      );
    }
  },
  onLoad() {
    this.generateCalendarDays();
    this.fetchEvents();
    // 如果是日视图模式，滚动到当前时间
    if (this.viewMode === 'day') {
      this.scrollToCurrentTime();
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    addTask() {
      uni.navigateTo({
        url: '/pages/tasks/task-create'
      });
    },
    prevMonth() {
      this.date = new Date(this.date.getFullYear(), this.date.getMonth() - 1, 1);
      this.generateCalendarDays();
    },
    nextMonth() {
      this.date = new Date(this.date.getFullYear(), this.date.getMonth() + 1, 1);
      this.generateCalendarDays();
    },
    switchView(mode) {
      this.viewMode = mode;
      if (mode === 'day') {
        this.$nextTick(() => {
          this.scrollToCurrentTime();
        });
      }
    },
    generateCalendarDays() {
      this.calendarDays = [];
      
      const year = this.date.getFullYear();
      const month = this.date.getMonth();
      
      // 获取当月第一天和最后一天
      const firstDay = new Date(year, month, 1);
      const lastDay = new Date(year, month + 1, 0);
      
      // 获取当月第一天是星期几
      const firstDayOfWeek = firstDay.getDay();
      
      // 获取上个月的最后一天
      const prevMonthLastDay = new Date(year, month, 0).getDate();
      
      // 生成日历格子总数（6行7列）
      const totalDays = 42;
      
      // 生成上个月的尾部日期
      for (let i = 0; i < firstDayOfWeek; i++) {
        const day = prevMonthLastDay - firstDayOfWeek + i + 1;
        this.calendarDays.push({
          dayNumber: day,
          isOtherMonth: true,
          isCurrentDay: false,
          date: new Date(year, month - 1, day),
          events: this.getEventsForDay(year, month - 1, day)
        });
      }
      
      // 生成当月的日期
      for (let i = 1; i <= lastDay.getDate(); i++) {
        const today = new Date();
        const isCurrentDay = (
          year === today.getFullYear() &&
          month === today.getMonth() &&
          i === today.getDate()
        );
        
        this.calendarDays.push({
          dayNumber: i,
          isOtherMonth: false,
          isCurrentDay: isCurrentDay,
          date: new Date(year, month, i),
          events: this.getEventsForDay(year, month, i)
        });
      }
      
      // 生成下一个月的头部日期
      const remainingDays = totalDays - (firstDayOfWeek + lastDay.getDate());
      for (let i = 1; i <= remainingDays; i++) {
        this.calendarDays.push({
          dayNumber: i,
          isOtherMonth: true,
          isCurrentDay: false,
          date: new Date(year, month + 1, i),
          events: this.getEventsForDay(year, month + 1, i)
        });
      }
    },
    selectDay(day) {
      this.selectedDate = new Date(day.date);
      this.switchView('day');
    },
    fetchEvents() {
      // 模拟获取事件数据，实际应用中应从服务器或本地存储获取
      this.events = [
        {
          id: 1,
          title: '与王总进行项目方案讨论',
          time: '15:00',
          duration: 60,
          priority: 'high',
          customer: '创新科技有限公司',
          location: '会议室A',
          year: 2023,
          month: 6,
          day: 15
        },
        {
          id: 2,
          title: '回电广州未来科技公司',
          time: '16:30',
          duration: 30,
          priority: 'medium',
          customer: '未来科技有限公司',
          year: 2023,
          month: 6,
          day: 15
        },
        {
          id: 3,
          title: '完成月度销售报表',
          time: '17:00',
          duration: 120,
          priority: 'low',
          year: this.date.getFullYear(),
          month: this.date.getMonth() + 1,
          day: this.date.getDate() // 当天
        },
        {
          id: 4,
          title: '准备季度销售会议演示文稿',
          time: '10:00',
          duration: 120,
          priority: 'high',
          year: 2023,
          month: 6,
          day: 16
        },
        {
          id: 5,
          title: '跟进李四的报价单',
          time: '14:00',
          duration: 45,
          priority: 'medium',
          customer: '未来科技有限公司',
          year: 2023,
          month: 6,
          day: 16
        }
      ];
      
      // 随机生成一些额外的事件
      for (let i = 0; i < 10; i++) {
        const randomDay = Math.floor(Math.random() * 28) + 1;
        if (Math.random() > 0.7) {
          this.events.push({
            id: 100 + i,
            title: `任务 ${Math.floor(Math.random() * 100)}`,
            time: `${Math.floor(Math.random() * 12) + 9}:${Math.random() > 0.5 ? '30' : '00'}`,
            duration: Math.floor(Math.random() * 2) * 30 + 30,
            priority: ['high', 'medium', 'low'][Math.floor(Math.random() * 3)],
            year: this.date.getFullYear(),
            month: this.date.getMonth() + 1,
            day: randomDay
          });
        }
      }
    },
    getEventsForDay(year, month, day) {
      // 从事件列表中筛选出指定日期的事件
      return this.events.filter(event => 
        event.year === year &&
        event.month === month + 1 &&
        event.day === day
      );
    },
    getEventsForHour(hour) {
      // 筛选指定小时的事件
      return this.events.filter(event => {
        if (
          event.year === this.selectedDate.getFullYear() &&
          event.month === this.selectedDate.getMonth() + 1 &&
          event.day === this.selectedDate.getDate()
        ) {
          const eventHour = parseInt(event.time.split(':')[0]);
          return eventHour === hour;
        }
        return false;
      });
    },
    formatHour(hour) {
      return `${hour < 10 ? '0' + hour : hour}:00`;
    },
    calculateEventTop(event) {
      const [hours, minutes] = event.time.split(':').map(Number);
      return (minutes / 60) * 120; // 每小时120rpx高度
    },
    calculateEventHeight(event) {
      const duration = event.duration || 60;
      return (duration / 60) * 120; // 每小时120rpx高度
    },
    calculateCurrentTimePosition() {
      const now = new Date();
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();
      return (currentHour * 60 + currentMinute) * (120 / 60); // 每小时120rpx高度
    },
    scrollToCurrentTime() {
      const now = new Date();
      const currentHour = now.getHours();
      
      // 获取要滚动到的位置
      this.scrollPosition = Math.max(0, (currentHour - 1) * 120);
    },
    navigateToTaskDetail(id) {
      uni.navigateTo({
        url: `/pages/tasks/task-detail?id=${id}`
      });
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #3c8dbc;
  color: #fff;
}

.left-button, .right-button {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.calendar-nav {
  display: flex;
  align-items: center;
}

.nav-button {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.current-month {
  font-size: 34rpx;
  margin: 0 20rpx;
  min-width: 200rpx;
  text-align: center;
}

.view-toggle {
  display: flex;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  overflow: hidden;
}

.toggle-button {
  padding: 10rpx 30rpx;
  font-size: 28rpx;
  background-color: #f8f8f8;
}

.toggle-button.active {
  background-color: #3c8dbc;
  color: #fff;
}

.weekday-header {
  display: flex;
  background-color: #fff;
  padding: 20rpx 0;
}

.weekday {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #666;
}

.calendar-grid {
  display: flex;
  flex-wrap: wrap;
  background-color: #fff;
}

.calendar-day {
  width: 14.28%;
  height: 180rpx;
  border-right: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
  box-sizing: border-box;
  padding: 10rpx;
  position: relative;
}

.other-month {
  background-color: #f9f9f9;
  color: #ccc;
}

.current-day .day-number {
  background-color: #3c8dbc;
  color: #fff;
  border-radius: 50%;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.day-number {
  font-size: 28rpx;
  margin-bottom: 10rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.day-events {
  font-size: 24rpx;
  overflow: hidden;
}

.event-item {
  margin-bottom: 6rpx;
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.event-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.event-dot.high {
  background-color: #f56c6c;
}

.event-dot.medium {
  background-color: #e6a23c;
}

.event-dot.low {
  background-color: #67c23a;
}

.event-time {
  margin-right: 8rpx;
  color: #666;
}

.event-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.event-count {
  font-size: 24rpx;
  color: #888;
  text-align: right;
  margin-top: 6rpx;
}

.day-view {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.current-date {
  padding: 20rpx 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  border-bottom: 1rpx solid #eee;
}

.timeline {
  flex: 1;
  position: relative;
}

.time-slot {
  display: flex;
  height: 120rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.time-label {
  width: 100rpx;
  padding: 10rpx;
  font-size: 24rpx;
  color: #999;
  text-align: center;
  border-right: 1rpx solid #f0f0f0;
}

.time-content {
  flex: 1;
  position: relative;
}

.timeline-event {
  position: absolute;
  left: 10rpx;
  right: 10rpx;
  padding: 10rpx;
  border-radius: 6rpx;
  color: #fff;
  font-size: 24rpx;
  overflow: hidden;
}

.timeline-event.high {
  background-color: #f56c6c;
}

.timeline-event.medium {
  background-color: #e6a23c;
}

.timeline-event.low {
  background-color: #67c23a;
}

.timeline-event-title {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.timeline-event-meta {
  font-size: 22rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.current-time {
  position: absolute;
  left: 0;
  right: 0;
  height: 2rpx;
  background-color: #f56c6c;
  z-index: 2;
}

.current-time:before {
  content: "";
  position: absolute;
  left: 100rpx;
  top: -4rpx;
  width: 10rpx;
  height: 10rpx;
  background-color: #f56c6c;
  border-radius: 50%;
}

.add-button {
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #3c8dbc;
  color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 50rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
  z-index: 99;
}
</style> 
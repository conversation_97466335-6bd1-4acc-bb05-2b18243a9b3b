<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="goBack">
        <text class="ri-arrow-left-line"></text>
      </view>
      <text class="page-title">员工管理</text>
      <view class="header-actions">
        <button type="button" class="action-button" @click="showFilterOptions">
          <text class="ri-filter-3-line"></text>
        </button>
        <button type="button" class="action-button" @click="showSortOptions">
          <text class="ri-sort-desc"></text>
        </button>
      </view>
    </view>
    
    <!-- 搜索栏 -->
    <view class="search-bar">
      <text class="ri-search-line search-icon"></text>
      <input 
        type="text" 
        class="search-input" 
        v-model="searchQuery" 
        placeholder="搜索员工姓名、部门、职位..." 
        @input="onSearch"
      />
    </view>
    
    <!-- 筛选栏 -->
    <scroll-view scroll-x class="filter-bar">
      <view 
        v-for="(filter, index) in filters" 
        :key="index" 
        class="filter-button"
        :class="{ active: currentFilter === filter.value }"
        @click="setFilter(filter.value)"
      >
        <text>{{filter.label}}</text>
      </view>
    </scroll-view>
    
    <!-- 部门统计 -->
    <view class="stats-panel">
      <view class="stats-card">
        <text class="stats-title">员工总数</text>
        <text class="stats-value">{{totalEmployees}}</text>
      </view>
      <view class="stats-card">
        <text class="stats-title">本月新入职</text>
        <text class="stats-value">{{newEmployees}}</text>
      </view>
      <view class="stats-card">
        <text class="stats-title">离职率</text>
        <text class="stats-value">{{turnoverRate}}%</text>
      </view>
    </view>
    
    <!-- 员工列表 -->
    <scroll-view 
      scroll-y 
      class="employee-list"
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="loadMore"
    >
      <view v-if="filteredEmployees.length > 0">
        <view 
          v-for="(employee, index) in filteredEmployees" 
          :key="index" 
          class="employee-item"
          @click="goToDetail(employee.id)"
        >
          <view class="employee-header">
            <view class="employee-avatar">
              <image :src="employee.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
            </view>
            <view class="employee-title-row">
              <text class="employee-name">{{employee.name}}</text>
              <text :class="['employee-status', employee.status === 'active' ? 'status-active' : 'status-inactive']">
                {{employee.status === 'active' ? '在职' : '离职'}}
              </text>
            </view>
          </view>
          
          <view class="employee-info">
            <view class="info-item">
              <text class="info-label">工号</text>
              <text class="info-value">{{employee.employeeId}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">部门</text>
              <text class="info-value">{{employee.department}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">职位</text>
              <text class="info-value">{{employee.position}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">入职日期</text>
              <text class="info-value">{{employee.hireDate}}</text>
            </view>
          </view>
          
          <view class="employee-footer">
            <view class="employee-contact">
              <text class="contact-phone ri-phone-line" @click.stop="callEmployee(employee)"> {{employee.phone}}</text>
              <text class="contact-email ri-mail-line" @click.stop="emailEmployee(employee)"> {{employee.email}}</text>
            </view>
            <view class="employee-actions">
              <view class="action-icon" @click.stop="viewEmployeePerformance(employee.id)">
                <text class="ri-line-chart-line"></text>
              </view>
              <view class="action-icon" @click.stop="editEmployee(employee.id)">
                <text class="ri-edit-line"></text>
              </view>
            </view>
          </view>
        </view>
        
        <view v-if="loadingMore" class="loading-more">
          <text>加载更多...</text>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-else class="empty-state">
        <text class="empty-icon ri-user-3-line"></text>
        <text class="empty-title">暂无员工记录</text>
        <text class="empty-text">添加您的第一位员工，开始管理团队</text>
        <button class="create-button" @click="createEmployee">
          <text class="ri-add-line"></text>
          <text>添加员工</text>
        </button>
      </view>
    </scroll-view>
    
    <!-- 创建员工浮动按钮 -->
    <view class="floating-button" @click="createEmployee">
      <text class="ri-add-line"></text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      searchQuery: '',
      currentFilter: 'all',
      filters: [
        { label: '全部', value: 'all' },
        { label: '在职', value: 'active' },
        { label: '离职', value: 'inactive' },
        { label: '销售部', value: 'sales' },
        { label: '技术部', value: 'tech' },
        { label: '市场部', value: 'marketing' },
        { label: '财务部', value: 'finance' },
        { label: '人事部', value: 'hr' }
      ],
      employees: [
        {
          id: '1',
          name: '张三',
          status: 'active',
          employeeId: 'EMP001',
          department: '销售部',
          position: '销售经理',
          hireDate: '2020-06-15',
          phone: '13812345678',
          email: '<EMAIL>',
          avatar: '/static/images/avatars/avatar1.png'
        },
        {
          id: '2',
          name: '李四',
          status: 'active',
          employeeId: 'EMP002',
          department: '技术部',
          position: '高级开发工程师',
          hireDate: '2020-08-20',
          phone: '13987654321',
          email: '<EMAIL>',
          avatar: '/static/images/avatars/avatar2.png'
        },
        {
          id: '3',
          name: '王五',
          status: 'active',
          employeeId: 'EMP003',
          department: '市场部',
          position: '市场总监',
          hireDate: '2021-03-10',
          phone: '13765432198',
          email: '<EMAIL>',
          avatar: '/static/images/avatars/avatar3.png'
        },
        {
          id: '4',
          name: '赵六',
          status: 'inactive',
          employeeId: 'EMP004',
          department: '财务部',
          position: '财务经理',
          hireDate: '2019-12-05',
          phone: '13698765432',
          email: '<EMAIL>',
          avatar: '/static/images/avatars/avatar4.png'
        }
      ],
      isRefreshing: false,
      loadingMore: false,
      page: 1,
      hasMore: true,
      totalEmployees: 65,
      newEmployees: 3,
      turnoverRate: 2.5
    }
  },
  computed: {
    filteredEmployees() {
      let result = [...this.employees];
      
      // 根据搜索关键词筛选
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        result = result.filter(employee => 
          employee.name.toLowerCase().includes(query) ||
          employee.department.toLowerCase().includes(query) ||
          employee.position.toLowerCase().includes(query) ||
          employee.employeeId.toLowerCase().includes(query)
        );
      }
      
      // 根据状态或部门筛选
      if (this.currentFilter !== 'all') {
        // 状态筛选
        if (this.currentFilter === 'active' || this.currentFilter === 'inactive') {
          result = result.filter(employee => employee.status === this.currentFilter);
        } 
        // 部门筛选
        else {
          const departmentMap = {
            'sales': '销售部',
            'tech': '技术部',
            'marketing': '市场部',
            'finance': '财务部',
            'hr': '人事部'
          };
          const department = departmentMap[this.currentFilter];
          result = result.filter(employee => employee.department === department);
        }
      }
      
      return result;
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    setFilter(filter) {
      this.currentFilter = filter;
    },
    onSearch(e) {
      // 搜索功能实现
      this.searchQuery = e.detail.value;
    },
    onRefresh() {
      this.isRefreshing = true;
      
      // 模拟刷新数据
      setTimeout(() => {
        this.isRefreshing = false;
      }, 1500);
    },
    loadMore() {
      if (!this.hasMore || this.loadingMore) return;
      
      this.loadingMore = true;
      
      // 模拟加载更多数据
      setTimeout(() => {
        // 这里应该调用API加载更多数据
        this.loadingMore = false;
        
        // 假设没有更多数据了
        if (this.page >= 3) {
          this.hasMore = false;
        } else {
          this.page++;
        }
      }, 1500);
    },
    goToDetail(id) {
      uni.navigateTo({
        url: `/pages/hr/employee-detail?id=${id}`
      });
    },
    createEmployee() {
      uni.navigateTo({
        url: '/pages/hr/employee-create'
      });
    },
    editEmployee(id) {
      uni.navigateTo({
        url: `/pages/hr/employee-edit?id=${id}`
      });
    },
    viewEmployeePerformance(id) {
      uni.navigateTo({
        url: `/pages/hr/employee-performance?id=${id}`
      });
    },
    callEmployee(employee) {
      // 处理电话功能
      uni.makePhoneCall({
        phoneNumber: employee.phone,
        success: () => {
          console.log('拨打电话成功');
        },
        fail: (err) => {
          console.error('拨打电话失败', err);
        }
      });
    },
    emailEmployee(employee) {
      // 复制邮箱到剪贴板
      uni.setClipboardData({
        data: employee.email,
        success: () => {
          uni.showToast({
            title: '邮箱已复制',
            icon: 'success'
          });
        }
      });
    },
    showFilterOptions() {
      // 显示更多筛选选项
      uni.showActionSheet({
        itemList: ['按入职日期排序', '按部门筛选', '按职位筛选', '自定义筛选'],
        success: (res) => {
          console.log('选择了: ' + res.tapIndex);
          // 根据选择执行对应的筛选逻辑
        }
      });
    },
    showSortOptions() {
      // 显示排序选项
      uni.showActionSheet({
        itemList: ['按姓名排序', '按入职日期排序', '按工号排序', '按部门排序'],
        success: (res) => {
          console.log('选择了: ' + res.tapIndex);
          // 根据选择执行对应的排序逻辑
        }
      });
    }
  }
};
</script>

<style>
.container {
  padding-bottom: 120rpx;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  position: relative;
  border-bottom: 1rpx solid #eaeaea;
}

.back-button {
  font-size: 40rpx;
  color: #333;
  padding: 10rpx;
}

.page-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
}

.action-button {
  background: none;
  border: none;
  font-size: 40rpx;
  color: #666;
  padding: 10rpx;
  margin-left: 20rpx;
}

.search-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eaeaea;
}

.search-icon {
  margin-right: 20rpx;
  color: #999;
  font-size: 32rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  background-color: #f5f7fa;
  border-radius: 35rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}

.filter-bar {
  display: flex;
  white-space: nowrap;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eaeaea;
  padding: 20rpx 0;
}

.filter-button {
  display: inline-block;
  padding: 10rpx 30rpx;
  margin: 0 15rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f5f7fa;
}

.filter-button.active {
  background-color: #4a6fff;
  color: #ffffff;
}

.stats-panel {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.stats-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx 0;
}

.stats-title {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.stats-value {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.employee-list {
  flex: 1;
  height: calc(100vh - 300rpx);
}

.employee-item {
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.employee-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eaeaea;
}

.employee-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  background-color: #eaeaea;
  margin-right: 20rpx;
}

.employee-avatar image {
  width: 100%;
  height: 100%;
}

.employee-title-row {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.employee-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.employee-status {
  font-size: 24rpx;
  padding: 6rpx 15rpx;
  border-radius: 15rpx;
}

.status-active {
  background-color: #e6f7ed;
  color: #52c41a;
}

.status-inactive {
  background-color: #fff1f0;
  color: #ff4d4f;
}

.employee-info {
  padding: 20rpx 30rpx;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
}

.employee-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eaeaea;
  background-color: #f9fafc;
}

.employee-contact {
  display: flex;
  flex-direction: column;
}

.contact-phone, .contact-email {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.employee-actions {
  display: flex;
  align-items: center;
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 15rpx;
  background-color: #f0f2f5;
  color: #666;
  font-size: 32rpx;
}

.loading-more {
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 26rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  color: #cccccc;
  margin-bottom: 20rpx;
}

.empty-title {
  font-size: 34rpx;
  color: #333;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
  text-align: center;
}

.create-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  background-color: #4a6fff;
  color: #ffffff;
  border-radius: 40rpx;
  font-size: 30rpx;
}

.create-button text {
  margin-right: 10rpx;
}

.floating-button {
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #4a6fff;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ffffff;
  font-size: 50rpx;
  box-shadow: 0 4rpx 16rpx rgba(74, 111, 255, 0.4);
  z-index: 999;
}
</style> 
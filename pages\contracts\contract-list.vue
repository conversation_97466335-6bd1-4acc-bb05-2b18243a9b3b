<template>
  <view class="container" :style="cssVars">
    <!-- 页面顶部Tabs -->
    <scroll-view class="tabs-container" scroll-x>
      <view
        class="tab"
        v-for="(status, index) in contractStatuses"
        :key="index"
        :class="{ active: filters.status === status.value }"
        @tap="setStatusFilter(status.value)"
      >
        {{ status.label }}
      </view>
    </scroll-view>

    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <view class="search-icon">
          <svg-icon name="search" type="svg" size="32"></svg-icon>
        </view>
        <input
          type="text"
          class="search-input"
          v-model="searchQuery"
          placeholder="搜索合同名称/客户"
          @input="onSearchInput"
        />
      </view>
    </view>

    <!-- 筛选栏 -->
    <view class="filter-bar">
      <button class="filter-button" @tap="toggleFilterPanel">
        <svg-icon name="filter" type="svg" size="28"></svg-icon>
        <text>筛选</text>
      </button>
      <button class="sort-button" @tap="showSortOptions">
        <svg-icon name="sort" type="svg" size="28"></svg-icon>
        <text>排序</text>
      </button>
    </view>

    <!-- 合同列表 -->
    <view class="contracts-list">
      <!-- 加载中状态 -->
      <!-- <view class="empty-state" v-if="loading">
        <view class="loading-icon">
          <svg-icon name="loading" type="svg" size="64"></svg-icon>
        </view>
        <view class="empty-title">正在加载</view>
        <view class="empty-description">正在获取合同数据，请稍候...</view>
      </view> -->

      <!-- 空状态提示 -->
      <!-- <view class="empty-state" v-else-if="contracts.length === 0">
        <view class="empty-icon">
          <svg-icon name="file-list" type="svg" size="96"></svg-icon>
        </view>
        <view class="empty-title">暂无合同</view>
        <view class="empty-description"
          >您还没有创建任何合同，点击右下角的加号创建新合同。</view
        >
        <button class="btn btn-primary" @tap="navigateToCreateContract">
          <svg-icon name="add" type="svg" size="32"></svg-icon> 创建合同
        </button>
      </view> -->

      <!-- 合同卡片列表 -->
      <view
        class="contract-card"
        v-for="(contract, index) in contracts"
        :key="index"
        :data-status="contract.contractStatus.code"
      >
        <view
          class="card-overlay"
          @tap="navigateToContractDetail(contract.id)"
        ></view>
        <view class="contract-header">
          <view class="contract-info">
            <view
              class="contract-name"
              @tap="navigateToContractDetail(contract.id)"
              >{{ contract.name }}</view
            >
            <view class="contract-tags">
              <text
                class="tag"
                :class="'tag-' + contract.contractStatus.code"
                >{{ getStatusLabel(contract.contractStatus.code) }}</text
              >
              <text class="tag tag-company">{{ contract.customName }}</text>
            </view>
          </view>
          <view class="contract-value"
            >¥{{ formatMoney(contract.amount) }}</view
          >
        </view>
        <view class="contract-content">
          <view class="contract-details">
            <view class="detail-item">
              <view class="detail-label">签订日期:</view>
              <view class="detail-value">{{
                contract.creationTime | formatDateFilter
              }}</view>
            </view>
            <view class="detail-item">
              <view class="detail-label">负责人:</view>
              <view class="detail-value">{{ contract.owner }}</view>
            </view>
            <view class="detail-item">
              <view class="detail-label">关联商机:</view>
              <view class="detail-value">{{
                contract.opportunityName || "无"
              }}</view>
            </view>
            <view class="detail-item">
              <view class="detail-label">客户:</view>
              <view class="detail-value">{{ contract.customName }}</view>
            </view>
          </view>
        </view>
        <view class="contract-footer">
          <view class="contract-action" @tap="viewContractFiles(contract.id)">
            <svg-icon name="file-list" type="svg" size="28"></svg-icon> 查看附件
          </view>
          <view
            class="contract-action"
            @tap="navigateToPaymentRecords(contract.id)"
          >
            <svg-icon
              name="money-dollar-circle"
              type="svg"
              size="28"
            ></svg-icon>
            收款记录
          </view>
          <view class="contract-action" @tap="editContract(contract.id)">
            <svg-icon name="edit" type="svg" size="28"></svg-icon> 编辑
          </view>
        </view>
      </view>

      <!-- <view
        v-if="hasMoreContracts && contracts.length > 0"
        class="loading-more"
      >
        <view class="loading-indicator"></view>
        <text>加载更多...</text>
      </view> -->

      <!-- <view
        v-if="!hasMoreContracts && contracts.length > 0"
        class="no-more-data"
      >
        <text>—— 没有更多数据了 ——</text>
      </view> -->
    </view>

    <!-- 筛选面板 -->
    <view class="modal-filter" v-if="showFilterPanel">
      <view class="modal-mask" @tap="toggleFilterPanel"></view>
      <view class="modal-dialog">
        <view class="modal-header">
          <view class="modal-title">筛选条件</view>
          <view class="modal-close" @tap="toggleFilterPanel">
            <svg-icon name="close" type="svg" size="32"></svg-icon>
          </view>
        </view>
        <scroll-view class="modal-content" scroll-y>
          <!-- 筛选表单内容 -->
          <view class="filter-group">
            <text class="filter-label">合同状态</text>
            <view class="checkbox-grid">
              <view
                v-for="(status, index) in contractStatuses.slice(1)"
                :key="index"
                :class="[
                  'status-option',
                  { active: filters.status === status.value },
                ]"
                @tap="setStatusFilter(status.value)"
              >
                {{ status.label }}
              </view>
            </view>
          </view>

          <view class="filter-group">
            <text class="filter-label">时间范围</text>
            <view class="date-range">
              <view class="date-picker" @tap="showStartDatePicker">
                <view class="date-value">{{
                  filters.dateRange.start || "开始日期"
                }}</view>
                <view class="date-icon">
                  <svg-icon name="calendar" type="svg" size="24"></svg-icon>
                </view>
              </view>
              <view class="date-separator">至</view>
              <view class="date-picker" @tap="showEndDatePicker">
                <view class="date-value">{{
                  filters.dateRange.end || "结束日期"
                }}</view>
                <view class="date-icon">
                  <svg-icon name="calendar" type="svg" size="24"></svg-icon>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
        <view class="modal-footer">
          <button class="btn btn-reset" @tap="resetFilters">重置</button>
          <button class="btn btn-confirm" @tap="applyFilters">应用</button>
        </view>
      </view>
    </view>

    <!-- 悬浮添加按钮 -->
    <view class="fab" @tap="navigateToCreateContract">
      <svg-icon name="add" type="svg" size="60" color="#FFFFFF"></svg-icon>
    </view>

    <!-- 自定义TabBar组件 -->
    <custom-tab-bar ref="customTabBar"></custom-tab-bar>
  </view>
</template>

<script>
import CustomTabBar from "@/components/CustomTabBar.vue";
import SvgIcon from "@/components/svg-icon.vue";
import { getContactList } from "@/api/contact.api.js";

export default {
  components: {
    CustomTabBar,
    SvgIcon,
  },
  computed: {
    // CSS变量计算值
    cssVars() {
      // 将主题色转换为RGB以便在rgba()中使用
      const primaryColor = "#0057ff"; // 假设的主色值
      const primaryColorRGB = this.hexToRgb(primaryColor);

      // 其他主题色变体
      const primaryColorLight = "#3a80ff";
      const primaryColorDark = "#0046cc";

      return {
        "--primary-color-rgb": primaryColorRGB,
        "--primary-color-light": primaryColorLight,
        "--primary-color-dark": primaryColorDark,
        "--light-color-rgb": "245, 247, 250",
        "--primary-color": primaryColor,
      };
    },
  },
  data() {
    return {
      searchQuery: "",
      showFilterPanel: false,
      contracts: [],
      loading: true,
      hasMoreContracts: true,
      page: 1,
      pageSize: 10,
      likeString: undefined,
      currentDateType: "", // 'start' 或 'end'
      contractStatuses: [
        { label: "草稿", value: "Draft" },
        { label: "已签署", value: "Signed" },
        { label: "已关闭", value: "Closed" },
      ],
      filters: {
        status: "",
        dateRange: {
          start: "",
          end: "",
        },
      },
    };
  },
  onReady() {
    // 获取系统信息以计算列表高度
    const systemInfo = uni.getSystemInfoSync();
    // 计算搜索栏和筛选面板高度（根据实际情况调整）
    const searchBarHeight = 60;
    const filterPanelHeight = this.showFilterPanel ? 260 : 0;
    // 计算列表高度，留出底部导航和顶部状态栏的位置
    this.listHeight =
      systemInfo.windowHeight - searchBarHeight - filterPanelHeight - 50;
  },
  onLoad() {
    this.getList();
  },
  onShow() {
    // 设置TabBar当前选中项
    if (typeof this.$refs.customTabBar !== "undefined") {
      this.$refs.customTabBar.current = 4; // 对应"更多"菜单
    }
  },
  methods: {
    // 获取合同列表
    async getList() {
      try {
        const params = {
          pageIndex: this.page,
          pageSize: this.pageSize,
          filter: {},
        };

        const res = await getContactList(params);
        this.contracts = res.items;
      } catch (error) {
        console.log(error);
      }
    },

    // 将十六进制颜色转换为RGB
    hexToRgb(hex) {
      // 移除#前缀如果存在
      hex = hex.replace(/^#/, "");

      // 解析十六进制
      let bigint = parseInt(hex, 16);
      let r = (bigint >> 16) & 255;
      let g = (bigint >> 8) & 255;
      let b = bigint & 255;

      return `${r}, ${g}, ${b}`;
    },

    // 搜索相关方法
    onSearchInput() {
      this.debounceSearch();
    },
    debounceSearch: function () {
      // 实际应用中应该使用防抖处理
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout);
      }
      this.searchTimeout = setTimeout(() => {
        this.page = 1;
        this.contracts = [];
        this.hasMoreContracts = true;
        this.fetchContracts();
      }, 500);
    },
    clearSearch() {
      this.searchQuery = "";
      this.page = 1;
      this.contracts = [];
      this.hasMoreContracts = true;
      this.fetchContracts();
    },

    // 筛选相关方法
    toggleFilterPanel() {
      this.showFilterPanel = !this.showFilterPanel;
      // 更新列表高度
      setTimeout(() => {
        this.updateListHeight();
      }, 0);
    },
    updateListHeight() {
      const systemInfo = uni.getSystemInfoSync();
      const searchBarHeight = 60;
      const filterPanelHeight = this.showFilterPanel ? 260 : 0;
      this.listHeight =
        systemInfo.windowHeight - searchBarHeight - filterPanelHeight - 50;
    },
    setStatusFilter(status) {
      this.filters.status = status;
    },
    onStartDateChange(e) {
      this.filters.dateRange.start = e.detail.value;
    },
    onEndDateChange(e) {
      this.filters.dateRange.end = e.detail.value;
    },
    resetFilters() {
      this.filters = {
        status: "",
        dateRange: {
          start: "",
          end: "",
        },
      };
    },
    applyFilters() {
      this.page = 1;
      this.contracts = [];
      this.hasMoreContracts = true;
      this.fetchContracts();
      this.showFilterPanel = false;
      this.updateListHeight();
    },

    // 数据相关方法
    fetchContracts() {
      this.loading = true;

      // 模拟API调用
      setTimeout(() => {
        // 模拟数据
        const mockContracts = [
          {
            id: "1",
            name: "软件开发服务合同",
            status: "active",
            customerName: "上海科技有限公司",
            amount: 150000,
            signDate: "2023-10-15",
            owner: "张经理",
          },
          {
            id: "2",
            name: "设备采购合同",
            status: "pending",
            customerName: "北京电子科技有限公司",
            amount: 230000,
            signDate: "2023-09-20",
            owner: "李经理",
          },
          {
            id: "3",
            name: "咨询服务协议",
            status: "completed",
            customerName: "广州互联网科技有限公司",
            amount: 50000,
            signDate: "2023-08-05",
            owner: "王经理",
          },
          {
            id: "4",
            name: "市场推广合同",
            status: "active",
            customerName: "深圳营销有限公司",
            amount: 80000,
            signDate: "2023-11-18",
            owner: "刘经理",
          },
          {
            id: "5",
            name: "产品维护协议",
            status: "draft",
            customerName: "杭州科技有限公司",
            amount: 35000,
            signDate: "2023-12-01",
            owner: "赵经理",
          },
        ];

        // 根据过滤条件筛选数据
        let filteredContracts = [...mockContracts];

        // 搜索条件过滤
        if (this.searchQuery) {
          const query = this.searchQuery.toLowerCase();
          filteredContracts = filteredContracts.filter(
            (contract) =>
              contract.name.toLowerCase().includes(query) ||
              contract.customerName.toLowerCase().includes(query)
          );
        }

        // 状态过滤
        if (this.filters.status) {
          filteredContracts = filteredContracts.filter(
            (contract) => contract.status === this.filters.status
          );
        }

        // 日期范围过滤
        if (this.filters.dateRange.start) {
          const startDate = new Date(this.filters.dateRange.start);
          filteredContracts = filteredContracts.filter(
            (contract) => new Date(contract.signDate) >= startDate
          );
        }

        if (this.filters.dateRange.end) {
          const endDate = new Date(this.filters.dateRange.end);
          filteredContracts = filteredContracts.filter(
            (contract) => new Date(contract.signDate) <= endDate
          );
        }

        // 模拟分页
        const newContracts = filteredContracts.slice(
          (this.page - 1) * this.pageSize,
          this.page * this.pageSize
        );

        this.contracts = [...this.contracts, ...newContracts];
        this.hasMoreContracts = newContracts.length === this.pageSize;
        this.loading = false;
      }, 1000);
    },
    loadMoreContracts() {
      if (this.hasMoreContracts && !this.loading) {
        this.page++;
        this.fetchContracts();
      }
    },

    // 辅助方法
    getStatusLabel(status) {
      const statusItem = this.contractStatuses.find(
        (item) => item.value === status
      );
      return statusItem ? statusItem.label : "未知状态";
    },
    formatMoney(amount) {
      return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },

    // 导航方法
    navigateToContractDetail(id) {
      uni.navigateTo({
        url: `/pages/contracts/contract-detail?id=${id}`,
      });
    },
    navigateToCreateContract() {
      uni.navigateTo({
        url: "/pages/contracts/contract-create",
      });
    },
    viewContractFiles(id) {
      uni.navigateTo({
        url: `/pages/contracts/contract-files?id=${id}`,
      });
    },
    navigateToPaymentRecords(id) {
      uni.navigateTo({
        url: `/pages/contracts/payment-records?id=${id}`,
      });
    },
  },
};
</script>

<style>
.container {
  background-color: #f8fafc;
  min-height: 100vh;
}

.tabs-container {
  display: flex;
  white-space: nowrap;
  background-color: #ffffff;
  border-bottom: 1rpx solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.tab {
  display: inline-block;
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--text-secondary);
  font-size: 28rpx;
  position: relative;
  transition: all 0.2s ease;
}

.tab.active {
  color: var(--primary-color);
  font-weight: 600;
}

.tab.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: var(--spacing-lg);
  right: var(--spacing-lg);
  height: 4rpx;
  background-color: var(--primary-color);
  border-radius: var(--radius-full);
}

/* 搜索栏样式 */
.search-container {
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: #ffffff;
  border-bottom: 1rpx solid var(--border-color);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
}

.search-box {
  display: flex;
  align-items: center;
  background-color: var(--light-color);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 0 var(--spacing-sm);
  overflow: hidden;
  box-shadow: inset 0 2rpx 5rpx rgba(0, 0, 0, 0.03);
}

.search-icon {
  color: var(--text-secondary);
  padding: var(--spacing-xs);
}

.search-input {
  flex: 1;
  border: none;
  padding: var(--spacing-sm);
  background-color: transparent;
  color: var(--text-primary);
  font-size: 28rpx;
}

/* 筛选栏样式 */
.filter-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: #ffffff;
  border-bottom: 1rpx solid var(--border-color);
}

.filter-button,
.sort-button {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: var(--text-secondary);
  padding: 16rpx 24rpx;
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--light-color);
  transition: all 0.2s ease;
}

.filter-button:active,
.sort-button:active {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  border-color: var(--primary-color-light);
}

.filter-button text,
.sort-button text {
  margin-left: 8rpx;
}

/* 合同列表和卡片样式 */
.contracts-list {
  padding: var(--spacing-md) var(--spacing-lg);
  padding-bottom: calc(var(--spacing-xl) * 4); /* 增加底部填充空间 */
}

.contract-card {
  background-color: #ffffff;
  border-radius: var(--radius-md);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  margin-bottom: var(--spacing-md);
  overflow: hidden;
  border: 1rpx solid var(--border-color);
  position: relative;
  transition: all 0.3s ease;
}

.contract-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 合同状态标识条 */
.contract-card::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background-color: var(--border-color);
  z-index: 2;
}

.contract-card[data-status="Draft"]::before {
  background-color: #9ca3af;
}

.contract-card[data-status="Signed"]::before {
  background-color: #f59e0b;
}

.contract-card[data-status="Closed"]::before {
  background-color: #3b82f6;
}

.contract-card[data-status="Completed"]::before {
  background-color: #10b981;
}

.contract-card[data-status="Cancelled"]::before {
  background-color: #ef4444;
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.contract-header,
.contract-content,
.contract-footer {
  position: relative;
  z-index: 2;
}

/* 确保所有可点击元素在overlay之上 */
.contract-name,
.contract-action {
  position: relative;
  z-index: 3;
}

.contract-header {
  padding: var(--spacing-md);
  border-bottom: 1rpx solid var(--border-color-light);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: linear-gradient(
    to right,
    rgba(249, 250, 251, 0.5),
    rgba(255, 255, 255, 0.8)
  );
}

.contract-info {
  flex: 1;
}

.contract-name {
  font-size: 32rpx;
  font-weight: 600;
  margin: 0 0 12rpx 0;
  color: var(--text-primary);
  cursor: pointer;
  position: relative;
  display: inline-block;
  padding: 4rpx 0;
}

.contract-name:hover,
.contract-name:active {
  color: var(--primary-color);
}

.contract-name:active:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background-color: var(--primary-color);
}

.contract-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: var(--spacing-xs);
}

.tag {
  padding: 4rpx 16rpx;
  border-radius: var(--radius-full);
  font-size: 22rpx;
  font-weight: 500;
}

.tag-Draft {
  background-color: #e5e7eb;
  color: #6b7280;
}

.tag-Signed {
  background-color: #fef3c7;
  color: #d97706;
}

.tag-Closed {
  background-color: #dbeafe;
  color: #2563eb;
}

.tag-completed {
  background-color: #d1fae5;
  color: #059669;
}

.tag-cancelled {
  background-color: #fee2e2;
  color: #dc2626;
}

.tag-company {
  background-color: #f3f4f6;
  color: #4b5563;
}

.contract-value {
  font-weight: 700;
  color: var(--primary-color);
  font-size: 32rpx;
  background-color: rgba(var(--primary-color-rgb), 0.1);
  padding: 8rpx 16rpx;
  border-radius: var(--radius-md);
  text-align: right;
}

.contract-content {
  padding: var(--spacing-md);
}

.contract-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
  background-color: rgba(var(--light-color-rgb), 0.3);
  padding: var(--spacing-md) var(--spacing-sm);
  border-radius: var(--radius-md);
}

.detail-item {
  display: flex;
  align-items: baseline;
  margin-bottom: var(--spacing-xs);
}

.detail-label {
  font-size: 24rpx;
  color: var(--text-tertiary);
  margin-right: 8rpx;
  white-space: nowrap;
}

.detail-value {
  font-size: 26rpx;
  color: var(--text-secondary);
  font-weight: 500;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 158rpx;
}

.contract-footer {
  display: flex;
  border-top: 1rpx solid var(--border-color);
  background: linear-gradient(to bottom, #ffffff, #f9fafb);
}

.contract-action {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 0;
  color: var(--text-secondary);
  font-size: 26rpx;
  transition: all 0.2s ease;
}

.contract-action:active {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
}

.contract-action:not(:last-child) {
  border-right: 1rpx solid var(--border-color-light);
}

/* 浮动操作按钮 */
.fab {
  position: fixed;
  bottom: calc(128rpx + var(--spacing-xl)); /* 调整底部位置，避开TabBar */
  right: var(--spacing-xl);
  width: 110rpx; /* 减小尺寸 */
  height: 110rpx; /* 减小尺寸 */
  border-radius: 50%;
  background: linear-gradient(135deg, #0a6bff, #0057ff);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6),
    0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  z-index: 100;
  transition: all 0.3s ease;
  border: 4rpx solid rgba(255, 255, 255, 0.7);
  animation: pulse 2s infinite; /* 添加脉动动画 */
}

.fab:active {
  transform: scale(0.95);
  box-shadow: 0 5rpx 10rpx rgba(0, 87, 255, 0.5),
    0 3rpx 3rpx rgba(0, 87, 255, 0.3);
  animation: none; /* 点击时停止动画 */
}

/* 添加脉动动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6),
      0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 15rpx 25rpx rgba(0, 87, 255, 0.7),
      0 8rpx 10rpx rgba(0, 87, 255, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6),
      0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  }
}

/* 筛选面板样式 */
.modal-filter {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1001;
}

.modal-mask {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-dialog {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideUp 0.3s ease;
  box-shadow: 0 -8rpx 24rpx rgba(0, 0, 0, 0.12);
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.modal-header {
  padding: 24rpx;
  border-bottom: 1rpx solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: var(--text-secondary);
}

.modal-content {
  padding: 24rpx;
  max-height: 60vh;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
}

.modal-footer {
  padding: 24rpx;
  display: flex;
  border-top: 1rpx solid var(--border-color-light);
  background-color: #f9fafb;
}

.filter-group {
  margin-bottom: 32rpx;
  width: 100%;
  box-sizing: border-box;
}

.filter-label {
  display: block;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-secondary);
}

.checkbox-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.status-option {
  padding: 12rpx 20rpx;
  border-radius: 100rpx;
  font-size: 26rpx;
  background-color: #f5f7fa;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  border: 1rpx solid transparent;
  box-sizing: border-box;
}

.status-option.active {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  font-weight: 500;
  border: 1rpx solid var(--primary-color);
}

.status-option:active {
  transform: scale(0.95);
}

.date-range {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.date-picker {
  flex: 1;
  padding: 20rpx 24rpx;
  background-color: #f5f7fa;
  border-radius: 12rpx;
  position: relative;
  border: 1rpx solid #e0e5ed;
  height: 80rpx;
  box-sizing: border-box;
}

.date-value {
  font-size: 28rpx;
  color: var(--text-primary);
  padding-right: 40rpx;
}

.date-icon {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

.date-separator {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.btn {
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  flex: 1;
  text-align: center;
}

.btn-reset {
  background-color: #f5f7fa;
  color: var(--text-secondary);
  margin-right: 16rpx;
  border: 1rpx solid #e0e5ed;
}

.btn-reset:active {
  background-color: #e5e7eb;
}

.btn-confirm {
  background-color: var(--primary-color);
  color: #ffffff;
  font-weight: 500;
}

.btn-confirm:active {
  background-color: var(--primary-color-dark);
}

/* 空状态样式 */
.empty-state {
  padding: var(--spacing-xl) var(--spacing-lg);
  text-align: center;
  background-color: #ffffff;
  border-radius: var(--radius-lg);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  margin: var(--spacing-lg) 0;
}

.empty-icon,
.loading-icon {
  margin-bottom: var(--spacing-md);
  color: var(--border-color);
  display: inline-block;
}

.loading-icon {
  animation: rotating 2s linear infinite;
  color: var(--primary-color);
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.empty-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: 1.5;
}

.btn-primary {
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: var(--primary-color);
  color: #ffffff;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* 列表底部加载更多样式 */
.loading-more {
  padding: 24rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  font-size: 24rpx;
}

.loading-more .loading-indicator {
  width: 24rpx;
  height: 24rpx;
  border: 2rpx solid #eee;
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: rotating 0.8s linear infinite;
  margin-right: 16rpx;
}

.no-more-data {
  text-align: center;
  padding: 24rpx 0;
  color: var(--text-tertiary);
  font-size: 24rpx;
}
</style>

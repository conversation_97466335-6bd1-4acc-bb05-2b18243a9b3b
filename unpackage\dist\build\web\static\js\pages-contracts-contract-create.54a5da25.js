(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-contracts-contract-create"],{"17c4":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.getDictionaryPageDetail=e.getDictionaryPage=void 0;var n=a("c475");e.getDictionaryPage=function(t){return(0,n.request)({url:"/api/DataDictionary/page",method:"POST",data:t})};e.getDictionaryPageDetail=function(t){return(0,n.request)({url:"/api/DataDictionary/pageDetail",method:"POST",data:t})}},2634:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
e.default=function(){return t};var t={},a=Object.prototype,i=a.hasOwnProperty,o=Object.defineProperty||function(t,e,a){t[e]=a.value},r="function"==typeof Symbol?Symbol:{},c=r.iterator||"@@iterator",s=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function u(t,e,a){return Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(T){u=function(t,e,a){return t[e]=a}}function d(t,e,a,n){var i=e&&e.prototype instanceof f?e:f,r=Object.create(i.prototype),c=new S(n||[]);return o(r,"_invoke",{value:_(t,a,c)}),r}function v(t,e,a){try{return{type:"normal",arg:t.call(e,a)}}catch(T){return{type:"throw",arg:T}}}t.wrap=d;var p={};function f(){}function m(){}function g(){}var h={};u(h,c,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(E([])));y&&y!==a&&i.call(y,c)&&(h=y);var x=g.prototype=f.prototype=Object.create(h);function w(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){var a;o(this,"_invoke",{value:function(o,r){function c(){return new e((function(a,c){(function a(o,r,c,s){var l=v(t[o],t,r);if("throw"!==l.type){var u=l.arg,d=u.value;return d&&"object"==(0,n.default)(d)&&i.call(d,"__await")?e.resolve(d.__await).then((function(t){a("next",t,c,s)}),(function(t){a("throw",t,c,s)})):e.resolve(d).then((function(t){u.value=t,c(u)}),(function(t){return a("throw",t,c,s)}))}s(l.arg)})(o,r,a,c)}))}return a=a?a.then(c,c):c()}})}function _(t,e,a){var n="suspendedStart";return function(i,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw o;return z()}for(a.method=i,a.arg=o;;){var r=a.delegate;if(r){var c=D(r,a);if(c){if(c===p)continue;return c}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if("suspendedStart"===n)throw n="completed",a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);n="executing";var s=v(t,e,a);if("normal"===s.type){if(n=a.done?"completed":"suspendedYield",s.arg===p)continue;return{value:s.arg,done:a.done}}"throw"===s.type&&(n="completed",a.method="throw",a.arg=s.arg)}}}function D(t,e){var a=e.method,n=t.iterator[a];if(void 0===n)return e.delegate=null,"throw"===a&&t.iterator["return"]&&(e.method="return",e.arg=void 0,D(t,e),"throw"===e.method)||"return"!==a&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+a+"' method")),p;var i=v(n,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,p;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,p):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function E(t){if(t){var e=t[c];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,n=function e(){for(;++a<t.length;)if(i.call(t,a))return e.value=t[a],e.done=!1,e;return e.value=void 0,e.done=!0,e};return n.next=n}}return{next:z}}function z(){return{value:void 0,done:!0}}return m.prototype=g,o(x,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:m,configurable:!0}),m.displayName=u(g,l,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===m||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,u(t,l,"GeneratorFunction")),t.prototype=Object.create(x),t},t.awrap=function(t){return{__await:t}},w(C.prototype),u(C.prototype,s,(function(){return this})),t.AsyncIterator=C,t.async=function(e,a,n,i,o){void 0===o&&(o=Promise);var r=new C(d(e,a,n,i),o);return t.isGeneratorFunction(a)?r:r.next().then((function(t){return t.done?t.value:r.next()}))},w(x),u(x,l,"Generator"),u(x,c,(function(){return this})),u(x,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),a=[];for(var n in e)a.push(n);return a.reverse(),function t(){for(;a.length;){var n=a.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=E,S.prototype={constructor:S,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(P),!t)for(var e in this)"t"===e.charAt(0)&&i.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function a(a,n){return r.type="throw",r.arg=t,e.next=a,n&&(e.method="next",e.arg=void 0),!!n}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],r=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=i.call(o,"catchLoc"),s=i.call(o,"finallyLoc");if(c&&s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var a=this.tryEntries.length-1;a>=0;--a){var n=this.tryEntries[a];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var r=o?o.completion:{};return r.type=t,r.arg=e,o?(this.method="next",this.next=o.finallyLoc,p):this.complete(r)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.finallyLoc===t)return this.complete(a.completion,a.afterLoc),P(a),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.tryLoc===t){var n=a.completion;if("throw"===n.type){var i=n.arg;P(a)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,a){return this.delegate={iterator:E(t),resultName:e,nextLoc:a},"next"===this.method&&(this.arg=void 0),p}},t},a("6a54"),a("01a2"),a("e39c"),a("bf0f"),a("844d"),a("18f7"),a("de6c"),a("3872e"),a("4e9b"),a("114e"),a("c240"),a("926e"),a("7a76"),a("c9b5"),a("aa9c"),a("2797"),a("8a8d"),a("dc69"),a("f7a5");var n=function(t){return t&&t.__esModule?t:{default:t}}(a("fcf3"))},"2fdc":function(t,e,a){"use strict";function n(t,e,a,n,i,o,r){try{var c=t[o](r),s=c.value}catch(l){return void a(l)}c.done?e(s):Promise.resolve(s).then(n,i)}a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return function(){var e=this,a=arguments;return new Promise((function(i,o){var r=t.apply(e,a);function c(t){n(r,i,o,c,s,"next",t)}function s(t){n(r,i,o,c,s,"throw",t)}c(void 0)}))}},a("bf0f")},"43b3":function(t,e,a){var n=a("862a");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("967d").default;i("4bb6869c",n,!0,{sourceMap:!1,shadowMode:!1})},"4ca5":function(t,e,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("2634")),o=n(a("2fdc")),r=n(a("9b1b"));a("bf0f"),a("2797"),a("e838"),a("01a2"),a("e39c"),a("8f71"),a("aa77"),a("aa9c"),a("dd2b"),a("fd3c");var c=n(a("8a0f")),s=n(a("c780")),l=a("d86f"),u={components:{SvgIcon:c.default},data:function(){return{currentStep:1,steps:["基本信息","产品信息","财务信息","款项/附件"],opportunities:[],showMoreBasicInfo:!1,showMoreFinancialInfo:!1,showDateInfo:!1,showPaymentInfo:!1,formData:{title:"",company:null,customer:null,contractType:null,agreement:null,opportunity:null,owner:null,signingDate:"",expirationDate:"",currency:{},currencyId:null,taxRate:null,taxedAmount:"",netAmount:"",paymentRatio:null,status:null,useProductTotal:!0,plannedCompletionDate:"",plannedAcceptanceDate:"",warrantyExpirationDate:"",businessName:"",businessId:"",items:[{productName:"",description:"",unit:"",quantity:"",unitPrice:"",taxRate:null,netUnitPrice:"",taxedUnitPrice:"",netAmount:"",taxedAmount:"",amount:"0.00",expanded:!1}],payments:[],attachments:[],remarks:""},companies:[],contractTypes:[],owners:[],currencies:[],currencyId:null,taxRates:[{id:1,name:"13%",value:.13},{id:2,name:"6%",value:.06},{id:3,name:"3%",value:.03},{id:4,name:"0%",value:0}],productTaxRates:[{id:1,name:"13%",value:.13},{id:2,name:"6%",value:.06},{id:3,name:"3%",value:.03},{id:4,name:"0%",value:0}],contractStatus:[{id:1,name:"草稿"},{id:2,name:"审批中"},{id:3,name:"已生效"},{id:4,name:"已完成"},{id:5,name:"已取消"}],paymentRatios:[{id:1,name:"50%-50%",ratios:[50,50]},{id:2,name:"30%-70%",ratios:[30,70]},{id:3,name:"40%-30%-30%",ratios:[40,30,30]},{id:4,name:"30%-30%-30%-10%",ratios:[30,30,30,10]}]}},computed:{totalAmount:function(){var t=0;return this.formData.items.forEach((function(e){t+=parseFloat(e.amount||0)})),t.toFixed(2)}},watch:{totalAmount:{handler:function(t){this.formData.useProductTotal&&t&&(this.formData.taxedAmount=t,this.calculateNetAmount())},immediate:!0}},onLoad:function(t){this.loadDraft(),this.loadDictionaryOptions(),this.loadBusinessData(),t.quotationId&&this.loadQuotationData(t.quotationId),this.formData.status=this.contractStatus[0]},onShow:function(){var t=uni.getStorageSync("selected_customer");t&&(this.formData.customer=t,uni.removeStorageSync("selected_customer"));var e=uni.getStorageSync("selected_product"),a=uni.getStorageSync("current_product_index");e&&""!==a&&void 0!==a&&(this.$set(this.formData.items,a,(0,r.default)((0,r.default)({},this.formData.items[a]),{},{productId:e.id,productName:e.name,unit:e.unit,unitPrice:e.price,taxRate:e.taxRate||this.productTaxRates[0],description:e.description||"",quantity:this.formData.items[a].quantity||"1",expanded:!1})),this.calculateItemAmount(a),uni.removeStorageSync("selected_product"),uni.removeStorageSync("current_product_index"))},methods:{goToStep:function(t){t<=this.currentStep&&(this.currentStep=t)},loadDictionaryOptions:function(){var t=this;return(0,o.default)((0,i.default)().mark((function e(){return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,s.default)("ContractType");case 3:return t.contractTypes=e.sent,e.next=6,(0,s.default)("CapitalType");case 6:return t.currencies=e.sent,e.next=9,(0,l.getCompanyList)();case 9:t.companies=e.sent,(0,l.getUserList)({pageIndex:1,pageSize:9999}).then((function(e){var a;e.items&&e.items.length>0&&(t.owners=null===e||void 0===e||null===(a=e.items)||void 0===a?void 0:a.filter((function(t){return t.isActive})))})),e.next=16;break;case 13:e.prev=13,e.t0=e["catch"](0),t.$message.error("加载字典数据失败");case 16:case"end":return e.stop()}}),e,null,[[0,13]])})))()},nextStep:function(){this.validateCurrentStep()&&(this.currentStep+=1)},prevStep:function(){this.currentStep-=1},toggleMoreBasicInfo:function(){this.showMoreBasicInfo=!this.showMoreBasicInfo},toggleMoreFinancialInfo:function(){this.showMoreFinancialInfo=!this.showMoreFinancialInfo},toggleDateInfo:function(){this.showDateInfo=!this.showDateInfo},togglePaymentInfo:function(){this.showPaymentInfo=!this.showPaymentInfo},goBack:function(){uni.navigateBack()},selectCustomer:function(){var t=this;uni.navigateTo({url:"/pages/sales/customer-select",events:{updateSelectedCustomer:this.updateSelectedCustomer,updateCurrency:function(e,a){t.formData.currency=e,t.formData.currencyId=a}}})},selectAgreement:function(){var t=this;uni.navigateTo({url:"/pages/contracts/agreement-select",events:{agreementSelected:function(e){if(t.formData.agreement=e,t.formData.businessName=e.businessName,t.formData.businessId=e.businessId,e.businessName&&e.businessId){var a=t.opportunities.find((function(t){return t.id===e.businessId}));console.log("business",a)}}}})},selectOpportunity:function(){var t=this;uni.navigateTo({url:"/pages/sales/opportunity-select",events:{opportunitySelected:function(e){t.formData.businessName=e.name,t.formData.businessId=e.id}}})},selectQuotation:function(){var t=this;uni.navigateTo({url:"/pages/sales/quotation-select",events:{quotationSelected:function(e){t.formData.quotation=e,t.loadQuotationData(e.id)}}})},onSigningDateChange:function(t){this.formData.signingDate=t.detail.value},onExpirationDateChange:function(t){this.formData.expirationDate=t.detail.value},onPlannedCompletionDateChange:function(t){this.formData.plannedCompletionDate=t.detail.value},onPlannedAcceptanceDateChange:function(t){this.formData.plannedAcceptanceDate=t.detail.value},onWarrantyExpirationDateChange:function(t){this.formData.warrantyExpirationDate=t.detail.value},onContractTypeChange:function(t){var e=t.detail.value;this.formData.contractType=this.contractTypes[e].displayText},onCompanyChange:function(t){var e=t.detail.value;this.formData.company=this.companies[e].displayName},onOwnerChange:function(t){var e=t.detail.value;this.formData.owner=this.owners[e].name},onCurrencyChange:function(t){var e=t.detail.value;console.log("onCurrencyChange",this.currencies[e]),this.formData.currency=this.currencies[e].displayText},onTaxRateChange:function(t){var e=t.detail.value;this.formData.taxRate=this.taxRates[e],this.calculateNetAmount()},onStatusChange:function(t){var e=t.detail.value;this.formData.status=this.contractStatus[e]},calculateNetAmount:function(){if(this.formData.taxedAmount){var t=parseFloat(this.formData.taxedAmount||0),e=this.formData.taxRate?this.formData.taxRate.value:0;this.formData.netAmount=(t/(1+e)).toFixed(2)}else this.formData.netAmount=""},toggleProductDetails:function(t){this.$set(this.formData.items[t],"expanded",!this.formData.items[t].expanded)},onProductTaxRateChange:function(t,e){var a=t.detail.value;this.$set(this.formData.items[e],"taxRate",this.productTaxRates[a]),this.calculateItemAmounts(e)},selectProduct:function(t){var e=this;uni.setStorageSync("current_product_index",t),uni.navigateTo({url:"/pages/products/product-select",events:{selectProduct:function(a){console.log("fdfee",a),e.formData.items[t].productName=a.name,e.formData.items[t].productId=a.id}}})},calculateFromNetPrice:function(t){var e=this.formData.items[t],a=e.taxRate?e.taxRate.value:0;e.netUnitPrice&&(e.taxedUnitPrice=(parseFloat(e.netUnitPrice)*(1+a)).toFixed(2),e.unitPrice=e.taxedUnitPrice),this.calculateItemAmounts(t)},calculateFromTaxedPrice:function(t){var e=this.formData.items[t],a=e.taxRate?e.taxRate.value:0;e.taxedUnitPrice&&(e.netUnitPrice=(parseFloat(e.taxedUnitPrice)/(1+a)).toFixed(2),e.unitPrice=e.taxedUnitPrice),this.calculateItemAmounts(t)},calculateItemAmount:function(t){var e=this.formData.items[t],a=parseFloat(e.unitPrice||0),n=parseFloat(e.quantity||0);e.taxedUnitPrice=a.toFixed(2);var i=e.taxRate?e.taxRate.value:0;e.netUnitPrice=(a/(1+i)).toFixed(2),e.amount=(a*n).toFixed(2),e.taxedAmount=e.amount,e.netAmount=(parseFloat(e.amount)/(1+i)).toFixed(2)},calculateItemAmounts:function(t){var e=this.formData.items[t],a=parseFloat(e.quantity||0),n=parseFloat(e.netUnitPrice||0),i=e.taxRate?e.taxRate.value:0;e.netAmount=(n*a).toFixed(2),e.taxedAmount=(parseFloat(e.netAmount)*(1+i)).toFixed(2),e.amount=e.taxedAmount},addItem:function(){this.formData.items.push({productName:"",description:"",unit:"",quantity:"",unitPrice:"",taxRate:null,netUnitPrice:"",taxedUnitPrice:"",netAmount:"",taxedAmount:"",amount:"0.00",expanded:!1})},deleteItem:function(t){this.formData.items.length>1?this.formData.items.splice(t,1):uni.showToast({title:"至少保留一个产品项",icon:"none"})},togglePaymentDetails:function(t){this.$set(this.formData.payments[t],"expanded",!this.formData.payments[t].expanded)},onPaymentDateChange:function(t,e){this.$set(this.formData.payments[e],"plannedDate",t.detail.value)},addPayment:function(){this.formData.payments.push({stage:"",amount:"",plannedDate:"",details:"",invoiceAmount:"",actualAmount:"",remarks:"",expanded:!1})},deletePayment:function(t){this.formData.payments.length>1?this.formData.payments.splice(t,1):uni.showToast({title:"至少保留一个收款计划",icon:"none"})},uploadContractFile:function(){var t=this;uni.chooseFile({count:1,success:function(e){var a=e.tempFiles[0];t.formData.attachments.push({name:a.name,path:a.path,size:a.size})}})},deleteFile:function(t){this.formData.attachments.splice(t,1)},updateSelectedCustomer:function(t){t&&t.id&&(this.formData.customer=t)},loadQuotationData:function(t){var e=this;setTimeout((function(){var a={id:t,title:"示例报价单",items:[{productName:"产品A",unit:"台",description:"高性能服务器",unitPrice:"10000.00",quantity:"2",taxRate:{id:1,name:"13%",value:.13},amount:"20000.00"},{productName:"服务B",unit:"项",description:"年度维保服务",unitPrice:"5000.00",quantity:"1",taxRate:{id:2,name:"6%",value:.06},amount:"5000.00"}]};e.formData.quotation={id:a.id,title:a.title},e.formData.items=a.items.map((function(t){var e=t.taxRate?t.taxRate.value:0,a=parseFloat(t.unitPrice),n=a/(1+e),i=parseFloat(t.amount),o=i/(1+e);return{productName:t.productName,description:t.description,unit:t.unit,quantity:t.quantity,unitPrice:t.unitPrice,taxRate:t.taxRate,netUnitPrice:n.toFixed(2),taxedUnitPrice:a.toFixed(2),netAmount:o.toFixed(2),taxedAmount:i.toFixed(2),amount:t.amount,expanded:!1}}))}),500)},validateCurrentStep:function(){switch(this.currentStep){case 1:return this.formData.title?this.formData.customer?this.formData.contractType?!!this.formData.signingDate||(uni.showToast({title:"请选择签约日期",icon:"none"}),!1):(uni.showToast({title:"请选择合同类型",icon:"none"}),!1):(uni.showToast({title:"请选择客户",icon:"none"}),!1):(uni.showToast({title:"请输入合同名称",icon:"none"}),!1);case 2:return!0;case 3:return!!this.formData.taxedAmount||(uni.showToast({title:"请输入合同金额",icon:"none"}),!1);default:return!0}},validateForm:function(){var t=this.validateCurrentStep(1);if(!t)return this.currentStep=1,!1;var e=this.validateCurrentStep(2);if(!e)return this.currentStep=2,!1;var a=this.validateCurrentStep(3);return!!a||(this.currentStep=3,!1)},saveDraft:function(){try{uni.setStorageSync("contract_draft",{formData:this.formData,currentStep:this.currentStep,savedAt:(new Date).toISOString()}),uni.showToast({title:"草稿保存成功",icon:"success"}),setTimeout((function(){uni.navigateBack()}),1500)}catch(t){uni.showToast({title:"保存失败",icon:"none"})}},cancel:function(){uni.showModal({title:"提示",content:"确定要取消编辑吗？未保存的内容将丢失",success:function(t){t.confirm&&uni.navigateBack()}})},submit:function(){this.validateForm()&&(uni.showLoading({title:"提交中..."}),setTimeout((function(){uni.hideLoading(),uni.showToast({title:"提交成功",icon:"success"}),setTimeout((function(){uni.navigateBack()}),1500)}),1e3))},toggleUseProductTotal:function(){this.formData.useProductTotal=!this.formData.useProductTotal,this.formData.useProductTotal&&(this.formData.taxedAmount=this.totalAmount,this.calculateNetAmount())},onPaymentRatioChange:function(t){var e=t.detail.value,a=this.paymentRatios[e];this.formData.paymentRatio=a,this.generatePaymentPlans(a.ratios)},generatePaymentPlans:function(t){var e=parseFloat(this.formData.taxedAmount||0);this.formData.payments=t.map((function(t,a){var n=(e*(t/100)).toFixed(2);return{stage:"第".concat(a+1,"期"),amount:n,plannedDate:"",details:"付款比例".concat(t,"%"),invoiceAmount:"",actualAmount:"",remarks:"",expanded:!1}}))},loadDraft:function(){var t=this;try{var e=uni.getStorageSync("contract_draft");e&&uni.showModal({title:"提示",content:"检测到未完成的合同草稿，是否继续编辑？",success:function(a){a.confirm?(t.formData=e.formData,t.currentStep=e.currentStep,uni.removeStorageSync("contract_draft"),uni.showToast({title:"已恢复草稿",icon:"success"})):uni.removeStorageSync("contract_draft")}})}catch(a){console.error("加载草稿失败:",a)}},loadBusinessData:function(){var t=this;return(0,o.default)((0,i.default)().mark((function e(){var a,n;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,s.default)("BusinessProcess");case 2:return a=e.sent,n=a.find((function(t){return"Win"===t.code})),e.next=6,(0,l.getOpportunityList)({pageIndex:1,pageSize:10,filter:{hasContract:!1,businessProcessId:n.id}}).then((function(e){t.opportunities=e}));case 6:case"end":return e.stop()}}),e)})))()}}};e.default=u},"53b9":function(t,e,a){"use strict";a.r(e);var n=a("7ff9"),i=a("f527");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("8ba9");var r=a("828b"),c=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"0809b710",null,!1,n["a"],void 0);e["default"]=c.exports},"7ff9":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n}));var n={svgIcon:a("8a0f").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"contract-create-page"},[a("v-uni-view",{staticClass:"header"},[a("v-uni-view",{staticClass:"left",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"back",type:"svg",size:"32",color:"#FFFFFF"}})],1),a("v-uni-view",{staticClass:"title"},[t._v("新建合同")]),a("v-uni-view",{staticClass:"right"})],1),a("v-uni-view",{staticClass:"steps-indicator"},t._l(t.steps,(function(e,n){return a("v-uni-view",{key:n,staticClass:"step",class:{active:t.currentStep===n+1,completed:t.currentStep>n+1},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goToStep(n+1)}}},[a("v-uni-view",{staticClass:"step-number"},[t._v(t._s(n+1))]),a("v-uni-view",{staticClass:"step-name"},[t._v(t._s(e))])],1)})),1),a("v-uni-scroll-view",{staticClass:"content-container",attrs:{"scroll-y":!0}},[1===t.currentStep?a("v-uni-view",{staticClass:"step-container"},[a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-title"},[a("svg-icon",{attrs:{name:"info",type:"svg",size:"32",color:"#3a86ff"}}),a("v-uni-text",[t._v("基本信息")])],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label required"},[t._v("合同名称")]),a("v-uni-input",{attrs:{type:"text",placeholder:"请输入合同名称"},model:{value:t.formData.title,callback:function(e){t.$set(t.formData,"title",e)},expression:"formData.title"}})],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label required"},[t._v("客户名称")]),a("v-uni-view",{staticClass:"selector",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectCustomer.apply(void 0,arguments)}}},[t.formData.customer?a("v-uni-text",[t._v(t._s(t.formData.customer.name))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择客户")]),a("svg-icon",{attrs:{name:"right",type:"svg",size:"28",color:"#999999"}})],1)],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label required"},[t._v("合同类型")]),a("v-uni-picker",{attrs:{mode:"selector",range:t.contractTypes,"range-key":"displayText"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onContractTypeChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[t.formData.contractType?a("v-uni-text",[t._v(t._s(t.formData.contractType))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择合同类型")]),a("svg-icon",{attrs:{name:"right",type:"svg",size:"28",color:"#999999"}})],1)],1)],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label required"},[t._v("签约日期")]),a("v-uni-picker",{attrs:{mode:"date",value:t.formData.signingDate},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onSigningDateChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[t.formData.signingDate?a("v-uni-text",[t._v(t._s(t.formData.signingDate))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择签约日期")]),a("svg-icon",{attrs:{name:"calendar",type:"svg",size:"28",color:"#999999"}})],1)],1)],1)],1)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"collapsible-section"},[a("v-uni-view",{staticClass:"section-header",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toggleMoreBasicInfo.apply(void 0,arguments)}}},[a("v-uni-text",[t._v("更多信息")]),a("svg-icon",{attrs:{name:t.showMoreBasicInfo?"up":"down",type:"svg",size:"28",color:"#999999"}})],1),t.showMoreBasicInfo?a("v-uni-view",{staticClass:"section-content"},[a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label"},[t._v("公司")]),a("v-uni-picker",{attrs:{mode:"selector",range:t.companies,"range-key":"displayName"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onCompanyChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[t.formData.company?a("v-uni-text",[t._v(t._s(t.formData.company))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择公司")]),a("svg-icon",{attrs:{name:"right",type:"svg",size:"28",color:"#999999"}})],1)],1)],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label"},[t._v("负责人")]),a("v-uni-picker",{attrs:{mode:"selector",range:t.owners,"range-key":"name"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onOwnerChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[t.formData.owner?a("v-uni-text",[t._v(t._s(t.formData.owner))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择负责人")]),a("svg-icon",{attrs:{name:"right",type:"svg",size:"28",color:"#999999"}})],1)],1)],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label"},[t._v("关联协议")]),a("v-uni-view",{staticClass:"selector",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectAgreement.apply(void 0,arguments)}}},[t.formData.agreement?a("v-uni-text",[t._v(t._s(t.formData.agreement.name))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择关联协议")]),a("svg-icon",{attrs:{name:"right",type:"svg",size:"28",color:"#999999"}})],1)],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label"},[t._v("关联商机")]),a("v-uni-view",{staticClass:"selector",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectOpportunity.apply(void 0,arguments)}}},[t.formData.businessName?a("v-uni-text",[t._v(t._s(t.formData.businessName))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择关联商机")]),a("svg-icon",{attrs:{name:"right",type:"svg",size:"28",color:"#999999"}})],1)],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label"},[t._v("到期日期")]),a("v-uni-picker",{attrs:{mode:"date",value:t.formData.expirationDate},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onExpirationDateChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[t.formData.expirationDate?a("v-uni-text",[t._v(t._s(t.formData.expirationDate))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择到期日期")]),a("svg-icon",{attrs:{name:"calendar",type:"svg",size:"28",color:"#999999"}})],1)],1)],1)],1):t._e()],1)],1),a("v-uni-view",{staticClass:"step-buttons"},[a("v-uni-button",{staticClass:"btn btn-primary",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.nextStep.apply(void 0,arguments)}}},[t._v("下一步")]),a("v-uni-button",{staticClass:"btn btn-outline",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveDraft.apply(void 0,arguments)}}},[t._v("保存草稿")]),a("v-uni-button",{staticClass:"btn btn-outline cancel",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.cancel.apply(void 0,arguments)}}},[t._v("取消")])],1)],1):t._e(),2===t.currentStep?a("v-uni-view",{staticClass:"step-container"},[a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-title"},[a("svg-icon",{attrs:{name:"product",type:"svg",size:"32",color:"#3a86ff"}}),a("v-uni-text",[t._v("产品信息")])],1),a("v-uni-view",{staticClass:"product-card"},[a("v-uni-view",{staticClass:"product-items"},t._l(t.formData.items,(function(e,n){return a("v-uni-view",{key:n,staticClass:"product-item"},[a("v-uni-view",{staticClass:"product-header"},[a("v-uni-text",{staticClass:"product-title"},[t._v("产品 "+t._s(n+1))]),a("v-uni-view",{staticClass:"product-actions"},[a("v-uni-view",{staticClass:"expand-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toggleProductDetails(n)}}},[a("svg-icon",{attrs:{name:e.expanded?"up":"down",type:"svg",size:"24",color:"#999999"}})],1),a("v-uni-view",{staticClass:"delete-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.deleteItem(n)}}},[a("svg-icon",{attrs:{name:"delete",type:"svg",size:"24",color:"#ff4d4f"}})],1)],1)],1),a("v-uni-view",{staticClass:"product-content"},[a("v-uni-view",{staticClass:"product-row"},[a("v-uni-text",{staticClass:"label"},[t._v("产品名称")]),a("v-uni-view",{staticClass:"selector",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectProduct(n)}}},[e.productName?a("v-uni-text",[t._v(t._s(e.productName))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择产品")]),a("svg-icon",{attrs:{name:"right",type:"svg",size:"28",color:"#999999"}})],1)],1),a("v-uni-view",{staticClass:"product-row"},[a("v-uni-text",{staticClass:"label"},[t._v("数量")]),a("v-uni-input",{attrs:{type:"number",placeholder:"请输入数量"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.calculateItemAmount(n)}},model:{value:e.quantity,callback:function(a){t.$set(e,"quantity",a)},expression:"item.quantity"}})],1),a("v-uni-view",{staticClass:"product-row"},[a("v-uni-text",{staticClass:"label"},[t._v("单价")]),a("v-uni-input",{attrs:{type:"number",placeholder:"请输入单价"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.calculateItemAmount(n)}},model:{value:e.unitPrice,callback:function(a){t.$set(e,"unitPrice",a)},expression:"item.unitPrice"}})],1),a("v-uni-view",{staticClass:"product-row"},[a("v-uni-text",{staticClass:"label"},[t._v("金额")]),a("v-uni-text",{staticClass:"amount"},[t._v("¥"+t._s(e.amount||"0.00"))])],1),e.expanded?a("v-uni-view",{staticClass:"product-details"},[a("v-uni-view",{staticClass:"product-row"},[a("v-uni-text",{staticClass:"label"},[t._v("产品描述")]),a("v-uni-text",{staticClass:"description"},[t._v(t._s(e.description||"暂无描述"))])],1),a("v-uni-view",{staticClass:"product-row"},[a("v-uni-text",{staticClass:"label"},[t._v("产品单位")]),a("v-uni-text",[t._v(t._s(e.unit||"个"))])],1),a("v-uni-view",{staticClass:"product-row"},[a("v-uni-text",{staticClass:"label"},[t._v("税率")]),a("v-uni-text",[t._v(t._s(e.taxRate?e.taxRate.name:"暂无"))])],1),a("v-uni-view",{staticClass:"product-row"},[a("v-uni-text",{staticClass:"label"},[t._v("未税单价")]),a("v-uni-text",[t._v("¥"+t._s(e.netUnitPrice||"0.00"))])],1),a("v-uni-view",{staticClass:"product-row"},[a("v-uni-text",{staticClass:"label"},[t._v("含税单价")]),a("v-uni-text",[t._v("¥"+t._s(e.taxedUnitPrice||"0.00"))])],1),a("v-uni-view",{staticClass:"product-row"},[a("v-uni-text",{staticClass:"label"},[t._v("未税金额")]),a("v-uni-text",[t._v("¥"+t._s(e.netAmount||"0.00"))])],1),a("v-uni-view",{staticClass:"product-row"},[a("v-uni-text",{staticClass:"label"},[t._v("含税金额")]),a("v-uni-text",[t._v("¥"+t._s(e.taxedAmount||"0.00"))])],1)],1):t._e()],1)],1)})),1),a("v-uni-view",{staticClass:"add-product",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.addItem.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"add",type:"svg",size:"28",color:"#3a86ff"}}),a("v-uni-text",[t._v("添加产品")])],1),a("v-uni-view",{staticClass:"total-amount"},[a("v-uni-text",[t._v("合计金额: ¥"+t._s(t.totalAmount))])],1)],1)],1),a("v-uni-view",{staticClass:"step-buttons"},[a("v-uni-button",{staticClass:"btn btn-secondary",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.prevStep.apply(void 0,arguments)}}},[t._v("上一步")]),a("v-uni-button",{staticClass:"btn btn-primary",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.nextStep.apply(void 0,arguments)}}},[t._v("下一步")]),a("v-uni-button",{staticClass:"btn btn-outline",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveDraft.apply(void 0,arguments)}}},[t._v("保存草稿")]),a("v-uni-button",{staticClass:"btn btn-outline",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.cancel.apply(void 0,arguments)}}},[t._v("取消")])],1)],1):t._e(),3===t.currentStep?a("v-uni-view",{staticClass:"step-container"},[a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-title"},[a("svg-icon",{attrs:{name:"money",type:"svg",size:"32",color:"#3a86ff"}}),a("v-uni-text",[t._v("财务信息")])],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label required"},[t._v("合同金额(含税)")]),a("v-uni-input",{attrs:{type:"digit",placeholder:"请输入合同金额",disabled:t.formData.useProductTotal},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.calculateNetAmount.apply(void 0,arguments)}},model:{value:t.formData.taxedAmount,callback:function(e){t.$set(t.formData,"taxedAmount",e)},expression:"formData.taxedAmount"}}),a("v-uni-view",{staticClass:"checkbox-container",staticStyle:{"margin-top":"10rpx",display:"flex","align-items":"center"}},[a("v-uni-checkbox",{staticStyle:{transform:"scale(0.8)"},attrs:{checked:t.formData.useProductTotal},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toggleUseProductTotal.apply(void 0,arguments)}}}),a("v-uni-text",{staticStyle:{"font-size":"24rpx","margin-left":"10rpx",color:"#666"}},[t._v("使用产品总价")])],1)],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label"},[t._v("合同税率")]),a("v-uni-picker",{attrs:{mode:"selector",range:t.taxRates,"range-key":"name"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onTaxRateChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[t.formData.taxRate?a("v-uni-text",[t._v(t._s(t.formData.taxRate.name))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择税率")]),a("svg-icon",{attrs:{name:"right",type:"svg",size:"28",color:"#999999"}})],1)],1)],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label"},[t._v("未税金额")]),a("v-uni-input",{attrs:{type:"digit",placeholder:"自动计算",disabled:!0},model:{value:t.formData.netAmount,callback:function(e){t.$set(t.formData,"netAmount",e)},expression:"formData.netAmount"}})],1)],1)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"collapsible-section"},[a("v-uni-view",{staticClass:"section-header",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toggleMoreFinancialInfo.apply(void 0,arguments)}}},[a("v-uni-text",[t._v("更多财务信息")]),a("svg-icon",{attrs:{name:t.showMoreFinancialInfo?"up":"down",type:"svg",size:"28",color:"#999999"}})],1),t.showMoreFinancialInfo?a("v-uni-view",{staticClass:"section-content"},[a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label"},[t._v("资金币种")]),a("v-uni-picker",{attrs:{mode:"selector",range:t.currencies,"range-key":"displayText"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onCurrencyChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[t.formData.currency?a("v-uni-text",[t._v(t._s(t.formData.currency))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择币种")]),a("svg-icon",{attrs:{name:"right",type:"svg",size:"28",color:"#999999"}})],1)],1)],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label"},[t._v("付款比例")]),a("v-uni-picker",{attrs:{mode:"selector",range:t.paymentRatios,"range-key":"name"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onPaymentRatioChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[t.formData.paymentRatio?a("v-uni-text",[t._v(t._s(t.formData.paymentRatio.name))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择付款比例")]),a("svg-icon",{attrs:{name:"right",type:"svg",size:"28",color:"#999999"}})],1)],1)],1)],1):t._e()],1)],1),t.formData.payments.length>0?a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-title"},[a("svg-icon",{attrs:{name:"money",type:"svg",size:"32",color:"#3a86ff"}}),a("v-uni-text",[t._v("应收款计划")])],1),a("v-uni-view",{staticClass:"payment-plans"},t._l(t.formData.payments,(function(e,n){return a("v-uni-view",{key:n,staticClass:"payment-plan-item"},[a("v-uni-view",{staticClass:"payment-plan-header"},[a("v-uni-text",{staticClass:"stage"},[t._v(t._s(e.stage))]),a("v-uni-text",{staticClass:"ratio"},[t._v(t._s(e.details))])],1),a("v-uni-view",{staticClass:"payment-plan-amount"},[a("v-uni-text",{staticClass:"amount-label"},[t._v("应收金额：")]),a("v-uni-text",{staticClass:"amount-value"},[t._v("¥"+t._s(e.amount))])],1)],1)})),1)],1):t._e(),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"collapsible-section"},[a("v-uni-view",{staticClass:"section-header",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toggleDateInfo.apply(void 0,arguments)}}},[a("v-uni-text",[t._v("日期信息")]),a("svg-icon",{attrs:{name:t.showDateInfo?"up":"down",type:"svg",size:"28",color:"#999999"}})],1),t.showDateInfo?a("v-uni-view",{staticClass:"section-content"},[a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label"},[t._v("计划完工日期")]),a("v-uni-picker",{attrs:{mode:"date",value:t.formData.plannedCompletionDate},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onPlannedCompletionDateChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[t.formData.plannedCompletionDate?a("v-uni-text",[t._v(t._s(t.formData.plannedCompletionDate))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择计划完工日期")]),a("svg-icon",{attrs:{name:"calendar",type:"svg",size:"28",color:"#999999"}})],1)],1)],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label"},[t._v("计划验收日期")]),a("v-uni-picker",{attrs:{mode:"date",value:t.formData.plannedAcceptanceDate},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onPlannedAcceptanceDateChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[t.formData.plannedAcceptanceDate?a("v-uni-text",[t._v(t._s(t.formData.plannedAcceptanceDate))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择计划验收日期")]),a("svg-icon",{attrs:{name:"calendar",type:"svg",size:"28",color:"#999999"}})],1)],1)],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label"},[t._v("维保限计划到期日期")]),a("v-uni-picker",{attrs:{mode:"date",value:t.formData.warrantyExpirationDate},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onWarrantyExpirationDateChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[t.formData.warrantyExpirationDate?a("v-uni-text",[t._v(t._s(t.formData.warrantyExpirationDate))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择维保到期日期")]),a("svg-icon",{attrs:{name:"calendar",type:"svg",size:"28",color:"#999999"}})],1)],1)],1)],1):t._e()],1)],1),a("v-uni-view",{staticClass:"step-buttons"},[a("v-uni-button",{staticClass:"btn btn-secondary",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.prevStep.apply(void 0,arguments)}}},[t._v("上一步")]),a("v-uni-button",{staticClass:"btn btn-primary",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.nextStep.apply(void 0,arguments)}}},[t._v("下一步")]),a("v-uni-button",{staticClass:"btn btn-outline",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveDraft.apply(void 0,arguments)}}},[t._v("保存草稿")]),a("v-uni-button",{staticClass:"btn btn-outline",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.cancel.apply(void 0,arguments)}}},[t._v("取消")])],1)],1):t._e(),4===t.currentStep?a("v-uni-view",{staticClass:"step-container"},[a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"collapsible-section"},[a("v-uni-view",{staticClass:"section-header",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.togglePaymentInfo.apply(void 0,arguments)}}},[a("v-uni-text",[t._v("回收款项")]),a("svg-icon",{attrs:{name:t.showPaymentInfo?"up":"down",type:"svg",size:"28",color:"#999999"}})],1),t.showPaymentInfo?a("v-uni-view",{staticClass:"section-content"},[a("v-uni-view",{staticClass:"payment-list"},[a("v-uni-view",{staticClass:"payment-header"},[a("v-uni-text",{staticClass:"col stage"},[t._v("收款阶段")]),a("v-uni-text",{staticClass:"col amount"},[t._v("应收金额")]),a("v-uni-text",{staticClass:"col date"},[t._v("计划日期")]),a("v-uni-text",{staticClass:"col action"},[t._v("操作")])],1),t._l(t.formData.payments,(function(e,n){return a("v-uni-view",{key:n,staticClass:"payment-item"},[a("v-uni-view",{staticClass:"payment-row"},[a("v-uni-input",{staticClass:"col stage",attrs:{type:"text",placeholder:"收款阶段"},model:{value:e.stage,callback:function(a){t.$set(e,"stage",a)},expression:"payment.stage"}}),a("v-uni-input",{staticClass:"col amount",attrs:{type:"digit",placeholder:"金额"},model:{value:e.amount,callback:function(a){t.$set(e,"amount",a)},expression:"payment.amount"}}),a("v-uni-picker",{staticClass:"col date",attrs:{mode:"date",value:e.plannedDate},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),function(e){return t.onPaymentDateChange(e,n)}.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[e.plannedDate?a("v-uni-text",[t._v(t._s(e.plannedDate))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("选择日期")])],1)],1),a("v-uni-view",{staticClass:"col action"},[a("v-uni-view",{staticClass:"expand-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.togglePaymentDetails(n)}}},[a("svg-icon",{attrs:{name:e.expanded?"up":"down",type:"svg",size:"24",color:"#999999"}})],1),a("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.deletePayment(n)}}},[a("svg-icon",{attrs:{name:"delete",type:"svg",size:"24",color:"#ff4d4f"}})],1)],1)],1),e.expanded?a("v-uni-view",{staticClass:"payment-details"},[a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label"},[t._v("计划收款明细")]),a("v-uni-textarea",{attrs:{placeholder:"请输入收款明细"},model:{value:e.details,callback:function(a){t.$set(e,"details",a)},expression:"payment.details"}})],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label"},[t._v("开票金额")]),a("v-uni-input",{attrs:{type:"digit",placeholder:"请输入开票金额"},model:{value:e.invoiceAmount,callback:function(a){t.$set(e,"invoiceAmount",a)},expression:"payment.invoiceAmount"}})],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label"},[t._v("实际收款金额")]),a("v-uni-input",{attrs:{type:"digit",placeholder:"请输入实际收款金额"},model:{value:e.actualAmount,callback:function(a){t.$set(e,"actualAmount",a)},expression:"payment.actualAmount"}})],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label"},[t._v("备注")]),a("v-uni-textarea",{attrs:{placeholder:"请输入备注"},model:{value:e.remarks,callback:function(a){t.$set(e,"remarks",a)},expression:"payment.remarks"}})],1)],1):t._e()],1)})),a("v-uni-view",{staticClass:"add-payment",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.addPayment.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"add",type:"svg",size:"28",color:"#3a86ff"}}),a("v-uni-text",[t._v("添加收款计划")])],1)],2)],1):t._e()],1)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-title"},[a("svg-icon",{attrs:{name:"file",type:"svg",size:"32",color:"#3a86ff"}}),a("v-uni-text",[t._v("附件与备注")])],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label"},[t._v("合同附件")]),a("v-uni-view",{staticClass:"file-uploader",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.uploadContractFile.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"upload",type:"svg",size:"32",color:"#999999"}}),a("v-uni-text",[t._v("点击上传附件")])],1),t.formData.attachments&&t.formData.attachments.length>0?a("v-uni-view",{staticClass:"file-list"},t._l(t.formData.attachments,(function(e,n){return a("v-uni-view",{key:n,staticClass:"file-item"},[a("v-uni-text",{staticClass:"file-name"},[t._v(t._s(e.name))]),a("v-uni-text",{staticClass:"file-delete",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.deleteFile(n)}}},[t._v("删除")])],1)})),1):t._e()],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label"},[t._v("备注")]),a("v-uni-textarea",{attrs:{placeholder:"请输入备注信息"},model:{value:t.formData.remarks,callback:function(e){t.$set(t.formData,"remarks",e)},expression:"formData.remarks"}})],1)],1)],1),4===t.currentStep?a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-title"},[a("svg-icon",{attrs:{name:"status",type:"svg",size:"32",color:"#3a86ff"}}),a("v-uni-text",[t._v("合同状态")])],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"label"},[t._v("状态")]),a("v-uni-picker",{attrs:{mode:"selector",range:t.contractStatus,"range-key":"name"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onStatusChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[t.formData.status?a("v-uni-text",[t._v(t._s(t.formData.status.name))]):a("v-uni-text",{staticClass:"placeholder"},[t._v("请选择状态")]),a("svg-icon",{attrs:{name:"right",type:"svg",size:"28",color:"#999999"}})],1)],1)],1)],1)],1):t._e(),4===t.currentStep?a("v-uni-view",{staticClass:"step-buttons"},[a("v-uni-button",{staticClass:"btn btn-secondary",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.prevStep.apply(void 0,arguments)}}},[t._v("上一步")]),a("v-uni-button",{staticClass:"btn btn-primary",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.submit.apply(void 0,arguments)}}},[t._v("审批")]),a("v-uni-button",{staticClass:"btn btn-outline",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveDraft.apply(void 0,arguments)}}},[t._v("存草稿")]),a("v-uni-button",{staticClass:"btn btn-outline",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.cancel.apply(void 0,arguments)}}},[t._v("取消")])],1):t._e()],1):t._e()],1)],1)},o=[]},"862a":function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";.contract-create-page[data-v-0809b710]{display:flex;flex-direction:column;height:100vh;background-color:#f5f5f5\n  /* 步骤导航样式 */\n  /* 表单部分通用样式 */\n  /* 产品列表样式 */\n  /* 收款列表样式 */\n  /* 文件上传区域样式 */\n  /* 底部按钮样式 */\n  /* 产品卡片样式 */\n  /* 应收款计划卡片样式 */}.contract-create-page .header[data-v-0809b710]{display:flex;justify-content:space-between;align-items:center;height:%?90?%;background-color:#3a86ff;color:#fff;padding:0 %?30?%;position:-webkit-sticky;position:sticky;top:0;z-index:100}.contract-create-page .header .left[data-v-0809b710]{display:flex;align-items:center}.contract-create-page .header .left uni-text[data-v-0809b710]{margin-left:%?10?%;font-size:%?30?%}.contract-create-page .header .title[data-v-0809b710]{font-size:%?34?%;font-weight:700}.contract-create-page .header .right[data-v-0809b710]{width:%?60?%}.contract-create-page .steps-indicator[data-v-0809b710]{display:flex;background-color:#fff;padding:%?20?% 0;border-bottom:%?1?% solid #eee;position:-webkit-sticky;position:sticky;top:%?90?%;z-index:99}.contract-create-page .steps-indicator .step[data-v-0809b710]{flex:1;display:flex;flex-direction:column;align-items:center;position:relative}.contract-create-page .steps-indicator .step[data-v-0809b710]:not(:last-child)::after{content:"";position:absolute;right:%?-10?%;top:%?30?%;width:%?20?%;height:%?2?%;background-color:#ddd}.contract-create-page .steps-indicator .step.active .step-number[data-v-0809b710]{background-color:#3a86ff;color:#fff}.contract-create-page .steps-indicator .step.active .step-name[data-v-0809b710]{color:#3a86ff;font-weight:500}.contract-create-page .steps-indicator .step.completed .step-number[data-v-0809b710]{background-color:#52c41a;color:#fff}.contract-create-page .steps-indicator .step-number[data-v-0809b710]{width:%?60?%;height:%?60?%;border-radius:50%;background-color:#f0f0f0;color:#999;display:flex;align-items:center;justify-content:center;font-size:%?26?%;margin-bottom:%?10?%}.contract-create-page .steps-indicator .step-name[data-v-0809b710]{font-size:%?24?%;color:#666}.contract-create-page .content-container[data-v-0809b710]{flex:1;padding-bottom:%?30?%;box-sizing:border-box}.contract-create-page .form-section[data-v-0809b710]{background-color:#fff;margin:%?20?% %?20?% 0;border-radius:%?12?%;overflow:hidden;box-shadow:0 %?2?% %?6?% rgba(0,0,0,.05)\n  /* 并排表单项 */\n  /* 可折叠区域样式 */}.contract-create-page .form-section .section-title[data-v-0809b710]{display:flex;align-items:center;padding:%?24?% %?30?%;border-bottom:%?1?% solid #eee}.contract-create-page .form-section .section-title uni-text[data-v-0809b710]{font-size:%?30?%;font-weight:700;color:#333;margin-left:%?12?%}.contract-create-page .form-section .form-group[data-v-0809b710]{padding:0 %?30?%}.contract-create-page .form-section .form-item[data-v-0809b710]{padding:%?24?% 0;border-bottom:%?1?% solid #eee}.contract-create-page .form-section .form-item[data-v-0809b710]:last-child{border-bottom:none}.contract-create-page .form-section .form-item .label[data-v-0809b710]{display:block;font-size:%?28?%;color:#666;margin-bottom:%?12?%}.contract-create-page .form-section .form-item .label.required[data-v-0809b710]:after{content:"*";color:#ff4d4f;margin-left:%?6?%}.contract-create-page .form-section .form-item uni-input[data-v-0809b710]{width:100%;height:%?88?%;font-size:%?28?%;color:#333;background-color:#f9f9f9;border-radius:%?8?%;padding:0 %?20?%;box-sizing:border-box;border:%?1?% solid #eee}.contract-create-page .form-section .form-item uni-textarea[data-v-0809b710]{width:100%;height:%?200?%;padding:%?20?%;border:%?1?% solid #eee;border-radius:%?8?%;font-size:%?28?%;background-color:#f9f9f9;box-sizing:border-box}.contract-create-page .form-section .form-item .selector[data-v-0809b710],\n.contract-create-page .form-section .form-item .picker-view[data-v-0809b710]{display:flex;justify-content:space-between;align-items:center;height:%?88?%;font-size:%?28?%;color:#333;background-color:#f9f9f9;border-radius:%?8?%;padding:0 %?20?%;box-sizing:border-box;border:%?1?% solid #eee}.contract-create-page .form-section .form-item .selector .placeholder[data-v-0809b710],\n.contract-create-page .form-section .form-item .picker-view .placeholder[data-v-0809b710]{color:#999}.contract-create-page .form-section .form-item-row[data-v-0809b710]{display:flex;justify-content:space-between;gap:%?20?%;padding:%?24?% 0;border-bottom:%?1?% solid #eee}.contract-create-page .form-section .form-item-row[data-v-0809b710]:last-child{border-bottom:none}.contract-create-page .form-section .form-item-row .form-item[data-v-0809b710]{flex:1;padding:0;border-bottom:none}.contract-create-page .form-section .form-item-row .half[data-v-0809b710]{flex:0 0 48%}.contract-create-page .form-section .collapsible-section .section-header[data-v-0809b710]{display:flex;justify-content:space-between;align-items:center;padding:%?24?% %?30?%;font-size:%?28?%;font-weight:500;color:#333;border-bottom:%?1?% solid #eee}.contract-create-page .form-section .collapsible-section .section-content[data-v-0809b710]{padding:0 %?30?%}.contract-create-page .products-list[data-v-0809b710]{padding:0 %?30?%}.contract-create-page .products-list .product-header[data-v-0809b710]{display:flex;padding:%?20?% 0;border-bottom:%?1?% solid #eee;font-size:%?26?%;color:#666;font-weight:500}.contract-create-page .products-list .product-row[data-v-0809b710]{display:flex;padding:%?15?% 0;border-bottom:%?1?% solid #eee}.contract-create-page .products-list .product-row uni-input[data-v-0809b710]{height:%?70?%;font-size:%?26?%;background-color:#f9f9f9;border-radius:%?6?%;padding:0 %?10?%;margin:%?5?%;box-sizing:border-box;border:%?1?% solid #eee}.contract-create-page .products-list .product-row .expand-button[data-v-0809b710]{margin-right:%?10?%}.contract-create-page .products-list .product-details[data-v-0809b710]{background-color:#f9f9f9;padding:%?20?%;border-radius:%?8?%;margin-bottom:%?20?%}.contract-create-page .products-list .col[data-v-0809b710]{display:flex;align-items:center;padding:0 %?5?%}.contract-create-page .products-list .name[data-v-0809b710]{flex:3}.contract-create-page .products-list .quantity[data-v-0809b710],\n.contract-create-page .products-list .price[data-v-0809b710],\n.contract-create-page .products-list .amount[data-v-0809b710]{flex:2}.contract-create-page .products-list .action[data-v-0809b710]{flex:1;display:flex;justify-content:center}.contract-create-page .products-list .add-product[data-v-0809b710]{display:flex;align-items:center;justify-content:center;padding:%?24?% 0;margin-top:%?20?%;color:#3a86ff;background-color:#f0f7ff;border-radius:%?8?%;border:%?1?% dashed #a0cfff}.contract-create-page .products-list .total-amount[data-v-0809b710]{display:flex;justify-content:flex-end;padding:%?30?% 0;font-size:%?32?%;font-weight:700;color:#ff6b18}.contract-create-page .payment-list .payment-header[data-v-0809b710]{display:flex;padding:%?20?% 0;border-bottom:%?1?% solid #eee;font-size:%?26?%;color:#666;font-weight:500}.contract-create-page .payment-list .payment-row[data-v-0809b710]{display:flex;padding:%?15?% 0;border-bottom:%?1?% solid #eee}.contract-create-page .payment-list .payment-row uni-input[data-v-0809b710],\n.contract-create-page .payment-list .payment-row .picker-view[data-v-0809b710]{height:%?70?%;font-size:%?26?%;background-color:#f9f9f9;border-radius:%?6?%;padding:0 %?10?%;margin:%?5?%;box-sizing:border-box;border:%?1?% solid #eee}.contract-create-page .payment-list .payment-row uni-input .placeholder[data-v-0809b710],\n.contract-create-page .payment-list .payment-row .picker-view .placeholder[data-v-0809b710]{color:#999;font-size:%?24?%}.contract-create-page .payment-list .payment-row .expand-button[data-v-0809b710]{margin-right:%?10?%}.contract-create-page .payment-list .payment-details[data-v-0809b710]{background-color:#f9f9f9;padding:%?20?%;border-radius:%?8?%;margin-bottom:%?20?%}.contract-create-page .payment-list .stage[data-v-0809b710]{flex:3}.contract-create-page .payment-list .amount[data-v-0809b710],\n.contract-create-page .payment-list .date[data-v-0809b710]{flex:2}.contract-create-page .payment-list .action[data-v-0809b710]{flex:1;display:flex;justify-content:center}.contract-create-page .payment-list .add-payment[data-v-0809b710]{display:flex;align-items:center;justify-content:center;padding:%?24?% 0;margin-top:%?20?%;color:#3a86ff;background-color:#f0f7ff;border-radius:%?8?%;border:%?1?% dashed #a0cfff}.contract-create-page .file-uploader[data-v-0809b710]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?40?% 0;background-color:#f9f9f9;border:%?1?% dashed #ddd;border-radius:%?8?%}.contract-create-page .file-uploader uni-text[data-v-0809b710]{margin-top:%?20?%;color:#666;font-size:%?28?%}.contract-create-page .file-list[data-v-0809b710]{margin-top:%?20?%}.contract-create-page .file-list .file-item[data-v-0809b710]{display:flex;justify-content:space-between;align-items:center;padding:%?20?%;background-color:#f0f7ff;border-radius:%?8?%;margin-bottom:%?10?%}.contract-create-page .file-list .file-item .file-name[data-v-0809b710]{color:#333;font-size:%?28?%}.contract-create-page .file-list .file-item .file-delete[data-v-0809b710]{color:#ff4d4f;font-size:%?26?%}.contract-create-page .step-buttons[data-v-0809b710]{display:grid;grid-template-columns:repeat(4,1fr);gap:%?20?%;padding:%?30?% %?20?%;margin-top:%?20?%;background-color:#fff;box-shadow:0 %?-2?% %?10?% rgba(0,0,0,.05)}.contract-create-page .step-buttons .btn[data-v-0809b710]{height:%?80?%;line-height:%?80?%;text-align:center;border-radius:%?8?%;font-size:%?28?%;padding:0;margin:0}.contract-create-page .step-buttons .btn.btn-primary[data-v-0809b710]{background-color:#3a86ff;color:#fff}.contract-create-page .step-buttons .btn.btn-secondary[data-v-0809b710]{background-color:#e6f0ff;color:#3a86ff}.contract-create-page .step-buttons .btn.btn-outline[data-v-0809b710]{background-color:#fff;color:#666;border:%?1?% solid #ddd}.contract-create-page .product-card[data-v-0809b710]{padding:0 %?20?%}.contract-create-page .product-card .product-items[data-v-0809b710]{margin-bottom:%?20?%}.contract-create-page .product-card .product-item[data-v-0809b710]{background-color:#fff;border-radius:%?12?%;overflow:hidden;margin-bottom:%?20?%;box-shadow:0 %?2?% %?8?% rgba(0,0,0,.05);border:%?1?% solid #eee}.contract-create-page .product-card .product-header[data-v-0809b710]{display:flex;justify-content:space-between;align-items:center;padding:%?20?%;background-color:#f9f9f9;border-bottom:%?1?% solid #eee}.contract-create-page .product-card .product-header .product-title[data-v-0809b710]{font-size:%?28?%;font-weight:500;color:#333}.contract-create-page .product-card .product-header .product-actions[data-v-0809b710]{display:flex}.contract-create-page .product-card .product-header .product-actions .expand-button[data-v-0809b710]{margin-right:%?20?%}.contract-create-page .product-card .product-content[data-v-0809b710]{padding:0 %?20?%}.contract-create-page .product-card .product-row[data-v-0809b710]{display:flex;justify-content:space-between;align-items:center;padding:%?20?% 0;border-bottom:%?1?% solid #eee}.contract-create-page .product-card .product-row[data-v-0809b710]:last-child{border-bottom:none}.contract-create-page .product-card .product-row .label[data-v-0809b710]{font-size:%?28?%;color:#666;width:%?180?%}.contract-create-page .product-card .product-row uni-input[data-v-0809b710]{flex:1;height:%?70?%;font-size:%?28?%;background-color:#f9f9f9;border-radius:%?6?%;padding:0 %?20?%;box-sizing:border-box;border:%?1?% solid #eee}.contract-create-page .product-card .product-row .amount[data-v-0809b710]{color:#ff6b18;font-weight:500}.contract-create-page .product-card .product-row .description[data-v-0809b710]{flex:1;color:#666;font-size:%?26?%;padding:0 %?10?%;text-align:right}.contract-create-page .product-card .product-row .selector[data-v-0809b710]{flex:1;display:flex;justify-content:space-between;align-items:center;height:%?70?%;font-size:%?28?%;color:#333;background-color:#f9f9f9;border-radius:%?6?%;padding:0 %?20?%;box-sizing:border-box;border:%?1?% solid #eee}.contract-create-page .product-card .product-row .selector .placeholder[data-v-0809b710]{color:#999}.contract-create-page .product-card .product-details[data-v-0809b710]{background-color:#f9f9f9;border-radius:%?8?%;margin:%?10?% 0 %?20?%;padding:%?10?%}.contract-create-page .product-card .add-product[data-v-0809b710]{display:flex;align-items:center;justify-content:center;padding:%?24?% 0;margin-top:%?20?%;color:#3a86ff;background-color:#f0f7ff;border-radius:%?8?%;border:%?1?% dashed #a0cfff}.contract-create-page .product-card .total-amount[data-v-0809b710]{display:flex;justify-content:flex-end;padding:%?30?% 0 %?10?%;font-size:%?32?%;font-weight:700;color:#ff6b18}.contract-create-page .payment-plans[data-v-0809b710]{padding:%?20?%}.contract-create-page .payment-plans .payment-plan-item[data-v-0809b710]{background-color:#f9f9f9;border-radius:%?12?%;padding:%?20?%;margin-bottom:%?20?%}.contract-create-page .payment-plans .payment-plan-item[data-v-0809b710]:last-child{margin-bottom:0}.contract-create-page .payment-plans .payment-plan-item .payment-plan-header[data-v-0809b710]{display:flex;justify-content:space-between;align-items:center;margin-bottom:%?10?%}.contract-create-page .payment-plans .payment-plan-item .payment-plan-header .stage[data-v-0809b710]{font-size:%?28?%;color:#333;font-weight:500}.contract-create-page .payment-plans .payment-plan-item .payment-plan-header .ratio[data-v-0809b710]{font-size:%?26?%;color:#666}.contract-create-page .payment-plans .payment-plan-item .payment-plan-amount[data-v-0809b710]{display:flex;align-items:baseline}.contract-create-page .payment-plans .payment-plan-item .payment-plan-amount .amount-label[data-v-0809b710]{font-size:%?26?%;color:#666}.contract-create-page .payment-plans .payment-plan-item .payment-plan-amount .amount-value[data-v-0809b710]{font-size:%?32?%;color:#ff6b18;font-weight:500;margin-left:%?10?%}',""]),t.exports=e},"8ba9":function(t,e,a){"use strict";var n=a("43b3"),i=a.n(n);i.a},c475:function(t,e,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.request=e.getTenantInfo=void 0;var i=n(a("9b1b"));a("bf0f"),a("4626"),a("5ac7");var o=null;e.getTenantInfo=function(t){return new Promise((function(e,a){uni.request({url:"/api/Tenants/getTenantsByLogin",method:"POST",data:t,header:{"Content-Type":"application/json"},success:function(t){200===t.statusCode?(o=t.data,e(t.data)):a(t.data)},fail:function(t){a(t)}})}))};e.request=function(t){return t.url.includes("/login")&&o&&(t.header=(0,i.default)((0,i.default)({},t.header),{},{__tenant:o[0].id})),new Promise((function(e,a){uni.request({url:t.url,method:t.method||"GET",data:t.data||{},header:(0,i.default)({Authorization:"Bearer "+uni.getStorageSync("token"),"Content-Type":"application/json"},t.header),success:function(t){200===t.statusCode?e(t.data):a(t.data)},fail:function(t){a(t)}})}))}},c780:function(t,e,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return c.apply(this,arguments)},a("8f71"),a("bf0f");var i=n(a("2634")),o=n(a("2fdc")),r=a("17c4");function c(){return c=(0,o.default)((0,i.default)().mark((function t(e){var a,n,o,c,s,l,u,d,v=arguments;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=v.length>1&&void 0!==v[1]?v[1]:{},n=a.pageIndex,o=void 0===n?1:n,c=a.pageSize,s=void 0===c?100:c,t.prev=2,t.next=5,(0,r.getDictionaryPage)({pageIndex:o,pageSize:s,filter:e});case 5:if(u=t.sent,null!==u&&void 0!==u&&null!==(l=u.items)&&void 0!==l&&l.length){t.next=8;break}return t.abrupt("return",[]);case 8:return t.next=10,(0,r.getDictionaryPageDetail)({pageIndex:o,pageSize:s,dataDictionaryId:u.items[0].id});case 10:return d=t.sent,t.abrupt("return",d.items.filter((function(t){return t.isEnabled})));case 14:throw t.prev=14,t.t0=t["catch"](2),console.error("Error fetching select options:",t.t0),t.t0;case 18:case"end":return t.stop()}}),t,null,[[2,14]])}))),c.apply(this,arguments)}},d86f:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.getUserList=e.getProductList=e.getOpportunityList=e.getContactList=e.getCompanyList=e.getAgreementList=void 0;var n=a("c475");e.getContactList=function(t){return(0,n.request)({url:"/api/crm/contract/getList",method:"POST",data:t})};e.getCompanyList=function(){return(0,n.request)({url:"/api/crm/contract/getAllCompanys",method:"GET"})};e.getUserList=function(t){return(0,n.request)({url:"/api/Users/<USER>",method:"POST",data:t})};e.getOpportunityList=function(t){return(0,n.request)({url:"/api/crm/business/getKanbanList",method:"POST",data:t})};e.getAgreementList=function(t){return(0,n.request)({url:"/api/crm/contract/getAgreementList",method:"POST",data:t})};e.getProductList=function(t){return(0,n.request)({url:"/api/crm/product/getList",method:"POST",data:t})}},f527:function(t,e,a){"use strict";a.r(e);var n=a("4ca5"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a}}]);
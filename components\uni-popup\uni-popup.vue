<template>
	<view @touchmove.stop.prevent="clear">
		<view class="uni-popup" :class="[popupstyle, animation === true ? 'ani' : '', position]" v-show="show">
			<view class="uni-popup__mask" @click="onTap" @touchmove.stop.prevent="clear" />
			<view class="uni-popup__wrapper" @click.stop="clear">
				<slot />
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'UniPopup',
	props: {
		// 开启动画
		animation: {
			type: Boolean,
			default: true
		},
		// 弹出层类型，可选值：top/bottom/center
		type: {
			type: String,
			default: 'center'
		},
		// 是否显示遮罩
		maskClick: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			duration: 300,
			ani: [],
			showPopup: false,
			showTrans: false,
			popupWidth: 0,
			popupHeight: 0,
			config: {
				top: 'top',
				bottom: 'bottom',
				center: 'center',
				left: 'left',
				right: 'right',
				message: 'top',
				dialog: 'center',
				share: 'bottom'
			}
		}
	},
	computed: {
		show() {
			return this.showPopup
		},
		position() {
			return 'uni-popup-' + this.config[this.type]
		},
		popupstyle() {
			return this.config[this.type] + '-popup'
		}
	},
	watch: {
		show(newValue) {
			if (newValue) {
				this.open()
			} else {
				this.close()
			}
		}
	},
	created() {},
	methods: {
		clear(e) {},
		open() {
			this.$emit('change', {
				show: true
			})
			this.showPopup = true
			this.$nextTick(() => {
				setTimeout(() => {
					this.showTrans = true
				}, 50)
			})
		},
		close(type) {
			this.showTrans = false
			this.$emit('change', {
				show: false
			})
			clearTimeout(this.timer)
			this.timer = setTimeout(() => {
				this.showPopup = false
			}, 300)
		},
		onTap() {
			if (!this.maskClick) return
			this.close()
		}
	}
}
</script>

<style>
.uni-popup {
	position: fixed;
	z-index: 99;
}

.uni-popup-mask {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: rgba(0, 0, 0, 0.4);
	opacity: 0;
}

.uni-popup-mask.ani {
	transition: all 0.3s;
}

.uni-popup-mask.uni-top-mask,
.uni-popup-mask.uni-bottom-mask,
.uni-popup-mask.uni-center-mask {
	opacity: 1;
}

.uni-popup-wrapper {
	position: absolute;
	box-sizing: border-box;
	background-color: #fff;
}

.uni-popup-wrapper.ani {
	transition: all 0.3s;
}

.uni-popup__mask {
	position: fixed;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: rgba(0, 0, 0, 0.4);
	z-index: 998;
}

.uni-popup__wrapper {
	position: fixed;
	z-index: 999;
	box-sizing: border-box;
}

.uni-popup-top {
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}

.uni-popup-top .uni-popup__wrapper {
	border-radius: 0 0 var(--radius-lg) var(--radius-lg);
	top: 0;
	left: 0;
	right: 0;
}

.uni-popup-bottom {
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}

.uni-popup-bottom .uni-popup__wrapper {
	border-radius: var(--radius-lg) var(--radius-lg) 0 0;
	bottom: 0;
	left: 0;
	right: 0;
}

.uni-popup-center {
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}

.uni-popup-center .uni-popup__wrapper {
	border-radius: var(--radius-lg);
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	min-width: 30%;
	min-height: 100rpx;
}
</style> 
<template>
  <view>
    <image 
      v-if="iconType === 'svg'" 
      :src="iconSrc" 
      :style="{ width: size + 'rpx', height: size + 'rpx', filter: color === '#FFFFFF' || color === '#ffffff' ? 'brightness(0) invert(1)' : 'none' }"
      :class="['svg-icon', customClass]"
      mode="aspectFit"
    ></image>
    <text 
      v-else-if="iconType === 'remix'" 
      :class="['remix-icon', 'ri-' + name, customClass]" 
      :style="{ fontSize: size + 'rpx', color: color }"
    ></text>
    <text 
      v-else-if="iconType === 'iconfont'" 
      :class="['iconfont', 'icon-' + name, customClass]" 
      :style="{ fontSize: size + 'rpx', color: color }"
    ></text>
  </view>
</template>

<script>
export default {
  name: 'SvgIcon',
  props: {
    name: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'svg',
      validator: function(value) {
        return ['svg', 'remix', 'iconfont'].indexOf(value) !== -1
      }
    },
    color: {
      type: String,
      default: ''
    },
    size: {
      type: [Number, String],
      default: 32
    },
    customClass: {
      type: String,
      default: ''
    }
  },
  computed: {
    iconType() {
      return this.type;
    },
    iconSrc() {
      if (this.type === 'svg') {
        return `/static/icons/${this.name}.svg`;
      }
      return '';
    }
  }
};
</script>

<style>
.svg-icon {
  display: inline-block;
}
</style> 
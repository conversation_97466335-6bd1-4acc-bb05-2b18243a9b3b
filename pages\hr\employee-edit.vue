<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="goBack">
        <text class="ri-arrow-left-line"></text>
      </view>
      <text class="page-title">编辑员工</text>
      <view class="header-actions">
        <button type="button" class="action-button submit-button" @click="submitForm">
          保存
        </button>
      </view>
    </view>
    
    <!-- 表单 -->
    <scroll-view scroll-y class="form-container">
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">基本信息</text>
          <text class="required-hint">* 为必填项</text>
        </view>
        
        <view class="form-group">
          <view class="avatar-upload">
            <view class="avatar-preview" @click="chooseAvatar">
              <image v-if="formData.avatar" :src="formData.avatar" mode="aspectFill"></image>
              <text v-else class="ri-user-line avatar-placeholder"></text>
            </view>
            <text class="upload-hint">点击更换头像</text>
          </view>
        </view>
        
        <view class="form-group">
          <text class="form-label required">姓名</text>
          <input 
            type="text" 
            class="form-input" 
            v-model="formData.name" 
            placeholder="请输入员工姓名"
            :class="{ 'error': errors.name }"
          />
          <text v-if="errors.name" class="error-message">{{errors.name}}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label required">工号</text>
          <input 
            type="text" 
            class="form-input" 
            v-model="formData.employeeId" 
            placeholder="请输入员工工号"
            :class="{ 'error': errors.employeeId }"
          />
          <text v-if="errors.employeeId" class="error-message">{{errors.employeeId}}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label required">手机号码</text>
          <input 
            type="number" 
            class="form-input" 
            v-model="formData.phone" 
            placeholder="请输入手机号码"
            :class="{ 'error': errors.phone }"
          />
          <text v-if="errors.phone" class="error-message">{{errors.phone}}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label">邮箱</text>
          <input 
            type="text" 
            class="form-input" 
            v-model="formData.email" 
            placeholder="请输入邮箱地址"
            :class="{ 'error': errors.email }"
          />
          <text v-if="errors.email" class="error-message">{{errors.email}}</text>
        </view>
      </view>
      
      <!-- 工作信息 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">工作信息</text>
        </view>
        
        <view class="form-group">
          <text class="form-label required">部门</text>
          <picker 
            mode="selector" 
            :range="departments" 
            range-key="name"
            @change="onDepartmentChange"
            class="form-picker"
          >
            <view class="picker-value">
              <text v-if="formData.department">{{formData.department}}</text>
              <text v-else class="placeholder">请选择部门</text>
              <text class="ri-arrow-down-s-line picker-icon"></text>
            </view>
          </picker>
          <text v-if="errors.department" class="error-message">{{errors.department}}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label required">职位</text>
          <input 
            type="text" 
            class="form-input" 
            v-model="formData.position" 
            placeholder="请输入职位名称"
            :class="{ 'error': errors.position }"
          />
          <text v-if="errors.position" class="error-message">{{errors.position}}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label required">员工状态</text>
          <view class="radio-group">
            <view class="radio-item" @click="formData.status = 'active'">
              <view class="radio-button" :class="{ active: formData.status === 'active' }"></view>
              <text class="radio-label">在职</text>
            </view>
            <view class="radio-item" @click="formData.status = 'inactive'">
              <view class="radio-button" :class="{ active: formData.status === 'inactive' }"></view>
              <text class="radio-label">离职</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 备注 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">备注</text>
        </view>
        
        <view class="form-group">
          <textarea 
            class="form-textarea" 
            v-model="formData.notes" 
            placeholder="添加员工相关备注信息"
          ></textarea>
        </view>
      </view>
      
      <!-- 操作记录 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">操作记录</text>
          <text class="section-hint">此次修改将创建新的操作记录</text>
        </view>
        
        <view class="history-hint">
          <text class="history-icon ri-history-line"></text>
          <text class="history-text">保存修改后，系统将自动记录本次变更</text>
        </view>
      </view>
      
      <!-- 提交按钮 -->
      <view class="form-actions">
        <button class="btn-cancel" @click="goBack">取消</button>
        <button class="btn-submit" @click="submitForm">保存</button>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      id: '',
      formData: {
        name: '',
        employeeId: '',
        phone: '',
        email: '',
        department: '',
        position: '',
        status: 'active',
        avatar: '',
        notes: ''
      },
      errors: {},
      departments: [
        { id: '1', name: '销售部' },
        { id: '2', name: '技术部' },
        { id: '3', name: '市场部' },
        { id: '4', name: '财务部' },
        { id: '5', name: '人事部' }
      ],
      originalData: null
    }
  },
  onLoad(options) {
    if (options.id) {
      this.id = options.id;
      // 获取员工数据
      this.fetchEmployeeData(this.id);
    }
  },
  methods: {
    fetchEmployeeData(id) {
      // 模拟从服务器获取员工数据
      // 实际应用中应该通过API请求获取
      
      const employee = {
        id: '1',
        name: '张三',
        employeeId: 'EMP001',
        phone: '13812345678',
        email: '<EMAIL>',
        department: '销售部',
        position: '销售经理',
        status: 'active',
        avatar: '/static/images/avatars/avatar1.png',
        notes: '优秀员工，负责华东区销售业务'
      };
      
      // 保存原始数据，用于比较变更
      this.originalData = JSON.parse(JSON.stringify(employee));
      
      // 设置表单数据
      this.formData = employee;
    },
    goBack() {
      uni.navigateBack();
    },
    chooseAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.formData.avatar = res.tempFilePaths[0];
        }
      });
    },
    onDepartmentChange(e) {
      const index = e.detail.value;
      this.formData.department = this.departments[index].name;
    },
    validateForm() {
      this.errors = {};
      let isValid = true;
      
      // 基本信息验证
      if (!this.formData.name) {
        this.errors.name = '请输入员工姓名';
        isValid = false;
      }
      
      if (!this.formData.employeeId) {
        this.errors.employeeId = '请输入员工工号';
        isValid = false;
      }
      
      if (!this.formData.phone) {
        this.errors.phone = '请输入手机号码';
        isValid = false;
      } else if (!/^1\d{10}$/.test(this.formData.phone)) {
        this.errors.phone = '请输入有效的手机号码';
        isValid = false;
      }
      
      if (this.formData.email && !/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(this.formData.email)) {
        this.errors.email = '请输入有效的邮箱地址';
        isValid = false;
      }
      
      // 工作信息验证
      if (!this.formData.department) {
        this.errors.department = '请选择部门';
        isValid = false;
      }
      
      if (!this.formData.position) {
        this.errors.position = '请输入职位名称';
        isValid = false;
      }
      
      return isValid;
    },
    getChangedFields() {
      const changes = [];
      
      if (this.originalData.name !== this.formData.name) {
        changes.push(`姓名从"${this.originalData.name}"修改为"${this.formData.name}"`);
      }
      
      if (this.originalData.position !== this.formData.position) {
        changes.push(`职位从"${this.originalData.position}"修改为"${this.formData.position}"`);
      }
      
      if (this.originalData.department !== this.formData.department) {
        changes.push(`部门从"${this.originalData.department}"修改为"${this.formData.department}"`);
      }
      
      if (this.originalData.status !== this.formData.status) {
        const statusText = {
          active: '在职',
          inactive: '离职'
        };
        changes.push(`状态从"${statusText[this.originalData.status]}"修改为"${statusText[this.formData.status]}"`);
      }
      
      if (this.originalData.phone !== this.formData.phone) {
        changes.push(`电话从"${this.originalData.phone}"修改为"${this.formData.phone}"`);
      }
      
      if (this.originalData.email !== this.formData.email) {
        changes.push(`邮箱从"${this.originalData.email}"修改为"${this.formData.email}"`);
      }
      
      return changes;
    },
    submitForm() {
      if (this.validateForm()) {
        // 获取变更的字段
        const changes = this.getChangedFields();
        
        // 创建操作记录
        if (changes.length > 0) {
          // 实际应用中，这里应该调用API保存变更和创建操作记录
          console.log('变更记录:', changes);
          
          // 显示提交成功提示
          uni.showToast({
            title: '保存成功',
            icon: 'success',
            duration: 2000,
            success: () => {
              // 延迟返回列表页
              setTimeout(() => {
                uni.navigateBack();
              }, 2000);
            }
          });
        } else {
          // 没有变更
          uni.showToast({
            title: '未检测到变更',
            icon: 'none',
            duration: 2000
          });
        }
      } else {
        // 表单验证失败
        uni.showToast({
          title: '请填写必填项',
          icon: 'none'
        });
      }
    }
  }
};
</script>

<style>
.container {
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  position: relative;
  border-bottom: 1rpx solid #eaeaea;
}

.back-button {
  font-size: 40rpx;
  color: #333;
  padding: 10rpx;
}

.page-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
}

.action-button {
  background: none;
  border: none;
  font-size: 32rpx;
  color: #4a6fff;
  padding: 10rpx;
}

.form-container {
  padding-bottom: 120rpx;
  height: calc(100vh - 100rpx);
}

.form-section {
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eaeaea;
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.section-hint {
  font-size: 24rpx;
  color: #999;
}

.required-hint {
  font-size: 24rpx;
  color: #ff4d4f;
}

.form-group {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-group:last-child {
  border-bottom: none;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.required:after {
  content: ' *';
  color: #ff4d4f;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #dcdfe6;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #ffffff;
}

.form-input.error {
  border-color: #ff4d4f;
}

.form-textarea {
  width: 100%;
  height: 180rpx;
  border: 1rpx solid #dcdfe6;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #ffffff;
}

.error-message {
  display: block;
  font-size: 24rpx;
  color: #ff4d4f;
  margin-top: 10rpx;
}

.avatar-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 10rpx 0 30rpx;
}

.avatar-preview {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  overflow: hidden;
  background-color: #f0f2f5;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15rpx;
}

.avatar-preview image {
  width: 100%;
  height: 100%;
}

.avatar-placeholder {
  font-size: 80rpx;
  color: #bbbec4;
}

.upload-hint {
  font-size: 24rpx;
  color: #999;
}

.radio-group {
  display: flex;
  align-items: center;
}

.radio-item {
  display: flex;
  align-items: center;
  margin-right: 60rpx;
}

.radio-button {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 1rpx solid #dcdfe6;
  margin-right: 10rpx;
  position: relative;
  background-color: #ffffff;
}

.radio-button.active {
  border-color: #4a6fff;
}

.radio-button.active:after {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #4a6fff;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.radio-label {
  font-size: 28rpx;
  color: #333;
}

.form-picker {
  width: 100%;
}

.picker-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  border: 1rpx solid #dcdfe6;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #ffffff;
}

.placeholder {
  color: #999;
}

.picker-icon {
  font-size: 32rpx;
  color: #999;
}

.history-hint {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #f9f9fc;
}

.history-icon {
  font-size: 40rpx;
  color: #4a6fff;
  margin-right: 15rpx;
}

.history-text {
  font-size: 28rpx;
  color: #666;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.btn-cancel {
  width: 48%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  color: #666;
  background-color: #f5f7fa;
  border: 1rpx solid #dcdfe6;
  font-size: 30rpx;
}

.btn-submit {
  width: 48%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  color: #ffffff;
  background-color: #4a6fff;
  font-size: 30rpx;
}
</style> 
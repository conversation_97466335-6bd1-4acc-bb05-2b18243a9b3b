<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="flex-row items-center gap-sm">
        <text class="page-title">销售报表</text>
      </view>
      <view class="header-actions">
        <button class="action-button" @click="shareReport">
          <text class="ri-share-line"></text>
        </button>
        <view class="dropdown">
          <button class="action-button" @click="toggleReportMenu">
            <text class="ri-file-list-3-line"></text>
          </button>
          <view class="dropdown-menu" :class="{'show': showReportMenu}">
            <navigator url="/pages/reports/sales-reports" class="dropdown-item active">
              <text class="ri-bar-chart-2-line"></text>
              <text>销售报表</text>
            </navigator>
            <navigator url="/pages/reports/team-performance" class="dropdown-item">
              <text class="ri-team-line"></text>
              <text>团队绩效</text>
            </navigator>
            <navigator url="/pages/reports/customer-analytics" class="dropdown-item">
              <text class="ri-user-search-line"></text>
              <text>客户分析</text>
            </navigator>
            <navigator url="/pages/reports/custom-reports" class="dropdown-item">
              <text class="ri-file-chart-line"></text>
              <text>自定义报表</text>
            </navigator>
          </view>
        </view>
      </view>
    </view>

    <!-- 时间筛选器 -->
    <scroll-view class="time-filter" scroll-x="true" show-scrollbar="false">
      <view 
        v-for="(option, index) in timeOptions" 
        :key="index" 
        class="time-option" 
        :class="{'active': selectedTimeIndex === index}"
        @click="selectTimeOption(index)"
      >
        <text>{{option}}</text>
      </view>
    </scroll-view>
    
    <scroll-view scroll-y="true" class="page-content">
      <!-- 数据卡片 -->
      <view class="data-cards">
        <view class="data-card" v-for="(card, index) in dataCards" :key="index">
          <text class="card-label">{{card.label}}</text>
          <text class="card-value">{{card.value}}</text>
          <view class="card-change" :class="{'change-positive': card.changePercent > 0, 'change-negative': card.changePercent < 0}">
            <text v-if="card.changePercent > 0" class="ri-arrow-up-line"></text>
            <text v-else class="ri-arrow-down-line"></text>
            <text>{{Math.abs(card.changePercent)}}%</text>
          </view>
        </view>
      </view>
      
      <!-- 销售趋势图表 -->
      <view class="chart-container">
        <view class="chart-header">
          <text class="chart-title">销售趋势</text>
          <view class="chart-actions">
            <button class="chart-action-btn" @click="downloadChart('salesTrend')">
              <text class="ri-download-line"></text>
            </button>
            <button class="chart-action-btn" @click="showChartOptions('salesTrend')">
              <text class="ri-more-2-fill"></text>
            </button>
          </view>
        </view>
        <view class="chart-wrapper">
          <!-- 在uni-app中，可以使用uni-app的图表组件或者第三方图表库如uCharts或者echarts -->
          <qiun-data-charts 
            type="line"
            :opts="salesTrendOpts"
            :chartData="salesTrendData"
          />
        </view>
      </view>
      
      <!-- 销售渠道分布图表 -->
      <view class="chart-container">
        <view class="chart-header">
          <text class="chart-title">销售渠道分布</text>
          <view class="chart-actions">
            <button class="chart-action-btn" @click="downloadChart('salesChannel')">
              <text class="ri-download-line"></text>
            </button>
            <button class="chart-action-btn" @click="showChartOptions('salesChannel')">
              <text class="ri-more-2-fill"></text>
            </button>
          </view>
        </view>
        <view class="chart-wrapper">
          <qiun-data-charts 
            type="pie"
            :opts="salesChannelOpts"
            :chartData="salesChannelData"
          />
        </view>
      </view>
      
      <!-- 产品销售排行表格 -->
      <view class="table-container">
        <view class="table-header">
          <text class="table-title">产品销售排行</text>
          <button class="chart-action-btn" @click="downloadProductRanking">
            <text class="ri-download-line"></text>
          </button>
        </view>
        <view class="table-wrapper">
          <view class="table-row table-header-row">
            <text class="table-cell table-head-cell">产品名称</text>
            <text class="table-cell table-head-cell">销售额</text>
            <text class="table-cell table-head-cell">销售量</text>
            <text class="table-cell table-head-cell">同比</text>
          </view>
          <view 
            class="table-row" 
            v-for="(product, index) in productRanking" 
            :key="index"
          >
            <text class="table-cell">{{product.name}}</text>
            <text class="table-cell">{{product.amount}}</text>
            <text class="table-cell">{{product.quantity}}</text>
            <text class="table-cell" :class="{'change-positive': product.percentChange > 0, 'change-negative': product.percentChange < 0}">
              {{product.percentChange > 0 ? '+' : ''}}{{product.percentChange}}%
            </text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 自定义TabBar组件 -->
    <custom-tab-bar ref="customTabBar"></custom-tab-bar>
  </view>
</template>

<script>
import CustomTabBar from '@/components/CustomTabBar.vue';

export default {
  components: {
    CustomTabBar
  },
  data() {
    return {
      showReportMenu: false,
      timeOptions: ['本周', '本月', '上个月', '本季度', '本年度', '自定义'],
      selectedTimeIndex: 0,
      
      // 数据卡片数据
      dataCards: [
        { 
          label: '总销售额', 
          value: '¥256,489', 
          changePercent: 12.5 
        },
        { 
          label: '完成订单', 
          value: '124', 
          changePercent: 8.3 
        },
        { 
          label: '客均价值', 
          value: '¥2,068', 
          changePercent: 4.2 
        },
        { 
          label: '转化率', 
          value: '23.5%', 
          changePercent: -1.8 
        }
      ],
      
      // 销售趋势图表数据
      salesTrendData: {
        categories: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        series: [
          {
            name: '本周',
            data: [12500, 18200, 21500, 15800, 24600, 28900, 32400]
          },
          {
            name: '上周',
            data: [10800, 15600, 19200, 14500, 22100, 25400, 29800]
          }
        ]
      },
      
      // 销售趋势图表配置
      salesTrendOpts: {
        color: ['#4a6fff', '#9ca3af'],
        padding: [15, 15, 0, 5],
        enableScroll: false,
        legend: {},
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          gridType: 'dash',
          formatter: (val) => {
            return '¥' + val.toLocaleString();
          }
        },
        extra: {
          line: {
            type: 'curve',
            width: 2,
            activeType: 'hollow'
          }
        }
      },
      
      // 销售渠道图表数据
      salesChannelData: {
        series: [
          {
            data: [
              { name: '线上销售', value: 35 },
              { name: '直销团队', value: 25 },
              { name: '合作伙伴', value: 20 },
              { name: '电话销售', value: 15 },
              { name: '其他', value: 5 }
            ]
          }
        ]
      },
      
      // 销售渠道图表配置
      salesChannelOpts: {
        color: ['#4a6fff', '#34d399', '#f59e0b', '#ec4899', '#9ca3af'],
        padding: [15, 15, 15, 15],
        enableScroll: false,
        legend: {
          position: 'right',
          itemGap: 10
        },
        extra: {
          pie: {
            activeOpacity: 0.5,
            activeRadius: 10,
            offsetAngle: 0,
            labelWidth: 15,
            border: false,
            borderWidth: 3,
            borderColor: '#FFFFFF'
          }
        }
      },
      
      // 产品销售排行数据
      productRanking: [
        { 
          name: '企业版CRM', 
          amount: '¥78,500', 
          quantity: 15, 
          percentChange: 12.4 
        },
        { 
          name: '商业智能分析套件', 
          amount: '¥56,200', 
          quantity: 28, 
          percentChange: 8.7 
        },
        { 
          name: '云存储服务', 
          amount: '¥45,800', 
          quantity: 42, 
          percentChange: -3.2 
        },
        { 
          name: '客户支持系统', 
          amount: '¥36,400', 
          quantity: 18, 
          percentChange: 15.1 
        },
        { 
          name: '营销自动化工具', 
          amount: '¥28,760', 
          quantity: 23, 
          percentChange: 5.8 
        }
      ]
    };
  },
  
  methods: {
    toggleReportMenu() {
      this.showReportMenu = !this.showReportMenu;
    },
    
    selectTimeOption(index) {
      this.selectedTimeIndex = index;
      // 这里添加数据刷新逻辑
      this.loadReportData(this.timeOptions[index]);
    },
    
    loadReportData(timeFrame) {
      // 根据选择的时间框架加载数据
      console.log('加载时间框架数据:', timeFrame);
      // 实际项目中，这里应该调用API获取数据
    },
    
    shareReport() {
      uni.showActionSheet({
        itemList: ['分享到微信', '发送邮件', '导出PDF', '导出Excel'],
        success: (res) => {
          uni.showToast({
            title: '分享功能开发中',
            icon: 'none'
          });
        }
      });
    },
    
    downloadChart(chartType) {
      uni.showToast({
        title: '下载功能开发中',
        icon: 'none'
      });
    },
    
    showChartOptions(chartType) {
      uni.showActionSheet({
        itemList: ['查看大图', '下载数据', '查看明细数据'],
        success: (res) => {
          uni.showToast({
            title: '功能开发中',
            icon: 'none'
          });
        }
      });
    },
    
    downloadProductRanking() {
      uni.showToast({
        title: '下载功能开发中',
        icon: 'none'
      });
    }
  },
  
  onLoad() {
    // 页面加载时初始化数据
    this.loadReportData(this.timeOptions[this.selectedTimeIndex]);
  },
  
  onShow() {
    // 重新进入页面时刷新数据
    this.loadReportData(this.timeOptions[this.selectedTimeIndex]);
    // 设置TabBar当前选中项
    if (typeof this.$refs.customTabBar !== 'undefined') {
      this.$refs.customTabBar.current = 4; // 对应"更多"菜单
    }
  },
  
  onHide() {
    // 隐藏菜单
    this.showReportMenu = false;
  }
};
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.page-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1px solid var(--border-color);
  background-color: #fff;
  position: relative;
  z-index: 10;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.gap-sm {
  gap: 20rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  flex-direction: row;
  gap: 20rpx;
}

.action-button {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #666;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  padding: 0;
  margin: 0;
  line-height: 1;
}

.time-filter {
  display: flex;
  flex-direction: row;
  white-space: nowrap;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  padding: 0 30rpx;
}

.time-option {
  padding: 30rpx;
  color: #666;
  font-size: 28rpx;
  position: relative;
}

.time-option.active {
  color: #4a6fff;
  font-weight: 500;
}

.time-option.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background-color: #4a6fff;
  border-radius: 4rpx;
}

.page-content {
  flex: 1;
  background-color: #f5f7fa;
}

.data-cards {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  padding: 30rpx;
}

.data-card {
  width: calc(50% - 20rpx);
  background-color: #fff;
  border-radius: 16rpx;
  border: 1px solid #e0e0e0;
  padding: 30rpx;
  margin: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.card-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.card-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.card-change {
  font-size: 24rpx;
  margin-top: 10rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.change-positive {
  color: #34d399;
}

.change-negative {
  color: #ef4444;
}

.chart-container {
  background-color: #fff;
  border-radius: 16rpx;
  border: 1px solid #e0e0e0;
  margin: 0 30rpx 30rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.chart-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.chart-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.chart-actions {
  display: flex;
  flex-direction: row;
  gap: 10rpx;
}

.chart-action-btn {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  color: #666;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  padding: 0;
  margin: 0;
  line-height: 1;
}

.chart-wrapper {
  width: 100%;
  height: 500rpx;
  position: relative;
}

.table-container {
  margin: 0 30rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  border: 1px solid #e0e0e0;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.table-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #e0e0e0;
}

.table-title {
  font-size: 30rpx;
  font-weight: 500;
}

.table-wrapper {
  width: 100%;
}

.table-row {
  display: flex;
  flex-direction: row;
  border-bottom: 1px solid #e0e0e0;
}

.table-row:last-child {
  border-bottom: none;
}

.table-header-row {
  background-color: #f5f7fa;
}

.table-cell {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  flex: 1;
}

.table-head-cell {
  font-weight: 500;
  color: #666;
}

.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  width: 360rpx;
  z-index: 1000;
  display: none;
  margin-top: 20rpx;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 30rpx;
  color: #333;
  font-size: 28rpx;
  border-bottom: 1px solid #e0e0e0;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item text:first-child {
  margin-right: 20rpx;
  font-size: 32rpx;
  color: #4a6fff;
}

.dropdown-item.active {
  font-weight: 500;
  background-color: rgba(74, 111, 255, 0.1);
}
</style> 
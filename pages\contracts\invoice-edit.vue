<template>
  <view class="invoice-edit-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="nav-back" @click="goBack">
        <text class="ri-arrow-left-s-line"></text>
      </view>
      <view class="page-title">编辑发票</view>
      <view class="header-actions">
        <view class="action-button history-btn" @click="goToHistory">
          <text class="ri-history-line"></text>
        </view>
      </view>
    </view>
    
    <!-- 页面内容区域 -->
    <scroll-view scroll-y class="page-content">
      <form id="invoiceForm">
        <!-- 基本信息 -->
        <view class="form-section">
          <view class="section-title">基本信息</view>
          
          <view class="form-group">
            <view class="form-label">发票号码</view>
            <input type="text" class="form-input" v-model="invoice.invoiceNumber" placeholder="系统自动生成"/>
            <view class="form-hint">提交后自动生成发票号码</view>
          </view>
          
          <view class="form-group">
            <view class="form-label">发票日期</view>
            <picker mode="date" :value="invoice.invoiceDate" @change="onInvoiceDateChange" class="date-picker">
              <view class="date-value">{{formatDate(invoice.invoiceDate)}}</view>
            </picker>
          </view>
          
          <view class="form-group">
            <view class="form-label">到期日期</view>
            <picker mode="date" :value="invoice.dueDate" @change="onDueDateChange" class="date-picker">
              <view class="date-value">{{formatDate(invoice.dueDate)}}</view>
            </picker>
          </view>
          
          <view class="form-group">
            <view class="form-label">发票类型</view>
            <picker :range="invoiceTypes" :value="invoice.typeIndex" @change="onTypeChange" class="form-picker">
              <view class="picker-value">{{invoiceTypes[invoice.typeIndex]}}</view>
            </picker>
          </view>
          
          <view class="form-group">
            <view class="form-label">发票状态</view>
            <picker :range="invoiceStatuses" :value="invoice.statusIndex" @change="onStatusChange" class="form-picker">
              <view class="picker-value">
                <view class="status-badge" :class="getStatusClass(invoice.status)">{{invoiceStatuses[invoice.statusIndex]}}</view>
              </view>
            </picker>
          </view>
        </view>
        
        <!-- 客户信息 -->
        <view class="form-section">
          <view class="section-title">客户信息</view>
          
          <view class="form-group" :class="{'has-error': errors.customer}">
            <view class="form-label required">客户</view>
            <picker :range="customers" range-key="name" :value="customerIndex" @change="onCustomerChange" class="form-picker">
              <view class="picker-value">
                <view v-if="selectedCustomer" class="customer-info">
                  <view class="customer-name">{{selectedCustomer.name}}</view>
                </view>
                <view v-else class="placeholder">选择客户</view>
              </view>
            </picker>
            <view v-if="errors.customer" class="error-message">必须选择客户</view>
          </view>
          
          <view class="form-group">
            <view class="form-label">税号</view>
            <input type="text" class="form-input" v-model="invoice.taxNumber" placeholder="税号"/>
          </view>
          
          <view class="form-group">
            <view class="form-label">关联合同</view>
            <picker :range="contracts" range-key="name" :value="contractIndex" @change="onContractChange" class="form-picker">
              <view class="picker-value">
                <view v-if="selectedContract" class="contract-info">
                  <view class="contract-name">{{selectedContract.name}}</view>
                </view>
                <view v-else class="placeholder">选择关联合同</view>
              </view>
            </picker>
          </view>
        </view>
        
        <!-- 发票明细 -->
        <view class="form-section">
          <view class="section-title">发票明细</view>
          
          <view class="item-list" id="itemList">
            <view class="item-row" v-for="(item, index) in invoice.items" :key="index">
              <view class="item-header">
                <view class="item-title">明细条目 #{{index+1}}</view>
                <view class="item-delete" @click="deleteItem(index)" v-if="invoice.items.length > 1">
                  <text class="ri-delete-bin-6-line"></text>
                </view>
              </view>
              <view class="item-fields">
                <input 
                  type="text" 
                  class="form-input item-description" 
                  v-model="item.description" 
                  placeholder="描述" 
                  :class="{'has-error': item.errors && item.errors.description}"
                  required
                />
                <input 
                  type="number" 
                  class="form-input item-quantity" 
                  v-model="item.quantity" 
                  placeholder="数量" 
                  min="1"
                />
                <view class="currency-input">
                  <text class="currency-symbol">¥</text>
                  <input 
                    type="digit" 
                    class="form-input item-price" 
                    v-model="item.price" 
                    placeholder="单价" 
                    :class="{'has-error': item.errors && item.errors.price}"
                    min="0" 
                    required
                  />
                </view>
              </view>
            </view>
          </view>
          
          <button type="button" class="add-item-button" @click="addItem">
            <text class="ri-add-line"></text>
            <text>添加明细条目</text>
          </button>
          
          <view class="tax-summary">
            <view class="tax-row">
              <view class="tax-label">小计：</view>
              <view class="tax-value">¥{{formatMoney(subtotal)}}</view>
            </view>
            <view class="tax-row">
              <view class="tax-label">税率：</view>
              <view class="tax-value">
                <picker :range="taxRates" :value="taxRateIndex" @change="onTaxRateChange" class="tax-picker">
                  <view class="picker-value">{{getTaxRateLabel(taxRateIndex)}}</view>
                </picker>
              </view>
            </view>
            <view class="tax-row">
              <view class="tax-label">税额：</view>
              <view class="tax-value">¥{{formatMoney(taxAmount)}}</view>
            </view>
            <view class="tax-row total-row">
              <view class="tax-label">总计：</view>
              <view class="tax-value">¥{{formatMoney(total)}}</view>
            </view>
          </view>
        </view>
        
        <!-- 备注信息 -->
        <view class="form-section">
          <view class="section-title">备注信息</view>
          
          <view class="form-group">
            <view class="form-label">备注</view>
            <textarea class="form-textarea" v-model="invoice.notes" placeholder="输入备注信息"></textarea>
          </view>
        </view>
      </form>
    </scroll-view>
    
    <!-- 浮动操作按钮 -->
    <view class="float-actions">
      <view class="action-btn secondary-action" @click="cancel">
        <text class="ri-close-line"></text>
        <text>取消</text>
      </view>
      <view class="action-btn primary-action" @click="saveInvoice">
        <text class="ri-save-line"></text>
        <text>保存修改</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      invoice: {
        id: null,
        invoiceNumber: '',
        invoiceDate: this.formatDateForPicker(new Date()),
        dueDate: this.formatDateForPicker(this.getDefaultDueDate()),
        type: 'VAT',
        typeIndex: 0,
        status: 'draft',
        statusIndex: 0,
        customerId: null,
        taxNumber: '',
        contractId: null,
        notes: '请在收到发票后30天内付款，谢谢合作。',
        items: [
          {
            id: 1,
            description: '系统集成服务',
            quantity: 1,
            price: 25000,
            errors: {}
          },
          {
            id: 2,
            description: '软件授权费用',
            quantity: 5,
            price: 2000,
            errors: {}
          }
        ]
      },
      invoiceTypes: ['增值税专用发票', '增值税普通发票', '电子发票'],
      invoiceStatuses: ['草稿', '已发送', '部分支付', '已支付', '逾期', '已取消'],
      customers: [
        { id: 1, name: '上海科技有限公司', taxNumber: '91310000MA1FL4CT3X' },
        { id: 2, name: '北京智能科技有限公司', taxNumber: '91110000X09YGHTZ7' },
        { id: 3, name: '广州数字科技有限公司', taxNumber: '91440000MA5CL4RX9B' },
        { id: 4, name: '成都创新科技有限公司', taxNumber: '91510100MA6DGEJK2X' }
      ],
      customerIndex: 0,
      contracts: [
        { id: 1, name: 'CRM系统实施合同' },
        { id: 2, name: '数据分析平台合同' },
        { id: 3, name: '软件维护服务合同' }
      ],
      contractIndex: 0,
      taxRates: [0.13, 0.09, 0.06, 0.03, 0],
      taxRateIndex: 0,
      errors: {
        customer: false
      }
    }
  },
  computed: {
    selectedCustomer() {
      return this.customerIndex >= 0 ? this.customers[this.customerIndex] : null;
    },
    selectedContract() {
      return this.contractIndex >= 0 ? this.contracts[this.contractIndex] : null;
    },
    subtotal() {
      return this.invoice.items.reduce((total, item) => {
        return total + (parseFloat(item.price) || 0) * (parseFloat(item.quantity) || 0);
      }, 0);
    },
    taxAmount() {
      return this.subtotal * this.taxRates[this.taxRateIndex];
    },
    total() {
      return this.subtotal + this.taxAmount;
    }
  },
  onLoad(options) {
    // 如果有传入ID，加载发票数据
    if (options.id) {
      this.loadInvoiceData(options.id);
    }
    
    // 如果有传入合同ID，自动选择该合同
    if (options.contractId) {
      this.setContractById(options.contractId);
    }
  },
  methods: {
    loadInvoiceData(id) {
      // 模拟从API加载发票数据
      // 实际项目中，这里应该是API调用
      console.log('加载发票ID:', id);
      
      // 设置客户和合同索引
      this.setCustomerById(this.invoice.customerId);
      this.setContractById(this.invoice.contractId);
    },
    setCustomerById(customerId) {
      if (!customerId) return;
      
      const index = this.customers.findIndex(c => c.id == customerId);
      if (index !== -1) {
        this.customerIndex = index;
        this.invoice.taxNumber = this.customers[index].taxNumber;
      }
    },
    setContractById(contractId) {
      if (!contractId) return;
      
      const index = this.contracts.findIndex(c => c.id == contractId);
      if (index !== -1) {
        this.contractIndex = index;
        this.invoice.contractId = contractId;
      }
    },
    formatDateForPicker(date) {
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    getDefaultDueDate() {
      const date = new Date();
      date.setDate(date.getDate() + 30); // 默认30天后到期
      return date;
    },
    formatDate(dateString) {
      if (!dateString) return '';
      const parts = dateString.split('-');
      return `${parts[0]}年${parts[1]}月${parts[2]}日`;
    },
    formatMoney(value) {
      return value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    getStatusClass(status) {
      const statusClasses = {
        'draft': 'status-draft',
        'sent': 'status-sent',
        'partial': 'status-partial',
        'paid': 'status-paid',
        'overdue': 'status-overdue',
        'cancelled': 'status-cancelled'
      };
      return statusClasses[status] || '';
    },
    getTaxRateLabel(index) {
      const rate = this.taxRates[index];
      return rate > 0 ? `${(rate * 100).toFixed(0)}%` : '免税';
    },
    onInvoiceDateChange(e) {
      this.invoice.invoiceDate = e.detail.value;
    },
    onDueDateChange(e) {
      this.invoice.dueDate = e.detail.value;
    },
    onTypeChange(e) {
      this.invoice.typeIndex = e.detail.value;
      const types = ['VAT', 'normal', 'electronic'];
      this.invoice.type = types[e.detail.value] || 'VAT';
    },
    onStatusChange(e) {
      this.invoice.statusIndex = e.detail.value;
      const statuses = ['draft', 'sent', 'partial', 'paid', 'overdue', 'cancelled'];
      this.invoice.status = statuses[e.detail.value] || 'draft';
    },
    onCustomerChange(e) {
      this.customerIndex = e.detail.value;
      if (this.customerIndex >= 0) {
        const customer = this.customers[this.customerIndex];
        this.invoice.customerId = customer.id;
        this.invoice.taxNumber = customer.taxNumber;
        this.errors.customer = false;
      }
    },
    onContractChange(e) {
      this.contractIndex = e.detail.value;
      if (this.contractIndex >= 0) {
        this.invoice.contractId = this.contracts[this.contractIndex].id;
      }
    },
    onTaxRateChange(e) {
      this.taxRateIndex = e.detail.value;
    },
    addItem() {
      const newId = Math.max(...this.invoice.items.map(item => item.id), 0) + 1;
      this.invoice.items.push({
        id: newId,
        description: '',
        quantity: 1,
        price: 0,
        errors: {}
      });
    },
    deleteItem(index) {
      if (this.invoice.items.length > 1) {
        this.invoice.items.splice(index, 1);
      } else {
        uni.showToast({
          title: '至少需要保留一个明细条目',
          icon: 'none'
        });
      }
    },
    validateForm() {
      let isValid = true;
      
      // 验证客户
      if (!this.invoice.customerId) {
        this.errors.customer = true;
        isValid = false;
      } else {
        this.errors.customer = false;
      }
      
      // 验证明细条目
      this.invoice.items.forEach(item => {
        item.errors = {};
        
        if (!item.description) {
          item.errors.description = true;
          isValid = false;
        }
        
        if (!item.price || parseFloat(item.price) <= 0) {
          item.errors.price = true;
          isValid = false;
        }
      });
      
      return isValid;
    },
    saveInvoice() {
      if (!this.validateForm()) {
        uni.showToast({
          title: '请填写所有必填项',
          icon: 'none'
        });
        return;
      }
      
      // 保存发票数据
      // 实际项目中，这里应该是API调用
      console.log('保存发票数据', this.invoice);
      
      uni.showToast({
        title: '发票修改已保存',
        icon: 'success',
        success: () => {
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        }
      });
    },
    cancel() {
      uni.showModal({
        title: '确认取消',
        content: '确定要取消编辑吗？所有未保存的修改将丢失。',
        success: (res) => {
          if (res.confirm) {
            uni.navigateBack();
          }
        }
      });
    },
    goBack() {
      uni.navigateBack();
    },
    goToHistory() {
      uni.navigateTo({
        url: '/pages/contracts/invoice-history'
      });
    }
  }
}
</script>

<style>
.invoice-edit-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.page-header {
  display: flex;
  align-items: center;
  padding: 0 15px;
  height: 56px;
  background-color: #ffffff;
  border-bottom: 1px solid #eaeaea;
  position: relative;
  z-index: 10;
}

.nav-back {
  font-size: 24px;
  line-height: 24px;
  margin-right: 10px;
  color: #333;
}

.page-title {
  flex: 1;
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.header-actions {
  display: flex;
}

.action-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-left: 8px;
  font-size: 20px;
  color: #666;
}

.page-content {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 120px;
}

.form-section {
  background-color: #fff;
  margin: 10px;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  color: #333;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 2px;
  bottom: 2px;
  width: 4px;
  background-color: #3b7ff3;
  border-radius: 2px;
}

.form-group {
  margin-bottom: 15px;
}

.form-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

.required:after {
  content: '*';
  color: #f56c6c;
  margin-left: 4px;
}

.form-input, .form-picker, .date-picker {
  width: 100%;
  height: 40px;
  padding: 0 12px;
  border: 1px solid #dcdee2;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
  color: #333;
}

.form-textarea {
  width: 100%;
  min-height: 80px;
  padding: 8px 12px;
  border: 1px solid #dcdee2;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
  color: #333;
}

.date-value, .picker-value {
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: #333;
}

.placeholder {
  color: #999;
}

.has-error .form-input,
.has-error .form-picker,
.form-input.has-error {
  border-color: #f56c6c;
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
}

.form-hint {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.status-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  background-color: #e8e8e8;
  color: #666;
}

.status-draft {
  background-color: #e8f4ff;
  color: #3b7ff3;
}

.status-sent {
  background-color: #ecf5ff;
  color: #409eff;
}

.status-partial {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.status-paid {
  background-color: #f0f9eb;
  color: #67c23a;
}

.status-overdue {
  background-color: #fef0f0;
  color: #f56c6c;
}

.status-cancelled {
  background-color: #f4f4f5;
  color: #909399;
}

.customer-info, .contract-info {
  display: flex;
  flex-direction: column;
}

.customer-name, .contract-name {
  font-size: 14px;
}

/* 发票明细样式 */
.item-row {
  border: 1px solid #eaeaea;
  border-radius: 6px;
  margin-bottom: 10px;
  background-color: #fcfcfc;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #eaeaea;
  background-color: #f9f9f9;
  border-radius: 6px 6px 0 0;
}

.item-title {
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.item-delete {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #f56c6c;
  font-size: 18px;
}

.item-fields {
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.currency-input {
  position: relative;
}

.currency-symbol {
  position: absolute;
  left: 12px;
  top: 11px;
  font-size: 14px;
  color: #666;
}

.item-price {
  padding-left: 24px;
}

.add-item-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 40px;
  background-color: #f0f4ff;
  border: 1px dashed #a0c0ff;
  border-radius: 4px;
  color: #3b7ff3;
  font-size: 14px;
  margin: 15px 0;
}

.add-item-button text {
  margin-right: 4px;
}

.tax-summary {
  border-top: 1px solid #eaeaea;
  padding-top: 15px;
  margin-top: 10px;
}

.tax-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.tax-label {
  font-size: 14px;
  color: #666;
}

.tax-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.tax-picker {
  width: 100px;
  height: 30px;
  text-align: right;
}

.total-row {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #eaeaea;
}

.total-row .tax-label,
.total-row .tax-value {
  font-size: 16px;
  font-weight: 600;
}

.float-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  height: 70px;
  background-color: #fff;
  border-top: 1px solid #eaeaea;
  padding: 10px 15px;
  box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
  z-index: 100;
}

.action-btn {
  flex: 1;
  height: 45px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  font-weight: 500;
  margin: 0 6px;
}

.action-btn text:first-child {
  margin-right: 4px;
  font-size: 18px;
}

.secondary-action {
  background-color: #f2f2f2;
  color: #666;
}

.primary-action {
  background-color: #3b7ff3;
  color: #fff;
}
</style> 
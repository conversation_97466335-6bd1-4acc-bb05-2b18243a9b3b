<template>
  <view class="page-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <text class="page-title">个人资料</text>
      <text class="edit-link" @tap="editProfile">编辑</text>
    </view>
    
    <!-- 个人资料头部 -->
    <view class="profile-header">
      <view class="profile-avatar">
        <text>{{ userName && userName[0] }}</text>
<!--        <view class="avatar-edit" @tap="uploadAvatar">
          <svg-icon name="camera" type="svg" size="36" color="#0066FF"></svg-icon>
        </view>-->
      </view>
      <text class="profile-name">{{ userName }}</text>
      <text class="profile-title">{{ userRole }}</text>
    </view>
    
    <!-- 统计数据 -->
<!--    <view class="profile-stats">
      <view class="stat-item" v-for="(stat, index) in stats" :key="index">
        <text class="stat-value">{{stat.value}}</text>
        <text class="stat-label">{{stat.label}}</text>
      </view>
    </view>-->
    <!-- 个人资料内容 -->
    <div class="profile-content">
      <!-- 个人信息 -->
      <view class="section-title">个人信息</view>
      <view class="profile-card">
        <view class="info-item" v-for="(item, index) in personalInfo" :key="index">
          <view class="info-icon">
            <svg-icon :name="item.icon" type="svg" size="40" color="#0066FF"></svg-icon>
          </view>
          <view class="info-content">
            <text class="info-label">{{item.label}}</text>
            <text class="info-value">{{item.value}}</text>
          </view>
        </view>
      </view>
      <!-- 账户设置 -->
<!--      <view class="section-title">账户设置</view>
      <view class="profile-card">
        <view class="setting-item" v-for="(item, index) in accountSettings" :key="index" @tap="navigateTo(item.url)">
          <view class="setting-left">
            <view class="setting-icon">
              <svg-icon :name="item.icon" type="svg" size="40" color="#0066FF"></svg-icon>
            </view>
            <text class="setting-label">{{item.label}}</text>
          </view>
          <view class="setting-right">
            <svg-icon name="arrow-right" type="svg" size="32" color="#666666"></svg-icon>
          </view>
        </view>
      </view>-->
      <!-- 应用设置 -->
      <view class="section-title">应用设置</view>
      <view class="profile-card">
        <view class="setting-item">
          <view class="setting-left">
            <view class="setting-icon">
              <svg-icon name="moon" type="svg" size="40" color="#0066FF"></svg-icon>
            </view>
            <text class="setting-label">深色模式</text>
          </view>
          <view class="setting-right">
            <switch :checked="isDarkMode" @change="toggleTheme" color="#0066FF" />
          </view>
        </view>
        
        <view class="setting-item" @tap="changeLanguage">
          <view class="setting-left">
            <view class="setting-icon">
              <svg-icon name="translate" type="svg" size="40" color="#0066FF"></svg-icon>
            </view>
            <text class="setting-label">语言</text>
          </view>
          <view class="setting-right">
            <text class="setting-value">简体中文</text>
            <svg-icon name="arrow-right" type="svg" size="32" color="#666666"></svg-icon>
          </view>
        </view>
        
        <view class="setting-item" @tap="showAbout">
          <view class="setting-left">
            <view class="setting-icon">
              <svg-icon name="info" type="svg" size="40" color="#0066FF"></svg-icon>
            </view>
            <text class="setting-label">关于应用</text>
          </view>
          <view class="setting-right">
            <svg-icon name="arrow-right" type="svg" size="32" color="#666666"></svg-icon>
          </view>
        </view>
      </view>
      <!-- 退出登录按钮 -->
      <button class="logout-btn" @tap="logout">退出登录</button>
    </div>
    <!-- 自定义TabBar组件 -->
    <custom-tab-bar></custom-tab-bar>
  </view>
</template>

<script>
import SvgIcon from '@/components/svg-icon.vue';
import CustomTabBar from '@/components/CustomTabBar.vue';

export default {
  components: {
    SvgIcon,
    CustomTabBar
  },
  data() {
    return {
      userName: '',
      userRole: '',
      isDarkMode: false,
      stats: [
        { value: '128', label: '客户' },
        { value: '26', label: '商机' },
        { value: '¥120万', label: '销售额' }
      ],
      personalInfo: [
        { icon: 'user', label: '姓名', value: '' },
        { icon: 'mail', label: '邮箱', value: '' },
        { icon: 'phone', label: '手机', value: '' },
        { icon: 'building', label: '公司', value: '' },
        // { icon: 'user', label: '职位', value: '' }
      ],
      accountSettings: [
        { icon: 'team', label: '团队管理', url: '/pages/settings/team-management' },
        { icon: 'bell', label: '通知设置', url: '/pages/settings/notifications' },
        { icon: 'password', label: '修改密码', url: '/pages/settings/change-password' }
      ]
    }
  },
  onLoad() {
    this.checkTheme();
    this.getUserInfo();
  },
  onShow() {
    // 设置TabBar选中项
    if (typeof this.$refs.customTabBar !== 'undefined') {
      this.$refs.customTabBar.current = 4;
    } else {
      // 如果refs还没准备好，尝试延迟设置
      setTimeout(() => {
        if (typeof this.$refs.customTabBar !== 'undefined') {
          this.$refs.customTabBar.current = 4;
          console.log('线索页面设置TabBar当前项为4');
        }
      }, 300);
    }
  },
  methods: {
    // 获取用户信息
    getUserInfo() {
      const userInfo = uni.getStorageSync("userInfo");
      this.userName = userInfo.name;
      this.userRole = userInfo.userName;
      this.personalInfo[0].value = userInfo.name;
      this.personalInfo[1].value = userInfo.email;
      this.personalInfo[2].value = userInfo.phoneNumber;
      this.personalInfo[3].value = userInfo.tenantName;
    },
    // 检查并应用当前主题设置
    checkTheme() {
      uni.getStorage({
        key: 'darkMode',
        success: (res) => {
          this.isDarkMode = res.data;
          this.applyTheme();
        }
      });
    },
    toggleTheme(e) {
      if (e) {
        this.isDarkMode = e.detail.value;
      } else {
        this.isDarkMode = !this.isDarkMode;
      }
      // 保存设置
      uni.setStorage({
        key: 'darkMode',
        data: this.isDarkMode
      });
      
      this.applyTheme();
    },
    applyTheme() {
      // 应用主题设置
      uni.$emit('theme-change', { darkMode: this.isDarkMode });
    },
    editProfile() {
      uni.showToast({
        title: '编辑功能正在开发中',
        icon: 'none'
      });
    },
    uploadAvatar() {
      uni.showActionSheet({
        itemList: ['拍照', '从相册选择'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 拍照
            uni.chooseImage({
              count: 1,
              sourceType: ['camera'],
              success: (res) => {
                // 处理头像上传
                this.handleAvatarUpload(res.tempFilePaths[0]);
              }
            });
          } else if (res.tapIndex === 1) {
            // 从相册选择
            uni.chooseImage({
              count: 1,
              sourceType: ['album'],
              success: (res) => {
                // 处理头像上传
                this.handleAvatarUpload(res.tempFilePaths[0]);
              }
            });
          }
        }
      });
    },
    handleAvatarUpload(filepath) {
      uni.showToast({
        title: '头像上传功能正在开发中',
        icon: 'none'
      });
    },
    navigateTo(url) {
      if (!url) {
        uni.showToast({
          title: '该功能正在开发中',
          icon: 'none'
        });
        return;
      }
      
      uni.navigateTo({
        url: url
      });
    },
    changeLanguage() {
      uni.showActionSheet({
        itemList: ['简体中文', 'English'],
        success: (res) => {
          uni.showToast({
            title: '语言切换功能正在开发中',
            icon: 'none'
          });
        }
      });
    },
    showAbout() {
      uni.navigateTo({
        url: '/pages/settings/about'
      });
    },
    logout() {
      uni.showModal({
        title: '提示',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '退出中...'
            });
            setTimeout(() => {
              uni.clearStorageSync();
              uni.reLaunch({
                url: '/pages/auth/login'
              });
            }, 300);
          }
        }
      });
    }
  }
}
</script>

<style>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
  position: relative;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #eaecef;
  background-color: #ffffff;
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.edit-link {
  color: #0066FF;
  font-size: 28rpx;
}

.profile-header {
  background-color: #0066FF;
  padding: 40rpx 40rpx 80rpx;
  text-align: center;
  color: #ffffff;
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

.profile-avatar {
  width: 180rpx;
  height: 180rpx;
  border-radius: 50%;
  margin: 0 auto 20rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 70rpx;
  font-weight: bold;
  color: #0066FF;
  position: relative;
  border: 8rpx solid rgba(255, 255, 255, 0.3);
}

.avatar-edit {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 64rpx;
  height: 64rpx;
  background-color: #d1e6ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #0066FF;
  font-size: 36rpx;
  border: 4rpx solid #ffffff;
}

.profile-name {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.profile-title {
  font-size: 32rpx;
  opacity: 0.9;
  margin-left: 8rpx;
}

.profile-stats {
  display: flex;
  justify-content: space-around;
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  padding: 40rpx 30rpx;
  margin-top: -60rpx;
  position: relative;
  width: 92%;
  left: 4%;
  box-sizing: border-box;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 28rpx;
  color: #666666;
}

.profile-content {
  flex: 1;
  padding: 20rpx 30rpx;
  background-color: #f5f7fa;
  width: 100%;
  box-sizing: border-box;
}

.section-title {
  font-size: 32rpx;
  color: #666666;
  margin: 20rpx 0;
  font-weight: 500;
  width: 100%;
  box-sizing: border-box;
}

.profile-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #eaecef;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eaecef;
}

.info-item:last-child {
  border-bottom: none;
}

.info-icon {
  margin-top: 10rpx;
  margin-right: 24rpx;
  width: 48rpx;
  text-align: center;
  display: flex;
  justify-content: center;
}

.info-content {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.info-label {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 8rpx;
  margin-right: 24rpx;
  word-break: break-all;
  width: 100%;
}

.info-value {
  font-size: 32rpx;
  color: #333333;
  word-break: break-all;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eaecef;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-left {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.setting-icon {
  margin-right: 30rpx;
  width: 48rpx;
  text-align: center;
  display: flex;
  justify-content: center;
}

.setting-label {
  font-size: 32rpx;
  color: #333333;
  flex: 1;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.setting-right {
  display: flex;
  align-items: center;
  margin-left: 16rpx;
  flex-shrink: 0;
}

.setting-value {
  font-size: 28rpx;
  color: #666666;
  margin-right: 8rpx;
}

.logout-btn {
  background-color: #fee2e2;
  color: #dc2626;
  border: none;
  border-radius: 16rpx;
  padding: 30rpx;
  width: 100%;
  font-size: 32rpx;
  font-weight: 500;
  margin-top: 40rpx;
  margin-bottom: 120rpx;
  box-sizing: border-box;
}
</style> 
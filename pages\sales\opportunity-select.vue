<template>
  <view class="page">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @tap="navBack">
        <svg-icon name="arrow-left" type="svg" size="24"></svg-icon>
      </view>
      <text class="page-title">选择商机</text>
    </view>

    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <view class="search-icon">
          <svg-icon name="search" type="svg" size="20"></svg-icon>
        </view>
        <input
          type="text"
          class="search-input"
          v-model="searchKeyword"
          placeholder="搜索商机名称"
          @input="onSearch"
        />
      </view>
    </view>

    <!-- 商机列表 -->
    <scroll-view
      scroll-y
      class="opportunity-list"
      :style="{ height: listHeight + 'px' }"
    >
      <!-- 商机列表项 -->
      <view
        class="opportunity-item"
        v-for="item in filterBusiness"
        :key="item.id"
        @tap="selectOpportunity(item)"
      >
        <view class="opportunity-info">
          <text class="opportunity-name">{{ item.name }}</text>
          <text class="opportunity-customer"></text>
        </view>
        <view class="opportunity-meta">
          <text class="opportunity-amount"
            >￥{{ item.expectedTransNoRateAmount }}</text
          >
          <text
            class="opportunity-stage"
            :class="'stage-' + item.businessProcessName"
            >{{ item.businessProcessName }}</text
          >
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import SvgIcon from "@/components/svg-icon.vue";
import { getOpportunityList } from "@/api/contact.api";
import getSelectOptions from "@/utils/dictionary.js";

export default {
  components: {
    SvgIcon,
  },
  data() {
    return {
      searchKeyword: "",
      businessProcessList: [],
      opportunities: [],
      isLoading: false,
      hasMore: true,
      page: 1,
      listHeight: 0,
    };
  },
  methods: {
    // getlist
    async getList() {
      this.businessProcessList = await getSelectOptions("BusinessProcess");
      const target = this.businessProcessList.find(
        (item) => item.code === "Win"
      );
      await getOpportunityList({
        pageIndex: 1,
        pageSize: 10,
        filter: { hasContract: false, businessProcessId: target.id },
      }).then((res) => {
        this.opportunities = res;
      });
    },

    navBack() {
      uni.navigateBack();
    },

    onSearch() {
      // 实际应用中这里应该调用搜索API
      console.log("搜索关键词:", this.searchKeyword);
    },

    selectOpportunity(opportunity) {
      // 通过事件返回选中的商机信息

      const eventChannel = this.getOpenerEventChannel();
      eventChannel.emit("opportunitySelected", {
        id: opportunity.id,
        name: opportunity.name,
      });
      console.log("selectOpportunity", opportunity);
      uni.navigateBack();
    },
    // 计算列表高度
    calculateListHeight() {
      const systemInfo = uni.getSystemInfoSync();
      // 减去头部和搜索框的高度
      this.listHeight = systemInfo.windowHeight - uni.upx2px(200);
    },
  },
  computed: {
    filterBusiness() {
      const search = this.searchKeyword.toLowerCase().trim();
      let filtered = this.opportunities;
      if (search) {
        filtered = filtered.filter((item) => {
          const name = item.name ? item.name.toLowerCase() : "";
          if (name.includes(search)) {
            return true;
          }
          return false;
        });
      }
      return filtered;
    },
  },

  onLoad() {
    this.calculateListHeight();
    this.getList();
  },
  onReady() {
    // 实际应用中这里应该加载初始数据
  },
};
</script>

<style>
.page {
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e0e0e0;
}

.back-button {
  padding: 10rpx;
  margin-right: 20rpx;
}

.page-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.search-container {
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e0e0e0;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  padding: 0 20rpx;
}

.search-icon {
  color: #999;
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  height: 72rpx;
  font-size: 28rpx;
}

.opportunity-list {
  padding: 16rpx 20rpx 0 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.opportunity-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.opportunity-info,
.opportunity-meta {
  width: 100%;
  box-sizing: border-box;
}

.opportunity-name,
.opportunity-customer,
.opportunity-amount,
.opportunity-stage {
  word-break: break-all;
  white-space: normal;
}

.opportunity-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.opportunity-customer {
  font-size: 26rpx;
  color: #666;
}

.opportunity-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.opportunity-amount {
  font-size: 28rpx;
  color: #3370ff;
  font-weight: 500;
}

.opportunity-stage {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 100rpx;
}

.stage-proposal {
  background-color: #fef3c7;
  color: #d97706;
}

.stage-qualifying {
  background-color: #dbeafe;
  color: #2563eb;
}

.stage-赢单 {
  background-color: #fee2e2;
  color: #dc2626;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  color: #999;
}

.loading-icon {
  margin-bottom: 20rpx;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.load-more {
  text-align: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 26rpx;
}
</style>

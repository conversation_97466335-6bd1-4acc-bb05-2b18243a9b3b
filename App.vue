<script>
	export default {
	onLaunch: function() {
		console.log('App onLaunch');
		const expirationTime = uni.getStorageSync('expirationTime');
		const userInfo = uni.getStorageSync('userInfo');
		const isTokenExpired = expirationTime && new Date().getTime() > new Date(expirationTime).getTime();
		const hasUserInfo = userInfo && Object.keys(userInfo).length > 0;
		if (!hasUserInfo || isTokenExpired) {
			uni.reLaunch({
				url: '/pages/auth/login'
			});
		} else {
			uni.reLaunch({
				url: '/pages/dashboard/main-dashboard'
			});
		}
	},
	onShow: function() {
			// console.log('App Show');
		},
	onHide: function() {
			// console.log('App Hide');
		}
	};
</script>

<style>
	@import url('./static/iconfont.css');
	/* 导入全局CSS变量 */
	@import './common/css-variables.css';

	/* 为自定义tabBar预留底部空间 */
	page {
		padding-bottom: 120rpx;
	}

	/* 全局样式 */
	page {
		font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
		font-size: 28rpx;
		color: #333333;
		background-color: #f5f7fa;
		min-height: 100vh;
	}

	/* 通用布局类 */
	.container {
		padding: 30rpx;
	}

	.flex-row {
		display: flex;
		flex-direction: row;
	}

	.flex-column {
		display: flex;
		flex-direction: column;
	}

	.items-center {
		align-items: center;
	}

	.justify-between {
		justify-content: space-between;
	}

	.justify-center {
		justify-content: center;
	}

	.text-center {
		text-align: center;
	}

	.flex-1 {
		flex: 1;
	}

	.gap-sm {
		gap: 20rpx;
	}

	.gap-md {
		gap: 30rpx;
	}

	.gap-lg {
		gap: 40rpx;
	}

	/* 全局卡片样式 */
	.card {
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 30rpx;
		box-shadow: var(--card-shadow);
		margin-bottom: 30rpx;
		border: 1rpx solid var(--border-color);
	}

	/* 全局按钮样式 */
	.btn {
		padding: 20rpx 40rpx;
		border-radius: 8rpx;
		font-size: 28rpx;
		line-height: 1.5;
		text-align: center;
	}

	.btn-primary {
		background-color: var(--primary-color);
		color: #ffffff;
	}

	.btn-outline {
		background-color: transparent;
		border: 1rpx solid var(--primary-color);
		color: var(--primary-color);
	}

	.btn-success {
		background-color: var(--success-color);
		color: #ffffff;
	}

	.btn-warning {
		background-color: var(--warning-color);
		color: #ffffff;
	}

	.btn-danger {
		background-color: var(--danger-color);
		color: #ffffff;
	}

	/* 文本和图标样式 */
	.icon-sm {
		font-size: 32rpx;
	}

	.icon-md {
		font-size: 40rpx;
	}

	.icon-lg {
		font-size: 48rpx;
	}

	.text-primary {
		color: var(--text-primary);
	}

	.text-secondary {
		color: var(--text-secondary);
	}

	.text-light {
		color: var(--text-light);
	}

	.text-primary-color {
		color: var(--primary-color);
	}

	.text-success {
		color: var(--success-color);
	}

	.text-warning {
		color: var(--warning-color);
	}

	.text-danger {
		color: var(--danger-color);
	}

	.font-bold {
		font-weight: bold;
	}

	.font-sm {
		font-size: 24rpx;
	}

	.font-md {
		font-size: 28rpx;
	}

	.font-lg {
		font-size: 32rpx;
	}

	.font-xl {
		font-size: 36rpx;
	}

	/* 边距样式 */
	.m-0 {
		margin: 0;
	}

	.mt-0 {
		margin-top: 0;
	}

	.mr-0 {
		margin-right: 0;
	}

	.mb-0 {
		margin-bottom: 0;
	}

	.ml-0 {
		margin-left: 0;
	}

	.m-1 {
		margin: 10rpx;
	}

	.mt-1 {
		margin-top: 10rpx;
	}

	.mr-1 {
		margin-right: 10rpx;
	}

	.mb-1 {
		margin-bottom: 10rpx;
	}

	.ml-1 {
		margin-left: 10rpx;
	}

	.m-2 {
		margin: 20rpx;
	}

	.mt-2 {
		margin-top: 20rpx;
	}

	.mr-2 {
		margin-right: 20rpx;
	}

	.mb-2 {
		margin-bottom: 20rpx;
	}

	.ml-2 {
		margin-left: 20rpx;
	}

	.m-3 {
		margin: 30rpx;
	}

	.mt-3 {
		margin-top: 30rpx;
	}

	.mr-3 {
		margin-right: 30rpx;
	}

	.mb-3 {
		margin-bottom: 30rpx;
	}

	.ml-3 {
		margin-left: 30rpx;
	}

	.p-0 {
		padding: 0;
	}

	.pt-0 {
		padding-top: 0;
	}

	.pr-0 {
		padding-right: 0;
	}

	.pb-0 {
		padding-bottom: 0;
	}

	.pl-0 {
		padding-left: 0;
	}

	.p-1 {
		padding: 10rpx;
	}

	.pt-1 {
		padding-top: 10rpx;
	}

	.pr-1 {
		padding-right: 10rpx;
	}

	.pb-1 {
		padding-bottom: 10rpx;
	}

	.pl-1 {
		padding-left: 10rpx;
	}

	.p-2 {
		padding: 20rpx;
	}

	.pt-2 {
		padding-top: 20rpx;
	}

	.pr-2 {
		padding-right: 20rpx;
	}

	.pb-2 {
		padding-bottom: 20rpx;
	}

	.pl-2 {
		padding-left: 20rpx;
	}

	.p-3 {
		padding: 30rpx;
	}

	.pt-3 {
		padding-top: 30rpx;
	}

	.pr-3 {
		padding-right: 30rpx;
	}

	.pb-3 {
		padding-bottom: 30rpx;
	}

	.pl-3 {
		padding-left: 30rpx;
	}
</style>
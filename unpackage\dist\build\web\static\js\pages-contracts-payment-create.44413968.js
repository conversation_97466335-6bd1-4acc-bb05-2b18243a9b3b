(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-contracts-payment-create"],{"008d":function(t,e,a){"use strict";a.r(e);var n=a("4bf3"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);e["default"]=i.a},"05b2":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"container"},[a("v-uni-view",{staticClass:"page-header"},[a("v-uni-view",{staticClass:"back-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-arrow-left-line"})],1),a("v-uni-text",{staticClass:"page-title"},[t._v("创建收款记录")])],1),a("v-uni-scroll-view",{staticClass:"create-container",attrs:{"scroll-y":!0}},[a("v-uni-form",{on:{submit:function(e){e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.savePayment.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-title"},[a("v-uni-text",{staticClass:"ri-file-list-line"}),a("v-uni-text",[t._v("基本信息")])],1),a("v-uni-view",{staticClass:"form-row"},[a("v-uni-text",{staticClass:"form-label"},[t._v("收款标题"),a("v-uni-text",{staticClass:"required-mark"},[t._v("*")])],1),a("v-uni-input",{staticClass:"form-input",class:{"input-error":t.errors.paymentTitle},attrs:{type:"text",placeholder:"请输入收款标题"},model:{value:t.formData.paymentTitle,callback:function(e){t.$set(t.formData,"paymentTitle",e)},expression:"formData.paymentTitle"}}),a("v-uni-text",{staticClass:"helper-text"},[t._v("例如：项目名称 - 第X期收款")]),t.errors.paymentTitle?a("v-uni-text",{staticClass:"error-message"},[t._v(t._s(t.errors.paymentTitle))]):t._e()],1),a("v-uni-view",{staticClass:"form-grid"},[a("v-uni-view",{staticClass:"form-row"},[a("v-uni-text",{staticClass:"form-label"},[t._v("收款编号")]),a("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"系统自动生成",disabled:!0},model:{value:t.formData.paymentNumber,callback:function(e){t.$set(t.formData,"paymentNumber",e)},expression:"formData.paymentNumber"}}),a("v-uni-text",{staticClass:"helper-text"},[t._v("保存时自动生成")])],1),a("v-uni-view",{staticClass:"form-row"},[a("v-uni-text",{staticClass:"form-label"},[t._v("收款状态"),a("v-uni-text",{staticClass:"required-mark"},[t._v("*")])],1),a("v-uni-picker",{attrs:{value:t.statusIndex,range:t.paymentStatuses,"range-key":"name"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onStatusChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[a("v-uni-text",[t._v(t._s(t.selectedStatus.name||"请选择收款状态"))]),a("v-uni-text",{staticClass:"ri-arrow-down-s-line"})],1)],1),t.errors.paymentStatus?a("v-uni-text",{staticClass:"error-message"},[t._v(t._s(t.errors.paymentStatus))]):t._e()],1),a("v-uni-view",{staticClass:"form-row"},[a("v-uni-text",{staticClass:"form-label"},[t._v("收款日期"),a("v-uni-text",{staticClass:"required-mark"},[t._v("*")])],1),a("v-uni-view",{staticClass:"date-input"},[a("v-uni-picker",{attrs:{mode:"date",value:t.formData.paymentDate},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onDateChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[a("v-uni-text",[t._v(t._s(t.formData.paymentDate||"请选择日期"))]),a("v-uni-text",{staticClass:"ri-calendar-line"})],1)],1)],1),t.errors.paymentDate?a("v-uni-text",{staticClass:"error-message"},[t._v(t._s(t.errors.paymentDate))]):t._e()],1),a("v-uni-view",{staticClass:"form-row"},[a("v-uni-text",{staticClass:"form-label"},[t._v("关联发票"),a("v-uni-text",{staticClass:"required-mark"},[t._v("*")])],1),a("v-uni-picker",{attrs:{value:t.invoiceIndex,range:t.invoices,"range-key":"name"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onInvoiceChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[a("v-uni-text",[t._v(t._s(t.selectedInvoice.name||"请选择关联发票"))]),a("v-uni-text",{staticClass:"ri-arrow-down-s-line"})],1)],1),t.errors.relatedInvoice?a("v-uni-text",{staticClass:"error-message"},[t._v(t._s(t.errors.relatedInvoice))]):t._e()],1),a("v-uni-view",{staticClass:"form-row"},[a("v-uni-text",{staticClass:"form-label"},[t._v("关联合同")]),a("v-uni-picker",{attrs:{value:t.contractIndex,range:t.contracts,"range-key":"name"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onContractChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[a("v-uni-text",[t._v(t._s(t.selectedContract.name||"请选择关联合同"))]),a("v-uni-text",{staticClass:"ri-arrow-down-s-line"})],1)],1)],1)],1)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-title"},[a("v-uni-text",{staticClass:"ri-user-3-line"}),a("v-uni-text",[t._v("客户信息")])],1),a("v-uni-view",{staticClass:"form-row"},[a("v-uni-text",{staticClass:"form-label"},[t._v("客户名称"),a("v-uni-text",{staticClass:"required-mark"},[t._v("*")])],1),a("v-uni-picker",{attrs:{value:t.customerIndex,range:t.customers,"range-key":"name"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onCustomerChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[a("v-uni-text",[t._v(t._s(t.selectedCustomer.name||"请选择客户"))]),a("v-uni-text",{staticClass:"ri-arrow-down-s-line"})],1)],1),a("v-uni-text",{staticClass:"helper-text"},[t._v("选择发票后将自动关联客户信息")]),t.errors.customer?a("v-uni-text",{staticClass:"error-message"},[t._v(t._s(t.errors.customer))]):t._e()],1),a("v-uni-view",{staticClass:"form-grid"},[a("v-uni-view",{staticClass:"form-row"},[a("v-uni-text",{staticClass:"form-label"},[t._v("联系人")]),a("v-uni-picker",{attrs:{value:t.contactIndex,range:t.contacts,"range-key":"name",disabled:!t.formData.customer},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onContactChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-view"},[a("v-uni-text",[t._v(t._s(t.selectedContact.name||"请选择联系人"))]),a("v-uni-text",{staticClass:"ri-arrow-down-s-line"})],1)],1)],1),a("v-uni-view",{staticClass:"form-row"},[a("v-uni-text",{staticClass:"form-label"},[t._v("客户账号")]),a("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入客户银行账号"},model:{value:t.formData.customerBankNumber,callback:function(e){t.$set(t.formData,"customerBankNumber",e)},expression:"formData.customerBankNumber"}})],1),a("v-uni-view",{staticClass:"form-row full-width"},[a("v-uni-text",{staticClass:"form-label"},[t._v("开户行")]),a("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入开户行名称"},model:{value:t.formData.customerBankName,callback:function(e){t.$set(t.formData,"customerBankName",e)},expression:"formData.customerBankName"}})],1)],1)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-title"},[a("v-uni-text",{staticClass:"ri-money-cny-circle-line"}),a("v-uni-text",[t._v("收款金额")])],1),a("v-uni-view",{staticClass:"form-grid"},[a("v-uni-view",{staticClass:"form-row"},[a("v-uni-text",{staticClass:"form-label"},[t._v("应收金额"),a("v-uni-text",{staticClass:"required-mark"},[t._v("*")])],1),a("v-uni-view",{staticClass:"input-addon"},[a("v-uni-text",{staticClass:"input-addon-text"},[t._v("¥")]),a("v-uni-input",{staticClass:"form-input",class:{"input-error":t.errors.expectedAmount},attrs:{type:"digit",placeholder:"0.00"},model:{value:t.formData.expectedAmount,callback:function(e){t.$set(t.formData,"expectedAmount",e)},expression:"formData.expectedAmount"}})],1),a("v-uni-text",{staticClass:"helper-text"},[t._v("选择发票后将自动填充")]),t.errors.expectedAmount?a("v-uni-text",{staticClass:"error-message"},[t._v(t._s(t.errors.expectedAmount))]):t._e()],1),a("v-uni-view",{staticClass:"form-row"},[a("v-uni-text",{staticClass:"form-label"},[t._v("实收金额"),a("v-uni-text",{staticClass:"required-mark"},[t._v("*")])],1),a("v-uni-view",{staticClass:"input-addon"},[a("v-uni-text",{staticClass:"input-addon-text"},[t._v("¥")]),a("v-uni-input",{staticClass:"form-input",class:{"input-error":t.errors.actualAmount},attrs:{type:"digit",placeholder:"0.00"},model:{value:t.formData.actualAmount,callback:function(e){t.$set(t.formData,"actualAmount",e)},expression:"formData.actualAmount"}})],1),t.errors.actualAmount?a("v-uni-text",{staticClass:"error-message"},[t._v(t._s(t.errors.actualAmount))]):t._e()],1)],1),a("v-uni-view",{staticClass:"form-row"},[a("v-uni-text",{staticClass:"form-label"},[t._v("收款方式"),a("v-uni-text",{staticClass:"required-mark"},[t._v("*")])],1),a("v-uni-scroll-view",{staticClass:"payment-method-group",attrs:{"scroll-x":!0}},t._l(t.paymentMethods,(function(e,n){return a("v-uni-view",{key:n,class:["payment-method-option",t.formData.paymentMethod===e.value?"selected":""],on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.selectPaymentMethod(e.value)}}},[a("v-uni-text",{class:["payment-method-icon",e.icon]}),a("v-uni-text",{staticClass:"payment-method-label"},[t._v(t._s(e.label))])],1)})),1),t.errors.paymentMethod?a("v-uni-text",{staticClass:"error-message"},[t._v(t._s(t.errors.paymentMethod))]):t._e()],1),a("v-uni-view",{staticClass:"form-row"},[a("v-uni-text",{staticClass:"form-label"},[t._v("交易参考号")]),a("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入交易参考号或流水号"},model:{value:t.formData.transactionReference,callback:function(e){t.$set(t.formData,"transactionReference",e)},expression:"formData.transactionReference"}})],1)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-title"},[a("v-uni-text",{staticClass:"ri-chat-1-line"}),a("v-uni-text",[t._v("备注信息")])],1),a("v-uni-view",{staticClass:"form-row"},[a("v-uni-text",{staticClass:"form-label"},[t._v("备注内容")]),a("v-uni-textarea",{staticClass:"form-input textarea",attrs:{placeholder:"请输入备注信息，如收款特殊情况说明等"},model:{value:t.formData.notes,callback:function(e){t.$set(t.formData,"notes",e)},expression:"formData.notes"}})],1)],1)],1)],1),a("v-uni-view",{staticClass:"action-bar"},[a("v-uni-button",{staticClass:"action-btn draft-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveDraft.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-draft-line"}),a("v-uni-text",[t._v("保存草稿")])],1),a("v-uni-button",{staticClass:"action-btn save-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.savePayment.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-save-line"}),a("v-uni-text",[t._v("创建收款")])],1)],1)],1)},i=[]},"2c7f":function(t,e,a){"use strict";a.r(e);var n=a("05b2"),i=a("008d");for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);a("3489");var o=a("828b"),r=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"f819495e",null,!1,n["a"],void 0);e["default"]=r.exports},3489:function(t,e,a){"use strict";var n=a("aa2d"),i=a.n(n);i.a},"36d8":function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,".container[data-v-f819495e]{display:flex;flex-direction:column;height:100vh;background-color:#f5f5f5}.page-header[data-v-f819495e]{display:flex;align-items:center;justify-content:space-between;padding:12px 16px;border-bottom:1px solid #eee;background-color:#fff;z-index:10}.page-title[data-v-f819495e]{font-size:18px;font-weight:700;color:#333}.back-button[data-v-f819495e]{color:#666;font-size:24px}.create-container[data-v-f819495e]{flex:1;padding:12px;margin-bottom:80px}.form-section[data-v-f819495e]{background-color:#fff;border-radius:8px;padding:16px;margin-bottom:12px;border:1px solid #eee;box-shadow:0 2px 4px rgba(0,0,0,.05)}.section-title[data-v-f819495e]{font-size:16px;font-weight:600;margin-bottom:16px;color:#333;display:flex;align-items:center;gap:8px}.form-row[data-v-f819495e]{margin-bottom:12px}.form-label[data-v-f819495e]{display:block;font-size:14px;color:#666;margin-bottom:4px}.required-mark[data-v-f819495e]{color:#f56c6c}.form-input[data-v-f819495e]{width:100%;padding:8px 12px;border:1px solid #ddd;border-radius:8px;font-size:14px;color:#333;background-color:#fff;box-sizing:border-box}.form-input[data-v-f819495e]:focus{border-color:#3a86ff;outline:none;box-shadow:0 0 0 2px rgba(58,134,255,.1)}.picker-view[data-v-f819495e]{display:flex;justify-content:space-between;align-items:center;width:100%;padding:8px 12px;border:1px solid #ddd;border-radius:8px;font-size:14px;color:#333;background-color:#fff;box-sizing:border-box}.form-grid[data-v-f819495e]{display:grid;grid-template-columns:repeat(2,1fr);gap:12px}.full-width[data-v-f819495e]{grid-column:1/-1}.input-addon[data-v-f819495e]{position:relative}.input-addon .form-input[data-v-f819495e]{padding-left:24px}.input-addon-text[data-v-f819495e]{position:absolute;left:12px;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#666;font-weight:500}.payment-method-group[data-v-f819495e]{display:flex;white-space:nowrap;margin-bottom:12px}.payment-method-option[data-v-f819495e]{display:inline-flex;flex-direction:column;align-items:center;gap:4px;padding:12px;border:1px solid #eee;border-radius:8px;min-width:80px;margin-right:8px}.payment-method-option.selected[data-v-f819495e]{border-color:#3a86ff;background-color:rgba(58,134,255,.1)}.payment-method-icon[data-v-f819495e]{font-size:24px;color:#666}.payment-method-option.selected .payment-method-icon[data-v-f819495e]{color:#3a86ff}.payment-method-label[data-v-f819495e]{font-size:12px;color:#666;text-align:center}.payment-method-option.selected .payment-method-label[data-v-f819495e]{color:#3a86ff;font-weight:500}.textarea[data-v-f819495e]{min-height:100px}.error-message[data-v-f819495e]{color:#f56c6c;font-size:12px;margin-top:4px}.input-error[data-v-f819495e]{border-color:#f56c6c}.helper-text[data-v-f819495e]{font-size:12px;color:#999;margin-top:4px}.action-bar[data-v-f819495e]{position:fixed;bottom:0;left:0;right:0;display:flex;justify-content:space-between;padding:12px;background-color:#fff;border-top:1px solid #eee;z-index:100}.action-btn[data-v-f819495e]{display:flex;align-items:center;justify-content:center;gap:4px;padding:8px 12px;border-radius:8px;font-size:14px;font-weight:500}.save-button[data-v-f819495e]{background-color:#3a86ff;color:#fff;border:none;flex:1;margin-left:12px}.draft-button[data-v-f819495e]{background-color:#f5f5f5;color:#333;border:1px solid #ddd}.date-input[data-v-f819495e]{position:relative}",""]),t.exports=e},"4bf3":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("8f71"),a("bf0f"),a("795c"),a("c223"),a("bd06"),a("0c26"),a("5c47"),a("0506");var n={data:function(){return{formData:{paymentTitle:"",paymentNumber:"",paymentStatus:"pending",paymentDate:this.getCurrentDate(),relatedInvoice:"",relatedContract:"",customer:"",contactPerson:"",customerBankNumber:"",customerBankName:"",expectedAmount:"",actualAmount:"",paymentMethod:"bankTransfer",transactionReference:"",notes:""},paymentStatuses:[{code:"completed",name:"已完成"},{code:"partial",name:"部分收款"},{code:"pending",name:"待收款"},{code:"overdue",name:"逾期未收"}],invoices:[{id:"",name:"请选择关联发票"},{id:"INV-2023-11-001",name:"INV-2023-11-001 (¥290,975.00)",amount:"290975.00",customerId:"1"},{id:"INV-2023-10-002",name:"INV-2023-10-002 (¥120,000.00)",amount:"120000.00",customerId:"2"},{id:"INV-2023-09-001",name:"INV-2023-09-001 (¥168,000.00)",amount:"168000.00",customerId:"3"}],contracts:[{id:"",name:"请选择关联合同"},{id:"CT-2023-09-001",name:"CT-2023-09-001 (企业系统集成项目合同)"},{id:"CT-2023-08-001",name:"CT-2023-08-001 (软件定制开发合同)"},{id:"CT-2023-07-001",name:"CT-2023-07-001 (年度维护服务合同)"}],customers:[{id:"",name:"请选择客户"},{id:"1",name:"上海智能科技有限公司"},{id:"2",name:"北京数字科技有限公司"},{id:"3",name:"深圳创新电子有限公司"}],contacts:[{id:"",name:"请选择联系人",customerId:""},{id:"1",name:"张总经理 (***********)",customerId:"1"},{id:"2",name:"李财务 (***********)",customerId:"1"},{id:"3",name:"王经理 (***********)",customerId:"2"},{id:"4",name:"赵总监 (***********)",customerId:"3"}],paymentMethods:[{value:"bankTransfer",label:"银行转账",icon:"ri-bank-line"},{value:"alipay",label:"支付宝",icon:"ri-alipay-line"},{value:"wechat",label:"微信支付",icon:"ri-wechat-pay-line"},{value:"cash",label:"现金",icon:"ri-money-cny-box-line"},{value:"other",label:"其他",icon:"ri-more-line"}],statusIndex:2,invoiceIndex:0,contractIndex:0,customerIndex:0,contactIndex:0,errors:{}}},computed:{selectedStatus:function(){return this.paymentStatuses[this.statusIndex]||{}},selectedInvoice:function(){return this.invoices[this.invoiceIndex]||{}},selectedContract:function(){return this.contracts[this.contractIndex]||{}},selectedCustomer:function(){return this.customers[this.customerIndex]||{}},selectedContact:function(){return this.contacts[this.contactIndex]||{}},availableContacts:function(){var t=this;return this.formData.customer?this.contacts.filter((function(e){return""===e.customerId||e.customerId===t.formData.customer})):[this.contacts[0]]}},methods:{getCurrentDate:function(){var t=new Date,e=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0"),n=String(t.getDate()).padStart(2,"0");return"".concat(e,"-").concat(a,"-").concat(n)},goBack:function(){uni.navigateBack()},onStatusChange:function(t){this.statusIndex=t.detail.value,this.formData.paymentStatus=this.paymentStatuses[this.statusIndex].code},onDateChange:function(t){this.formData.paymentDate=t.detail.value},onInvoiceChange:function(t){this.invoiceIndex=t.detail.value;var e=this.invoices[this.invoiceIndex];e&&e.id?(this.formData.relatedInvoice=e.id,this.formData.expectedAmount=e.amount,e.customerId&&(this.formData.customer=e.customerId,this.customerIndex=this.customers.findIndex((function(t){return t.id===e.customerId})),this.formData.contactPerson="",this.contactIndex=0)):(this.formData.relatedInvoice="",this.formData.expectedAmount="")},onContractChange:function(t){this.contractIndex=t.detail.value,this.formData.relatedContract=this.contracts[this.contractIndex].id},onCustomerChange:function(t){this.customerIndex=t.detail.value,this.formData.customer=this.customers[this.customerIndex].id,this.formData.contactPerson="",this.contactIndex=0},onContactChange:function(t){this.contactIndex=t.detail.value,this.formData.contactPerson=this.contacts[this.contactIndex].id},selectPaymentMethod:function(t){this.formData.paymentMethod=t},validateForm:function(){this.errors={};var t=!0;return this.formData.paymentTitle.trim()||(this.errors.paymentTitle="请输入收款标题",t=!1),this.formData.paymentStatus||(this.errors.paymentStatus="请选择收款状态",t=!1),this.formData.paymentDate||(this.errors.paymentDate="请选择收款日期",t=!1),this.formData.relatedInvoice||(this.errors.relatedInvoice="请选择关联发票",t=!1),this.formData.customer||(this.errors.customer="请选择客户",t=!1),this.formData.expectedAmount?/^\d+(\.\d{1,2})?$/.test(this.formData.expectedAmount)||(this.errors.expectedAmount="请输入有效的金额格式 (例如: 123.45)",t=!1):(this.errors.expectedAmount="请输入应收金额",t=!1),this.formData.actualAmount?/^\d+(\.\d{1,2})?$/.test(this.formData.actualAmount)||(this.errors.actualAmount="请输入有效的金额格式 (例如: 123.45)",t=!1):(this.errors.actualAmount="请输入实收金额",t=!1),this.formData.paymentMethod||(this.errors.paymentMethod="请选择收款方式",t=!1),t},saveDraft:function(){uni.showToast({title:"收款记录已保存为草稿！",icon:"success",duration:2e3}),setTimeout((function(){uni.navigateBack()}),1500)},savePayment:function(){this.validateForm()?(uni.showLoading({title:"保存中..."}),setTimeout((function(){uni.hideLoading(),uni.showToast({title:"收款记录已创建成功！",icon:"success"}),setTimeout((function(){uni.navigateBack()}),1500)}),1e3)):uni.showToast({title:"请完善必填信息",icon:"none"})}}};e.default=n},aa2d:function(t,e,a){var n=a("36d8");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("967d").default;i("73ab203a",n,!0,{sourceMap:!1,shadowMode:!1})}}]);
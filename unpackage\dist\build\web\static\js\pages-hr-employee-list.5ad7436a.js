(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-hr-employee-list"],{"30f7":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},a("7a76"),a("c9b5")},"379f":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"container"},[a("v-uni-view",{staticClass:"page-header"},[a("v-uni-view",{staticClass:"back-button",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.goBack.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-arrow-left-line"})],1),a("v-uni-text",{staticClass:"page-title"},[e._v("员工管理")]),a("v-uni-view",{staticClass:"header-actions"},[a("v-uni-button",{staticClass:"action-button",attrs:{type:"button"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showFilterOptions.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-filter-3-line"})],1),a("v-uni-button",{staticClass:"action-button",attrs:{type:"button"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showSortOptions.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-sort-desc"})],1)],1)],1),a("v-uni-view",{staticClass:"search-bar"},[a("v-uni-text",{staticClass:"ri-search-line search-icon"}),a("v-uni-input",{staticClass:"search-input",attrs:{type:"text",placeholder:"搜索员工姓名、部门、职位..."},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.onSearch.apply(void 0,arguments)}},model:{value:e.searchQuery,callback:function(t){e.searchQuery=t},expression:"searchQuery"}})],1),a("v-uni-scroll-view",{staticClass:"filter-bar",attrs:{"scroll-x":!0}},e._l(e.filters,(function(t,i){return a("v-uni-view",{key:i,staticClass:"filter-button",class:{active:e.currentFilter===t.value},on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.setFilter(t.value)}}},[a("v-uni-text",[e._v(e._s(t.label))])],1)})),1),a("v-uni-view",{staticClass:"stats-panel"},[a("v-uni-view",{staticClass:"stats-card"},[a("v-uni-text",{staticClass:"stats-title"},[e._v("员工总数")]),a("v-uni-text",{staticClass:"stats-value"},[e._v(e._s(e.totalEmployees))])],1),a("v-uni-view",{staticClass:"stats-card"},[a("v-uni-text",{staticClass:"stats-title"},[e._v("本月新入职")]),a("v-uni-text",{staticClass:"stats-value"},[e._v(e._s(e.newEmployees))])],1),a("v-uni-view",{staticClass:"stats-card"},[a("v-uni-text",{staticClass:"stats-title"},[e._v("离职率")]),a("v-uni-text",{staticClass:"stats-value"},[e._v(e._s(e.turnoverRate)+"%")])],1)],1),a("v-uni-scroll-view",{staticClass:"employee-list",attrs:{"scroll-y":!0,"refresher-enabled":!0,"refresher-triggered":e.isRefreshing},on:{refresherrefresh:function(t){arguments[0]=t=e.$handleEvent(t),e.onRefresh.apply(void 0,arguments)},scrolltolower:function(t){arguments[0]=t=e.$handleEvent(t),e.loadMore.apply(void 0,arguments)}}},[e.filteredEmployees.length>0?a("v-uni-view",[e._l(e.filteredEmployees,(function(t,i){return a("v-uni-view",{key:i,staticClass:"employee-item",on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.goToDetail(t.id)}}},[a("v-uni-view",{staticClass:"employee-header"},[a("v-uni-view",{staticClass:"employee-avatar"},[a("v-uni-image",{attrs:{src:t.avatar||"/static/images/default-avatar.png",mode:"aspectFill"}})],1),a("v-uni-view",{staticClass:"employee-title-row"},[a("v-uni-text",{staticClass:"employee-name"},[e._v(e._s(t.name))]),a("v-uni-text",{class:["employee-status","active"===t.status?"status-active":"status-inactive"]},[e._v(e._s("active"===t.status?"在职":"离职"))])],1)],1),a("v-uni-view",{staticClass:"employee-info"},[a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"info-label"},[e._v("工号")]),a("v-uni-text",{staticClass:"info-value"},[e._v(e._s(t.employeeId))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"info-label"},[e._v("部门")]),a("v-uni-text",{staticClass:"info-value"},[e._v(e._s(t.department))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"info-label"},[e._v("职位")]),a("v-uni-text",{staticClass:"info-value"},[e._v(e._s(t.position))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"info-label"},[e._v("入职日期")]),a("v-uni-text",{staticClass:"info-value"},[e._v(e._s(t.hireDate))])],1)],1),a("v-uni-view",{staticClass:"employee-footer"},[a("v-uni-view",{staticClass:"employee-contact"},[a("v-uni-text",{staticClass:"contact-phone ri-phone-line",on:{click:function(a){a.stopPropagation(),arguments[0]=a=e.$handleEvent(a),e.callEmployee(t)}}},[e._v(e._s(t.phone))]),a("v-uni-text",{staticClass:"contact-email ri-mail-line",on:{click:function(a){a.stopPropagation(),arguments[0]=a=e.$handleEvent(a),e.emailEmployee(t)}}},[e._v(e._s(t.email))])],1),a("v-uni-view",{staticClass:"employee-actions"},[a("v-uni-view",{staticClass:"action-icon",on:{click:function(a){a.stopPropagation(),arguments[0]=a=e.$handleEvent(a),e.viewEmployeePerformance(t.id)}}},[a("v-uni-text",{staticClass:"ri-line-chart-line"})],1),a("v-uni-view",{staticClass:"action-icon",on:{click:function(a){a.stopPropagation(),arguments[0]=a=e.$handleEvent(a),e.editEmployee(t.id)}}},[a("v-uni-text",{staticClass:"ri-edit-line"})],1)],1)],1)],1)})),e.loadingMore?a("v-uni-view",{staticClass:"loading-more"},[a("v-uni-text",[e._v("加载更多...")])],1):e._e()],2):a("v-uni-view",{staticClass:"empty-state"},[a("v-uni-text",{staticClass:"empty-icon ri-user-3-line"}),a("v-uni-text",{staticClass:"empty-title"},[e._v("暂无员工记录")]),a("v-uni-text",{staticClass:"empty-text"},[e._v("添加您的第一位员工，开始管理团队")]),a("v-uni-button",{staticClass:"create-button",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.createEmployee.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-add-line"}),a("v-uni-text",[e._v("添加员工")])],1)],1)],1),a("v-uni-view",{staticClass:"floating-button",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.createEmployee.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ri-add-line"})],1)],1)},n=[]},"40e1":function(e,t,a){"use strict";a.r(t);var i=a("379f"),n=a("cbfe");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("6abc");var s=a("828b"),l=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"0e71d9ba",null,!1,i["a"],void 0);t["default"]=l.exports},4733:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if(Array.isArray(e))return(0,i.default)(e)};var i=function(e){return e&&e.__esModule?e:{default:e}}(a("8d0b"))},"66bd":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,".container[data-v-0e71d9ba]{padding-bottom:%?120?%;background-color:#f5f7fa;min-height:100vh}.page-header[data-v-0e71d9ba]{display:flex;align-items:center;padding:%?20?% %?30?%;background-color:#fff;position:relative;border-bottom:%?1?% solid #eaeaea}.back-button[data-v-0e71d9ba]{font-size:%?40?%;color:#333;padding:%?10?%}.page-title[data-v-0e71d9ba]{flex:1;text-align:center;font-size:%?36?%;font-weight:500;color:#333}.header-actions[data-v-0e71d9ba]{display:flex;align-items:center}.action-button[data-v-0e71d9ba]{background:none;border:none;font-size:%?40?%;color:#666;padding:%?10?%;margin-left:%?20?%}.search-bar[data-v-0e71d9ba]{display:flex;align-items:center;padding:%?20?% %?30?%;background-color:#fff;border-bottom:%?1?% solid #eaeaea}.search-icon[data-v-0e71d9ba]{margin-right:%?20?%;color:#999;font-size:%?32?%}.search-input[data-v-0e71d9ba]{flex:1;height:%?70?%;background-color:#f5f7fa;border-radius:%?35?%;padding:0 %?30?%;font-size:%?28?%}.filter-bar[data-v-0e71d9ba]{display:flex;white-space:nowrap;background-color:#fff;border-bottom:%?1?% solid #eaeaea;padding:%?20?% 0}.filter-button[data-v-0e71d9ba]{display:inline-block;padding:%?10?% %?30?%;margin:0 %?15?%;border-radius:%?30?%;font-size:%?28?%;color:#666;background-color:#f5f7fa}.filter-button.active[data-v-0e71d9ba]{background-color:#4a6fff;color:#fff}.stats-panel[data-v-0e71d9ba]{display:flex;justify-content:space-between;padding:%?20?% %?30?%;background-color:#fff;margin-bottom:%?20?%;box-shadow:0 %?2?% %?10?% rgba(0,0,0,.05)}.stats-card[data-v-0e71d9ba]{flex:1;display:flex;flex-direction:column;align-items:center;padding:%?15?% 0}.stats-title[data-v-0e71d9ba]{font-size:%?24?%;color:#999;margin-bottom:%?10?%}.stats-value[data-v-0e71d9ba]{font-size:%?36?%;font-weight:500;color:#333}.employee-list[data-v-0e71d9ba]{flex:1;height:calc(100vh - %?300?%)}.employee-item[data-v-0e71d9ba]{margin:%?20?% %?30?%;border-radius:%?20?%;background-color:#fff;box-shadow:0 %?2?% %?10?% rgba(0,0,0,.05);overflow:hidden}.employee-header[data-v-0e71d9ba]{display:flex;align-items:center;padding:%?20?% %?30?%;border-bottom:%?1?% solid #eaeaea}.employee-avatar[data-v-0e71d9ba]{width:%?100?%;height:%?100?%;border-radius:50%;overflow:hidden;background-color:#eaeaea;margin-right:%?20?%}.employee-avatar uni-image[data-v-0e71d9ba]{width:100%;height:100%}.employee-title-row[data-v-0e71d9ba]{flex:1;display:flex;justify-content:space-between;align-items:center}.employee-name[data-v-0e71d9ba]{font-size:%?32?%;font-weight:500;color:#333}.employee-status[data-v-0e71d9ba]{font-size:%?24?%;padding:%?6?% %?15?%;border-radius:%?15?%}.status-active[data-v-0e71d9ba]{background-color:#e6f7ed;color:#52c41a}.status-inactive[data-v-0e71d9ba]{background-color:#fff1f0;color:#ff4d4f}.employee-info[data-v-0e71d9ba]{padding:%?20?% %?30?%;display:grid;grid-template-columns:repeat(2,1fr);gap:%?20?%}.info-item[data-v-0e71d9ba]{display:flex;flex-direction:column}.info-label[data-v-0e71d9ba]{font-size:%?24?%;color:#999;margin-bottom:%?8?%}.info-value[data-v-0e71d9ba]{font-size:%?28?%;color:#333}.employee-footer[data-v-0e71d9ba]{display:flex;justify-content:space-between;align-items:center;padding:%?20?% %?30?%;border-top:%?1?% solid #eaeaea;background-color:#f9fafc}.employee-contact[data-v-0e71d9ba]{display:flex;flex-direction:column}.contact-phone[data-v-0e71d9ba], .contact-email[data-v-0e71d9ba]{font-size:%?24?%;color:#666;margin-bottom:%?8?%}.employee-actions[data-v-0e71d9ba]{display:flex;align-items:center}.action-icon[data-v-0e71d9ba]{width:%?60?%;height:%?60?%;border-radius:%?30?%;display:flex;justify-content:center;align-items:center;margin-left:%?15?%;background-color:#f0f2f5;color:#666;font-size:%?32?%}.loading-more[data-v-0e71d9ba]{text-align:center;padding:%?30?% 0;color:#999;font-size:%?26?%}.empty-state[data-v-0e71d9ba]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?100?% 0}.empty-icon[data-v-0e71d9ba]{font-size:%?120?%;color:#ccc;margin-bottom:%?20?%}.empty-title[data-v-0e71d9ba]{font-size:%?34?%;color:#333;margin-bottom:%?10?%;font-weight:500}.empty-text[data-v-0e71d9ba]{font-size:%?28?%;color:#999;margin-bottom:%?40?%;text-align:center}.create-button[data-v-0e71d9ba]{display:flex;align-items:center;justify-content:center;padding:%?20?% %?40?%;background-color:#4a6fff;color:#fff;border-radius:%?40?%;font-size:%?30?%}.create-button uni-text[data-v-0e71d9ba]{margin-right:%?10?%}.floating-button[data-v-0e71d9ba]{position:fixed;right:%?40?%;bottom:%?40?%;width:%?100?%;height:%?100?%;border-radius:50%;background-color:#4a6fff;display:flex;justify-content:center;align-items:center;color:#fff;font-size:%?50?%;box-shadow:0 %?4?% %?16?% rgba(74,111,255,.4);z-index:999}",""]),e.exports=t},"6abc":function(e,t,a){"use strict";var i=a("e5c0"),n=a.n(i);n.a},b7c7:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return(0,i.default)(e)||(0,n.default)(e)||(0,o.default)(e)||(0,s.default)()};var i=l(a("4733")),n=l(a("d14d")),o=l(a("5d6b")),s=l(a("30f7"));function l(e){return e&&e.__esModule?e:{default:e}}},cbfe:function(e,t,a){"use strict";a.r(t);var i=a("f5f2"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);t["default"]=n.a},d14d:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},a("01a2"),a("e39c"),a("bf0f"),a("844d"),a("18f7"),a("de6c"),a("08eb")},e5c0:function(e,t,a){var i=a("66bd");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var n=a("967d").default;n("66287c00",i,!0,{sourceMap:!1,shadowMode:!1})},f5f2:function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("8f71"),a("bf0f"),a("4626"),a("5ac7");var n=i(a("b7c7")),o={data:function(){return{searchQuery:"",currentFilter:"all",filters:[{label:"全部",value:"all"},{label:"在职",value:"active"},{label:"离职",value:"inactive"},{label:"销售部",value:"sales"},{label:"技术部",value:"tech"},{label:"市场部",value:"marketing"},{label:"财务部",value:"finance"},{label:"人事部",value:"hr"}],employees:[{id:"1",name:"张三",status:"active",employeeId:"EMP001",department:"销售部",position:"销售经理",hireDate:"2020-06-15",phone:"13812345678",email:"<EMAIL>",avatar:"/static/images/avatars/avatar1.png"},{id:"2",name:"李四",status:"active",employeeId:"EMP002",department:"技术部",position:"高级开发工程师",hireDate:"2020-08-20",phone:"13987654321",email:"<EMAIL>",avatar:"/static/images/avatars/avatar2.png"},{id:"3",name:"王五",status:"active",employeeId:"EMP003",department:"市场部",position:"市场总监",hireDate:"2021-03-10",phone:"13765432198",email:"<EMAIL>",avatar:"/static/images/avatars/avatar3.png"},{id:"4",name:"赵六",status:"inactive",employeeId:"EMP004",department:"财务部",position:"财务经理",hireDate:"2019-12-05",phone:"13698765432",email:"<EMAIL>",avatar:"/static/images/avatars/avatar4.png"}],isRefreshing:!1,loadingMore:!1,page:1,hasMore:!0,totalEmployees:65,newEmployees:3,turnoverRate:2.5}},computed:{filteredEmployees:function(){var e=this,t=(0,n.default)(this.employees);if(this.searchQuery){var a=this.searchQuery.toLowerCase();t=t.filter((function(e){return e.name.toLowerCase().includes(a)||e.department.toLowerCase().includes(a)||e.position.toLowerCase().includes(a)||e.employeeId.toLowerCase().includes(a)}))}if("all"!==this.currentFilter)if("active"===this.currentFilter||"inactive"===this.currentFilter)t=t.filter((function(t){return t.status===e.currentFilter}));else{var i={sales:"销售部",tech:"技术部",marketing:"市场部",finance:"财务部",hr:"人事部"}[this.currentFilter];t=t.filter((function(e){return e.department===i}))}return t}},methods:{goBack:function(){uni.navigateBack()},setFilter:function(e){this.currentFilter=e},onSearch:function(e){this.searchQuery=e.detail.value},onRefresh:function(){var e=this;this.isRefreshing=!0,setTimeout((function(){e.isRefreshing=!1}),1500)},loadMore:function(){var e=this;this.hasMore&&!this.loadingMore&&(this.loadingMore=!0,setTimeout((function(){e.loadingMore=!1,e.page>=3?e.hasMore=!1:e.page++}),1500))},goToDetail:function(e){uni.navigateTo({url:"/pages/hr/employee-detail?id=".concat(e)})},createEmployee:function(){uni.navigateTo({url:"/pages/hr/employee-create"})},editEmployee:function(e){uni.navigateTo({url:"/pages/hr/employee-edit?id=".concat(e)})},viewEmployeePerformance:function(e){uni.navigateTo({url:"/pages/hr/employee-performance?id=".concat(e)})},callEmployee:function(e){uni.makePhoneCall({phoneNumber:e.phone,success:function(){console.log("拨打电话成功")},fail:function(e){console.error("拨打电话失败",e)}})},emailEmployee:function(e){uni.setClipboardData({data:e.email,success:function(){uni.showToast({title:"邮箱已复制",icon:"success"})}})},showFilterOptions:function(){uni.showActionSheet({itemList:["按入职日期排序","按部门筛选","按职位筛选","自定义筛选"],success:function(e){console.log("选择了: "+e.tapIndex)}})},showSortOptions:function(){uni.showActionSheet({itemList:["按姓名排序","按入职日期排序","按工号排序","按部门排序"],success:function(e){console.log("选择了: "+e.tapIndex)}})}}};t.default=o}}]);
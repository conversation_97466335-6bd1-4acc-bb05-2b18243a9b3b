// 检查登录态是否有效
export function checkLoginStatus() {
  // 1. 获取本地存储的 expirationTime 和 userInfo
  const expirationTime = uni.getStorageSync('expirationTime');
  const userInfo = uni.getStorageSync('userInfo');
  // 2. 判断 expirationTime 是否过期
  const isTokenExpired = expirationTime && new Date().getTime() > new Date(expirationTime).getTime();
  // 3. 检查 userInfo 是否存在
  const hasUserInfo = userInfo && Object.keys(userInfo).length > 0;
  if (!hasUserInfo || isTokenExpired) {
    uni.showToast({
      title: '登录已过期，请重新登录',
      icon: 'none'
    });
    uni.reLaunch({
      url: '/pages/auth/login'
    });
    return false;
  }
  return true;
}
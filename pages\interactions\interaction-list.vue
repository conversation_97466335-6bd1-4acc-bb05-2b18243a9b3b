<template>
  <view class="page-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="page-title">沟通记录</view>
      <view class="header-actions">
        <view class="header-icon" @tap="showFilterPanel">
          <svg-icon name="filter" type="svg" size="28"></svg-icon>
        </view>
        <view class="header-icon" @tap="navigateToSearch">
          <svg-icon name="search" type="svg" size="28"></svg-icon>
        </view>
        <view class="header-icon" @tap="navigateToCreate">
          <svg-icon name="add" type="svg" size="28"></svg-icon>
        </view>
      </view>
    </view>

    <!-- 选项卡 -->
    <view class="tabs-container">
      <view
        v-for="(tab, index) in tabs"
        :key="index"
        class="tab-item"
        :class="{ active: currentTab === tab.value }"
        @tap="changeTab(tab.value)"
      >
        <text>{{ tab.label }}</text>
      </view>
    </view>

    <scroll-view scroll-y class="interaction-list" @scrolltolower="loadMore">
      <block v-if="filteredInteractions.length > 0">
        <view 
          v-for="(item, index) in filteredInteractions" 
          :key="index"
          class="interaction-card"
          :data-type="item.type"
          @tap="navigateToDetail(item.id)"
          hover-class="card-hover"
        >
          <view class="interaction-header">
            <view class="interaction-icon" :class="'icon-' + item.type">
              <svg-icon :name="getIconName(item.type)" type="svg" size="28"></svg-icon>
            </view>
            <view class="interaction-title">
              <text class="interaction-type">{{getTypeName(item.type)}}</text>
              <text class="interaction-subject">{{item.subject}}</text>
            </view>
            <text class="interaction-time">{{item.time}}</text>
          </view>
          
          <view class="interaction-detail" v-if="item.relatedObject">
            <text class="detail-label">关联对象</text>
            <text class="detail-value">{{item.relatedObject}}</text>
          </view>
          
          <view class="interaction-detail" v-if="item.contacts">
            <text class="detail-label">{{getContactLabel(item.type)}}</text>
            <text class="detail-value">{{item.contacts}}</text>
          </view>
          
          <view class="interaction-detail" v-if="item.owner">
            <text class="detail-label">负责人</text>
            <text class="detail-value">{{item.owner}}</text>
          </view>
          
          <view class="interaction-content">
            {{item.content}}
          </view>
          
          <!-- 下一步计划 (仅跟进记录) -->
          <view class="next-steps" v-if="item.nextSteps && item.nextSteps.length > 0">
            <view class="next-step-item" v-for="(step, stepIndex) in item.nextSteps" :key="stepIndex">
              {{step}}
            </view>
          </view>
          
          <view class="tag-container" v-if="item.tags && item.tags.length > 0">
            <text class="tag" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">{{tag}}</text>
          </view>
        </view>
      </block>
      
      <!-- 空状态 -->
      <view class="empty-state" v-else>
        <svg-icon name="empty-box" type="svg" size="100"></svg-icon>
        <text class="empty-title">暂无沟通记录</text>
        <text class="empty-desc">点击右上角"+"按钮添加沟通记录</text>
      </view>
      
      <!-- 加载更多 -->
      <view class="loading-more" v-if="showLoadMore">
        <text>加载中...</text>
      </view>
    </scroll-view>

    <!-- 浮动添加按钮 -->
    <view class="fab" @tap="navigateToCreate">
      <svg-icon name="add" type="svg" size="60" color="#FFFFFF"></svg-icon>
    </view>
    
    <!-- 筛选面板 -->
    <view class="modal-filter" v-if="showFilter">
      <view class="modal-mask" @tap="hideFilterPanel"></view>
      <view class="modal-dialog">
        <view class="modal-header">
          <view class="modal-title">筛选条件</view>
          <view class="modal-close" @tap="hideFilterPanel">
            <svg-icon name="close" type="svg" size="32"></svg-icon>
          </view>
        </view>
        
        <view class="modal-body">
          <view class="filter-section">
            <text class="filter-title">记录类型</text>
            <view class="filter-options">
              <view 
                v-for="(option, index) in typeOptions" 
                :key="index"
                class="filter-tag"
                :class="{ active: selectedTypes.includes(option.value) }"
                @tap="toggleTypeFilter(option.value)"
              >
                {{option.label}}
              </view>
            </view>
          </view>
          
          <view class="filter-section">
            <text class="filter-title">关联对象</text>
            <view class="filter-options">
              <view 
                v-for="(option, index) in relatedOptions" 
                :key="index"
                class="filter-tag"
                :class="{ active: selectedRelatedTypes.includes(option.value) }"
                @tap="toggleRelatedFilter(option.value)"
              >
                {{option.label}}
              </view>
            </view>
          </view>
          
          <view class="filter-section">
            <text class="filter-title">时间范围</text>
            <view class="filter-options">
              <view 
                v-for="(option, index) in timeOptions" 
                :key="index"
                class="filter-tag"
                :class="{ active: selectedTimeRange === option.value }"
                @tap="setTimeRangeFilter(option.value)"
              >
                {{option.label}}
              </view>
            </view>
          </view>
        </view>
        
        <view class="modal-footer">
          <view class="filter-actions">
            <button class="btn btn-outline" @tap="resetFilters">重置</button>
            <button class="btn btn-primary" @tap="applyFilters">应用</button>
          </view>
        </view>
      </view>
    </view>
    
    <!-- TabBar -->
    <custom-tab-bar ref="customTabBar"></custom-tab-bar>
  </view>
</template>

<script>
import SvgIcon from '@/components/svg-icon.vue';
import CustomTabBar from '@/components/CustomTabBar.vue';

export default {
  components: {
    SvgIcon,
    CustomTabBar
  },
  data() {
    return {
      currentTab: 'all',
      showFilter: false,
      showLoadMore: false,
      searchText: '',
      
      // 选项卡配置
      tabs: [
        { label: '全部', value: 'all' },
        { label: '客户跟进', value: 'follow-up' },
        { label: '详细沟通', value: 'communication' },
        { label: '我的', value: 'mine' }
      ],
      
      // 筛选选项
      typeOptions: [
        { label: '电话', value: 'call' },
        { label: '会议', value: 'meeting' },
        { label: '邮件', value: 'email' },
        { label: '拜访', value: 'visit' },
        { label: '其他', value: 'other' }
      ],
      relatedOptions: [
        { label: '客户', value: 'customer' },
        { label: '商机', value: 'opportunity' },
        { label: '合同', value: 'contract' },
        { label: '联系人', value: 'contact' }
      ],
      timeOptions: [
        { label: '今天', value: 'today' },
        { label: '昨天', value: 'yesterday' },
        { label: '本周', value: 'thisWeek' },
        { label: '本月', value: 'thisMonth' },
        { label: '全部', value: 'all' }
      ],
      
      // 筛选条件
      selectedTypes: [],
      selectedRelatedTypes: [],
      selectedTimeRange: 'all',
      
      // 数据源
      interactionItems: [
        // 跟进记录示例
        {
          id: '1',
          type: 'call',
          recordType: 'follow-up',
          subject: '电话沟通产品需求',
          time: '今天 15:30',
          relatedObject: '北京科技有限公司',
          contacts: '张经理',
          owner: '李销售',
          content: '与张经理沟通了产品功能和交付时间需求，客户希望在下周一前收到详细方案。',
          nextSteps: ['发送方案', '安排面谈'],
          tags: []
        },
        {
          id: '2',
          type: 'visit',
          recordType: 'follow-up',
          subject: '客户现场拜访',
          time: '昨天 14:00',
          relatedObject: '上海电子科技有限公司',
          contacts: '王总监',
          owner: '赵销售',
          content: '拜访了客户总部，与技术和采购部门进行了深入交流，了解了客户核心需求。',
          nextSteps: ['制定详细方案', '跟进技术需求'],
          tags: ['重要客户']
        },
        
        // 沟通记录示例
        {
          id: '3',
          type: 'meeting',
          recordType: 'communication',
          subject: '项目需求讨论会议',
          time: '2023-10-20 10:00',
          relatedObject: '上海电子科技有限公司 - 企业ERP升级项目',
          contacts: '李总, 王经理, 郑工程师, 赵销售',
          owner: '赵销售',
          content: '与客户进行了2小时的需求讨论，确定了项目范围和主要功能模块。客户希望3个月内完成第一阶段上线，预算有限，需要优化方案。',
          tags: ['需求确认', '进行中']
        },
        {
          id: '4',
          type: 'email',
          recordType: 'communication',
          subject: '发送产品规格说明书',
          time: '2023-10-18 16:30',
          relatedObject: '广州未来科技有限公司 - 数据中心升级项目',
          contacts: '陈经理',
          owner: '刘销售',
          content: '根据客户要求，发送了产品详细规格说明书和初步报价单，等待客户确认。',
          tags: ['跟进中']
        }
      ]
    }
  },
  computed: {
    filteredInteractions() {
      if (this.showFilter) {
        return this.interactionItems;
      }
      
      // 根据tab过滤
      let filtered = [...this.interactionItems];
      
      if (this.currentTab === 'follow-up') {
        filtered = filtered.filter(item => item.recordType === 'follow-up');
      } else if (this.currentTab === 'communication') {
        filtered = filtered.filter(item => item.recordType === 'communication');
      } else if (this.currentTab === 'mine') {
        // 根据当前登录用户过滤(示例使用固定用户名)
        filtered = filtered.filter(item => item.owner === '李销售');
      }
      
      // 如果有搜索关键词，再次过滤
      if (this.searchText) {
        const keyword = this.searchText.toLowerCase();
        filtered = filtered.filter(item => {
          return (
            item.subject.toLowerCase().includes(keyword) ||
            (item.content && item.content.toLowerCase().includes(keyword)) ||
            (item.relatedObject && item.relatedObject.toLowerCase().includes(keyword))
          );
        });
      }
      
      return filtered;
    }
  },
  methods: {
    changeTab(tab) {
      this.currentTab = tab;
    },
    navigateToSearch() {
      uni.navigateTo({
        url: '/pages/common/search?type=interaction'
      });
    },
    navigateToCreate() {
      // 根据当前选中的tab决定创建的记录类型
      let url = '/pages/interactions/interaction-create';
      
      if (this.currentTab === 'follow-up') {
        url += '?recordType=follow-up';
      } else if (this.currentTab === 'communication') {
        url += '?recordType=communication';
      }
      
      uni.navigateTo({ url });
    },
    navigateToDetail(id) {
      // 查找此沟通记录
      const interaction = this.interactionItems.find(item => item.id === id);
      if (!interaction) return;
      
      uni.navigateTo({
        url: `/pages/interactions/interaction-detail?id=${id}&recordType=${interaction.recordType}`
      });
    },
    loadMore() {
      if (this.showLoadMore) return;
      
      this.showLoadMore = true;
      
      // 模拟加载更多数据
      setTimeout(() => {
        this.showLoadMore = false;
        // 这里应该调用API加载更多数据
      }, 1500);
    },
    showFilterPanel() {
      this.showFilter = true;
    },
    hideFilterPanel() {
      this.showFilter = false;
    },
    toggleTypeFilter(type) {
      const index = this.selectedTypes.indexOf(type);
      if (index > -1) {
        this.selectedTypes.splice(index, 1);
      } else {
        this.selectedTypes.push(type);
      }
    },
    toggleRelatedFilter(type) {
      const index = this.selectedRelatedTypes.indexOf(type);
      if (index > -1) {
        this.selectedRelatedTypes.splice(index, 1);
      } else {
        this.selectedRelatedTypes.push(type);
      }
    },
    setTimeRangeFilter(range) {
      this.selectedTimeRange = range;
    },
    resetFilters() {
      this.selectedTypes = [];
      this.selectedRelatedTypes = [];
      this.selectedTimeRange = 'all';
    },
    applyFilters() {
      // 应用筛选条件（实际应用中应该根据筛选条件重新获取数据）
      this.hideFilterPanel();
      
      // 仅作为示例
      uni.showToast({
        title: '筛选已应用',
        icon: 'none'
      });
    },
    getIconName(type) {
      const iconMap = {
        'call': 'phone',
        'meeting': 'team',
        'email': 'mail',
        'visit': 'navigation',
        'note': 'file-list',
        'other': 'message'
      };
      
      return iconMap[type] || 'message';
    },
    getTypeName(type) {
      const typeMap = {
        'call': '电话',
        'meeting': '会议',
        'email': '邮件',
        'visit': '拜访',
        'note': '记录',
        'other': '其他'
      };
      
      return typeMap[type] || '沟通';
    },
    getContactLabel(type) {
      if (type === 'meeting') {
        return '参与人';
      } else if (type === 'email') {
        return '收件人';
      } else {
        return '联系人';
      }
    }
  },
  onLoad() {
    // 加载数据
    // 实际应用中应该从API获取沟通记录列表
    console.log('加载沟通记录列表');
  },
  onShow() {
    // 设置TabBar选中项
    if (typeof this.$refs.customTabBar !== 'undefined') {
      this.$refs.customTabBar.current = 2; // 沟通在TabBar中的位置
    }
  },
  onPullDownRefresh() {
    // 下拉刷新
    setTimeout(() => {
      // 刷新数据
      uni.stopPullDownRefresh();
    }, 1000);
  }
}
</script>

<style>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #e0e0e0;
  background-color: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 30rpx;
}

.header-icon {
  color: #666;
  font-size: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tabs-container {
  display: flex;
  padding: 0 20rpx;
  border-bottom: 1rpx solid #e0e0e0;
  background-color: white;
  position: sticky;
  top: 120rpx;
  z-index: 9;
}

.tab-item {
  padding: 24rpx 40rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #3370ff;
  font-weight: 500;
}

.tab-item.active:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 40rpx;
  right: 40rpx;
  height: 4rpx;
  background-color: #3370ff;
  border-radius: 4rpx;
}

.interaction-list {
  flex: 1;
  padding: 20rpx;
}

.interaction-card {
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.interaction-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.interaction-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  margin-right: 20rpx;
  color: white;
}

.icon-call {
  background-color: #3370ff;
}

.icon-meeting {
  background-color: #722ed1;
}

.icon-email {
  background-color: #13c2c2;
}

.icon-visit {
  background-color: #fa8c16;
}

.icon-note, .icon-other {
  background-color: #52c41a;
}

.interaction-title {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.interaction-type {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.interaction-subject {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.interaction-time {
  font-size: 24rpx;
  color: #999;
}

.interaction-detail {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 26rpx;
}

.detail-label {
  color: #999;
  width: 160rpx;
}

.detail-value {
  color: #666;
  flex: 1;
}

.interaction-content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  margin-bottom: 20rpx;
  word-break: break-all;
}

.next-steps {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.next-step-item {
  font-size: 24rpx;
  padding: 8rpx 20rpx;
  background-color: #e6f7ff;
  color: #1890ff;
  border-radius: 100rpx;
}

.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag {
  font-size: 24rpx;
  padding: 8rpx 20rpx;
  background-color: #f5f5f5;
  color: #666;
  border-radius: 100rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  color: #a0a0a0;
}

.empty-title {
  font-size: 32rpx;
  margin: 40rpx 0 20rpx;
}

.empty-desc {
  font-size: 28rpx;
}

.loading-more {
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 28rpx;
}

.fab {
  position: fixed;
  right: 40rpx;
  bottom: 140rpx;
  width: 120rpx;
  height: 120rpx;
  background-color: #3370ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(51, 112, 255, 0.3);
  z-index: 100;
}

.modal-filter {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-dialog {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  padding-bottom: 40rpx;
  max-height: 80vh;
  overflow: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
}

.modal-close {
  color: #999;
}

.modal-body {
  padding: 30rpx;
}

.filter-section {
  margin-bottom: 40rpx;
}

.filter-title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.filter-tag {
  padding: 16rpx 30rpx;
  border-radius: 100rpx;
  font-size: 26rpx;
  background-color: #f5f7fa;
  color: #666;
}

.filter-tag.active {
  background-color: #e6f0ff;
  color: #3370ff;
  font-weight: 500;
}

.modal-footer {
  padding: 0 30rpx;
}

.filter-actions {
  display: flex;
  gap: 30rpx;
}

.btn {
  flex: 1;
  height: 88rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}

.btn-outline {
  border: 1rpx solid #d9d9d9;
  color: #666;
}

.btn-primary {
  background-color: #3370ff;
  color: white;
}

.card-hover {
  opacity: 0.8;
}
</style> 
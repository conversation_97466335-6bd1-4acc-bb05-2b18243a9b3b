import { getDictionaryPage, getDictionaryPageDetail } from '@/api/common.api';

export default async function getSelectOptions(code, config = {}) {
  const { pageIndex = 1, pageSize = 100 } = config;
  try {
    const pageResult = await getDictionaryPage({
      pageIndex,
      pageSize,
      filter: code,
    });
    if (!pageResult?.items?.length) {
      return [];
    }
    const dataResult = await getDictionaryPageDetail({
      pageIndex,
      pageSize,
      dataDictionaryId: pageResult.items[0].id,
    });
    return dataResult.items.filter(item => item.isEnabled);
  } catch (error) {
    console.error('Error fetching select options:', error);
    throw error; // 重新抛出错误，让调用方可以捕获
  }
}

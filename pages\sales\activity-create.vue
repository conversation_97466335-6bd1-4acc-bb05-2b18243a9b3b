<template>
  <view class="page">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="navBack">
        <svg-icon name="arrow-left" type="svg" size="24"></svg-icon>
      </view>
      <text class="page-title">创建活动</text>
      <view class="header-spacer"></view>
    </view>

    <scroll-view scroll-y class="page-container">
      <!-- 基本信息区域 -->
      <view class="form-section">
        <view class="section-header">
          <svg-icon name="information" type="svg" size="20"></svg-icon>
          <text class="section-title">基本信息</text>
        </view>
        <view class="section-content">
          <view class="related-info" v-if="relatedInfo.title">
            <text class="related-title">关联商机</text>
            <text class="related-content">{{relatedInfo.title}}</text>
          </view>
          
          <view class="form-group">
            <text class="form-label required">活动类型</text>
            <view class="activity-types">
              <view 
                v-for="(type, index) in activityTypes" 
                :key="index" 
                class="type-option" 
                :class="{ selected: formData.type === type.value }"
                @click="formData.type = type.value"
              >
                <svg-icon 
                  :name="type.icon" 
                  :type="type.iconType || 'svg'" 
                  size="24"
                  :color="formData.type === type.value ? '#3370ff' : ''"
                ></svg-icon>
                <text>{{type.label}}</text>
              </view>
            </view>
          </view>
          
          <view class="form-group">
            <text class="form-label required">活动主题</text>
            <input 
              type="text" 
              class="form-control" 
              v-model="formData.title" 
              placeholder="例如：产品演示会议"
            />
          </view>
          
          <view class="form-group">
            <text class="form-label required">开始时间</text>
            <view class="date-picker">
              <picker 
                mode="datetime" 
                :value="formData.startTime" 
                @change="onStartTimeChange"
              >
                <view class="uni-input">
                  {{formatDateTime(formData.startTime)}}
                  <view class="input-icon">
                    <svg-icon name="calendar" type="svg" size="20"></svg-icon>
                  </view>
                </view>
              </picker>
            </view>
          </view>
          
          <view class="form-group">
            <text class="form-label">结束时间</text>
            <view class="date-picker">
              <picker 
                mode="datetime" 
                :value="formData.endTime" 
                @change="onEndTimeChange"
              >
                <view class="uni-input">
                  {{formatDateTime(formData.endTime)}}
                  <view class="input-icon">
                    <svg-icon name="calendar" type="svg" size="20"></svg-icon>
                  </view>
                </view>
              </picker>
            </view>
          </view>
          
          <view class="toggle-switch">
            <view class="toggle-label">全天活动</view>
            <switch 
              :checked="formData.isAllDay" 
              @change="onAllDayChange" 
              color="#3370ff"
            />
          </view>
          
          <view class="toggle-switch">
            <view class="toggle-label">添加提醒</view>
            <switch 
              :checked="formData.hasReminder" 
              @change="onReminderChange" 
              color="#3370ff"
            />
          </view>
          
          <view class="form-group" v-if="formData.hasReminder">
            <picker 
              :value="reminderIndex" 
              :range="reminderOptions.map(item => item.label)" 
              @change="onReminderTimeChange"
              mode="selector"
            >
              <view class="form-select">
                <view class="uni-input">{{reminderOptions[reminderIndex].label}}
                  <view class="input-icon">
                    <svg-icon name="reminder" type="svg" size="20"></svg-icon>
                  </view>
                </view>
               
              </view>
            </picker>
          </view>
        </view>
      </view>
      
      <!-- 参与人区域 -->
      <view class="form-section">
        <view class="section-header">
          <svg-icon name="user-group" type="svg" size="20"></svg-icon>
          <text class="section-title">参与人</text>
        </view>
        <view class="section-content">
          <view class="participant-list">
            <view class="participant-item" v-for="(participant, index) in participants" :key="index">
              <view class="participant-avatar">
                <image v-if="participant.avatar" :src="participant.avatar" mode="aspectFill"></image>
                <svg-icon v-else name="user" type="svg" size="24"></svg-icon>
              </view>
              <view class="participant-info">
                <view class="participant-name">{{participant.name}}</view>
                <view class="participant-role">{{participant.role}}</view>
              </view>
              <view class="participant-remove" :style="{visibility: participant.isOwner ? 'hidden' : 'visible'}" @click="removeParticipant(index)">
                <svg-icon name="delete-bin" type="svg" size="22" color="#ff4d4f"></svg-icon>
              </view>
            </view>
          </view>
          
          <view class="add-participant" @click="showParticipantModal">
            <svg-icon name="user-add" type="svg" size="20"></svg-icon>
            <text>添加参与人</text>
          </view>
        </view>
      </view>
      
      <!-- 活动详情区域 -->
      <view class="form-section">
        <view class="section-header">
          <svg-icon name="align-left" type="svg" size="20"></svg-icon>
          <text class="section-title">活动详情</text>
        </view>
        <view class="section-content">
          <view class="form-group">
            <textarea 
              class="form-control" 
              v-model="formData.content" 
              placeholder="输入活动详情..."
            />
          </view>
          
          <view class="form-group">
            <text class="form-label">地点</text>
            <input 
              type="text" 
              class="form-control" 
              v-model="formData.location" 
              placeholder="例如：公司会议室或客户地址"
            />
          </view>
        </view>
      </view>
      
      <!-- 底部占位 -->
      <view class="bottom-spacer"></view>
    </scroll-view>
    
    <!-- 底部操作栏 -->
    <view class="action-bar">
      <button class="btn btn-outline" @click="navBack">取消</button>
      <button class="btn btn-primary" @click="saveActivity">保存</button>
    </view>
    
    <!-- 参与人选择弹窗 -->
    <uni-popup ref="participantPopup" :type="'bottom'">
      <view class="modal-content">
        <view class="modal-header">
          <text>选择参与人</text>
          <view class="close-modal" @click="hideParticipantModal">
            <svg-icon name="close" type="svg" size="24"></svg-icon>
          </view>
        </view>
        <view class="modal-body">
          <view class="search-box">
            <svg-icon name="search" type="svg" size="20"></svg-icon>
            <input type="text" placeholder="搜索参与人" v-model="searchText" class="search-input" />
          </view>
          <view class="participants-list">
            <view 
              class="participant-item"
              v-for="(person, index) in filteredContacts" 
              :key="index"
              v-show="person.show"
            >
              <view class="participant-info">
                <image :src="person.avatar || '../../static/images/avatar-default.png'" class="participant-avatar" mode="aspectFill"></image>
                <view>
                  <view class="participant-name">{{person.name}}</view>
                  <view class="participant-role">{{person.role}}</view>
                </view>
              </view>
              <view class="checkbox-wrapper">
                <checkbox :checked="person.selected" @click="toggleContactSelection(index)" />
              </view>
            </view>
          </view>
        </view>
        <view class="modal-footer">
          <button class="btn btn-outline" @click="hideParticipantModal">取消</button>
          <button class="btn btn-primary" @click="confirmParticipantSelection">确定</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import SvgIcon from '@/components/svg-icon.vue';
import uniPopup from '@/components/uni-popup/uni-popup.vue';

export default {
  components: {
    SvgIcon,
    uniPopup
  },
  data() {
    const now = new Date();
    const startTime = new Date(now.getTime() + 60 * 60 * 1000); // 当前时间加1小时
    const endTime = new Date(now.getTime() + 2 * 60 * 60 * 1000); // 当前时间加2小时
    
    return {
      relatedInfo: {
        id: '', // 关联对象ID
        type: 'opportunity', // 关联类型：商机、客户等
        title: '企业ERP系统实施项目' // 关联对象名称
      },
      formData: {
        type: 'call', // 活动类型：call, meeting, visit, email
        title: '', // 活动主题
        startTime: startTime.toISOString(), // 开始时间
        endTime: endTime.toISOString(), // 结束时间
        isAllDay: false, // 是否全天
        hasReminder: true, // 是否有提醒
        reminderTime: 15, // 提醒时间（分钟）
        content: '', // 活动详情
        location: '' // 地点
      },
      activityTypes: [
        { label: '电话沟通', value: 'call', icon: 'phone' },
        { label: '会议', value: 'meeting', icon: 'team' },
        { label: '客户拜访', value: 'visit', icon: 'customer' },
        { label: '邮件', value: 'email', icon: 'mail' }
      ],
      reminderOptions: [
        { label: '活动开始前5分钟', value: 5 },
        { label: '活动开始前15分钟', value: 15 },
        { label: '活动开始前30分钟', value: 30 },
        { label: '活动开始前1小时', value: 60 },
        { label: '活动开始前2小时', value: 120 },
        { label: '活动开始前1天', value: 1440 }
      ],
      reminderIndex: 1, // 默认选择15分钟
      participants: [
        { 
          id: '1', 
          name: '王销售', 
          role: '负责人', 
          avatar: '', 
          isOwner: true 
        },
        { 
          id: '2', 
          name: '张总经理', 
          role: '未来科技有限公司', 
          avatar: '', 
          isOwner: false 
        }
      ],
      contacts: [
        { id: '3', name: '张经理', role: '销售总监', avatar: '../../static/images/avatar1.jpg', selected: true, show: true },
        { id: '4', name: '李经理', role: '销售经理', avatar: '../../static/images/avatar2.jpg', selected: true, show: true },
        { id: '5', name: '王经理', role: '销售经理', avatar: '../../static/images/avatar3.jpg', selected: false, show: true }
      ],
      searchText: ''
    }
  },
  computed: {
    filteredContacts() {
      if (!this.searchText) {
        return this.contacts.map(contact => {
          contact.show = true;
          return contact;
        });
      }
      
      const searchText = this.searchText.toLowerCase();
      return this.contacts.map(contact => {
        const name = contact.name.toLowerCase();
        const role = contact.role.toLowerCase();
        contact.show = name.includes(searchText) || role.includes(searchText);
        return contact;
      });
    }
  },
  onLoad(options) {
    // 如果有关联信息，处理关联数据
    if (options.id && options.type) {
      this.relatedInfo.id = options.id;
      this.relatedInfo.type = options.type;
      
      // 根据类型和ID获取关联信息标题
      this.loadRelatedInfo(options.type, options.id);
    }
    
    // 设置默认负责人
    this.setDefaultOwner();
  },
  methods: {
    navBack() {
      uni.navigateBack();
    },
    formatDateTime(isoString) {
      const date = new Date(isoString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      
      if (this.formData.isAllDay) {
        return `${year}-${month}-${day}`;
      } else {
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      }
    },
    onStartTimeChange(e) {
      const dateStr = e.detail.value;
      this.formData.startTime = new Date(dateStr.replace(/-/g, '/')).toISOString();
    },
    onEndTimeChange(e) {
      const dateStr = e.detail.value;
      this.formData.endTime = new Date(dateStr.replace(/-/g, '/')).toISOString();
    },
    onAllDayChange(e) {
      this.formData.isAllDay = e.detail.value;
    },
    onReminderChange(e) {
      this.formData.hasReminder = e.detail.value;
    },
    onReminderTimeChange(e) {
      this.reminderIndex = e.detail.value;
      this.formData.reminderTime = this.reminderOptions[this.reminderIndex].value;
    },
    loadRelatedInfo(type, id) {
      // 模拟从API获取关联信息
      // 实际开发中应该根据type和id从服务器获取关联对象的详细信息
      if (type === 'opportunity' && id === '123') {
        this.relatedInfo.title = '企业ERP系统实施项目';
      }
    },
    setDefaultOwner() {
      // 设置默认负责人
      // 实际开发中，应该从用户信息中获取当前登录用户作为默认负责人
    },
    removeParticipant(index) {
      // 不允许删除负责人
      if (!this.participants[index].isOwner) {
        this.participants.splice(index, 1);
      }
    },
    showParticipantModal() {
      this.$refs.participantPopup.open();
    },
    hideParticipantModal() {
      this.$refs.participantPopup.close();
    },
    toggleContactSelection(index) {
      this.contacts[index].selected = !this.contacts[index].selected;
    },
    confirmParticipantSelection() {
      // 获取选中的联系人
      const selectedContacts = this.contacts.filter(contact => contact.selected);
      
      // 添加到参与人列表（排除已存在的）
      selectedContacts.forEach(contact => {
        // 检查是否已存在
        const exists = this.participants.some(p => p.id === contact.id);
        if (!exists) {
          this.participants.push({
            id: contact.id,
            name: contact.name,
            role: contact.role,
            avatar: contact.avatar,
            isOwner: false
          });
        }
      });
      
      this.hideParticipantModal();
    },
    validateForm() {
      if (!this.formData.title.trim()) {
        uni.showToast({
          title: '请输入活动主题',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.formData.startTime) {
        uni.showToast({
          title: '请选择开始时间',
          icon: 'none'
        });
        return false;
      }
      
      return true;
    },
    saveActivity() {
      if (!this.validateForm()) return;
      
      // 构建要保存的数据
      const activityData = {
        ...this.formData,
        participants: this.participants.map(p => p.id),
        relatedInfo: this.relatedInfo
      };
      
      // 模拟保存成功
      console.log('保存的活动数据:', activityData);
      
      uni.showToast({
        title: '活动安排成功！',
        success: () => {
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        }
      });
    }
  }
}
</script>

<style>
page {
  background-color: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  color: #333;
  --primary-color: #3370ff;
  --secondary-color: #1a56ff;
  --text-primary: #333;
  --text-secondary: #666;
  --text-tertiary: #999;
  --border-color: #e0e0e0;
  --background-light: #f5f7fa;
  --background-card: #fff;
  --radius-sm: 4px;
  --radius-md: 8px;
  --spacing-xs: 5px;
  --spacing-sm: 10px;
  --spacing-md: 15px;
  --spacing-lg: 20px;
  --spacing-xl: 30px;
}

.page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  box-sizing: border-box;
  width: 100%;
  overflow-x: hidden; /* 防止水平溢出 */
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: #fff;
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-button, .close-button {
  font-size: 24px;
  color: var(--text-secondary);
}

.page-title {
  font-size: 18px;
  font-weight: 500;
  flex: 1;
  text-align: center;
}

.save-button {
  color: var(--primary-color);
  font-size: 16px;
  font-weight: 500;
}

.page-container {
  flex: 1;
  padding-bottom: 80px;
  height: calc(100vh - 50px);
  box-sizing: border-box;
}

.form-section {
  background-color: #fff;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  box-sizing: border-box;
  width: 100%;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.section-header text {
  font-size: 18px;
  margin-right: var(--spacing-xs);
}

.section-title {
  font-size: 16px;
  font-weight: 500;
}

.related-info {
  background-color: var(--background-light);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-md);
}

.related-title {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: 5px;
}

.related-content {
  color: var(--text-primary);
  font-weight: 500;
}

.activity-types {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 8px;
  margin: 0;
  margin-bottom: var(--spacing-md);
  width: 100%;
}

.type-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 5px 0;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  background-color: #fff;
}

.type-option text {
  font-size: 12px;
  margin-top: 3px;
}

.type-option.selected {
  background-color: #e6f0ff;
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.form-group {
  margin-bottom: var(--spacing-md);
  width: 100%;
  box-sizing: border-box;
  overflow: hidden; /* 确保内容不溢出 */
}

.form-label {
  display: block;
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.required::after {
  content: '*';
  color: #ff4d4f;
  margin-left: 4px;
}

.form-control, .uni-input {
  width: 100%;
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 16px;
  background-color: #fff;
  box-sizing: border-box;
}

textarea.form-control {
  height: 120px;
}

.date-picker {
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

.date-picker .ri-calendar-line {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
}

.toggle-switch {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: var(--spacing-md) 0;
}

.toggle-label {
  font-size: 16px;
  color: var(--text-primary);
}

.form-select {
  position: relative;
  width: 100%;
}

.form-select .ri-arrow-down-s-line {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
}

.participant-list {
  margin-bottom: var(--spacing-md);
}

.participant-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid var(--border-color);
}

.participant-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  overflow: hidden;
}

.participant-avatar svg-icon {
  color: #999;
}

.participant-info {
  flex: 1;
}

.participant-name {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.participant-role {
  font-size: 14px;
  color: var(--text-secondary);
}

.participant-remove {
  color: #ff4d4f;
  padding: var(--spacing-xs);
}

.add-participant {
  display: flex;
  align-items: center;
  color: var(--primary-color);
  font-size: 14px;
  margin-top: var(--spacing-md);
  white-space: nowrap;
}

.add-participant text {
  margin-left: var(--spacing-xs);
}

.bottom-spacer {
  height: 80px;
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: var(--spacing-md) var(--spacing-lg);
  padding-bottom: calc(var(--spacing-md) + env(safe-area-inset-bottom, 0));
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  z-index: 10;
}

.btn {
  padding: 10px 0;
  border-radius: var(--radius-md);
  font-size: 16px;
  font-weight: 500;
  flex: 1;
  text-align: center;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
}

/* 弹窗样式 */
.modal-content {
  background-color: #fff;
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  overflow: hidden;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  font-size: 18px;
  font-weight: 500;
}

.close-modal {
  font-size: 20px;
  color: var(--text-tertiary);
}

.modal-body {
  padding: var(--spacing-md) var(--spacing-lg);
  flex: 1;
  overflow-y: auto;
}

.search-box {
  position: relative;
  margin-bottom: var(--spacing-md);
}

.search-box svg-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
}

.search-input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 16px;
}

.participants-list .participant-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid var(--border-color);
}

.participants-list .participant-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #eee;
  margin-right: var(--spacing-md);
}

.modal-footer {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

.checkbox-wrapper checkbox {
  transform: scale(1.2);
}

.header-spacer {
  width: 44px; /* 与返回按钮宽度一致 */
}

.input-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.uni-input {
  position: relative;
  padding-right: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 修复日期选择器问题 */
.date-picker .uni-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

/* 确保文本不会溢出 */
.uni-input {
  position: relative;
  padding-right: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 修复活动类型图标大小 */
.type-option svg-icon {
  height: 20px;
  min-height: 20px;
}

/* 确保所有表单元素不会超出容器 */
.section-content {
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}
</style> 
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-contracts-invoice-detail"],{2371:function(i,t,e){var a=e("c86c");t=a(!1),t.push([i.i,".invoice-detail-container[data-v-181066ca]{display:flex;flex-direction:column;height:100vh;background-color:#f5f6fa}.page-header[data-v-181066ca]{display:flex;align-items:center;padding:%?20?% %?30?%;background-color:#fff;box-shadow:0 %?2?% %?10?% rgba(0,0,0,.05);position:relative;z-index:10}.back-button[data-v-181066ca]{width:%?60?%;height:%?60?%;display:flex;align-items:center;justify-content:center}.page-title[data-v-181066ca]{flex:1;font-size:%?36?%;font-weight:600;text-align:center;margin-right:%?60?%}.header-actions[data-v-181066ca]{width:%?60?%;display:flex;justify-content:center}.action-button[data-v-181066ca]{background:none;border:none;padding:0;margin:0;font-size:%?40?%;line-height:1}.invoice-container[data-v-181066ca]{flex:1;padding:%?30?%}.section-card[data-v-181066ca]{background-color:#fff;border-radius:%?16?%;padding:%?30?%;margin-bottom:%?30?%;box-shadow:0 %?2?% %?8?% rgba(0,0,0,.03)}.section-title[data-v-181066ca]{display:flex;justify-content:space-between;align-items:center;font-size:%?32?%;font-weight:600;margin-bottom:%?20?%}.status-badge[data-v-181066ca]{font-size:%?24?%;padding:%?6?% %?20?%;border-radius:%?30?%;font-weight:400}.status-paid[data-v-181066ca]{background-color:#e6f7ee;color:#00b578}.status-pending[data-v-181066ca]{background-color:#fff5e6;color:#ff9a2a}.status-overdue[data-v-181066ca]{background-color:#ffece8;color:#ff4d4f}.info-grid[data-v-181066ca]{display:flex;flex-wrap:wrap}.info-item[data-v-181066ca]{width:50%;margin-bottom:%?24?%}.info-label[data-v-181066ca]{font-size:%?24?%;color:#8c8c8c;margin-bottom:%?8?%}.info-value[data-v-181066ca]{font-size:%?28?%;color:#333}.amount-table[data-v-181066ca]{width:100%}.amount-row[data-v-181066ca]{display:flex;justify-content:space-between;padding:%?16?% 0;border-bottom:%?2?% solid #f0f0f0}.total-row[data-v-181066ca]{border-bottom:none;padding-top:%?30?%;font-weight:600;font-size:%?32?%}.invoice-items[data-v-181066ca]{margin-top:%?20?%}.invoice-item-row[data-v-181066ca]{display:flex;justify-content:space-between;padding:%?24?% 0;border-bottom:%?2?% solid #f0f0f0}.invoice-item-row[data-v-181066ca]:last-child{border-bottom:none}.invoice-item-name[data-v-181066ca]{flex:1;font-size:%?28?%}.invoice-item-details[data-v-181066ca]{text-align:right}.invoice-item-quantity[data-v-181066ca]{font-size:%?24?%;color:#8c8c8c;margin-bottom:%?8?%}.invoice-item-price[data-v-181066ca]{font-size:%?28?%;font-weight:500}.timeline[data-v-181066ca]{margin-top:%?20?%}.timeline-item[data-v-181066ca]{display:flex;margin-bottom:%?30?%}.timeline-icon[data-v-181066ca]{width:%?60?%;height:%?60?%;border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:%?20?%}.timeline-icon.paid[data-v-181066ca]{background-color:#e6f7ee;color:#00b578}.timeline-icon.sent[data-v-181066ca]{background-color:#e6f4ff;color:#1890ff}.timeline-icon.created[data-v-181066ca]{background-color:#f5f5f5;color:#666}.timeline-content[data-v-181066ca]{flex:1}.timeline-title[data-v-181066ca]{font-size:%?28?%;margin-bottom:%?8?%}.timeline-date[data-v-181066ca]{font-size:%?24?%;color:#8c8c8c}.file-list[data-v-181066ca]{margin-top:%?20?%}.file-item[data-v-181066ca]{display:flex;align-items:center;padding:%?20?% 0;border-bottom:%?2?% solid #f0f0f0}.file-item[data-v-181066ca]:last-child{border-bottom:none}.file-item uni-text[data-v-181066ca]:first-child{font-size:%?40?%;margin-right:%?20?%;color:#8c8c8c}.file-item uni-text[data-v-181066ca]:nth-child(2){flex:1;font-size:%?28?%}.download-button[data-v-181066ca]{width:%?64?%;height:%?64?%;display:flex;align-items:center;justify-content:center;border-radius:50%;color:#8c8c8c;background-color:#f5f5f5;border:none;padding:0}.notes-content[data-v-181066ca]{font-size:%?28?%;line-height:1.5;color:#333}.add-note-button[data-v-181066ca]{display:flex;align-items:center;justify-content:center;gap:%?10?%;margin-top:%?30?%;padding:%?20?%;background-color:#f5f5f5;border:%?2?% dashed #e8e8e8;border-radius:%?16?%;color:#8c8c8c;font-size:%?28?%}.float-actions[data-v-181066ca]{position:fixed;bottom:0;left:0;right:0;display:flex;padding:%?30?%;background-color:#fff;border-top:%?2?% solid #f0f0f0;z-index:100}.action-btn[data-v-181066ca]{flex:1;display:flex;align-items:center;justify-content:center;gap:%?10?%;padding:%?24?%;border-radius:%?16?%;font-size:%?28?%;font-weight:500}.primary-action[data-v-181066ca]{background-color:#2979ff;color:#fff;border:none}.secondary-action[data-v-181066ca]{background-color:#f5f5f5;color:#333;border:%?2?% solid #e8e8e8;margin-right:%?20?%}",""]),i.exports=t},"622f":function(i,t,e){var a=e("2371");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[i.i,a,""]]),a.locals&&(i.exports=a.locals);var n=e("967d").default;n("0246d06c",a,!0,{sourceMap:!1,shadowMode:!1})},7088:function(i,t,e){"use strict";var a=e("622f"),n=e.n(a);n.a},a161:function(i,t,e){"use strict";e("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a={data:function(){return{invoice:{title:"系统集成项目 - 第一期",statusCode:"paid",statusText:"已付款",invoiceNumber:"INV-2023-11-001",contractNumber:"CT-2023-09-001",invoiceDate:"2023-11-05",dueDate:"2023-11-15",paymentDate:"2023-11-12",invoiceType:"增值税专用发票",customer:{name:"上海智能科技",taxNumber:"91310000MA1FL4CT3X",bank:"中国建设银行上海张江支行",accountNumber:"31050161393600000123",addressAndPhone:"上海市浦东新区张江高科技园区科苑路88号 021-********"},amountBeforeTax:257500,taxRate:13,taxAmount:33475,totalAmount:290975,items:[{name:"软件系统集成服务",quantity:1,unit:"项",price:17e4},{name:"数据迁移服务",quantity:1,unit:"项",price:87500}],timeline:[{type:"paid",icon:"ri-bank-card-line",title:"收到付款",date:"2023-11-12 10:25"},{type:"sent",icon:"ri-mail-send-line",title:"发送发票给客户",date:"2023-11-05 14:30"},{type:"created",icon:"ri-add-line",title:"创建发票",date:"2023-11-05 11:10"}],files:[{name:"INV-2023-11-001_电子发票.pdf",icon:"ri-file-pdf-line",url:""},{name:"发票明细清单.xlsx",icon:"ri-file-excel-line",url:""}],notes:"此发票为系统集成项目第一期款项，按照合同CT-2023-09-001约定开具，税率为13%。"}}},onLoad:function(i){i.id&&this.loadInvoiceData(i.id)},methods:{loadInvoiceData:function(i){console.log("加载发票ID:",i)},formatMoney:function(i){return i.toLocaleString("zh-CN")},goBack:function(){uni.navigateBack()},viewHistory:function(){uni.navigateTo({url:"/pages/contracts/invoice-history"})},downloadFile:function(i){uni.showToast({title:"下载功能开发中...",icon:"none"})},addNote:function(){var i=this;uni.showModal({title:"添加备注",editable:!0,placeholderText:"请输入备注信息",success:function(t){t.confirm&&t.content&&(i.invoice.notes=i.invoice.notes?i.invoice.notes+"\n"+t.content:t.content)}})},sendInvoice:function(){uni.showToast({title:"发送发票功能开发中...",icon:"none"})},recordPayment:function(){uni.navigateTo({url:"/pages/contracts/payment-create?invoiceId="+this.invoice.invoiceNumber})},showActionMenu:function(){var i=this;uni.showActionSheet({itemList:["查看历史记录","编辑发票","打印发票","导出为PDF","删除发票"],success:function(t){switch(t.tapIndex){case 0:i.viewHistory();break;case 1:uni.navigateTo({url:"/pages/contracts/invoice-edit?id="+i.invoice.invoiceNumber});break;default:uni.showToast({title:"该功能开发中...",icon:"none"})}}})}}};t.default=a},a945:function(i,t,e){"use strict";e.r(t);var a=e("a161"),n=e.n(a);for(var o in a)["default"].indexOf(o)<0&&function(i){e.d(t,i,(function(){return a[i]}))}(o);t["default"]=n.a},f7f6:function(i,t,e){"use strict";e.d(t,"b",(function(){return a})),e.d(t,"c",(function(){return n})),e.d(t,"a",(function(){}));var a=function(){var i=this,t=i.$createElement,e=i._self._c||t;return e("v-uni-view",{staticClass:"invoice-detail-container"},[e("v-uni-view",{staticClass:"page-header"},[e("v-uni-view",{staticClass:"back-button",on:{click:function(t){arguments[0]=t=i.$handleEvent(t),i.goBack.apply(void 0,arguments)}}},[e("v-uni-text",{staticClass:"ri-arrow-left-line"})],1),e("v-uni-text",{staticClass:"page-title"},[i._v("发票详情")]),e("v-uni-view",{staticClass:"header-actions"},[e("v-uni-button",{staticClass:"action-button",attrs:{type:"button"},on:{click:function(t){arguments[0]=t=i.$handleEvent(t),i.showActionMenu.apply(void 0,arguments)}}},[e("v-uni-text",{staticClass:"ri-more-2-fill"})],1)],1)],1),e("v-uni-scroll-view",{staticClass:"invoice-container",attrs:{"scroll-y":!0}},[e("v-uni-view",{staticClass:"section-card"},[e("v-uni-view",{staticClass:"section-title"},[e("v-uni-text",[i._v(i._s(i.invoice.title))]),e("v-uni-text",{class:["status-badge","status-"+i.invoice.statusCode]},[i._v(i._s(i.invoice.statusText))])],1),e("v-uni-view",{staticClass:"info-grid"},[e("v-uni-view",{staticClass:"info-item"},[e("v-uni-view",{staticClass:"info-label"},[i._v("发票号")]),e("v-uni-view",{staticClass:"info-value"},[i._v(i._s(i.invoice.invoiceNumber))])],1),e("v-uni-view",{staticClass:"info-item"},[e("v-uni-view",{staticClass:"info-label"},[i._v("关联合同")]),e("v-uni-view",{staticClass:"info-value"},[i._v(i._s(i.invoice.contractNumber))])],1),e("v-uni-view",{staticClass:"info-item"},[e("v-uni-view",{staticClass:"info-label"},[i._v("开票日期")]),e("v-uni-view",{staticClass:"info-value"},[i._v(i._s(i.invoice.invoiceDate))])],1),e("v-uni-view",{staticClass:"info-item"},[e("v-uni-view",{staticClass:"info-label"},[i._v("付款期限")]),e("v-uni-view",{staticClass:"info-value"},[i._v(i._s(i.invoice.dueDate))])],1),e("v-uni-view",{staticClass:"info-item"},[e("v-uni-view",{staticClass:"info-label"},[i._v("付款日期")]),e("v-uni-view",{staticClass:"info-value"},[i._v(i._s(i.invoice.paymentDate||"-"))])],1),e("v-uni-view",{staticClass:"info-item"},[e("v-uni-view",{staticClass:"info-label"},[i._v("发票类型")]),e("v-uni-view",{staticClass:"info-value"},[i._v(i._s(i.invoice.invoiceType))])],1)],1)],1),e("v-uni-view",{staticClass:"section-card"},[e("v-uni-view",{staticClass:"section-title"},[i._v("客户信息")]),e("v-uni-view",{staticClass:"info-item"},[e("v-uni-view",{staticClass:"info-label"},[i._v("客户名称")]),e("v-uni-view",{staticClass:"info-value"},[i._v(i._s(i.invoice.customer.name))])],1),e("v-uni-view",{staticClass:"info-item"},[e("v-uni-view",{staticClass:"info-label"},[i._v("税号")]),e("v-uni-view",{staticClass:"info-value"},[i._v(i._s(i.invoice.customer.taxNumber))])],1),e("v-uni-view",{staticClass:"info-item"},[e("v-uni-view",{staticClass:"info-label"},[i._v("开户行")]),e("v-uni-view",{staticClass:"info-value"},[i._v(i._s(i.invoice.customer.bank))])],1),e("v-uni-view",{staticClass:"info-item"},[e("v-uni-view",{staticClass:"info-label"},[i._v("账号")]),e("v-uni-view",{staticClass:"info-value"},[i._v(i._s(i.invoice.customer.accountNumber))])],1),e("v-uni-view",{staticClass:"info-item"},[e("v-uni-view",{staticClass:"info-label"},[i._v("地址电话")]),e("v-uni-view",{staticClass:"info-value"},[i._v(i._s(i.invoice.customer.addressAndPhone))])],1)],1),e("v-uni-view",{staticClass:"section-card"},[e("v-uni-view",{staticClass:"section-title"},[i._v("发票金额")]),e("v-uni-view",{staticClass:"amount-table"},[e("v-uni-view",{staticClass:"amount-row"},[e("v-uni-view",{staticClass:"label"},[i._v("不含税金额")]),e("v-uni-view",{staticClass:"value"},[i._v("¥"+i._s(i.formatMoney(i.invoice.amountBeforeTax)))])],1),e("v-uni-view",{staticClass:"amount-row"},[e("v-uni-view",{staticClass:"label"},[i._v("税额 ("+i._s(i.invoice.taxRate)+"%)")]),e("v-uni-view",{staticClass:"value"},[i._v("¥"+i._s(i.formatMoney(i.invoice.taxAmount)))])],1),e("v-uni-view",{staticClass:"amount-row total-row"},[e("v-uni-view",{staticClass:"label"},[i._v("总计")]),e("v-uni-view",{staticClass:"value"},[i._v("¥"+i._s(i.formatMoney(i.invoice.totalAmount)))])],1)],1)],1),e("v-uni-view",{staticClass:"section-card"},[e("v-uni-view",{staticClass:"section-title"},[i._v("发票明细")]),e("v-uni-view",{staticClass:"invoice-items"},i._l(i.invoice.items,(function(t,a){return e("v-uni-view",{key:a,staticClass:"invoice-item-row"},[e("v-uni-view",{staticClass:"invoice-item-name"},[i._v(i._s(t.name))]),e("v-uni-view",{staticClass:"invoice-item-details"},[e("v-uni-view",{staticClass:"invoice-item-quantity"},[i._v(i._s(t.quantity)+" "+i._s(t.unit))]),e("v-uni-view",{staticClass:"invoice-item-price"},[i._v("¥"+i._s(i.formatMoney(t.price)))])],1)],1)})),1)],1),e("v-uni-view",{staticClass:"section-card"},[e("v-uni-view",{staticClass:"section-title"},[i._v("历史记录")]),e("v-uni-view",{staticClass:"timeline"},i._l(i.invoice.timeline,(function(t,a){return e("v-uni-view",{key:a,staticClass:"timeline-item"},[e("v-uni-view",{class:["timeline-icon",t.type]},[e("v-uni-text",{class:t.icon})],1),e("v-uni-view",{staticClass:"timeline-content"},[e("v-uni-view",{staticClass:"timeline-title"},[i._v(i._s(t.title))]),e("v-uni-view",{staticClass:"timeline-date"},[i._v(i._s(t.date))])],1)],1)})),1),e("v-uni-view",{staticClass:"add-note-button",on:{click:function(t){arguments[0]=t=i.$handleEvent(t),i.viewHistory.apply(void 0,arguments)}}},[e("v-uni-text",{staticClass:"ri-history-line"}),e("v-uni-text",[i._v("查看完整历史记录")])],1)],1),e("v-uni-view",{staticClass:"section-card"},[e("v-uni-view",{staticClass:"section-title"},[i._v("发票文件")]),e("v-uni-view",{staticClass:"file-list"},i._l(i.invoice.files,(function(t,a){return e("v-uni-view",{key:a,staticClass:"file-item"},[e("v-uni-text",{class:t.icon}),e("v-uni-text",[i._v(i._s(t.name))]),e("v-uni-button",{staticClass:"download-button",attrs:{type:"button"},on:{click:function(e){arguments[0]=e=i.$handleEvent(e),i.downloadFile(t)}}},[e("v-uni-text",{staticClass:"ri-download-line"})],1)],1)})),1)],1),e("v-uni-view",{staticClass:"section-card"},[e("v-uni-view",{staticClass:"section-title"},[i._v("备注信息")]),e("v-uni-view",{staticClass:"notes-content"},[e("v-uni-text",[i._v(i._s(i.invoice.notes||"暂无备注"))])],1),e("v-uni-view",{staticClass:"add-note-button",on:{click:function(t){arguments[0]=t=i.$handleEvent(t),i.addNote.apply(void 0,arguments)}}},[e("v-uni-text",{staticClass:"ri-add-line"}),e("v-uni-text",[i._v("添加备注")])],1)],1)],1),e("v-uni-view",{staticClass:"float-actions"},[e("v-uni-button",{staticClass:"action-btn secondary-action",attrs:{type:"button"},on:{click:function(t){arguments[0]=t=i.$handleEvent(t),i.sendInvoice.apply(void 0,arguments)}}},[e("v-uni-text",{staticClass:"ri-mail-send-line"}),e("v-uni-text",[i._v("发送发票")])],1),e("v-uni-button",{staticClass:"action-btn primary-action",attrs:{type:"button"},on:{click:function(t){arguments[0]=t=i.$handleEvent(t),i.recordPayment.apply(void 0,arguments)}}},[e("v-uni-text",{staticClass:"ri-bank-card-line"}),e("v-uni-text",[i._v("记录付款")])],1)],1)],1)},n=[]},fff8:function(i,t,e){"use strict";e.r(t);var a=e("f7f6"),n=e("a945");for(var o in n)["default"].indexOf(o)<0&&function(i){e.d(t,i,(function(){return n[i]}))}(o);e("7088");var s=e("828b"),c=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"181066ca",null,!1,a["a"],void 0);t["default"]=c.exports}}]);
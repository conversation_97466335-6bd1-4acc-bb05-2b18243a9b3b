(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-sales-customer-select"],{"17c4":function(t,e,r){"use strict";r("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.getDictionaryPageDetail=e.getDictionaryPage=void 0;var n=r("c475");e.getDictionaryPage=function(t){return(0,n.request)({url:"/api/DataDictionary/page",method:"POST",data:t})};e.getDictionaryPageDetail=function(t){return(0,n.request)({url:"/api/DataDictionary/pageDetail",method:"POST",data:t})}},"218e":function(t,e,r){"use strict";r("6a54");var n=r("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,r("3efd");var a=n(r("2634")),o=n(r("2fdc")),i=n(r("8a0f")),c=n(r("c780")),u=r("f8c5"),s={components:{SvgIcon:i.default},data:function(){return{searchText:"",currentFilter:void 0,filterTabs:[],recentCustomers:[],allCustomers:[]}},onLoad:function(){this.loadDictionaryOptions(),this.getCustomList()},methods:{loadDictionaryOptions:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,c.default)("CustomLevel");case 3:t.filterTabs=e.sent,t.filterTabs.unshift({displayText:"全部客户",id:void 0}),e.next=10;break;case 7:e.prev=7,e.t0=e["catch"](0),t.$message.error("加载字典数据失败");case 10:case"end":return e.stop()}}),e,null,[[0,7]])})))()},getCustomList:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var r,n;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,r={pageIndex:1,pageSize:999,filter:{customLevelId:t.currentFilter,likeString:t.searchText}},e.next=4,(0,u.getCustomerList)(r);case 4:n=e.sent,t.allCustomers=n.items,e.next=11;break;case 8:e.prev=8,e.t0=e["catch"](0),console.log(e.t0);case 11:case"end":return e.stop()}}),e,null,[[0,8]])})))()},navigateBack:function(){uni.navigateBack()},switchTab:function(t){this.currentFilter=t,this.getCustomList()},selectCustomer:function(t){uni.setStorageSync("selected_customer",t);var e=this.getOpenerEventChannel&&this.getOpenerEventChannel();e&&(e.emit("selectCustomer",{id:t.id,name:t.name}),e.emit("updateSelectedCustomer",t),console.log("selectCustomer",t),e.emit("updateCurrency",t.capitalTypeName,t.capitalTypeId)),uni.navigateBack()}}};e.default=s},2634:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
e.default=function(){return t};var t={},r=Object.prototype,a=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},c=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(S){f=function(t,e,r){return t[e]=r}}function l(t,e,r,n){var a=e&&e.prototype instanceof p?e:p,i=Object.create(a.prototype),c=new O(n||[]);return o(i,"_invoke",{value:C(t,r,c)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(S){return{type:"throw",arg:S}}}t.wrap=l;var v={};function p(){}function h(){}function g(){}var m={};f(m,c,(function(){return this}));var y=Object.getPrototypeOf,b=y&&y(y(P([])));b&&b!==r&&a.call(b,c)&&(m=b);var w=g.prototype=p.prototype=Object.create(m);function x(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){var r;o(this,"_invoke",{value:function(o,i){function c(){return new e((function(r,c){(function r(o,i,c,u){var s=d(t[o],t,i);if("throw"!==s.type){var f=s.arg,l=f.value;return l&&"object"==(0,n.default)(l)&&a.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,c,u)}),(function(t){r("throw",t,c,u)})):e.resolve(l).then((function(t){f.value=t,c(f)}),(function(t){return r("throw",t,c,u)}))}u(s.arg)})(o,i,r,c)}))}return r=r?r.then(c,c):c()}})}function C(t,e,r){var n="suspendedStart";return function(a,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===a)throw o;return E()}for(r.method=a,r.arg=o;;){var i=r.delegate;if(i){var c=T(i,r);if(c){if(c===v)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=d(t,e,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===v)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}function T(t,e){var r=e.method,n=t.iterator[r];if(void 0===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=void 0,T(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var a=d(n,t.iterator,e.arg);if("throw"===a.type)return e.method="throw",e.arg=a.arg,e.delegate=null,v;var o=a.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,v):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,v)}function _(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function L(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(_,this),this.reset(!0)}function P(t){if(t){var e=t[c];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,n=function e(){for(;++r<t.length;)if(a.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return n.next=n}}return{next:E}}function E(){return{value:void 0,done:!0}}return h.prototype=g,o(w,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:h,configurable:!0}),h.displayName=f(g,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,f(t,s,"GeneratorFunction")),t.prototype=Object.create(w),t},t.awrap=function(t){return{__await:t}},x(k.prototype),f(k.prototype,u,(function(){return this})),t.AsyncIterator=k,t.async=function(e,r,n,a,o){void 0===o&&(o=Promise);var i=new k(l(e,r,n,a),o);return t.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},x(w),f(w,s,"Generator"),f(w,c,(function(){return this})),f(w,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=P,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(L),!t)for(var e in this)"t"===e.charAt(0)&&a.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(r,n){return i.type="throw",i.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],i=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var c=a.call(o,"catchLoc"),u=a.call(o,"finallyLoc");if(c&&u){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),L(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;L(r)}return a}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:P(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),v}},t},r("6a54"),r("01a2"),r("e39c"),r("bf0f"),r("844d"),r("18f7"),r("de6c"),r("3872e"),r("4e9b"),r("114e"),r("c240"),r("926e"),r("7a76"),r("c9b5"),r("aa9c"),r("2797"),r("8a8d"),r("dc69"),r("f7a5");var n=function(t){return t&&t.__esModule?t:{default:t}}(r("fcf3"))},"2fdc":function(t,e,r){"use strict";function n(t,e,r,n,a,o,i){try{var c=t[o](i),u=c.value}catch(s){return void r(s)}c.done?e(u):Promise.resolve(u).then(n,a)}r("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return function(){var e=this,r=arguments;return new Promise((function(a,o){var i=t.apply(e,r);function c(t){n(i,a,o,c,u,"next",t)}function u(t){n(i,a,o,c,u,"throw",t)}c(void 0)}))}},r("bf0f")},"33f0":function(t,e,r){var n=r("94cc");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=r("967d").default;a("b736c03e",n,!0,{sourceMap:!1,shadowMode:!1})},"45d2":function(t,e,r){"use strict";r.r(e);var n=r("218e"),a=r.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){r.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},"849c":function(t,e,r){"use strict";var n=r("33f0"),a=r.n(n);a.a},"94cc":function(t,e,r){var n=r("c86c");e=n(!1),e.push([t.i,'.page-container[data-v-6cff9c51]{min-height:100vh;background-color:#f5f7fa;display:flex;flex-direction:column;width:100%;overflow-x:hidden;box-sizing:border-box}.page-header[data-v-6cff9c51]{display:flex;align-items:center;justify-content:space-between;padding:%?30?% %?40?%;border-bottom:%?1?% solid #e0e0e0;background-color:#fff;position:-webkit-sticky;position:sticky;top:0;z-index:10}.page-title[data-v-6cff9c51]{font-size:%?36?%;font-weight:700;color:#333}.back-button[data-v-6cff9c51]{padding:%?10?%}.header-spacer[data-v-6cff9c51]{width:%?44?%}.search-container[data-v-6cff9c51]{position:relative;padding:%?20?% %?30?%;background-color:#fff}.search-box[data-v-6cff9c51]{display:flex;align-items:center;background-color:#f5f7fa;border-radius:%?8?%;padding:0 %?20?%}.search-icon[data-v-6cff9c51]{position:absolute;left:%?40?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);z-index:1;color:#999}.search-input[data-v-6cff9c51]{\n  /*width: 100%;*/height:%?80?%;padding:0 %?20?% 0 %?60?%;font-size:%?28?%;background-color:#f5f7fa;border-radius:%?8?%}.filter-tabs[data-v-6cff9c51]{display:flex;padding:0 %?30?%;background-color:#fff;border-bottom:%?1?% solid #e0e0e0;overflow-x:auto}.filter-tab[data-v-6cff9c51]{padding:%?20?% %?30?%;font-size:%?28?%;color:#666;position:relative;white-space:nowrap}.filter-tab.active[data-v-6cff9c51]{color:#3370ff;font-weight:500}.filter-tab.active[data-v-6cff9c51]::after{content:"";position:absolute;bottom:0;left:0;right:0;height:%?4?%;background-color:#3370ff}.customer-list[data-v-6cff9c51]{flex:1;padding:%?20?% %?30?%}.section-header[data-v-6cff9c51]{font-size:%?28?%;font-weight:500;color:#999;margin:%?20?% 0}.customer-item[data-v-6cff9c51]{display:flex;align-items:center;padding:%?30?%;background-color:#fff;border-radius:%?12?%;margin-bottom:%?20?%}.customer-avatar[data-v-6cff9c51]{width:%?80?%;height:%?80?%;border-radius:50%;background-color:#f0f7ff;display:flex;align-items:center;justify-content:center;margin-right:%?20?%}.customer-info[data-v-6cff9c51]{flex:1}.customer-name[data-v-6cff9c51]{font-size:%?32?%;font-weight:500;color:#333;margin-bottom:%?10?%}.customer-detail[data-v-6cff9c51]{font-size:%?26?%;color:#666;margin-bottom:%?10?%}.customer-tags[data-v-6cff9c51]{display:flex;gap:%?10?%;margin-top:var(--spacing-xs)}.tag[data-v-6cff9c51]{padding:%?4?% %?16?%;border-radius:%?100?%;font-size:%?24?%}.tag.tag-A[data-v-6cff9c51]{background-color:#fef3c7;color:#d97706}.tag.tag-B[data-v-6cff9c51]{background-color:#e0f2fe;color:#0284c7}.tag.tag-C[data-v-6cff9c51]{background-color:#dbeafe;color:#2563eb}.tag.tag-default[data-v-6cff9c51]{background-color:#9ca3af;color:#000}.tag.tag-industry[data-v-6cff9c51]{background-color:#f5f5f5;color:#666}.empty-state[data-v-6cff9c51]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?100?% 0}.empty-icon[data-v-6cff9c51]{margin-bottom:%?20?%;color:#999}.empty-text[data-v-6cff9c51]{font-size:%?28?%;color:#999}',""]),t.exports=e},c475:function(t,e,r){"use strict";r("6a54");var n=r("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.request=e.getTenantInfo=void 0;var a=n(r("9b1b"));r("bf0f"),r("4626"),r("5ac7");var o=null;e.getTenantInfo=function(t){return new Promise((function(e,r){uni.request({url:"/api/Tenants/getTenantsByLogin",method:"POST",data:t,header:{"Content-Type":"application/json"},success:function(t){200===t.statusCode?(o=t.data,e(t.data)):r(t.data)},fail:function(t){r(t)}})}))};e.request=function(t){return t.url.includes("/login")&&o&&(t.header=(0,a.default)((0,a.default)({},t.header),{},{__tenant:o[0].id})),new Promise((function(e,r){uni.request({url:t.url,method:t.method||"GET",data:t.data||{},header:(0,a.default)({Authorization:"Bearer "+uni.getStorageSync("token"),"Content-Type":"application/json"},t.header),success:function(t){200===t.statusCode?e(t.data):r(t.data)},fail:function(t){r(t)}})}))}},c780:function(t,e,r){"use strict";r("6a54");var n=r("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return c.apply(this,arguments)},r("8f71"),r("bf0f");var a=n(r("2634")),o=n(r("2fdc")),i=r("17c4");function c(){return c=(0,o.default)((0,a.default)().mark((function t(e){var r,n,o,c,u,s,f,l,d=arguments;return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r=d.length>1&&void 0!==d[1]?d[1]:{},n=r.pageIndex,o=void 0===n?1:n,c=r.pageSize,u=void 0===c?100:c,t.prev=2,t.next=5,(0,i.getDictionaryPage)({pageIndex:o,pageSize:u,filter:e});case 5:if(f=t.sent,null!==f&&void 0!==f&&null!==(s=f.items)&&void 0!==s&&s.length){t.next=8;break}return t.abrupt("return",[]);case 8:return t.next=10,(0,i.getDictionaryPageDetail)({pageIndex:o,pageSize:u,dataDictionaryId:f.items[0].id});case 10:return l=t.sent,t.abrupt("return",l.items.filter((function(t){return t.isEnabled})));case 14:throw t.prev=14,t.t0=t["catch"](2),console.error("Error fetching select options:",t.t0),t.t0;case 18:case"end":return t.stop()}}),t,null,[[2,14]])}))),c.apply(this,arguments)}},d070:function(t,e,r){"use strict";r.r(e);var n=r("d594"),a=r("45d2");for(var o in a)["default"].indexOf(o)<0&&function(t){r.d(e,t,(function(){return a[t]}))}(o);r("849c");var i=r("828b"),c=Object(i["a"])(a["default"],n["b"],n["c"],!1,null,"6cff9c51",null,!1,n["a"],void 0);e["default"]=c.exports},d594:function(t,e,r){"use strict";r.d(e,"b",(function(){return a})),r.d(e,"c",(function(){return o})),r.d(e,"a",(function(){return n}));var n={svgIcon:r("8a0f").default},a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("v-uni-view",{staticClass:"page-container"},[r("v-uni-view",{staticClass:"search-container"},[r("v-uni-view",{staticClass:"search-icon"},[r("svg-icon",{attrs:{name:"search",type:"svg",size:"24"}})],1),r("v-uni-input",{staticClass:"search-input",attrs:{type:"text",placeholder:"搜索客户名称..."},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.getCustomList.apply(void 0,arguments)}},model:{value:t.searchText,callback:function(e){t.searchText=e},expression:"searchText"}})],1),r("v-uni-view",{staticClass:"filter-tabs"},t._l(t.filterTabs,(function(e){return r("v-uni-view",{key:e.id,staticClass:"filter-tab",class:{active:t.currentFilter===e.id},on:{click:function(r){arguments[0]=r=t.$handleEvent(r),t.switchTab(e.id)}}},[t._v(t._s(e.displayText))])})),1),r("v-uni-view",{staticClass:"customer-list"},[t._l(t.allCustomers,(function(e){return r("v-uni-view",{key:e.id,staticClass:"customer-item",on:{click:function(r){arguments[0]=r=t.$handleEvent(r),t.selectCustomer(e)}}},[r("v-uni-view",{staticClass:"customer-avatar"},[r("svg-icon",{attrs:{name:"building",type:"svg",size:"24"}})],1),r("v-uni-view",{staticClass:"customer-info"},[r("v-uni-text",{staticClass:"customer-name"},[t._v(t._s(e.name))]),r("v-uni-view",{staticClass:"customer-tags"},[e.customLevelName?r("v-uni-text",{staticClass:"tag",class:null===e.customLevel?"tag-default":"tag-"+e.customLevel.code},[t._v(t._s(e.customLevelName))]):t._e(),e.industry?r("v-uni-text",{staticClass:"tag tag-industry"},[t._v(t._s(e.industry))]):t._e()],1)],1)],1)})),0===t.allCustomers.length?r("v-uni-view",{staticClass:"empty-state"},[r("v-uni-view",{staticClass:"empty-icon"},[r("svg-icon",{attrs:{name:"search",type:"svg",size:"48"}})],1),r("v-uni-text",{staticClass:"empty-text"},[t._v("没有找到匹配的客户")])],1):t._e()],2)],1)},o=[]},f8c5:function(t,e,r){"use strict";r("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.updateTaskDescription=e.updateCustomerById=e.getTaskDetail=e.getOwnerList=e.getOrganizeUsers=e.getCustomerList=e.getCustomerDetail=e.getCustomerById=e.deleteCustomer=e.createCustomer=e.TmsTaskTimeEnd=e.TmsTaskCreate=void 0,r("c223");var n=r("c475");e.getCustomerList=function(t){return(0,n.request)({url:"/api/crm/custom/getList",method:"POST",data:t})};e.getCustomerDetail=function(t){return(0,n.request)({url:"/api/crm/custom/getCustomById?id="+t,method:"GET"})};e.createCustomer=function(t){return(0,n.request)({url:"/api/crm/custom/create",method:"POST",data:t})};e.getCustomerById=function(t){return(0,n.request)({url:"/api/crm/custom/getCustomById?id="+t,method:"GET"})};e.updateCustomerById=function(t,e){return(0,n.request)({url:"/api/crm/custom/update?id="+t,method:"POST",data:e})};e.getOwnerList=function(t){return(0,n.request)({url:"/api/Users/<USER>",method:"POST",data:t})};e.deleteCustomer=function(t){return(0,n.request)({url:"/api/crm/custom/delete?id="+t,method:"POST"})};e.getOrganizeUsers=function(){return(0,n.request)({url:"/api/tms/taskTemplate/getOrganizeUsers",method:"GET"})};e.TmsTaskCreate=function(t){return(0,n.request)({url:"/api/tms/taskItem/create",method:"POST",data:t})};e.TmsTaskTimeEnd=function(t,e){return(0,n.request)({url:"/api/tms/taskItem/editPlanDoneDate?id=".concat(t,"&newDate=").concat(e),method:"POST"})};e.updateTaskDescription=function(t,e){return(0,n.request)({url:"/api/tms/taskItem/updateDesc?id="+t,method:"POST",data:e})};e.getTaskDetail=function(t){return(0,n.request)({url:"/api/tms/taskItem/getList",method:"POST",data:t})}}}]);
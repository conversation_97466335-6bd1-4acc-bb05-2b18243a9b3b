(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-customers-customer-create"],{"005d":function(t,a,e){"use strict";var i=e("4228"),n=e.n(i);n.a},"1cb0":function(t,a,e){"use strict";e("6a54");var i=e("f5bd").default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0,e("8f71"),e("bf0f"),e("aa9c"),e("dd2b");var n=i(e("39d8")),o=i(e("8a0f")),s=i(e("c780")),r=i(e("4f21")),c=i(e("df3c")),l=e("f8c5"),u={components:{SvgIcon:o.default},data:function(){var t;return{formValid:!1,countryList:r.default,provinceList:c.default.province,cityList:[],currencyList:[],listingSectorList:[],customerLevelList:[],basicInfoShow:!1,financialInfoShow:!1,businessInfoShow:!1,otherInfoShow:!1,formData:(t={name:"",code:"",contactTelephone:"",creationTime:"",creatorName:"",companyName:"",customerType:"",customerTypeId:"",capitalTypeId:"",customerLevelId:"",customTypeId:"",industry:"",size:"",foundYear:"",owner:"",address:"",website:"",creditCode:"",source:"",remark:"",country:"",province:"",city:"",currency:"",isListed:!1,listingSector:"",listingSectorId:"",annualSales:"",socialInsuranceNum:0,businessType:"",customerLevel:"",customLevelId:"",paidCapital:0,invoiceInfo:"",introduction:"",upAndDownCustoms:""},(0,n.default)(t,"website",""),(0,n.default)(t,"isDealt",!1),(0,n.default)(t,"ownerId",""),(0,n.default)(t,"owner",""),(0,n.default)(t,"registeredCapital",0),(0,n.default)(t,"annualSalesId",""),(0,n.default)(t,"contacts",[{address:"",birthday:"",clueId:"",code:"",creationTime:"",creatorName:"",department:"",experience:"",fixPhone:"",hobby:"",owner:"",ownerId:"",position:"",remark:"",telephone:"",weChat:"",name:"",title:"",phone:"",email:"",other:"",isPrimary:!0}]),t),customerTypes:[],industries:[],companySizes:[],annualSalesList:["0-1000万","1000-5000万","5000-1亿","1亿-5亿","5亿-10亿","10亿-50亿","50亿-100亿"],owners:[],sources:[]}},methods:{loadData:function(){var t=this;(0,s.default)("CustomLevel").then((function(a){t.customerLevelList=a})),(0,s.default)("CapitalType").then((function(a){t.currencyList=a})),(0,s.default)("listingSector").then((function(a){t.listingSectorList=a})),(0,s.default)("CustomType").then((function(a){t.customerTypes=a})),(0,s.default)("CustomIndustry").then((function(a){t.industries=a}))},onCustomerLevelChange:function(t){this.formData.customerLevel=this.customerLevelList[t.detail.value].displayText,this.formData.customLevelId=this.customerLevelList[t.detail.value].id},onCountryChange:function(t){this.formData.country=this.countryList[t.detail.value].name,"中国"!==this.formData.country&&(this.formData.province="",this.formData.city="")},onListingSectorChange:function(t){this.formData.listingSector=this.listingSectorList[t.detail.value].displayText,this.formData.listingSectorId=this.listingSectorList[t.detail.value].id},onAnnualSalesChange:function(t){this.formData.annualSales=this.annualSalesList[t.detail.value],this.formData.annualSalesId=this.annualSalesList[t.detail.value].id},onCustomerTypeChange:function(t){this.formData.customerType=this.customerTypes[t.detail.value].displayText,this.formData.customTypeId=this.customerTypes[t.detail.value].id},onProvinceChange:function(t){var a=this;this.formData.province=this.provinceList[t.detail.value].name,this.cityList=c.default.city.filter((function(e){return e.ProID===a.provinceList[t.detail.value].ProID}))},onCityChange:function(t){this.formData.city=this.cityList[t.detail.value].name},onCurrencyChange:function(t){this.formData.currency=this.currencyList[t.detail.value].displayText,this.formData.capitalTypeId=this.currencyList[t.detail.value].id},onListedChange:function(t){this.formData.isListed="true"===t.detail.value},onIsDealChange:function(t){this.formData.isDealt="true"===t.detail.value},onIndustryChange:function(t){this.formData.industry=this.industries[t.detail.value].displayText},onSizeChange:function(t){this.formData.size=this.companySizes[t.detail.value]},onFoundYearChange:function(t){this.formData.foundYear=t.detail.value},onOwnerChange:function(t){this.formData.owner=this.owners[t.detail.value]},onSourceChange:function(t){this.formData.source=this.sources[t.detail.value]},addContact:function(){this.formData.contacts.push({name:"",title:"",phone:"",email:"",other:"",isPrimary:!1})},removeContact:function(t){this.formData.contacts[t].isPrimary&&this.formData.contacts.length>1&&(this.formData.contacts[0].isPrimary=!0),this.formData.contacts.splice(t,1),this.validateForm()},validateForm:function(){},cancel:function(){uni.showModal({title:"提示",content:"确定要放弃创建客户吗？",success:function(t){t.confirm&&uni.navigateBack()}})},saveCustomer:function(){console.log(this.formData),(0,l.createCustomer)(this.formData).then((function(t){uni.navigateBack()}))}},onLoad:function(){this.validateForm(),this.loadData()},onReady:function(){uni.setNavigationBarTitle({title:"创建客户"})}};a.default=u},4228:function(t,a,e){var i=e("f865");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=e("967d").default;n("94c2e3f2",i,!0,{sourceMap:!1,shadowMode:!1})},"4eb6":function(t,a,e){"use strict";e.r(a);var i=e("b110"),n=e("fe69");for(var o in n)["default"].indexOf(o)<0&&function(t){e.d(a,t,(function(){return n[t]}))}(o);e("005d");var s=e("828b"),r=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"547d37c2",null,!1,i["a"],void 0);a["default"]=r.exports},b110:function(t,a,e){"use strict";e.d(a,"b",(function(){return n})),e.d(a,"c",(function(){return o})),e.d(a,"a",(function(){return i}));var i={svgIcon:e("8a0f").default},n=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("v-uni-view",{staticClass:"container"},[e("v-uni-view",{staticClass:"page-content"},[e("v-uni-form",{on:{submit:function(a){a.preventDefault(),arguments[0]=a=t.$handleEvent(a),t.saveCustomer.apply(void 0,arguments)}}},[e("v-uni-view",{staticClass:"form-section"},[e("v-uni-view",{staticClass:"section-title section-title-active"},[e("v-uni-text",[e("i",{staticClass:"ri-info-card-line"}),t._v("基本信息")]),t.basicInfoShow?t._e():e("v-uni-text",{on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.basicInfoShow=!t.basicInfoShow}}},[e("i",{staticClass:"ri-arrow-down-double-fill"})]),t.basicInfoShow?e("v-uni-text",{on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.basicInfoShow=!t.basicInfoShow}}},[e("i",{staticClass:"ri-arrow-up-double-fill"})]):t._e()],1),t.basicInfoShow?e("v-uni-view",[e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label required"},[t._v("客户名称")]),e("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入客户名称"},on:{input:function(a){arguments[0]=a=t.$handleEvent(a),t.validateForm.apply(void 0,arguments)}},model:{value:t.formData.name,callback:function(a){t.$set(t.formData,"name",a)},expression:"formData.name"}})],1),t.basicInfoShow?t._e():e("v-uni-view",{staticClass:"basic-info-more",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.basicInfoShow=!t.basicInfoShow}}},[e("v-uni-text",{staticClass:"form-label"},[e("i",{staticClass:"ri-arrow-down-double-fill"})])],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("所属国家")]),e("v-uni-view",{staticClass:"type-selector"},[e("v-uni-view",{staticClass:"form-select"},[e("v-uni-picker",{attrs:{mode:"selector",range:t.countryList,"range-key":"name"},on:{change:function(a){arguments[0]=a=t.$handleEvent(a),t.onCountryChange.apply(void 0,arguments)}}},[e("v-uni-text",[t._v(t._s(t.formData.country||"请选择所属国家"))])],1)],1)],1)],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("所属省份")]),e("v-uni-view",{staticClass:"type-selector"},["中国"===t.formData.country?e("v-uni-view",{staticClass:"form-select"},[e("v-uni-picker",{attrs:{mode:"selector",range:t.provinceList,"range-key":"name"},on:{change:function(a){arguments[0]=a=t.$handleEvent(a),t.onProvinceChange.apply(void 0,arguments)}}},[e("v-uni-text",[t._v(t._s(t.formData.province||"请选择所属省份"))])],1)],1):e("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入所属省份"},model:{value:t.formData.province,callback:function(a){t.$set(t.formData,"province",a)},expression:"formData.province"}})],1)],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("所属城市")]),e("v-uni-view",{staticClass:"type-selector"},["中国"===t.formData.country?e("v-uni-view",{staticClass:"form-select"},[e("v-uni-picker",{attrs:{mode:"selector",range:t.cityList,"range-key":"name"},on:{change:function(a){arguments[0]=a=t.$handleEvent(a),t.onCityChange.apply(void 0,arguments)}}},[e("v-uni-text",[t._v(t._s(t.formData.city||"请选择所属城市"))])],1)],1):e("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入所属城市"},model:{value:t.formData.city,callback:function(a){t.$set(t.formData,"city",a)},expression:"formData.city"}})],1)],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("详细地址")]),e("v-uni-view",[e("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入详细地址"},on:{input:function(a){arguments[0]=a=t.$handleEvent(a),t.validateForm.apply(void 0,arguments)}},model:{value:t.formData.address,callback:function(a){t.$set(t.formData,"address",a)},expression:"formData.address"}})],1)],1)],1):t._e()],1),e("v-uni-view",{staticClass:"form-section"},[e("v-uni-view",{staticClass:"section-title section-title-active"},[e("v-uni-text",[e("i",{staticClass:"ri-money-cny-circle-fill"}),t._v("财务信息")]),t.financialInfoShow?t._e():e("v-uni-text",{on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.financialInfoShow=!t.financialInfoShow}}},[e("i",{staticClass:"ri-arrow-down-double-fill"})]),t.financialInfoShow?e("v-uni-text",{on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.financialInfoShow=!t.financialInfoShow}}},[e("i",{staticClass:"ri-arrow-up-double-fill"})]):t._e()],1),t.financialInfoShow?e("v-uni-view",[e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("资金币种")]),e("v-uni-view",{staticClass:"type-selector"},[e("v-uni-view",{staticClass:"form-select"},[e("v-uni-picker",{attrs:{mode:"selector",range:t.currencyList,"range-key":"displayText"},on:{change:function(a){arguments[0]=a=t.$handleEvent(a),t.onCurrencyChange.apply(void 0,arguments)}}},[e("v-uni-text",[t._v(t._s(t.formData.currency||"请选择资金币种"))])],1)],1)],1)],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("注册资金(w)")]),e("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入注册资金"},model:{value:t.formData.registeredCapital,callback:function(a){t.$set(t.formData,"registeredCapital",a)},expression:"formData.registeredCapital"}})],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("实缴资金(w)")]),e("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入实缴资金"},model:{value:t.formData.paidCapital,callback:function(a){t.$set(t.formData,"paidCapital",a)},expression:"formData.paidCapital"}})],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("是否上市")]),e("v-uni-radio-group",{staticClass:"radio",on:{change:function(a){arguments[0]=a=t.$handleEvent(a),t.onListedChange.apply(void 0,arguments)}}},[e("v-uni-label",[e("v-uni-radio",{attrs:{value:"true"}}),t._v("是")],1),e("v-uni-label",[e("v-uni-radio",{attrs:{value:"false"}}),t._v("否")],1)],1)],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("上市板块")]),e("v-uni-view",{staticClass:"type-selector"},[e("v-uni-view",{staticClass:"form-select"},[e("v-uni-picker",{attrs:{mode:"selector",range:t.listingSectorList,"range-key":"displayText"},on:{change:function(a){arguments[0]=a=t.$handleEvent(a),t.onListingSectorChange.apply(void 0,arguments)}}},[e("v-uni-text",[t._v(t._s(t.formData.listingSector||"请选择上市板块"))])],1)],1)],1)],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("社保人数")]),e("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入社保人数"},model:{value:t.formData.socialInsuranceNum,callback:function(a){t.$set(t.formData,"socialInsuranceNum",a)},expression:"formData.socialInsuranceNum"}})],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("年销售额")]),e("v-uni-view",{staticClass:"type-selector"},[e("v-uni-view",{staticClass:"form-select"},[e("v-uni-picker",{attrs:{mode:"selector",range:t.annualSalesList,"range-key":"displayText"},on:{change:function(a){arguments[0]=a=t.$handleEvent(a),t.onAnnualSalesChange.apply(void 0,arguments)}}},[e("v-uni-text",[t._v(t._s(t.formData.annualSales||"请选择年销售额"))])],1)],1)],1)],1)],1):t._e()],1),e("v-uni-view",{staticClass:"form-section"},[e("v-uni-view",{staticClass:"section-title section-title-active"},[e("v-uni-text",[e("i",{staticClass:"ri-briefcase-5-fill"}),t._v("业务信息")]),t.businessInfoShow?t._e():e("v-uni-text",{on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.businessInfoShow=!t.businessInfoShow}}},[e("i",{staticClass:"ri-arrow-down-double-fill"})]),t.businessInfoShow?e("v-uni-text",{on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.businessInfoShow=!t.businessInfoShow}}},[e("i",{staticClass:"ri-arrow-up-double-fill"})]):t._e()],1),t.businessInfoShow?e("v-uni-view",[e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("客户上下游")]),e("v-uni-view",[e("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入客户上下游"},model:{value:t.formData.upAndDownCustoms,callback:function(a){t.$set(t.formData,"upAndDownCustoms",a)},expression:"formData.upAndDownCustoms"}})],1)],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("客户等级")]),e("v-uni-view",{staticClass:"type-selector"},[e("v-uni-view",{staticClass:"form-select"},[e("v-uni-picker",{attrs:{mode:"selector",range:t.customerLevelList,"range-key":"displayText"},on:{change:function(a){arguments[0]=a=t.$handleEvent(a),t.onCustomerLevelChange.apply(void 0,arguments)}}},[e("v-uni-text",[t._v(t._s(t.formData.customerLevel||"请选择客户等级"))])],1)],1)],1)],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("客户类型")]),e("v-uni-view",{staticClass:"type-selector"},[e("v-uni-view",{staticClass:"form-select"},[e("v-uni-picker",{attrs:{mode:"selector",range:t.customerTypes,"range-key":"displayText"},on:{change:function(a){arguments[0]=a=t.$handleEvent(a),t.onCustomerTypeChange.apply(void 0,arguments)}}},[e("v-uni-text",[t._v(t._s(t.formData.customerType||"请选择客户类型"))])],1)],1)],1)],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("客户网址")]),e("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入客户网址"},model:{value:t.formData.website,callback:function(a){t.$set(t.formData,"website",a)},expression:"formData.website"}})],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("所属行业")]),e("v-uni-view",{staticClass:"type-selector"},[e("v-uni-view",{staticClass:"form-select"},[e("v-uni-picker",{attrs:{mode:"selector",range:t.industries,"range-key":"displayText"},on:{change:function(a){arguments[0]=a=t.$handleEvent(a),t.onIndustryChange.apply(void 0,arguments)}}},[e("v-uni-text",[t._v(t._s(t.formData.industry||"请选择所属行业"))])],1)],1)],1)],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("是否已成交")]),e("v-uni-radio-group",{staticClass:"radio",on:{change:function(a){arguments[0]=a=t.$handleEvent(a),t.onIsDealChange.apply(void 0,arguments)}}},[e("v-uni-label",[e("v-uni-radio",{attrs:{value:"true"}}),t._v("是")],1),e("v-uni-label",[e("v-uni-radio",{attrs:{value:"false"}}),t._v("否")],1)],1)],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("开票信息")]),e("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入开票信息"},model:{value:t.formData.invoiceInfo,callback:function(a){t.$set(t.formData,"invoiceInfo",a)},expression:"formData.invoiceInfo"}})],1)],1):t._e()],1),e("v-uni-view",{staticClass:"form-section"},[e("v-uni-view",{staticClass:"section-title section-title-active"},[e("v-uni-text",[e("i",{staticClass:"ri-coin-fill"}),t._v("其它信息")]),t.otherInfoShow?t._e():e("v-uni-text",{on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.otherInfoShow=!t.otherInfoShow}}},[e("i",{staticClass:"ri-arrow-down-double-fill"})]),t.otherInfoShow?e("v-uni-text",{on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.otherInfoShow=!t.otherInfoShow}}},[e("i",{staticClass:"ri-arrow-up-double-fill"})]):t._e()],1),t.otherInfoShow?e("v-uni-view",[e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("公司介绍")]),e("v-uni-textarea",{staticClass:"form-textarea",attrs:{placeholder:"请输入公司介绍"},model:{value:t.formData.introduction,callback:function(a){t.$set(t.formData,"introduction",a)},expression:"formData.introduction"}})],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("备注")]),e("v-uni-textarea",{staticClass:"form-textarea",attrs:{placeholder:"请输入备注"},model:{value:t.formData.remark,callback:function(a){t.$set(t.formData,"remark",a)},expression:"formData.remark"}})],1)],1):t._e()],1),e("v-uni-view",{staticClass:"form-section"},[e("v-uni-view",{staticClass:"section-title"},[e("v-uni-text",{staticClass:" ri-contacts-book-2-fill"}),e("v-uni-text",[t._v("联系人信息")])],1),e("v-uni-view",{staticClass:"contacts-section"},[t._l(t.formData.contacts,(function(a,i){return e("v-uni-view",{key:i,staticClass:"contact-card"},[e("v-uni-view",{staticClass:"contact-header"},[e("v-uni-view",{staticClass:"contact-title"},[t._v(t._s(0===i?"主要联系人":"联系人 "+(i+1)))]),t.formData.contacts.length>1||i>0?e("v-uni-view",{staticClass:"remove-contact",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.removeContact(i)}}},[e("v-uni-text",{staticClass:"iconfont icon-delete"}),e("v-uni-text",[t._v("移除")])],1):t._e()],1),e("v-uni-view",{staticClass:"input-group"},[e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label "},[t._v("姓名")]),e("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入姓名"},on:{input:function(a){arguments[0]=a=t.$handleEvent(a),t.validateForm.apply(void 0,arguments)}},model:{value:a.name,callback:function(e){t.$set(a,"name",e)},expression:"contact.name"}})],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("固定电话")]),e("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入固定电话"},model:{value:a.fixPhone,callback:function(e){t.$set(a,"fixPhone",e)},expression:"contact.fixPhone"}})],1)],1),e("v-uni-view",{staticClass:"input-group"},[e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("电话")]),e("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入电话"},model:{value:a.telephone,callback:function(e){t.$set(a,"telephone",e)},expression:"contact.telephone"}})],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("邮箱")]),e("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入邮箱"},model:{value:a.email,callback:function(e){t.$set(a,"email",e)},expression:"contact.email"}})],1)],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"form-label"},[t._v("其他联系方式")]),e("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"如微信号等"},model:{value:a.weChat,callback:function(e){t.$set(a,"weChat",e)},expression:"contact.weChat"}})],1)],1)})),e("v-uni-view",{staticClass:"add-contact-btn",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.addContact.apply(void 0,arguments)}}},[e("svg-icon",{attrs:{name:"add",type:"svg",size:"24"}}),e("v-uni-text",[t._v("添加联系人")])],1)],2)],1)],1)],1),e("v-uni-view",{staticClass:"bottom-bar"},[e("v-uni-button",{staticClass:"btn btn-outline",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.cancel.apply(void 0,arguments)}}},[t._v("取消")]),e("v-uni-button",{staticClass:"btn btn-primary",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.saveCustomer.apply(void 0,arguments)}}},[t._v("保存")])],1)],1)},o=[]},f865:function(t,a,e){var i=e("c86c");a=i(!1),a.push([t.i,'.page-header[data-v-547d37c2]{display:flex;align-items:center;justify-content:space-between;padding:var(--spacing-md) var(--spacing-lg);border-bottom:%?1?% solid var(--border-color);background-color:#fff;position:-webkit-sticky;position:sticky;top:0;z-index:10}.page-title[data-v-547d37c2]{font-size:%?32?%;font-weight:700;color:var(--text-primary)}.back-button[data-v-547d37c2]{color:var(--text-secondary);display:flex;align-items:center}.save-button[data-v-547d37c2]{color:var(--primary-color);font-weight:500;font-size:%?28?%;padding:var(--spacing-sm) var(--spacing-md);background-color:initial;border:none;line-height:1}.save-button[disabled][data-v-547d37c2]{color:var(--text-tertiary);opacity:.5}\n\n/* 表单样式 */.form-section[data-v-547d37c2]{background-color:#fff;padding:var(--spacing-lg);margin-top:var(--spacing-md);border-top:%?1?% solid var(--border-color);border-bottom:%?1?% solid var(--border-color)}.section-title[data-v-547d37c2]{font-size:%?30?%;font-weight:600;color:var(--text-primary);margin-bottom:var(--spacing-md);display:flex;align-items:center}.section-title-active[data-v-547d37c2]{display:flex;justify-content:space-between}.section-title uni-text[data-v-547d37c2]:first-child{\n  /* color: var(--primary-color); */margin-right:var(--spacing-xs)}.form-group[data-v-547d37c2]{margin-bottom:var(--spacing-md)}.form-label[data-v-547d37c2]{display:block;margin-bottom:var(--spacing-xs);font-size:%?28?%;font-weight:500;color:var(--text-secondary)}.required[data-v-547d37c2]::after{content:"*";color:var(--danger-color);margin-left:%?8?%}.form-input[data-v-547d37c2],\n.form-select[data-v-547d37c2],\n.form-textarea[data-v-547d37c2]{width:100%;padding:%?24?%;border:%?1?% solid var(--border-color);border-radius:var(--radius-md);background-color:#fff;font-size:%?28?%;color:var(--text-primary)}.form-textarea[data-v-547d37c2]{min-height:%?200?%;width:100%}.input-group[data-v-547d37c2]{display:flex;gap:var(--spacing-md)}.input-group .form-group[data-v-547d37c2]{flex:1}\n\n/* 联系人区域样式 */.contact-card[data-v-547d37c2]{background-color:var(--light-color);border-radius:var(--radius-md);padding:var(--spacing-md);margin-bottom:var(--spacing-md);border:%?1?% solid var(--border-color)}.contact-header[data-v-547d37c2]{display:flex;justify-content:space-between;align-items:center;margin-bottom:var(--spacing-md)}.contact-title[data-v-547d37c2]{font-weight:500;font-size:%?30?%;color:var(--text-primary)}.remove-contact[data-v-547d37c2]{color:var(--danger-color);font-size:%?28?%;display:flex;align-items:center}.remove-contact uni-text[data-v-547d37c2]:first-child{margin-right:%?4?%}.add-contact-btn[data-v-547d37c2]{display:flex;align-items:center;justify-content:center;gap:var(--spacing-xs);margin-top:var(--spacing-md);color:var(--primary-color);background-color:var(--primary-light);padding:var(--spacing-sm) 0;border-radius:var(--radius-md);border:%?1?% dashed var(--primary-color)}\n\n/* 客户类型选择器样式 */.type-selector[data-v-547d37c2]{display:flex;gap:var(--spacing-sm);margin-top:var(--spacing-xs)}.type-option[data-v-547d37c2]{flex:1;position:relative}.type-option uni-label[data-v-547d37c2]{display:flex;align-items:center;justify-content:center;height:%?84?%;padding:var(--spacing-sm);border:%?1?% solid var(--border-color);border-radius:var(--radius-md);background-color:#fff;transition:all .3s ease;text-align:center;font-size:%?28?%}\n\n/* radio:checked + label {\n  border-color: var(--primary-color);\n  background-color: var(--primary-light);\n  color: var(--primary-color);\n  font-weight: 500;\n} */\n\n/* 底部操作栏 */.bottom-bar[data-v-547d37c2]{position:fixed;bottom:0;left:0;right:0;background-color:#fff;border-top:%?1?% solid var(--border-color);padding:var(--spacing-md);display:flex;gap:var(--spacing-md);z-index:100}.bottom-bar .btn[data-v-547d37c2]{flex:1}\n\n/* 内容区域添加底部间距，避免被底部栏遮挡 */.page-content[data-v-547d37c2]{padding-bottom:%?160?%}.radio[data-v-547d37c2]{display:flex;justify-content:space-around}.basic-info-more[data-v-547d37c2]{display:flex;justify-content:center;align-items:center;margin-bottom:%?10?%}',""]),t.exports=a},fe69:function(t,a,e){"use strict";e.r(a);var i=e("1cb0"),n=e.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(a,t,(function(){return i[t]}))}(o);a["default"]=n.a}}]);
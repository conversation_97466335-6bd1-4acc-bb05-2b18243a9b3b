(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-contracts-payment-list"],{1880:function(t,e,a){"use strict";a.r(e);var n=a("1a37"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},"1a37":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4626"),a("5ac7"),a("bf0f"),a("bd06");var n={name:"CustomTabBar",data:function(){return{current:0,color:"#333333",activeColor:"#007AFF",showMoreMenu:!1,tabList:[{pagePath:"/pages/dashboard/main-dashboard",text:"首页",iconPath:"dashboard",selectedIconPath:"dashboard"},{pagePath:"/pages/customers/customer-list",text:"客户",iconPath:"customer",selectedIconPath:"customer"},{pagePath:"/pages/sales/opportunity-list",text:"销售",iconPath:"sales",selectedIconPath:"sales"},{type:"more",text:"更多",iconPath:"more",selectedIconPath:"more"},{pagePath:"/pages/settings/profile",text:"我的",iconPath:"user",selectedIconPath:"user"}],moreMenuList:[{pagePath:"/pages/marketing/leads",text:"线索",iconPath:"lead"},{pagePath:"/pages/interactions/interaction-list",text:"沟通",iconPath:"communication"},{pagePath:"/pages/sales/quotation-list",text:"报价",iconPath:"quotation"},{pagePath:"/pages/contracts/contract-list",text:"合同",iconPath:"contract"},{pagePath:"/pages/contracts/invoice-list",text:"发票",iconPath:"file-text"},{pagePath:"/pages/contracts/payment-list",text:"收款",iconPath:"money"},{pagePath:"/pages/reports/report-list",text:"报表",iconPath:"report"}]}},created:function(){this.updateCurrentTab()},onLoad:function(){this.updateCurrentTab()},onShow:function(){var t=this;setTimeout((function(){t.updateCurrentTab()}),100)},methods:{updateCurrentTab:function(){try{var t=getCurrentPages(),e=t[t.length-1];if(!e||!e.route)return;var a=e.route;console.log("当前路由:",a),a.includes("/pages/dashboard/")?this.current=0:a.includes("/pages/customers/")?this.current=1:a.includes("/pages/sales/")?this.current=2:a.includes("/pages/actions/")?this.current=3:a.includes("/pages/settings/")&&(this.current=5)}catch(n){console.error("更新Tab出错:",n)}},handleTabClick:function(t,e){"more"===t.type?(this.toggleMoreMenu(),this.current=e):this.switchTab(t.pagePath,e)},switchTab:function(t,e){this.current!==e&&(this.current=e,uni.switchTab({url:t}))},toggleMoreMenu:function(){this.showMoreMenu=!this.showMoreMenu},closeMoreMenu:function(){this.showMoreMenu=!1},navigateToPage:function(t){var e=this.tabList.some((function(e){return e.pagePath===t}));if(e){uni.switchTab({url:t});var a=this.tabList.findIndex((function(e){return e.pagePath===t}));-1!==a&&(this.current=a)}else uni.navigateTo({url:t});this.closeMoreMenu()}},watch:{$route:{handler:function(){this.updateCurrentTab()},immediate:!0}}};e.default=n},"30f7":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},a("7a76"),a("c9b5")},3655:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n}));var n={svgIcon:a("8a0f").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"container"},[a("v-uni-view",{staticClass:"page-header"},[a("v-uni-view",{staticClass:"back-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"arrow-left",type:"svg",size:"24"}})],1),a("v-uni-text",{staticClass:"page-title"},[t._v("收款管理")]),a("v-uni-view",{staticClass:"header-actions"},[a("v-uni-button",{staticClass:"action-button",attrs:{type:"button"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showFilterOptions.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"filter-3",type:"svg",size:"24"}})],1),a("v-uni-button",{staticClass:"action-button",attrs:{type:"button"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showSortOptions.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"sort-desc",type:"svg",size:"24"}})],1)],1)],1),a("v-uni-view",{staticClass:"totals-overview"},[a("v-uni-view",{staticClass:"total-card received"},[a("v-uni-text",{staticClass:"total-label"},[t._v("已收款金额")]),a("v-uni-text",{staticClass:"total-value"},[t._v("¥"+t._s(t.formatMoney(t.totals.received)))])],1),a("v-uni-view",{staticClass:"total-card pending"},[a("v-uni-text",{staticClass:"total-label"},[t._v("待收款金额")]),a("v-uni-text",{staticClass:"total-value"},[t._v("¥"+t._s(t.formatMoney(t.totals.pending)))])],1)],1),a("v-uni-view",{staticClass:"search-bar"},[a("svg-icon",{staticClass:"search-icon",attrs:{name:"search",type:"svg",size:"24"}}),a("v-uni-input",{staticClass:"search-input",attrs:{type:"text",placeholder:"搜索付款号、客户名称..."},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.onSearch.apply(void 0,arguments)}},model:{value:t.searchQuery,callback:function(e){t.searchQuery=e},expression:"searchQuery"}})],1),a("v-uni-scroll-view",{staticClass:"filter-bar",attrs:{"scroll-x":!0}},t._l(t.filters,(function(e,n){return a("v-uni-view",{key:n,class:["filter-button",t.currentFilter===e.value?"active":""],on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.setFilter(e.value)}}},[a("v-uni-text",[t._v(t._s(e.label))])],1)})),1),a("v-uni-scroll-view",{staticClass:"payment-list",attrs:{"scroll-y":!0,"refresher-enabled":!0,"refresher-triggered":t.isRefreshing},on:{refresherrefresh:function(e){arguments[0]=e=t.$handleEvent(e),t.onRefresh.apply(void 0,arguments)},scrolltolower:function(e){arguments[0]=e=t.$handleEvent(e),t.loadMore.apply(void 0,arguments)}}},[t.filteredPayments.length>0?a("v-uni-view",[t._l(t.filteredPayments,(function(e,n){return a("v-uni-view",{key:n,staticClass:"payment-item",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.goToDetail(e.id)}}},[a("v-uni-view",{staticClass:"payment-header"},[a("v-uni-text",{staticClass:"payment-title"},[t._v(t._s(e.title))]),a("v-uni-text",{class:["payment-status","status-"+e.status]},[t._v(t._s(t.getStatusText(e.status)))])],1),a("v-uni-view",{staticClass:"payment-info"},[a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"info-label"},[t._v("收款编号")]),a("v-uni-text",{staticClass:"info-value"},[t._v(t._s(e.paymentNumber))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"info-label"},[t._v("客户")]),a("v-uni-text",{staticClass:"info-value"},[t._v(t._s(e.customer))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"info-label"},[t._v(t._s(e.dateLabel))]),a("v-uni-text",{staticClass:"info-value"},[t._v(t._s(e.paymentDate||"-"))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"info-label"},[t._v(t._s(e.secondaryLabel))]),a("v-uni-text",{staticClass:"info-value"},[t._v(t._s(e.secondaryValue||"-"))])],1)],1),"partial"===e.status?a("v-uni-view",{staticClass:"progress-section"},[a("v-uni-view",{staticClass:"progress-container"},[a("v-uni-view",{staticClass:"progress-bar",style:{width:e.paidAmount/e.totalAmount*100+"%"}})],1),a("v-uni-view",{staticClass:"payment-progress"},[t._v("已收:"),a("v-uni-text",[t._v("¥"+t._s(t.formatMoney(e.paidAmount)))]),t._v("/ 总计:"),a("v-uni-text",[t._v("¥"+t._s(t.formatMoney(e.totalAmount)))])],1)],1):t._e(),a("v-uni-view",{staticClass:"payment-footer"},[a("v-uni-text",{staticClass:"payment-amount"},[t._v("¥"+t._s(t.formatMoney(e.amount)))]),a("v-uni-view",{staticClass:"payment-actions"},["completed"===e.status||"partial"===e.status?a("v-uni-view",{staticClass:"action-icon",on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.viewReceipt(e)}}},[a("svg-icon",{attrs:{name:"file-list-3",type:"svg",size:"20"}})],1):t._e(),"completed"===e.status?a("v-uni-view",{staticClass:"action-icon",on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.printReceipt(e)}}},[a("svg-icon",{attrs:{name:"printer",type:"svg",size:"20"}})],1):t._e(),"partial"===e.status?a("v-uni-view",{staticClass:"action-icon",on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.makePayment(e)}}},[a("svg-icon",{attrs:{name:"bank-card",type:"svg",size:"20"}})],1):t._e(),"pending"===e.status||"overdue"===e.status?a("v-uni-view",{staticClass:"action-icon",on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.sendReminder(e)}}},[a("svg-icon",{attrs:{name:"mail-send",type:"svg",size:"20"}})],1):t._e(),"overdue"===e.status?a("v-uni-view",{staticClass:"action-icon",on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.callCustomer(e)}}},[a("svg-icon",{attrs:{name:"phone",type:"svg",size:"20"}})],1):t._e(),"pending"===e.status?a("v-uni-view",{staticClass:"action-icon",on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.setReminder(e)}}},[a("svg-icon",{attrs:{name:"calendar-todo",type:"svg",size:"20"}})],1):t._e()],1)],1)],1)})),t.loadingMore?a("v-uni-view",{staticClass:"loading-more"},[a("v-uni-text",[t._v("加载更多...")])],1):t._e()],2):a("v-uni-view",{staticClass:"empty-state"},[a("v-uni-view",{staticClass:"empty-icon"},[a("svg-icon",{attrs:{name:"bank-card",type:"svg",size:"64"}})],1),a("v-uni-text",{staticClass:"empty-title"},[t._v("暂无收款记录")]),a("v-uni-text",{staticClass:"empty-text"},[t._v("创建您的第一笔收款记录，开始管理您的收款")]),a("v-uni-button",{staticClass:"create-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.createPayment.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"add",type:"svg",size:"20"}}),a("v-uni-text",[t._v("创建收款记录")])],1)],1)],1),a("v-uni-view",{staticClass:"floating-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.createPayment.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"add",type:"svg",size:"60",color:"#ffffff"}})],1),a("custom-tab-bar",{ref:"customTabBar"})],1)},o=[]},4733:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if(Array.isArray(t))return(0,n.default)(t)};var n=function(t){return t&&t.__esModule?t:{default:t}}(a("8d0b"))},"518b":function(t,e,a){"use strict";a.r(e);var n=a("efaa"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},5411:function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,".container[data-v-6b2dc3ee]{display:flex;flex-direction:column;height:100vh;background-color:#f5f5f5}.page-header[data-v-6b2dc3ee]{display:flex;align-items:center;justify-content:space-between;padding:12px 16px;border-bottom:1px solid #eee;background-color:#fff;z-index:10}.page-title[data-v-6b2dc3ee]{font-size:18px;font-weight:700;color:#333}.back-button[data-v-6b2dc3ee]{color:#666;font-size:24px}.header-actions[data-v-6b2dc3ee]{display:flex;gap:8px}.action-button[data-v-6b2dc3ee]{width:36px;height:36px;display:flex;align-items:center;justify-content:center;border-radius:50%;color:#666;background-color:#f5f5f5;border:1px solid #eee;padding:0;margin:0;font-size:20px}.totals-overview[data-v-6b2dc3ee]{display:grid;grid-template-columns:repeat(2,1fr);gap:10px;margin:15px;margin-bottom:20px}.total-card[data-v-6b2dc3ee]{background-color:#fff;border-radius:8px;padding:15px;box-shadow:0 2px 4px rgba(0,0,0,.05);border:1px solid #eee}.total-label[data-v-6b2dc3ee]{font-size:13px;color:#999;margin-bottom:8px}.total-value[data-v-6b2dc3ee]{font-size:22px;font-weight:600;color:#333}.total-card.received .total-value[data-v-6b2dc3ee]{color:#00b578}.total-card.pending .total-value[data-v-6b2dc3ee]{color:#ff9a2a}.search-bar[data-v-6b2dc3ee]{margin:0 15px 15px;position:relative}.search-input[data-v-6b2dc3ee]{width:100%;padding:10px 12px;padding-left:40px;border-radius:8px;border:1px solid #eee;background-color:#f7f7f7;font-size:14px;color:#333;box-sizing:border-box}.search-icon[data-v-6b2dc3ee]{position:absolute;left:12px;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#999;font-size:20px}.filter-bar[data-v-6b2dc3ee]{display:flex;white-space:nowrap;padding:0 15px;margin-bottom:15px}.filter-button[data-v-6b2dc3ee]{display:inline-block;background-color:#f5f5f5;border:1px solid #eee;border-radius:16px;padding:6px 12px;font-size:14px;color:#666;margin-right:10px}.filter-button.active[data-v-6b2dc3ee]{background-color:#3a86ff;color:#fff;border-color:#3a86ff}.payment-list[data-v-6b2dc3ee]{flex:1;margin-bottom:80px}.payment-item[data-v-6b2dc3ee]{display:flex;flex-direction:column;padding:16px;background-color:#fff;border-radius:8px;margin:0 15px 15px;box-shadow:0 2px 4px rgba(0,0,0,.05);border:1px solid #eee}.payment-header[data-v-6b2dc3ee]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:12px}.payment-title[data-v-6b2dc3ee]{font-size:16px;font-weight:600;color:#333;margin-right:10px}.payment-status[data-v-6b2dc3ee]{padding:2px 8px;border-radius:12px;font-size:12px;font-weight:500}.status-completed[data-v-6b2dc3ee]{background-color:#e6f7ee;color:#00b578}.status-partial[data-v-6b2dc3ee]{background-color:#fff5e6;color:#ff9a2a}.status-pending[data-v-6b2dc3ee]{background-color:#e6f4ff;color:#1890ff}.status-overdue[data-v-6b2dc3ee]{background-color:#ffece8;color:#ff4d4f}.payment-info[data-v-6b2dc3ee]{display:grid;grid-template-columns:repeat(2,1fr);gap:10px;margin-bottom:10px}.info-item[data-v-6b2dc3ee]{display:flex;flex-direction:column}.info-label[data-v-6b2dc3ee]{font-size:12px;color:#999;margin-bottom:2px}.info-value[data-v-6b2dc3ee]{font-size:14px;color:#333;font-weight:500}.progress-section[data-v-6b2dc3ee]{margin-bottom:10px}.progress-container[data-v-6b2dc3ee]{background-color:#f5f5f5;border-radius:8px;height:6px;overflow:hidden}.progress-bar[data-v-6b2dc3ee]{height:100%;background-color:#00b578}.payment-progress[data-v-6b2dc3ee]{margin-top:5px;font-size:12px;color:#999}.payment-progress uni-text[data-v-6b2dc3ee]{font-weight:500;color:#333}.payment-footer[data-v-6b2dc3ee]{display:flex;justify-content:space-between;align-items:center;margin-top:5px;padding-top:10px;border-top:1px solid #f0f0f0}.payment-amount[data-v-6b2dc3ee]{font-size:18px;font-weight:600;color:#3a86ff}.payment-actions[data-v-6b2dc3ee]{display:flex;gap:8px}.action-icon[data-v-6b2dc3ee]{width:32px;height:32px;display:flex;align-items:center;justify-content:center;border-radius:50%;color:#666;background-color:#f5f5f5}.floating-button[data-v-6b2dc3ee]{position:fixed;bottom:calc(%?128?% + var(--spacing-xl)); /* 调整底部位置，避开TabBar */right:var(--spacing-xl);width:%?110?%; /* 减小尺寸 */height:%?110?%; /* 减小尺寸 */border-radius:50%;background:linear-gradient(135deg,#0a6bff,#0057ff);color:#fff;display:flex;align-items:center;justify-content:center;box-shadow:0 %?10?% %?20?% rgba(0,87,255,.6),0 %?6?% %?6?% rgba(0,87,255,.4);z-index:100;transition:all .3s ease;border:%?4?% solid hsla(0,0%,100%,.7);-webkit-animation:pulse-data-v-6b2dc3ee 2s infinite;animation:pulse-data-v-6b2dc3ee 2s infinite /* 添加脉动动画 */}.floating-button[data-v-6b2dc3ee]:active{-webkit-transform:scale(.95);transform:scale(.95);box-shadow:0 %?5?% %?10?% rgba(0,87,255,.5),0 %?3?% %?3?% rgba(0,87,255,.3);-webkit-animation:none;animation:none /* 点击时停止动画 */}\n\n/* 添加脉动动画 */@-webkit-keyframes pulse-data-v-6b2dc3ee{0%{-webkit-transform:scale(1);transform:scale(1);box-shadow:0 %?10?% %?20?% rgba(0,87,255,.6),0 %?6?% %?6?% rgba(0,87,255,.4)}50%{-webkit-transform:scale(1.05);transform:scale(1.05);box-shadow:0 %?15?% %?25?% rgba(0,87,255,.7),0 %?8?% %?10?% rgba(0,87,255,.5)}100%{-webkit-transform:scale(1);transform:scale(1);box-shadow:0 %?10?% %?20?% rgba(0,87,255,.6),0 %?6?% %?6?% rgba(0,87,255,.4)}}@keyframes pulse-data-v-6b2dc3ee{0%{-webkit-transform:scale(1);transform:scale(1);box-shadow:0 %?10?% %?20?% rgba(0,87,255,.6),0 %?6?% %?6?% rgba(0,87,255,.4)}50%{-webkit-transform:scale(1.05);transform:scale(1.05);box-shadow:0 %?15?% %?25?% rgba(0,87,255,.7),0 %?8?% %?10?% rgba(0,87,255,.5)}100%{-webkit-transform:scale(1);transform:scale(1);box-shadow:0 %?10?% %?20?% rgba(0,87,255,.6),0 %?6?% %?6?% rgba(0,87,255,.4)}}.empty-state[data-v-6b2dc3ee]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:32px 20px;text-align:center;margin-top:20px}.empty-icon[data-v-6b2dc3ee]{font-size:64px;color:#ddd;margin-bottom:16px}.empty-title[data-v-6b2dc3ee]{font-size:18px;font-weight:600;color:#333;margin-bottom:8px}.empty-text[data-v-6b2dc3ee]{font-size:14px;color:#999;margin-bottom:24px}.create-button[data-v-6b2dc3ee]{padding:10px 20px;background-color:#3a86ff;color:#fff;border:none;border-radius:8px;font-size:14px;font-weight:500;display:flex;align-items:center;gap:8px}.loading-more[data-v-6b2dc3ee]{text-align:center;padding:20px 0;color:#999;font-size:14px}",""]),t.exports=e},7775:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n}));var n={svgIcon:a("8a0f").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"custom-tab-bar"},[t._l(t.tabList,(function(e,n){return a("v-uni-view",{key:n,staticClass:"tab-item",class:{active:t.current===n},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.handleTabClick(e,n)}}},[a("svg-icon",{attrs:{name:t.current===n?e.selectedIconPath:e.iconPath,type:"svg",size:24,color:t.current===n?t.activeColor:t.color}}),a("v-uni-text",{staticClass:"tab-text",class:{"active-text":t.current===n}},[t._v(t._s(e.text))])],1)})),t.showMoreMenu?a("v-uni-view",{staticClass:"more-menu"},[a("v-uni-view",{staticClass:"menu-overlay",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeMoreMenu.apply(void 0,arguments)}}}),a("v-uni-view",{staticClass:"menu-content"},[a("v-uni-view",{staticClass:"menu-header"},[a("v-uni-text",{staticClass:"menu-title"},[t._v("更多功能")]),a("v-uni-view",{staticClass:"menu-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeMoreMenu.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"close",type:"svg",size:32,color:"#666"}})],1)],1),a("v-uni-view",{staticClass:"menu-list"},t._l(t.moreMenuList,(function(e,n){return a("v-uni-view",{key:n,staticClass:"menu-item",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.navigateToPage(e.pagePath)}}},[a("svg-icon",{attrs:{name:e.iconPath,type:"svg",size:24,color:"#333333"}}),a("v-uni-text",{staticClass:"menu-item-text"},[t._v(t._s(e.text))])],1)})),1)],1)],1):t._e()],2)},o=[]},ab74:function(t,e,a){var n=a("5411");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("967d").default;i("7d234a68",n,!0,{sourceMap:!1,shadowMode:!1})},b7c7:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,n.default)(t)||(0,i.default)(t)||(0,o.default)(t)||(0,s.default)()};var n=r(a("4733")),i=r(a("d14d")),o=r(a("5d6b")),s=r(a("30f7"));function r(t){return t&&t.__esModule?t:{default:t}}},bf69:function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,".custom-tab-bar[data-v-6a709636]{display:flex;justify-content:space-around;align-items:center;background-color:#fff;box-shadow:0 -1px 5px rgba(0,0,0,.1);height:%?100?%;position:fixed;bottom:0;left:0;right:0;z-index:999;padding-bottom:env(safe-area-inset-bottom)}.tab-item[data-v-6a709636]{display:flex;flex-direction:column;align-items:center;justify-content:center;flex:1;height:100%;padding:%?10?% 0}.tab-text[data-v-6a709636]{font-size:%?22?%;color:#333;margin-top:%?4?%}.active-text[data-v-6a709636]{color:#007aff}\n\n/* 更多菜单样式 */.more-menu[data-v-6a709636]{position:fixed;top:0;right:0;bottom:0;left:0;z-index:1000}.menu-overlay[data-v-6a709636]{position:absolute;top:0;right:0;bottom:0;left:0;background-color:rgba(0,0,0,.5)}.menu-content[data-v-6a709636]{position:absolute;bottom:%?100?%;left:0;right:0;background-color:#fff;border-top-left-radius:%?20?%;border-top-right-radius:%?20?%;overflow:hidden;-webkit-animation:slideUp-data-v-6a709636 .3s ease;animation:slideUp-data-v-6a709636 .3s ease;box-shadow:0 -2px 10px rgba(0,0,0,.1)}.menu-header[data-v-6a709636]{display:flex;justify-content:space-between;align-items:center;padding:%?30?%;border-bottom:1px solid #f0f0f0}.menu-title[data-v-6a709636]{font-size:%?32?%;font-weight:500;color:#333}.menu-close[data-v-6a709636]{padding:%?10?%}.menu-list[data-v-6a709636]{display:flex;flex-wrap:wrap;padding:%?20?%}.menu-item[data-v-6a709636]{width:25%;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?20?% 0}.menu-item-text[data-v-6a709636]{font-size:%?24?%;color:#333;margin-top:%?10?%;text-align:center}@-webkit-keyframes slideUp-data-v-6a709636{from{-webkit-transform:translateY(100%);transform:translateY(100%)}to{-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes slideUp-data-v-6a709636{from{-webkit-transform:translateY(100%);transform:translateY(100%)}to{-webkit-transform:translateY(0);transform:translateY(0)}}",""]),t.exports=e},c9cf:function(t,e,a){"use strict";var n=a("e9f7"),i=a.n(n);i.a},d14d:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},a("01a2"),a("e39c"),a("bf0f"),a("844d"),a("18f7"),a("de6c"),a("08eb")},d875:function(t,e,a){"use strict";var n=a("ab74"),i=a.n(n);i.a},e9f7:function(t,e,a){var n=a("bf69");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("967d").default;i("9e21a296",n,!0,{sourceMap:!1,shadowMode:!1})},eab4:function(t,e,a){"use strict";a.r(e);var n=a("7775"),i=a("1880");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("c9cf");var s=a("828b"),r=Object(s["a"])(i["default"],n["b"],n["c"],!1,null,"6a709636",null,!1,n["a"],void 0);e["default"]=r.exports},efaa:function(t,e,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("8f71"),a("bf0f"),a("4626"),a("5ac7");var i=n(a("b7c7")),o=n(a("eab4")),s=n(a("8a0f")),r={components:{CustomTabBar:o.default,SvgIcon:s.default},data:function(){return{searchQuery:"",currentFilter:"all",filters:[{label:"全部",value:"all"},{label:"已完成",value:"completed"},{label:"部分付款",value:"partial"},{label:"待付款",value:"pending"},{label:"已逾期",value:"overdue"}],totals:{received:487290,pending:294750},payments:[{id:"1",title:"系统集成项目 - 第一期",status:"completed",paymentNumber:"PAY-2023-11-001",customer:"上海智能科技",paymentDate:"2023-11-12",dateLabel:"收款日期",secondaryLabel:"付款方式",secondaryValue:"银行转账",amount:290975},{id:"2",title:"软件维护服务 - 季度账单",status:"partial",paymentNumber:"PAY-2023-11-002",customer:"北京电子科技",paymentDate:"2023-11-18",dateLabel:"收款日期",secondaryLabel:"付款方式",secondaryValue:"支付宝",amount:37500,paidAmount:37500,totalAmount:75e3},{id:"3",title:"数据迁移项目",status:"overdue",paymentNumber:"PAY-2023-11-003",customer:"广州数联网络",paymentDate:"2023-11-10",dateLabel:"应收日期",secondaryLabel:"逾期天数",secondaryValue:"14天",amount:125400},{id:"4",title:"培训服务费用",status:"pending",paymentNumber:"PAY-2023-11-004",customer:"成都星辰科技",paymentDate:"2023-12-05",dateLabel:"应收日期",secondaryLabel:"剩余天数",secondaryValue:"11天",amount:45e3}],isRefreshing:!1,loadingMore:!1,page:1,hasMore:!0}},computed:{filteredPayments:function(){var t=this,e=(0,i.default)(this.payments);if(this.searchQuery){var a=this.searchQuery.toLowerCase();e=e.filter((function(t){return t.title.toLowerCase().includes(a)||t.paymentNumber.toLowerCase().includes(a)||t.customer.toLowerCase().includes(a)}))}return"all"!==this.currentFilter&&(e=e.filter((function(e){return e.status===t.currentFilter}))),e}},methods:{goBack:function(){uni.navigateBack()},formatMoney:function(t){return t.toLocaleString("zh-CN")},getStatusText:function(t){return{completed:"已完成",partial:"部分付款",pending:"待付款",overdue:"已逾期"}[t]||t},setFilter:function(t){this.currentFilter=t},onSearch:function(t){this.searchQuery=t.detail.value},onRefresh:function(){var t=this;this.isRefreshing=!0,setTimeout((function(){t.loadPayments(),t.isRefreshing=!1,uni.showToast({title:"刷新成功",icon:"success"})}),1e3)},loadMore:function(){var t=this;this.hasMore&&!this.loadingMore&&(this.loadingMore=!0,setTimeout((function(){t.page++,t.page>3&&(t.hasMore=!1),t.loadingMore=!1}),1e3))},loadPayments:function(){this.page=1,this.hasMore=!0},goToDetail:function(t){uni.navigateTo({url:"/pages/contracts/payment-detail?id=".concat(t)})},createPayment:function(){uni.navigateTo({url:"/pages/contracts/payment-create"})},viewReceipt:function(t){uni.showToast({title:"查看收款凭证功能开发中...",icon:"none"})},printReceipt:function(t){uni.showToast({title:"打印收款记录功能开发中...",icon:"none"})},makePayment:function(t){uni.navigateTo({url:"/pages/contracts/payment-create?relatedPaymentId=".concat(t.id)})},sendReminder:function(t){uni.showToast({title:"发送付款提醒功能开发中...",icon:"none"})},callCustomer:function(t){uni.showToast({title:"电话提醒功能开发中...",icon:"none"})},setReminder:function(t){uni.showToast({title:"设置日程提醒功能开发中...",icon:"none"})},showFilterOptions:function(){uni.showToast({title:"高级筛选功能开发中...",icon:"none"})},showSortOptions:function(){uni.showActionSheet({itemList:["最新收款","预计收款日期","金额大小","客户名称"],success:function(t){var e=["date","expected","amount","customer"][t.tapIndex];console.log("选择的排序方式:",e)}})}},onLoad:function(){this.loadPayments()},onShow:function(){"undefined"!==typeof this.$refs.customTabBar&&(this.$refs.customTabBar.current=4)}};e.default=r},f310:function(t,e,a){"use strict";a.r(e);var n=a("3655"),i=a("518b");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("d875");var s=a("828b"),r=Object(s["a"])(i["default"],n["b"],n["c"],!1,null,"6b2dc3ee",null,!1,n["a"],void 0);e["default"]=r.exports}}]);
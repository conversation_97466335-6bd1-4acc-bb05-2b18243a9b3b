<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="navigateBack">
        <text class="ri-arrow-left-line"></text>
      </view>
      <text class="page-title">合同条款</text>
      <view class="header-actions">
        <button type="button" class="action-button" @click="shareContract">
          <text class="ri-share-line"></text>
        </button>
      </view>
    </view>
    
    <!-- 内容区域 -->
    <scroll-view scroll-y class="terms-container">
      <!-- 合同基本信息 -->
      <view class="contract-info">
        <text class="contract-title">{{contract.title}}</text>
        <view class="contract-meta">
          <text>合同编号: {{contract.number}}</text>
          <text>版本: {{contract.version}}</text>
        </view>
        <view class="contract-meta">
          <text>签署日期: {{contract.signingDate}}</text>
          <text>有效期至: {{contract.expiryDate}}</text>
        </view>
      </view>
      
      <!-- 合同主体 -->
      <view class="section-card">
        <text class="section-title">合同主体</text>
        <view class="terms-content">
          <view class="terms-paragraph">
            <text class="terms-title">甲方（客户）：</text>
            <text>{{contract.customer.name}}（以下简称"甲方"）</text>
            <text>地址：{{contract.customer.address}}</text>
            <text>联系人：{{contract.customer.contact}}</text>
            <text>电话：{{contract.customer.phone}}</text>
          </view>
          
          <view class="terms-paragraph">
            <text class="terms-title">乙方（服务提供商）：</text>
            <text>{{contract.provider.name}}（以下简称"乙方"）</text>
            <text>地址：{{contract.provider.address}}</text>
            <text>联系人：{{contract.provider.contact}}</text>
            <text>电话：{{contract.provider.phone}}</text>
          </view>
        </view>
      </view>
      
      <!-- 项目概述 -->
      <view class="section-card">
        <text class="section-title">项目概述</text>
        <view class="terms-content">
          <text class="terms-paragraph">{{contract.projectOverview}}</text>
          
          <view class="terms-paragraph">
            <text class="terms-title">项目范围：</text>
            <view class="terms-list">
              <text v-for="(item, index) in contract.projectScope" :key="index" class="list-item">{{index + 1}}. {{item}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 费用与支付 -->
      <view class="section-card">
        <text class="section-title">费用与支付</text>
        <view class="terms-content">
          <view class="terms-paragraph">
            <text class="terms-title">合同总金额：</text>
            <text>人民币 {{contract.totalAmount}} 元（大写：{{contract.totalAmountChinese}}），包含增值税（税率{{contract.taxRate}}%）。</text>
          </view>
          
          <view class="terms-paragraph">
            <text class="terms-title">费用明细：</text>
            <view class="terms-list">
              <text v-for="(item, index) in contract.costDetails" :key="index" class="list-item">· {{item.name}}：¥{{item.amount}}</text>
            </view>
          </view>
          
          <view class="terms-paragraph">
            <text class="terms-title">支付方式：</text>
            <view class="terms-list">
              <text v-for="(item, index) in contract.paymentTerms" :key="index" class="list-item">{{index + 1}}. {{item.description}}：{{item.percentage}}%，即¥{{item.amount}}</text>
            </view>
          </view>
          
          <view class="terms-paragraph">
            <text class="terms-title">付款方式：</text>
            <text>{{contract.paymentMethod}}</text>
          </view>
        </view>
      </view>
      
      <!-- 项目周期 -->
      <view class="section-card">
        <text class="section-title">项目周期</text>
        <view class="terms-content">
          <view class="terms-paragraph">
            <text class="terms-title">项目总周期：</text>
            <text>{{contract.projectDuration}}</text>
          </view>
          
          <view class="terms-paragraph">
            <text class="terms-title">项目里程碑：</text>
            <view class="terms-list">
              <text v-for="(item, index) in contract.milestones" :key="index" class="list-item">{{index + 1}}. {{item.name}}：{{item.duration}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 知识产权 -->
      <view class="section-card">
        <text class="section-title">知识产权</text>
        <view class="terms-content">
          <text v-for="(paragraph, index) in contract.intellectualProperty" :key="index" class="terms-paragraph">{{paragraph}}</text>
        </view>
      </view>
      
      <!-- 保密条款 -->
      <view class="section-card">
        <text class="section-title">保密条款</text>
        <view class="terms-content">
          <text class="terms-paragraph">{{contract.confidentiality.main}}</text>
          
          <text class="terms-paragraph">本保密义务不适用于以下信息：</text>
          <view class="terms-list">
            <text v-for="(item, index) in contract.confidentiality.exceptions" :key="index" class="list-item">{{index + 1}}. {{item}}</text>
          </view>
          
          <text class="terms-paragraph">{{contract.confidentiality.period}}</text>
        </view>
      </view>
      
      <!-- 违约责任 -->
      <view class="section-card">
        <text class="section-title">违约责任</text>
        <view class="terms-content">
          <text class="terms-paragraph">{{contract.liability.general}}</text>
          
          <view class="terms-paragraph">
            <text class="terms-title">甲方违约：</text>
            <text>{{contract.liability.customer}}</text>
          </view>
          
          <view class="terms-paragraph">
            <text class="terms-title">乙方违约：</text>
            <text>{{contract.liability.provider}}</text>
          </view>
        </view>
      </view>
      
      <!-- 争议解决 -->
      <view class="section-card">
        <text class="section-title">争议解决</text>
        <view class="terms-content">
          <text v-for="(paragraph, index) in contract.disputeResolution" :key="index" class="terms-paragraph">{{paragraph}}</text>
        </view>
      </view>
      
      <!-- 其他条款 -->
      <view class="section-card">
        <text class="section-title">其他条款</text>
        <view class="terms-content">
          <text v-for="(paragraph, index) in contract.otherTerms" :key="index" class="terms-paragraph">{{paragraph}}</text>
        </view>
      </view>
    </scroll-view>
    
    <!-- 打印按钮 -->
    <view class="print-button" @click="printContract">
      <text class="ri-printer-line"></text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      contract: {
        title: '企业系统集成项目合同',
        number: 'CT-2023-09-001',
        version: 'V1.0',
        signingDate: '2023-10-25',
        expiryDate: '2024-10-24',
        customer: {
          name: '上海智能科技有限公司',
          address: '上海市浦东新区张江高科技园区科苑路88号',
          contact: '张总监',
          phone: '13812345678'
        },
        provider: {
          name: '优信科技有限公司',
          address: '上海市静安区南京西路1788号',
          contact: '王经理',
          phone: '13987654321'
        },
        projectOverview: '甲方委托乙方为其提供企业系统集成服务，包括但不限于系统设计、开发、集成、测试、部署、培训和技术支持等工作。双方经友好协商，在平等自愿的基础上，达成如下协议。',
        projectScope: [
          '企业资源规划（ERP）系统与客户关系管理（CRM）系统的集成',
          '数据仓库和商业智能（BI）平台建设',
          '系统性能优化和安全加固',
          '移动应用开发与集成',
          '员工培训及技术文档编写'
        ],
        totalAmount: '581,950',
        totalAmountChinese: '伍拾捌万壹仟玖佰伍拾元整',
        taxRate: 13,
        costDetails: [
          { name: '系统集成', amount: '320,000' },
          { name: '定制开发', amount: '150,000' },
          { name: '培训与支持', amount: '45,000' },
          { name: '增值税（13%）', amount: '66,950' }
        ],
        paymentTerms: [
          { description: '首付款：合同签署后7天内支付总额的50%', percentage: 50, amount: '290,975' },
          { description: '中期款：项目交付后7天内支付总额的30%', percentage: 30, amount: '174,585' },
          { description: '尾款：项目验收通过后7天内支付总额的20%', percentage: 20, amount: '116,390' }
        ],
        paymentMethod: '甲方应通过电汇方式将款项支付至乙方指定的银行账户。',
        projectDuration: '自合同签署之日起，总计90个工作日。',
        milestones: [
          { name: '需求分析与方案设计', duration: '20个工作日' },
          { name: '系统开发与集成', duration: '40个工作日' },
          { name: '系统测试与优化', duration: '15个工作日' },
          { name: '系统上线与验收', duration: '10个工作日' },
          { name: '培训与文档交付', duration: '5个工作日' }
        ],
        intellectualProperty: [
          '本项目开发过程中产生的所有成果（包括但不限于源代码、设计文档、用户手册等）的知识产权归甲方所有，乙方对项目开发过程中产生的源代码、文档等不得用于本合同之外的其他用途。',
          '乙方保证其交付的开发成果不侵犯任何第三方的知识产权。如因此发生纠纷，由乙方负责解决并赔偿甲方因此遭受的所有损失。',
          '在开发过程中使用的第三方软件、组件或库，乙方应确保已获得合法授权，相关授权证明应随交付成果一并提交给甲方。'
        ],
        confidentiality: {
          main: '双方应对在合作过程中获知的对方的商业秘密、技术信息、经营信息及其他标明保密的信息（以下简称"保密信息"）严格保密，未经信息所有方书面同意，不得向任何第三方披露。',
          exceptions: [
            '在披露前已为接收方合法拥有的信息',
            '已公开或非因接收方原因而公开的信息',
            '接收方从无保密义务的第三方合法获得的信息',
            '根据法律法规或政府机构要求必须披露的信息'
          ],
          period: '本保密条款的有效期为合同终止后三年。'
        },
        liability: {
          general: '任何一方违反本合同约定，应当承担违约责任，赔偿因此给对方造成的损失。',
          customer: '如甲方未按合同约定时间付款，每逾期一日，应向乙方支付应付款项0.05%的违约金。逾期超过30日，乙方有权终止合同，甲方应支付合同总金额30%的违约金。',
          provider: '如乙方未按合同约定时间交付成果，每逾期一日，应向甲方支付合同总金额0.05%的违约金。逾期超过30日，甲方有权终止合同，乙方应返还已收取的款项，并支付合同总金额30%的违约金。'
        },
        disputeResolution: [
          '双方因履行本合同而发生的争议，应首先通过友好协商解决。协商不成的，任何一方均有权向合同签订地有管辖权的人民法院提起诉讼。',
          '本合同的订立、效力、解释、履行及争议的解决均适用中华人民共和国法律。'
        ],
        otherTerms: [
          '本合同自双方法定代表人或授权代表签字并加盖公章之日起生效。',
          '本合同一式两份，甲乙双方各执一份，具有同等法律效力。',
          '本合同附件与合同具有同等法律效力。如附件与合同正文有冲突，以合同正文为准。',
          '未尽事宜，双方可另行协商并签订补充协议。补充协议与本合同具有同等法律效力。'
        ]
      }
    }
  },
  onLoad(options) {
    // 获取合同ID
    if (options.id) {
      this.loadContractTerms(options.id);
    }
  },
  methods: {
    navigateBack() {
      uni.navigateBack();
    },
    shareContract() {
      uni.showToast({
        title: '分享功能开发中...',
        icon: 'none'
      });
    },
    printContract() {
      uni.showToast({
        title: '打印功能开发中...',
        icon: 'none'
      });
    },
    loadContractTerms(contractId) {
      // 实际应用中，这里应该请求API获取合同条款数据
      uni.showLoading({
        title: '加载中...'
      });
      
      // 模拟请求
      setTimeout(() => {
        uni.hideLoading();
        // 实际应用中，这里应该是从API返回数据后更新this.contract
      }, 500);
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #eaeaea;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.back-button {
  color: #666;
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.action-button {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #666;
  background-color: #f5f7fa;
  border: 1px solid #eaeaea;
}

.terms-container {
  flex: 1;
  padding: 20rpx;
  margin-bottom: 140rpx;
}

.contract-info {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1px solid #eaeaea;
}

.contract-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  color: #333;
  display: block;
}

.contract-meta {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: flex;
  justify-content: space-between;
}

.section-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1px solid #eaeaea;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  margin-bottom: 24rpx;
  padding-bottom: 12rpx;
  border-bottom: 1px solid #eef0f5;
  color: #333;
  display: block;
}

.terms-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

.terms-paragraph {
  margin-bottom: 24rpx;
  display: block;
}

.terms-title {
  font-weight: 600;
  margin-bottom: 8rpx;
  font-size: 28rpx;
  display: block;
}

.terms-list {
  padding-left: 20rpx;
  margin: 16rpx 0;
}

.list-item {
  margin-bottom: 12rpx;
  display: block;
}

.print-button {
  position: fixed;
  bottom: 140rpx;
  right: 30rpx;
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  background-color: #3370ff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  z-index: 100;
}

/* 响应式布局 */
@media (min-width: 768px) {
  .terms-container {
    max-width: 700px;
    margin: 0 auto;
    padding: 30rpx;
  }
}
</style> 
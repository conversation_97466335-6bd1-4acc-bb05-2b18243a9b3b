<template>
</template>


<script>
import { getTenantInfo } from '@/utils/request';
import { loginApi } from "@/api/account.api";
export default {
  data() {
    return {
      isLoading: false,
    };
  },
  computed: {
    canLogin() {
      return this.loginForm.name && this.loginForm.password;
    },
  },
  methods: {
    async login() {
      try {
        // 1. 先获取租户信息
        await getTenantInfo({
          emailPhoneNumber: this.loginForm.name,
          password: this.loginForm.password
        });
        // 2. 再调用登录接口
        const loginRes = await loginApi(this.loginForm);
        uni.switchTab({
          url: "/pages/dashboard/main-dashboard",
        });
        uni.setStorageSync("token", loginRes.token);
        uni.setStorageSync("expirationTime", loginRes.expirationTime);
        uni.setStorageSync("userInfo", loginRes);
      } catch (error) {
        console.error("登录失败", error);
      }
    },
    togglePasswordVisibility() {
      this.passwordVisible = !this.passwordVisible;
    },
    toggleRememberMe() {
      this.rememberMe = !this.rememberMe;
    },
    handleLogin() {
      this.login();
    },
    loginWithWechat() {
      uni.showToast({
        title: "微信登录功能开发中",
        icon: "none",
      });
    },
    loginWithSms() {
      // 跳转到短信登录页面或展示短信登录模态框
      uni.showToast({
        title: "短信登录功能开发中",
        icon: "none",
      });
    },
  },
};
</script>

<style>
</style>
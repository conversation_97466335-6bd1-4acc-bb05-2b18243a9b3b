(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-contracts-payment-detail"],{"2f93":function(t,e,i){"use strict";i.r(e);var a=i("fe83"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"2fa3":function(t,e,i){var a=i("e9dc");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("357de42a",a,!0,{sourceMap:!1,shadowMode:!1})},8546:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={svgIcon:i("8a0f").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"page-container"},[i("v-uni-view",{staticClass:"page-header"},[i("v-uni-view",{staticClass:"back-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[i("svg-icon",{attrs:{name:"arrow-left",type:"svg",size:"24"}})],1),i("v-uni-text",{staticClass:"page-title"},[t._v("收款详情")]),i("v-uni-view",{staticClass:"header-actions"},[i("v-uni-view",{staticClass:"action-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showMenu.apply(void 0,arguments)}}},[i("svg-icon",{attrs:{name:"more",type:"svg",size:"24"}})],1)],1)],1),i("v-uni-scroll-view",{staticClass:"payment-container",attrs:{"scroll-y":!0}},[i("v-uni-view",{staticClass:"section-card"},[i("v-uni-view",{staticClass:"section-title"},[i("v-uni-text",[t._v(t._s(t.payment.title))]),i("v-uni-text",{class:["status-badge","status-"+t.payment.statusClass]},[t._v(t._s(t.payment.statusText))])],1),i("v-uni-view",{staticClass:"info-grid"},[i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("收款编号")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.payment.code))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("关联发票")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.payment.invoice))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("关联合同")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.payment.contract))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("收款日期")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.payment.date))])],1)],1)],1),i("v-uni-view",{staticClass:"section-card"},[i("v-uni-text",{staticClass:"section-title"},[t._v("收款金额")]),i("v-uni-view",{staticClass:"amount-container"},[i("v-uni-view",{staticClass:"amount-row"},[i("v-uni-text",{staticClass:"amount-label"},[t._v("应收金额")]),i("v-uni-text",{staticClass:"amount-value"},[t._v("¥"+t._s(t.payment.amountDue))])],1),i("v-uni-view",{staticClass:"amount-row"},[i("v-uni-text",{staticClass:"amount-label"},[t._v("实收金额")]),i("v-uni-text",{staticClass:"amount-value"},[t._v("¥"+t._s(t.payment.amountReceived))])],1),i("v-uni-view",{staticClass:"amount-row total-row"},[i("v-uni-text",{staticClass:"amount-label"},[t._v("差额")]),i("v-uni-text",{staticClass:"amount-value"},[t._v("¥"+t._s(t.payment.difference))])],1)],1),i("v-uni-view",{staticClass:"payment-method"},[i("v-uni-view",{staticClass:"payment-method-icon"},[i("svg-icon",{attrs:{name:"bank",type:"svg",size:"24"}})],1),i("v-uni-view",{staticClass:"payment-method-details"},[i("v-uni-text",{staticClass:"payment-method-name"},[t._v(t._s(t.payment.method))]),i("v-uni-text",{staticClass:"payment-method-info"},[t._v(t._s(t.payment.methodDetails))])],1)],1)],1),i("v-uni-view",{staticClass:"section-card"},[i("v-uni-text",{staticClass:"section-title"},[t._v("客户信息")]),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("客户名称")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.payment.customer.name))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("联系人")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.payment.customer.contact))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("开户行")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.payment.customer.bank))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"info-label"},[t._v("账号")]),i("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.payment.customer.account))])],1)],1),i("v-uni-view",{staticClass:"section-card"},[i("v-uni-text",{staticClass:"section-title"},[t._v("关联单据")]),i("v-uni-view",{staticClass:"linked-docs"},t._l(t.payment.linkedDocs,(function(e,a){return i("v-uni-view",{key:a,staticClass:"linked-doc-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.viewLinkedDoc(e)}}},[i("v-uni-view",{class:["doc-icon","doc-"+e.type]},[i("svg-icon",{attrs:{name:"invoice"===e.type?"file-text":"contract",type:"svg",size:"24"}})],1),i("v-uni-view",{staticClass:"doc-details"},[i("v-uni-text",{staticClass:"doc-title"},[t._v(t._s(e.title))]),i("v-uni-text",{staticClass:"doc-meta"},[t._v(t._s(e.meta))])],1),i("svg-icon",{attrs:{name:"arrow-right",type:"svg",size:"20"}})],1)})),1)],1),i("v-uni-view",{staticClass:"section-card"},[i("v-uni-text",{staticClass:"section-title"},[t._v("收款凭证")]),i("v-uni-view",{staticClass:"file-list"},t._l(t.payment.files,(function(e,a){return i("v-uni-view",{key:a,staticClass:"file-item"},[i("svg-icon",{attrs:{name:"pdf"===e.type?"file-pdf":"image",type:"svg",size:"24"}}),i("v-uni-text",[t._v(t._s(e.name))]),i("v-uni-view",{staticClass:"download-button",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.downloadFile(e)}}},[i("svg-icon",{attrs:{name:"download",type:"svg",size:"20"}})],1)],1)})),1),i("v-uni-view",{staticClass:"add-note-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.uploadFile.apply(void 0,arguments)}}},[i("svg-icon",{attrs:{name:"upload",type:"svg",size:"20"}}),i("v-uni-text",[t._v("上传凭证")])],1)],1),i("v-uni-view",{staticClass:"section-card"},[i("v-uni-text",{staticClass:"section-title"},[t._v("合同收款情况")]),i("v-uni-view",{staticClass:"progress-section"},[i("v-uni-view",{staticClass:"progress-title"},[i("v-uni-text",[t._v("收款进度")]),i("v-uni-text",{staticClass:"progress-ratio"},[t._v(t._s(t.payment.progressPercentage)+"%")])],1),i("v-uni-view",{staticClass:"progress-container"},[i("v-uni-view",{staticClass:"progress-bar",style:{width:t.payment.progressPercentage+"%"}})],1)],1),i("v-uni-view",{staticClass:"amount-container",staticStyle:{"margin-top":"12px"}},[i("v-uni-view",{staticClass:"amount-row"},[i("v-uni-text",{staticClass:"amount-label"},[t._v("合同总金额")]),i("v-uni-text",{staticClass:"amount-value"},[t._v("¥"+t._s(t.payment.contractAmount))])],1),i("v-uni-view",{staticClass:"amount-row"},[i("v-uni-text",{staticClass:"amount-label"},[t._v("已收款金额")]),i("v-uni-text",{staticClass:"amount-value"},[t._v("¥"+t._s(t.payment.paidAmount))])],1),i("v-uni-view",{staticClass:"amount-row total-row"},[i("v-uni-text",{staticClass:"amount-label"},[t._v("收款比例")]),i("v-uni-text",{staticClass:"amount-value"},[t._v(t._s(t.payment.progressPercentage)+"%")])],1)],1)],1),i("v-uni-view",{staticClass:"section-card"},[i("v-uni-text",{staticClass:"section-title"},[t._v("操作记录")]),i("v-uni-view",{staticClass:"timeline"},t._l(t.payment.records,(function(e,a){return i("v-uni-view",{key:a,staticClass:"timeline-item"},[i("v-uni-view",{class:["timeline-icon",e.type]},[i("svg-icon",{attrs:{name:"payment"===e.type?"bank-card":"add",type:"svg",size:"20"}})],1),i("v-uni-view",{staticClass:"timeline-content"},[i("v-uni-text",{staticClass:"timeline-title"},[t._v(t._s(e.title))]),i("v-uni-text",{staticClass:"timeline-date"},[t._v(t._s(e.date)+" | "+t._s(e.operator))])],1)],1)})),1),i("v-uni-view",{staticClass:"add-note-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.viewHistory.apply(void 0,arguments)}}},[i("svg-icon",{attrs:{name:"history",type:"svg",size:"20"}}),i("v-uni-text",[t._v("查看详细历史记录")])],1)],1),i("v-uni-view",{staticClass:"section-card"},[i("v-uni-text",{staticClass:"section-title"},[t._v("备注信息")]),i("v-uni-view",{staticClass:"notes-content"},[i("v-uni-text",[t._v(t._s(t.payment.notes||"暂无备注信息"))])],1),i("v-uni-view",{staticClass:"add-note-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.addNote.apply(void 0,arguments)}}},[i("svg-icon",{attrs:{name:"add",type:"svg",size:"20"}}),i("v-uni-text",[t._v("添加备注")])],1)],1)],1),i("v-uni-view",{staticClass:"float-actions"},[i("v-uni-view",{staticClass:"action-btn secondary-action",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.printReceipt.apply(void 0,arguments)}}},[i("svg-icon",{attrs:{name:"printer",type:"svg",size:"20"}}),i("v-uni-text",[t._v("打印回执")])],1),i("v-uni-view",{staticClass:"action-btn primary-action",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.sendReceipt.apply(void 0,arguments)}}},[i("svg-icon",{attrs:{name:"mail-send",type:"svg",size:"20"}}),i("v-uni-text",[t._v("发送回执")])],1)],1)],1)},o=[]},"85bf":function(t,e,i){"use strict";var a=i("2fa3"),n=i.n(a);n.a},b929:function(t,e,i){"use strict";i.r(e);var a=i("8546"),n=i("2f93");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("85bf");var s=i("828b"),d=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"d1b6d852",null,!1,a["a"],void 0);e["default"]=d.exports},e9dc:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,".page-container[data-v-d1b6d852]{display:flex;flex-direction:column;height:100vh;background-color:#f5f5f5;width:100%;overflow:hidden;box-sizing:border-box}.page-header[data-v-d1b6d852]{display:flex;align-items:center;justify-content:space-between;padding:12px 16px;border-bottom:1px solid #e0e0e0;background-color:#fff;position:-webkit-sticky;position:sticky;top:0;z-index:10}.page-title[data-v-d1b6d852]{font-size:18px;font-weight:700;color:#333}.back-button[data-v-d1b6d852]{color:#666;display:flex;align-items:center}.header-actions[data-v-d1b6d852]{display:flex;gap:8px}.action-button[data-v-d1b6d852]{width:36px;height:36px;display:flex;align-items:center;justify-content:center;border-radius:50%;color:#666;background-color:#f5f5f5;border:1px solid #e0e0e0}.payment-container[data-v-d1b6d852]{flex:1;padding:12px;padding-bottom:140px;width:100%;box-sizing:border-box}.section-card[data-v-d1b6d852]{background-color:#fff;border-radius:8px;padding:16px;margin-bottom:12px;border:1px solid #e0e0e0;box-shadow:0 2px 4px rgba(0,0,0,.05);width:100%;box-sizing:border-box;overflow:hidden}.section-title[data-v-d1b6d852]{font-size:16px;font-weight:600;margin-bottom:12px;color:#333;display:flex;align-items:center;justify-content:space-between}.status-badge[data-v-d1b6d852]{padding:4px 12px;border-radius:20px;font-size:12px;font-weight:500;white-space:nowrap}.status-completed[data-v-d1b6d852]{background-color:rgba(52,199,89,.1);color:#34c759}.status-partial[data-v-d1b6d852]{background-color:rgba(255,204,0,.1);color:#fc0}.status-pending[data-v-d1b6d852]{background-color:rgba(0,122,255,.1);color:#007aff}.status-overdue[data-v-d1b6d852]{background-color:rgba(255,59,48,.1);color:#ff3b30}.info-grid[data-v-d1b6d852]{display:grid;grid-template-columns:repeat(2,1fr);gap:12px;width:100%;box-sizing:border-box}.info-item[data-v-d1b6d852]{margin-bottom:8px;min-width:0;overflow:hidden}.info-label[data-v-d1b6d852]{font-size:13px;color:#666;margin-bottom:4px}.info-value[data-v-d1b6d852]{font-size:14px;color:#333;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.amount-container[data-v-d1b6d852]{background-color:rgba(0,122,255,.05);border-radius:8px;padding:12px;margin-bottom:12px;width:100%;box-sizing:border-box}.amount-row[data-v-d1b6d852]{display:flex;justify-content:space-between;padding:4px 0;width:100%;box-sizing:border-box;overflow:hidden}.amount-label[data-v-d1b6d852]{font-size:14px;color:#666;flex-shrink:0}.amount-value[data-v-d1b6d852]{font-size:14px;color:#333;font-weight:500;text-align:right;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.total-row[data-v-d1b6d852]{border-top:1px solid rgba(0,0,0,.05);margin-top:4px;padding-top:8px}.total-row .amount-label[data-v-d1b6d852]{font-size:15px;color:#333;font-weight:500}.total-row .amount-value[data-v-d1b6d852]{font-size:17px;color:#007aff;font-weight:600}.payment-method[data-v-d1b6d852]{display:flex;align-items:center;gap:12px;padding:12px;background-color:#f5f5f5;border-radius:8px;margin-bottom:12px;width:100%;box-sizing:border-box;overflow:hidden}.payment-method-icon[data-v-d1b6d852]{width:48px;height:48px;display:flex;align-items:center;justify-content:center;border-radius:8px;background-color:#fff;color:#007aff;box-shadow:0 2px 4px rgba(0,0,0,.05)}.payment-method-details[data-v-d1b6d852]{flex:1;min-width:0;overflow:hidden}.payment-method-name[data-v-d1b6d852]{font-weight:600;color:#333;margin-bottom:2px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.payment-method-info[data-v-d1b6d852]{font-size:13px;color:#666;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.linked-docs[data-v-d1b6d852]{display:flex;flex-direction:column;gap:8px;width:100%;box-sizing:border-box}.linked-doc-item[data-v-d1b6d852]{display:flex;align-items:center;gap:12px;padding:8px;border:1px solid #e0e0e0;border-radius:8px;background-color:#fff;width:100%;box-sizing:border-box;overflow:hidden}.doc-icon[data-v-d1b6d852]{width:40px;height:40px;display:flex;align-items:center;justify-content:center;background-color:#f5f5f5;border-radius:8px}.doc-invoice[data-v-d1b6d852]{background-color:rgba(255,204,0,.1);color:#fc0}.doc-contract[data-v-d1b6d852]{background-color:rgba(0,122,255,.1);color:#007aff}.doc-details[data-v-d1b6d852]{flex:1;overflow:hidden;min-width:0}.doc-title[data-v-d1b6d852]{font-weight:500;color:#333;margin-bottom:2px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.doc-meta[data-v-d1b6d852]{font-size:12px;color:#666;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.file-list[data-v-d1b6d852]{margin-top:12px;width:100%;box-sizing:border-box}.file-item[data-v-d1b6d852]{display:flex;align-items:center;gap:8px;padding:8px 0;border-bottom:1px solid #f0f0f0;width:100%;box-sizing:border-box;overflow:hidden}.file-item[data-v-d1b6d852]:last-child{border-bottom:none}.file-item uni-text[data-v-d1b6d852]{flex:1;font-size:14px;color:#333;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;min-width:0}.download-button[data-v-d1b6d852]{width:32px;height:32px;display:flex;align-items:center;justify-content:center;border-radius:50%;background-color:#f5f5f5}.progress-section[data-v-d1b6d852]{display:flex;flex-direction:column;gap:8px;width:100%;box-sizing:border-box}.progress-title[data-v-d1b6d852]{font-weight:500;color:#333;display:flex;justify-content:space-between;margin-bottom:4px;width:100%;box-sizing:border-box}.progress-ratio[data-v-d1b6d852]{font-weight:600;color:#007aff}.progress-container[data-v-d1b6d852]{height:8px;background-color:#f5f5f5;border-radius:4px;overflow:hidden;width:100%;box-sizing:border-box}.progress-bar[data-v-d1b6d852]{height:100%;background-color:#34c759;border-radius:4px}.notes-content[data-v-d1b6d852]{font-size:14px;line-height:1.5;color:#333;width:100%;box-sizing:border-box;word-break:break-all;white-space:pre-wrap}.add-note-button[data-v-d1b6d852]{display:flex;align-items:center;justify-content:center;gap:4px;margin-top:8px;padding:8px;width:100%;box-sizing:border-box;background-color:#f5f5f5;border:1px dashed #e0e0e0;border-radius:8px;color:#666;font-size:14px}.timeline[data-v-d1b6d852]{margin-top:12px;width:100%;box-sizing:border-box}.timeline-item[data-v-d1b6d852]{display:flex;margin-bottom:12px;width:100%;box-sizing:border-box;overflow:hidden}.timeline-icon[data-v-d1b6d852]{flex:0 0 32px;height:32px;border-radius:50%;background-color:#f5f5f5;display:flex;align-items:center;justify-content:center;margin-right:12px;color:#666}.timeline-icon.payment[data-v-d1b6d852]{background-color:rgba(52,199,89,.1);color:#34c759}.timeline-icon.create[data-v-d1b6d852]{background-color:rgba(0,122,255,.1);color:#007aff}.timeline-content[data-v-d1b6d852]{flex:1;min-width:0;overflow:hidden}.timeline-title[data-v-d1b6d852]{font-weight:500;margin-bottom:2px;color:#333;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.timeline-date[data-v-d1b6d852]{font-size:13px;color:#666;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.float-actions[data-v-d1b6d852]{position:fixed;bottom:0;left:0;right:0;display:flex;padding:12px 16px;gap:12px;background-color:#fff;box-shadow:0 -1px 10px rgba(0,0,0,.1);z-index:100;padding-bottom:calc(12px + env(safe-area-inset-bottom));width:100%;box-sizing:border-box}.action-btn[data-v-d1b6d852]{flex:1;height:44px;display:flex;align-items:center;justify-content:center;border-radius:22px;font-size:16px;font-weight:500;gap:8px}.primary-action[data-v-d1b6d852]{background-color:#3a86ff;color:#fff}.secondary-action[data-v-d1b6d852]{background-color:#f5f5f5;color:#333;border:1px solid #e0e0e0}",""]),t.exports=e},fe83:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("8a0f")),o={components:{SvgIcon:n.default},data:function(){return{payment:{title:"系统集成项目 - 第一期",statusText:"已完成",statusClass:"completed",code:"PAY-2023-11-001",invoice:"INV-2023-11-001",contract:"CT-2023-09-001",date:"2023-11-12",amountDue:"290,975.00",amountReceived:"290,975.00",difference:"0.00",method:"银行转账",methodDetails:"中国建设银行 | 到账时间：2023-11-12",customer:{name:"上海智能科技",contact:"张总经理 | ***********",bank:"中国建设银行上海张江支行",account:"31050161393600000123"},linkedDocs:[{type:"invoice",title:"发票 #INV-2023-11-001",meta:"金额: ¥290,975.00 | 状态: 已付款",path:"/pages/contracts/invoice-detail"},{type:"contract",title:"合同 #CT-2023-09-001",meta:"企业系统集成项目合同",path:"/pages/contracts/contract-detail"}],files:[{type:"pdf",name:"收款回执单-上海智能科技.pdf",url:"#"},{type:"image",name:"银行转账凭证.jpg",url:"#"}],progressPercentage:100,contractAmount:"580,000.00",paidAmount:"580,000.00",records:[{type:"payment",title:"确认收款",date:"2023-11-12 14:30",operator:"李财务"},{type:"create",title:"创建收款记录",date:"2023-11-12 10:15",operator:"李财务"}],notes:"客户已通过银行转账方式支付全款，交易参考号: REF2023111201254895。"}}},methods:{goBack:function(){uni.navigateBack()},showMenu:function(){var t=this;uni.showActionSheet({itemList:["查看历史记录","编辑收款信息","导出收款凭证","删除收款记录"],success:function(e){switch(e.tapIndex){case 0:t.viewHistory();break;case 1:uni.navigateTo({url:"/pages/contracts/payment-edit"});break;default:uni.showToast({title:"该功能开发中...",icon:"none"})}}})},viewLinkedDoc:function(t){uni.navigateTo({url:t.path})},downloadFile:function(t){uni.showToast({title:"下载功能开发中...",icon:"none"})},uploadFile:function(){uni.showToast({title:"上传凭证功能开发中...",icon:"none"})},viewHistory:function(){uni.navigateTo({url:"/pages/contracts/payment-history"})},addNote:function(){var t=this;uni.showModal({title:"添加备注",content:"",editable:!0,placeholderText:"请输入备注信息",success:function(e){e.confirm&&e.content&&(t.payment.notes=t.payment.notes?t.payment.notes+"\n"+e.content:e.content)}})},printReceipt:function(){uni.showToast({title:"打印收款回执功能开发中...",icon:"none"})},sendReceipt:function(){uni.showToast({title:"发送收款回执功能开发中...",icon:"none"})}}};e.default=o}}]);
<template>
  <view class="page-container">
    <view class="page-header">
      <view class="back-button" @click="navigateBack">
        <text class="ri-arrow-left-line"></text>
      </view>
      <text class="page-title">沟通记录详情</text>
      <view class="header-actions">
        <view class="action-button" @click="shareRecord">
          <text class="ri-share-line"></text>
        </view>
        <view class="action-button" @click="showMoreActions">
          <text class="ri-more-2-fill"></text>
        </view>
      </view>
    </view>

    <scroll-view scroll-y class="content-container">
      <view class="communication-header">
        <view class="communication-icon" :class="'icon-' + communicationDetail.type">
          <text :class="getIconClass(communicationDetail.type)"></text>
        </view>
        <text class="communication-title">{{communicationDetail.subject}}</text>
        
        <view class="communication-meta">
          <view class="meta-item">
            <text class="ri-calendar-line"></text>
            <text>{{communicationDetail.dateTime}}</text>
          </view>
          <view class="meta-item">
            <text class="ri-user-line"></text>
            <text>负责人: {{communicationDetail.owner}}</text>
          </view>
          <view class="meta-item" v-if="communicationDetail.duration">
            <text class="ri-time-line"></text>
            <text>时长: {{communicationDetail.duration}}</text>
          </view>
        </view>
        
        <view class="tag-container" v-if="communicationDetail.tags && communicationDetail.tags.length > 0">
          <text class="tag" v-for="(tag, index) in communicationDetail.tags" :key="index">{{tag}}</text>
        </view>
      </view>

      <view class="info-section">
        <view class="section-title">
          <text>沟通内容</text>
        </view>
        <view class="communication-content">
          <rich-text :nodes="formatContent(communicationDetail.content)"></rich-text>
        </view>
      </view>
      
      <view class="info-section" v-if="communicationDetail.relatedItems && communicationDetail.relatedItems.length > 0">
        <view class="section-title">
          <text>关联信息</text>
        </view>
        <view 
          class="associated-item" 
          v-for="(item, index) in communicationDetail.relatedItems" 
          :key="index"
          @click="navigateToRelated(item)"
        >
          <view class="associated-icon">
            <text :class="getRelatedIconClass(item.type)"></text>
          </view>
          <view class="associated-content">
            <text class="associated-name">{{item.name}}</text>
            <text class="associated-type">{{item.type}}</text>
          </view>
          <view class="associated-action">
            <text class="ri-arrow-right-s-line"></text>
          </view>
        </view>
      </view>
      
      <view class="info-section" v-if="communicationDetail.participants && communicationDetail.participants.length > 0">
        <view class="section-title">
          <text>参与人员</text>
        </view>
        <view class="participant-list">
          <view class="participant" v-for="(person, index) in communicationDetail.participants" :key="index">
            <text class="ri-user-line"></text>
            <text>{{person.name}}{{person.role ? '（'+person.role+'）' : ''}}</text>
          </view>
        </view>
      </view>
      
      <view class="info-section" v-if="communicationDetail.attachments && communicationDetail.attachments.length > 0">
        <view class="section-title">
          <text>附件</text>
        </view>
        <view 
          class="attachment-item" 
          v-for="(file, index) in communicationDetail.attachments" 
          :key="index"
        >
          <view class="attachment-icon">
            <text :class="getFileIconClass(file.type)"></text>
          </view>
          <view class="attachment-info">
            <text class="attachment-name">{{file.name}}</text>
            <text class="attachment-meta">{{file.size}} · {{file.uploadTime}}</text>
          </view>
          <view class="attachment-action" @click.stop="downloadFile(file)">
            <text class="ri-download-line"></text>
          </view>
        </view>
      </view>
      
      <view class="info-section notes-container">
        <view class="section-title">
          <text>跟进记录</text>
          <text class="action" @click="addFollowUp">添加</text>
        </view>
        <view class="communication-content" v-if="communicationDetail.followUps && communicationDetail.followUps.length > 0">
          <block v-for="(note, index) in communicationDetail.followUps" :key="index">
            <text class="follow-up-header">{{note.time}} - {{note.creator}}</text>
            <rich-text :nodes="formatContent(note.content)"></rich-text>
          </block>
        </view>
        <view class="empty-notes" v-else>
          <text>暂无跟进记录</text>
        </view>
      </view>
    </scroll-view>

    <view class="action-bar">
      <button class="btn btn-outline" @click="editRecord">
        <text class="ri-edit-line"></text> 编辑记录
      </button>
      <button class="btn btn-primary" @click="addFollowUp">
        <text class="ri-add-line"></text> 新增跟进
      </button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      id: '',
      communicationDetail: {
        id: '1',
        type: 'call',
        subject: '产品功能确认电话',
        dateTime: '2023-10-15 15:30',
        owner: '李销售',
        duration: '30分钟',
        content: `<p>与张经理通话30分钟，讨论了产品功能细节和交付时间。客户关心以下几点：</p>
<ol>
  <li>数据安全问题，需要准备详细的技术方案</li>
  <li>并发用户数量支持情况，客户预计有300-500名用户同时在线</li>
  <li>与现有ERP系统的集成方案</li>
  <li>培训和技术支持的周期与方式</li>
</ol>
<p>客户表示对我们的初步方案满意，但希望能在下周一前收到更详细的技术文档和价格清单。</p>`,
        tags: ['跟进中', '重要客户'],
        relatedItems: [
          {
            id: '101',
            name: '北京科技有限公司',
            type: '客户',
            path: '/pages/customers/customer-detail'
          },
          {
            id: '201',
            name: '云数据分析平台项目',
            type: '商机',
            path: '/pages/sales/opportunity-detail'
          }
        ],
        participants: [
          { name: '张经理', role: '客户联系人' },
          { name: '李销售', role: '负责人' },
          { name: '王工程师', role: '技术支持' }
        ],
        attachments: [
          { 
            id: '1',
            name: '产品技术规格说明.pdf', 
            size: '2.5MB', 
            uploadTime: '2023-10-15 上传',
            type: 'pdf'
          },
          { 
            id: '2',
            name: '项目报价清单.xlsx', 
            size: '578KB', 
            uploadTime: '2023-10-15 上传',
            type: 'excel'
          }
        ],
        followUps: [
          {
            id: '1',
            time: '2023-10-16 10:30',
            creator: '李销售',
            content: '已将客户需求整理并转交给技术部门，预计周五前完成详细技术方案的编写'
          }
        ]
      }
    }
  },
  onLoad(options) {
    if (options.id) {
      this.id = options.id;
      this.loadCommunicationDetail();
    }
  },
  methods: {
    navigateBack() {
      uni.navigateBack();
    },
    loadCommunicationDetail() {
      // 实际应用中应通过API获取沟通记录详情
      console.log('加载ID为' + this.id + '的沟通记录');
      // 这里使用静态数据模拟，实际开发需替换为API调用
    },
    formatContent(content) {
      // 在简单实现中直接返回HTML内容
      // 在完整应用中可能需要更复杂的处理，如将markdown转为HTML等
      return content;
    },
    getIconClass(type) {
      const iconMap = {
        'call': 'ri-phone-line',
        'meeting': 'ri-team-line',
        'email': 'ri-mail-line',
        'note': 'ri-file-list-line',
        'visit': 'ri-building-line'
      };
      return iconMap[type] || 'ri-chat-3-line';
    },
    getRelatedIconClass(type) {
      const iconMap = {
        '客户': 'ri-building-2-line',
        '商机': 'ri-briefcase-line',
        '合同': 'ri-file-paper-line',
        '联系人': 'ri-user-line'
      };
      return iconMap[type] || 'ri-link';
    },
    getFileIconClass(type) {
      const iconMap = {
        'pdf': 'ri-file-pdf-line',
        'excel': 'ri-file-excel-line',
        'word': 'ri-file-word-line',
        'image': 'ri-image-line',
        'zip': 'ri-file-zip-line'
      };
      return iconMap[type] || 'ri-file-line';
    },
    navigateToRelated(item) {
      if (item.path) {
        uni.navigateTo({
          url: `${item.path}?id=${item.id}`
        });
      }
    },
    downloadFile(file) {
      uni.showToast({
        title: `正在下载: ${file.name}`,
        icon: 'none'
      });
      // 实际开发中应实现文件下载逻辑
    },
    shareRecord() {
      uni.showToast({
        title: '分享功能开发中',
        icon: 'none'
      });
    },
    showMoreActions() {
      uni.showActionSheet({
        itemList: ['删除记录', '复制链接', '添加到收藏'],
        success: (res) => {
          const index = res.tapIndex;
          if (index === 0) {
            this.confirmDelete();
          } else {
            uni.showToast({
              title: '功能开发中',
              icon: 'none'
            });
          }
        }
      });
    },
    editRecord() {
      uni.navigateTo({
        url: `/pages/communication/communication-edit?id=${this.communicationDetail.id}`
      });
    },
    addFollowUp() {
      uni.showToast({
        title: '添加跟进记录功能开发中',
        icon: 'none'
      });
    },
    confirmDelete() {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除此沟通记录吗？此操作不可恢复。',
        confirmText: '删除',
        confirmColor: '#ff4d4f',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '正在删除'
            });
            
            setTimeout(() => {
              uni.hideLoading();
              uni.showToast({
                title: '删除成功',
                icon: 'success',
                duration: 2000,
                success: () => {
                  setTimeout(() => {
                    uni.navigateBack();
                  }, 2000);
                }
              });
            }, 1500);
          }
        }
      });
    }
  }
}
</script>

<style>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;
  background-color: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

.back-button {
  color: #666;
  font-size: 20px;
}

.page-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.action-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #666;
  background-color: #f5f7fa;
  border: 1px solid #e0e0e0;
}

.content-container {
  flex: 1;
  height: calc(100vh - 121px); /* 减去头部和底部操作栏高度 */
}

.communication-header {
  background-color: white;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.communication-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  font-size: 20px;
}

.icon-call {
  background-color: #fef3c7;
  color: #d97706;
}

.icon-meeting {
  background-color: #e0e7ff;
  color: #4f46e5;
}

.icon-email {
  background-color: #dcfce7;
  color: #16a34a;
}

.icon-note {
  background-color: #dbeafe;
  color: #2563eb;
}

.icon-visit {
  background-color: #f3e8ff;
  color: #9333ea;
}

.communication-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #333;
}

.communication-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.meta-item text:first-child {
  margin-right: 5px;
  font-size: 16px;
}

.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 10px;
}

.tag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 20px;
  background-color: #f5f7fa;
  color: #666;
}

.info-section {
  background-color: white;
  margin: 15px 0;
  padding: 20px;
  border-top: 1px solid #e0e0e0;
  border-bottom: 1px solid #e0e0e0;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-title .action {
  font-size: 14px;
  font-weight: normal;
  color: #3370ff;
}

.communication-content {
  border-radius: 8px;
  padding: 15px;
  background-color: #f5f7fa;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

.associated-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 8px;
  background-color: #f5f7fa;
  margin-bottom: 10px;
}

.associated-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e0f2ff;
  color: #3370ff;
  margin-right: 10px;
}

.associated-content {
  flex: 1;
}

.associated-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.associated-type {
  font-size: 12px;
  color: #666;
}

.associated-action {
  color: #3370ff;
  font-size: 18px;
}

.participant-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.participant {
  display: flex;
  align-items: center;
  padding: 5px 10px;
  background-color: #f5f7fa;
  border-radius: 20px;
  font-size: 12px;
  color: #666;
}

.participant text:first-child {
  margin-right: 5px;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 8px;
  background-color: #f5f7fa;
  margin-bottom: 10px;
}

.attachment-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e0e7ff;
  color: #4f46e5;
  border-radius: 8px;
  margin-right: 10px;
}

.attachment-info {
  flex: 1;
}

.attachment-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.attachment-meta {
  font-size: 12px;
  color: #666;
}

.attachment-action {
  color: #3370ff;
  font-size: 18px;
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  gap: 15px;
  border-top: 1px solid #e0e0e0;
  z-index: 10;
}

.action-bar .btn {
  flex: 1;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  font-size: 14px;
}

.btn-outline {
  border: 1px solid #3370ff;
  color: #3370ff;
  background-color: white;
}

.btn-primary {
  background-color: #3370ff;
  color: white;
  border: none;
}

.btn text {
  margin-right: 5px;
}

.notes-container {
  margin-bottom: 80px;
}

.follow-up-header {
  font-weight: bold;
  margin-bottom: 5px;
  display: block;
}

.empty-notes {
  text-align: center;
  color: #999;
  padding: 20px 0;
}
</style> 
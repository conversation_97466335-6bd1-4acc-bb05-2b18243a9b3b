<template>
  <view class="redirect-container">
    <text>跳转中...</text>
  </view>
</template>

<script>
export default {
  onLoad() {
    // 重定向到任务详情页，并带上new=true参数表示新建任务
    setTimeout(() => {
      uni.redirectTo({
        url: '/pages/tasks/task-detail?new=true'
      });
    }, 300);
  }
}
</script>

<style>
.redirect-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f7fa;
  font-size: 30rpx;
  color: #666;
}
</style> 
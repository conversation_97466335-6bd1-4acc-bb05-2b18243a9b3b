<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="goBack">
        <svg-icon name="arrow-left" type="svg" size="24"></svg-icon>
      </view>
      <text class="page-title">收款管理</text>
      <view class="header-actions">
        <button type="button" class="action-button" @click="showFilterOptions">
          <svg-icon name="filter-3" type="svg" size="24"></svg-icon>
        </button>
        <button type="button" class="action-button" @click="showSortOptions">
          <svg-icon name="sort-desc" type="svg" size="24"></svg-icon>
        </button>
      </view>
    </view>
    
    <!-- 收款总览 -->
    <view class="totals-overview">
      <view class="total-card received">
        <text class="total-label">已收款金额</text>
        <text class="total-value">¥{{formatMoney(totals.received)}}</text>
      </view>
      <view class="total-card pending">
        <text class="total-label">待收款金额</text>
        <text class="total-value">¥{{formatMoney(totals.pending)}}</text>
      </view>
    </view>
    
    <!-- 搜索栏 -->
    <view class="search-bar">
      <svg-icon name="search" type="svg" size="24" class="search-icon"></svg-icon>
      <input 
        type="text" 
        class="search-input" 
        v-model="searchQuery" 
        placeholder="搜索付款号、客户名称..." 
        @input="onSearch"
      />
    </view>
    
    <!-- 筛选栏 -->
    <scroll-view scroll-x class="filter-bar">
      <view 
        v-for="(filter, index) in filters" 
        :key="index" 
        :class="['filter-button', currentFilter === filter.value ? 'active' : '']"
        @click="setFilter(filter.value)"
      >
        <text>{{filter.label}}</text>
      </view>
    </scroll-view>
    
    <!-- 付款列表 -->
    <scroll-view 
      scroll-y 
      class="payment-list"
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="loadMore"
    >
      <view v-if="filteredPayments.length > 0">
        <view 
          v-for="(payment, index) in filteredPayments" 
          :key="index" 
          class="payment-item"
          @click="goToDetail(payment.id)"
        >
          <view class="payment-header">
            <text class="payment-title">{{payment.title}}</text>
            <text :class="['payment-status', 'status-' + payment.status]">{{getStatusText(payment.status)}}</text>
          </view>
          
          <view class="payment-info">
            <view class="info-item">
              <text class="info-label">收款编号</text>
              <text class="info-value">{{payment.paymentNumber}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">客户</text>
              <text class="info-value">{{payment.customer}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">{{payment.dateLabel}}</text>
              <text class="info-value">{{payment.paymentDate || '-'}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">{{payment.secondaryLabel}}</text>
              <text class="info-value">{{payment.secondaryValue || '-'}}</text>
            </view>
          </view>
          
          <!-- 进度条（部分付款时显示） -->
          <view v-if="payment.status === 'partial'" class="progress-section">
            <view class="progress-container">
              <view 
                class="progress-bar" 
                :style="{width: (payment.paidAmount / payment.totalAmount * 100) + '%'}"
              ></view>
            </view>
            <view class="payment-progress">
              已收: <text>¥{{formatMoney(payment.paidAmount)}}</text> / 总计: <text>¥{{formatMoney(payment.totalAmount)}}</text>
            </view>
          </view>
          
          <view class="payment-footer">
            <text class="payment-amount">¥{{formatMoney(payment.amount)}}</text>
            <view class="payment-actions">
              <view 
                class="action-icon" 
                v-if="payment.status === 'completed' || payment.status === 'partial'"
                @click.stop="viewReceipt(payment)"
              >
                <svg-icon name="file-list-3" type="svg" size="20"></svg-icon>
              </view>
              <view 
                class="action-icon" 
                v-if="payment.status === 'completed'"
                @click.stop="printReceipt(payment)"
              >
                <svg-icon name="printer" type="svg" size="20"></svg-icon>
              </view>
              <view 
                class="action-icon" 
                v-if="payment.status === 'partial'"
                @click.stop="makePayment(payment)"
              >
                <svg-icon name="bank-card" type="svg" size="20"></svg-icon>
              </view>
              <view 
                class="action-icon" 
                v-if="payment.status === 'pending' || payment.status === 'overdue'"
                @click.stop="sendReminder(payment)"
              >
                <svg-icon name="mail-send" type="svg" size="20"></svg-icon>
              </view>
              <view 
                class="action-icon" 
                v-if="payment.status === 'overdue'"
                @click.stop="callCustomer(payment)"
              >
                <svg-icon name="phone" type="svg" size="20"></svg-icon>
              </view>
              <view 
                class="action-icon" 
                v-if="payment.status === 'pending'"
                @click.stop="setReminder(payment)"
              >
                <svg-icon name="calendar-todo" type="svg" size="20"></svg-icon>
              </view>
            </view>
          </view>
        </view>
        
        <view v-if="loadingMore" class="loading-more">
          <text>加载更多...</text>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-else class="empty-state">
        <view class="empty-icon">
          <svg-icon name="bank-card" type="svg" size="64"></svg-icon>
        </view>
        <text class="empty-title">暂无收款记录</text>
        <text class="empty-text">创建您的第一笔收款记录，开始管理您的收款</text>
        <button class="create-button" @click="createPayment">
          <svg-icon name="add" type="svg" size="20"></svg-icon>
          <text>创建收款记录</text>
        </button>
      </view>
    </scroll-view>
    
    <!-- 创建收款记录浮动按钮 -->
    <view class="floating-button" @click="createPayment">
      <svg-icon name="add" type="svg" size="60" color="#ffffff"></svg-icon>
    </view>

    <!-- 自定义TabBar组件 -->
    <custom-tab-bar ref="customTabBar"></custom-tab-bar>
  </view>
</template>

<script>
import CustomTabBar from '@/components/CustomTabBar.vue';
import SvgIcon from '@/components/svg-icon.vue';

export default {
  components: {
    CustomTabBar,
    SvgIcon
  },
  data() {
    return {
      searchQuery: '',
      currentFilter: 'all',
      filters: [
        { label: '全部', value: 'all' },
        { label: '已完成', value: 'completed' },
        { label: '部分付款', value: 'partial' },
        { label: '待付款', value: 'pending' },
        { label: '已逾期', value: 'overdue' }
      ],
      totals: {
        received: 487290,
        pending: 294750
      },
      payments: [
        {
          id: '1',
          title: '系统集成项目 - 第一期',
          status: 'completed',
          paymentNumber: 'PAY-2023-11-001',
          customer: '上海智能科技',
          paymentDate: '2023-11-12',
          dateLabel: '收款日期',
          secondaryLabel: '付款方式',
          secondaryValue: '银行转账',
          amount: 290975
        },
        {
          id: '2',
          title: '软件维护服务 - 季度账单',
          status: 'partial',
          paymentNumber: 'PAY-2023-11-002',
          customer: '北京电子科技',
          paymentDate: '2023-11-18',
          dateLabel: '收款日期',
          secondaryLabel: '付款方式',
          secondaryValue: '支付宝',
          amount: 37500,
          paidAmount: 37500,
          totalAmount: 75000
        },
        {
          id: '3',
          title: '数据迁移项目',
          status: 'overdue',
          paymentNumber: 'PAY-2023-11-003',
          customer: '广州数联网络',
          paymentDate: '2023-11-10',
          dateLabel: '应收日期',
          secondaryLabel: '逾期天数',
          secondaryValue: '14天',
          amount: 125400
        },
        {
          id: '4',
          title: '培训服务费用',
          status: 'pending',
          paymentNumber: 'PAY-2023-11-004',
          customer: '成都星辰科技',
          paymentDate: '2023-12-05',
          dateLabel: '应收日期',
          secondaryLabel: '剩余天数',
          secondaryValue: '11天',
          amount: 45000
        }
      ],
      isRefreshing: false,
      loadingMore: false,
      page: 1,
      hasMore: true
    }
  },
  computed: {
    filteredPayments() {
      let result = [...this.payments];
      
      // 根据搜索关键词筛选
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        result = result.filter(payment => 
          payment.title.toLowerCase().includes(query) ||
          payment.paymentNumber.toLowerCase().includes(query) ||
          payment.customer.toLowerCase().includes(query)
        );
      }
      
      // 根据状态筛选
      if (this.currentFilter !== 'all') {
        result = result.filter(payment => payment.status === this.currentFilter);
      }
      
      return result;
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    formatMoney(amount) {
      return amount.toLocaleString('zh-CN');
    },
    getStatusText(status) {
      const statusMap = {
        'completed': '已完成',
        'partial': '部分付款',
        'pending': '待付款',
        'overdue': '已逾期'
      };
      return statusMap[status] || status;
    },
    setFilter(filter) {
      this.currentFilter = filter;
    },
    onSearch(e) {
      // 搜索功能实现
      this.searchQuery = e.detail.value;
    },
    onRefresh() {
      this.isRefreshing = true;
      
      // 模拟刷新
      setTimeout(() => {
        this.loadPayments();
        this.isRefreshing = false;
        uni.showToast({
          title: '刷新成功',
          icon: 'success'
        });
      }, 1000);
    },
    loadMore() {
      if (!this.hasMore || this.loadingMore) return;
      
      this.loadingMore = true;
      
      // 模拟加载更多
      setTimeout(() => {
        // 这里实际应用中应该是从API获取更多数据
        this.page++;
        if (this.page > 3) {
          this.hasMore = false;
        }
        this.loadingMore = false;
      }, 1000);
    },
    loadPayments() {
      // 实际应用中，这里应该是从API获取付款数据
      // 这里使用模拟数据
      this.page = 1;
      this.hasMore = true;
    },
    goToDetail(id) {
      uni.navigateTo({
        url: `/pages/contracts/payment-detail?id=${id}`
      });
    },
    createPayment() {
      uni.navigateTo({
        url: '/pages/contracts/payment-create'
      });
    },
    viewReceipt(payment) {
      uni.showToast({
        title: '查看收款凭证功能开发中...',
        icon: 'none'
      });
    },
    printReceipt(payment) {
      uni.showToast({
        title: '打印收款记录功能开发中...',
        icon: 'none'
      });
    },
    makePayment(payment) {
      uni.navigateTo({
        url: `/pages/contracts/payment-create?relatedPaymentId=${payment.id}`
      });
    },
    sendReminder(payment) {
      uni.showToast({
        title: '发送付款提醒功能开发中...',
        icon: 'none'
      });
    },
    callCustomer(payment) {
      uni.showToast({
        title: '电话提醒功能开发中...',
        icon: 'none'
      });
    },
    setReminder(payment) {
      uni.showToast({
        title: '设置日程提醒功能开发中...',
        icon: 'none'
      });
    },
    showFilterOptions() {
      uni.showToast({
        title: '高级筛选功能开发中...',
        icon: 'none'
      });
    },
    showSortOptions() {
      uni.showActionSheet({
        itemList: ['最新收款', '预计收款日期', '金额大小', '客户名称'],
        success: (res) => {
          // 实际应用中，这里应该是根据选择进行排序
          const sortTypes = ['date', 'expected', 'amount', 'customer'];
          const selectedSort = sortTypes[res.tapIndex];
          console.log('选择的排序方式:', selectedSort);
        }
      });
    }
  },
  onLoad() {
    this.loadPayments();
  },
  onShow() {
    // 设置TabBar当前选中项
    if (typeof this.$refs.customTabBar !== 'undefined') {
      this.$refs.customTabBar.current = 4; // 对应"更多"菜单
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  background-color: #fff;
  z-index: 10;
}

.page-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.back-button {
  color: #666;
  font-size: 24px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #666;
  background-color: #f5f5f5;
  border: 1px solid #eee;
  padding: 0;
  margin: 0;
  font-size: 20px;
}

.totals-overview {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin: 15px;
  margin-bottom: 20px;
}

.total-card {
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  border: 1px solid #eee;
}

.total-label {
  font-size: 13px;
  color: #999;
  margin-bottom: 8px;
}

.total-value {
  font-size: 22px;
  font-weight: 600;
  color: #333;
}

.total-card.received .total-value {
  color: #00b578;
}

.total-card.pending .total-value {
  color: #ff9a2a;
}

.search-bar {
  margin: 0 15px 15px;
  position: relative;
}

.search-input {
  width: 100%;
  padding: 10px 12px;
  padding-left: 40px;
  border-radius: 8px;
  border: 1px solid #eee;
  background-color: #f7f7f7;
  font-size: 14px;
  color: #333;
  box-sizing: border-box;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 20px;
}

.filter-bar {
  display: flex;
  white-space: nowrap;
  padding: 0 15px;
  margin-bottom: 15px;
}

.filter-button {
  display: inline-block;
  background-color: #f5f5f5;
  border: 1px solid #eee;
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 14px;
  color: #666;
  margin-right: 10px;
}

.filter-button.active {
  background-color: #3a86ff;
  color: white;
  border-color: #3a86ff;
}

.payment-list {
  flex: 1;
  margin-bottom: 80px;
}

.payment-item {
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
  border-radius: 8px;
  margin: 0 15px 15px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  border: 1px solid #eee;
}

.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.payment-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-right: 10px;
}

.payment-status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-completed {
  background-color: #e6f7ee;
  color: #00b578;
}

.status-partial {
  background-color: #fff5e6;
  color: #ff9a2a;
}

.status-pending {
  background-color: #e6f4ff;
  color: #1890ff;
}

.status-overdue {
  background-color: #ffece8;
  color: #ff4d4f;
}

.payment-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin-bottom: 10px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
}

.info-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.progress-section {
  margin-bottom: 10px;
}

.progress-container {
  background-color: #f5f5f5;
  border-radius: 8px;
  height: 6px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: #00b578;
}

.payment-progress {
  margin-top: 5px;
  font-size: 12px;
  color: #999;
}

.payment-progress text {
  font-weight: 500;
  color: #333;
}

.payment-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 5px;
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
}

.payment-amount {
  font-size: 18px;
  font-weight: 600;
  color: #3a86ff;
}

.payment-actions {
  display: flex;
  gap: 8px;
}

.action-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #666;
  background-color: #f5f5f5;
}

.floating-button {
  position: fixed;
  bottom: calc(128rpx + var(--spacing-xl)); /* 调整底部位置，避开TabBar */
  right: var(--spacing-xl);
  width: 110rpx; /* 减小尺寸 */
  height: 110rpx; /* 减小尺寸 */
  border-radius: 50%;
  background: linear-gradient(135deg, #0a6bff, #0057ff);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6), 0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  z-index: 100;
  transition: all 0.3s ease;
  border: 4rpx solid rgba(255, 255, 255, 0.7);
  animation: pulse 2s infinite; /* 添加脉动动画 */
}

.floating-button:active {
  transform: scale(0.95);
  box-shadow: 0 5rpx 10rpx rgba(0, 87, 255, 0.5), 0 3rpx 3rpx rgba(0, 87, 255, 0.3);
  animation: none; /* 点击时停止动画 */
}

/* 添加脉动动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6), 0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 15rpx 25rpx rgba(0, 87, 255, 0.7), 0 8rpx 10rpx rgba(0, 87, 255, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 10rpx 20rpx rgba(0, 87, 255, 0.6), 0 6rpx 6rpx rgba(0, 87, 255, 0.4);
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 20px;
  text-align: center;
  margin-top: 20px;
}

.empty-icon {
  font-size: 64px;
  color: #ddd;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.empty-text {
  font-size: 14px;
  color: #999;
  margin-bottom: 24px;
}

.create-button {
  padding: 10px 20px;
  background-color: #3a86ff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-more {
  text-align: center;
  padding: 20px 0;
  color: #999;
  font-size: 14px;
}
</style> 
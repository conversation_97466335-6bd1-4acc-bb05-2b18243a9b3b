<template>
	<view class="popup-item" :class="{ 'is-with-icon': withIcon }" @click="onClick">
		<view class="popup-item__icon" v-if="icon">
			<svg-icon :name="icon" type="svg" size="20"></svg-icon>
		</view>
		<view class="popup-item__label">{{ label }}</view>
	</view>
</template>

<script>
export default {
	name: 'PopupItem',
	props: {
		label: {
			type: String,
			default: ''
		},
		icon: {
			type: String,
			default: ''
		},
		value: {
			type: [String, Number, Object],
			default: ''
		}
	},
	computed: {
		withIcon() {
			return !!this.icon
		}
	},
	methods: {
		onClick() {
			this.$emit('click', this.value)
		}
	}
}
</script>

<style>
.popup-item {
	padding: 14px 16px;
	position: relative;
	display: flex;
	align-items: center;
}

.popup-item:active {
	background-color: var(--color-bg-hover);
}

.popup-item__icon {
	margin-right: 12px;
	color: var(--color-text-secondary);
}

.popup-item__label {
	font-size: 16px;
	color: var(--color-text-primary);
}

.popup-item.is-with-icon {
	padding-left: 16px;
}
</style> 
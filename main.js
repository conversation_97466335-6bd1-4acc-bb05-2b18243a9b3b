import Vue from "vue";
import App from "./App";

// 引入全局CSS变量
import "@/common/css-variables.css";
// 引入Remix Icon图标库
// import "@/common/remixicon.css";
import "./node_modules/remixicon/fonts/remixicon.css";
// 引入Iconfont图标库
import "@/common/iconfont.css";
import { formatToDate } from "@/utils/dateUtil";

// 全局注册uni-icon组件
import UniIcon from "@/components/uni-icon.vue";
Vue.component("uni-icon", UniIcon);

// 全局注册svg-icon组件
import SvgIcon from "@/components/svg-icon.vue";
Vue.component("svg-icon", SvgIcon);

Vue.config.productionTip = false;

// 添加页面路由改变事件
uni.onTabBarMidButtonTap(() => {
  console.log("点击了中间按钮");
});

// 页面切换事件
const updateTabBar = (pagePath) => {
  const app = getApp();
  if (app && app.globalData && app.globalData.customTabBar) {
    const index = [
      "/pages/dashboard/main-dashboard",
      "/pages/customers/customer-list",
      "/pages/sales/opportunity-list",
      "/pages/tasks/task-list",
      "/pages/settings/profile",
    ].findIndex((path) => path === pagePath);

    if (index !== -1) {
      app.globalData.customTabBar.setTabIndex(index);
    }
  }
};

// 页面显示时更新tabBar
Vue.mixin({
  onShow() {
    if (this.$mp && this.$mp.page) {
      const pages = getCurrentPages();
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        const route = `/${currentPage.route}`;
        updateTabBar(route);
        uni.$emit("tabChange", route);
      }
    }
  },
});

// 全局混入
Vue.mixin({
  methods: {
    // 页面跳转
    // navigateTo(url) {
    //   uni.navigateTo({
    //     url,
    //   });
    // },
    // 返回上一页
    navigateBack(delta = 1) {
      uni.navigateBack({
        delta,
      });
    },
    // 显示提示
    // showToast(title, icon = "none") {
    //   uni.showToast({
    //     title,
    //     icon,
    //   });
    // },
    // 确认框
    showConfirm(content, title = "提示") {
      return new Promise((resolve, reject) => {
        uni.showModal({
          title,
          content,
          success: (res) => {
            if (res.confirm) {
              resolve(true);
            } else {
              resolve(false);
            }
          },
          fail: () => {
            reject(new Error("Modal error"));
          },
        });
      });
    },
    // 格式化价格
    formatPrice(price) {
      return "¥" + parseFloat(price).toLocaleString("zh-CN");
    },
    // 格式化日期
    formatDate(date) {
      if (!date) return "";
      if (typeof date === "string") {
        date = new Date(date);
      }
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
        2,
        "0"
      )}-${String(date.getDate()).padStart(2, "0")}`;
    },
  },
});

// 全局过滤器
Vue.filter("price", (value) => {
  if (!value) return "0";
  return parseFloat(value).toLocaleString("zh-CN");
  // return "¥" + parseFloat(value).toLocaleString("zh-CN");
});

// 全局日期过滤器
Vue.filter("formatDateFilter", formatToDate);
// 全局字段为空过滤器
Vue.filter("formatEmptyFilter", (value) => {
  if (!value) return "--";
  return value;
});

App.mpType = "app";

const app = new Vue({
  ...App,
});
app.$mount();

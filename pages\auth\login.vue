<template>
  <view class="auth-container">
	<view v-if="!isLoading">
		<view class="auth-header">
		  <view class="logo">CRM</view>
		  <text class="welcome-text">欢迎回来</text>
		  <text class="sub-text">请登录您的账号以继续</text>
		</view>

		<view class="auth-form">
		  <view class="form-group">
			<text class="form-label">账号</text>
			<input
			  class="form-input"
			  placeholder="请输入账号"
			  v-model="loginForm.name"
			/>
		  </view>

		  <view class="form-group">
			<text class="form-label">密码</text>
			<view class="password-input-group">
			  <input
				:type="passwordVisible ? 'text' : 'password'"
				class="form-input"
				placeholder="请输入密码"
				v-model="loginForm.password"
			  />
			  <text class="password-toggle" @tap="togglePasswordVisibility">
				<text v-if="passwordVisible" class="ri-eye-off-line"></text>
				<text v-else class="ri-eye-line"></text>
			  </text>
			</view>
		  </view>

		  <view class="auth-options">
			<view class="remember-me">
			  <checkbox
				:checked="rememberMe"
				@tap="toggleRememberMe"
				color="#3a86ff"
			  />
			  <text @tap="toggleRememberMe">记住我</text>
			</view>
			<navigator url="/pages/auth/forgot-password" class="forgot-password">忘记密码？</navigator>
		  </view>

		  <button
			class="btn btn-primary btn-full"
			:disabled="!canLogin"
			@tap="handleLogin"
		  >
			登录
		  </button>

	<!--
		  <view class="or-divider">
			<view class="line"></view>
			<text>或</text>
			<view class="line"></view>
		  </view>
	-->

		  <!-- <view class="other-login-methods">
			<view class="login-method" @tap="loginWithWechat">
			  <view class="login-icon wechat-icon">
				<text class="ri-wechat-line"></text>
			  </view>
			  <text>微信登录</text>
			</view>
			
			<view class="login-method" @tap="loginWithSms">
			  <view class="login-icon sms-icon">
				<text class="ri-message-2-line"></text>
			  </view>
			  <text>短信登录</text>
			</view>
		  </view> -->
		</view>

		<!-- <view class="auth-footer">
		  <text class="auth-footer-text">
			还没有账号？ 
		  </text>
		  <navigator url="/pages/auth/register" class="auth-footer-link">立即注册</navigator>
		</view> -->
	</view>
	<!-- 跳转直接登录 -->
	<view v-if="isLoading">
	  <!-- 处理中状态 -->
	  <view class="processing-state">
	    <view class="auth-header">
	      <view class="logo">CRM</view>
	      <text class="processing-title">正在验证登录</text>
	      <text class="processing-subtitle">请稍候，正在处理您的登录信息...</text>
	    </view>
	
	    <view class="loading-section">
	      <view class="loading-spinner">
	        <view class="spinner"></view>
	      </view>
	    </view>
	  </view>
	</view>
  </view>
 
</template>

<script>
import { getTenantInfo } from '@/utils/request';
import { loginApi } from "@/api/account.api";
export default {
  data() {
    return {
      loginForm: {
        name: "",
        password: "",
      },
      passwordVisible: false,
      rememberMe: false,
      isLoading: this.$route.query.token,
    };
  },
  computed: {
    canLogin() {
      return this.loginForm.name && this.loginForm.password;
    },
  },
  methods: {
    async login() {
      try {
        // 1. 先获取租户信息
        await getTenantInfo({
          emailPhoneNumber: this.loginForm.name,
          password: this.loginForm.password
        });
        // 2. 再调用登录接口
        const loginRes = await loginApi(this.loginForm);
        uni.switchTab({
          url: "/pages/dashboard/main-dashboard",
        });
        uni.setStorageSync("token", loginRes.token);
        uni.setStorageSync("expirationTime", loginRes.expirationTime);
        uni.setStorageSync("userInfo", loginRes);
      } catch (error) {
        console.error("登录失败", error);
      }
    },
    togglePasswordVisibility() {
      this.passwordVisible = !this.passwordVisible;
    },
    toggleRememberMe() {
      this.rememberMe = !this.rememberMe;
    },
    handleLogin() {
      this.login();
    },
    loginWithWechat() {
      uni.showToast({
        title: "微信登录功能开发中",
        icon: "none",
      });
    },
    loginWithSms() {
      // 跳转到短信登录页面或展示短信登录模态框
      uni.showToast({
        title: "短信登录功能开发中",
        icon: "none",
      });
    },
  },
};
</script>

<style>
.auth-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 30rpx;
  background-color: #ffffff;
}
 
.auth-header {
  text-align: center;
  margin: 60rpx 0;
}

.logo {
  font-size: 80rpx;
  font-weight: bold;
  color: #3a86ff;
  margin-bottom: 20rpx;
}

.welcome-text {
  font-size: 48rpx;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}

.sub-text {
  font-size: 32rpx;
  color: #666666;
  display: block;
}

.auth-form {
  margin-top: 20rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  margin-bottom: 10rpx;
  color: #333333;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 90rpx;
  padding: 0 30rpx;
  border: 1px solid #dddddd;
  border-radius: 12rpx;
  font-size: 32rpx;
  box-sizing: border-box;
}

.password-input-group {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #666666;
}

.auth-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 60rpx;
}

.remember-me {
  display: flex;
  align-items: center;
}

.remember-me text {
  margin-left: 10rpx;
  font-size: 28rpx;
  color: #666666;
}

.forgot-password {
  font-size: 28rpx;
  color: #3a86ff;
}

.btn {
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.btn-primary {
  background-color: #3a86ff;
  color: #ffffff;
}

.btn-primary[disabled] {
  background-color: #a6c8ff;
}

.btn-full {
  width: 100%;
}

.or-divider {
  display: flex;
  align-items: center;
  margin: 40rpx 0;
}

.or-divider .line {
  flex: 1;
  height: 1px;
  background-color: #eeeeee;
}

.or-divider text {
  margin: 0 20rpx;
  color: #999999;
  font-size: 28rpx;
}

.other-login-methods {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;
}

.login-method {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 30rpx;
}

.login-icon {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx;
}

.login-icon text {
  font-size: 40rpx;
  color: #ffffff;
}

.wechat-icon {
  background-color: #07c160;
}

.sms-icon {
  background-color: #ff9500;
}

.login-method text {
  font-size: 24rpx;
  color: #666666;
}

.auth-footer {
  text-align: center;
  margin-top: auto;
  padding: 40rpx 0;
  display: flex;
  justify-content: center;
}

.auth-footer-text {
  color: #666666;
  font-size: 28rpx;
}

.auth-footer-link {
  color: #3a86ff;
  font-size: 28rpx;
  font-weight: 500;
  margin-left: 10rpx;
}


 
 .loading-section {
   flex: 1;
   display: flex;
   flex-direction: column;
   align-items: center;
   justify-content: center;
 }
 
 .loading-spinner {
   margin-bottom: 60rpx;
 }
 
 .spinner {
   width: 80rpx;
   height: 80rpx;
   border: 6rpx solid #f0f0f0;
   border-top: 6rpx solid #3a86ff;
   border-radius: 50%;
   animation: spin 1s linear infinite;
 }
 
 @keyframes spin {
   0% { transform: rotate(0deg); }
   100% { transform: rotate(360deg); }
 }
 

</style>

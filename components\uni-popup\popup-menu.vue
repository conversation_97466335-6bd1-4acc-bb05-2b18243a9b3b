<template>
	<view class="popup-menu">
		<view class="popup-menu-container">
			<slot></slot>
			<menu-item 
				v-if="showCancel" 
				:text="cancelText" 
				@click="onCancel"
			/>
		</view>
	</view>
</template>

<script>
export default {
	name: 'popup-menu',
	props: {
		showCancel: {
			type: Boolean,
			default: true
		},
		cancelText: {
			type: String,
			default: '取消'
		}
	},
	methods: {
		onCancel() {
			this.$emit('cancel')
			// 关闭popup
			const popup = this.getParentPopup()
			if (popup) {
				popup.close()
			}
		},
		getParentPopup() {
			let parent = this.$parent
			while (parent) {
				if (parent.$options.name === 'uni-popup') {
					return parent
				}
				parent = parent.$parent
			}
			return null
		}
	}
}
</script>

<style>
.popup-menu {
	background-color: #fff;
	border-radius: 12px 12px 0 0;
	overflow: hidden;
	padding-bottom: env(safe-area-inset-bottom);
}

.popup-menu-container {
	max-height: 70vh;
	overflow-y: auto;
}
</style> 
import { request } from "@/utils/request";

// 获取联系人列表
export const getContactList = (data) => {
  return request({
    url: "/api/crm/contract/getList",
    method: "POST",
    data: data,
  });
};

// 获取所有公司
export const getCompanyList = () => {
  return request({
    url: "/api/crm/contract/getAllCompanys",
    method: "GET",
  });
};

// 获取所有用户
export const getUserList = (data) => {
  return request({
    url: "/api/Users/<USER>",
    method: "POST",
    data: data,
  });
};

// 获得关联商机列表
export const getOpportunityList = (data) => {
  return request({
    url: "/api/crm/business/getKanbanList",
    method: "POST",
    data: data,
  });
};

// 获得协议列表
export const getAgreementList = (data) => {
  return request({
    url: "/api/crm/contract/getAgreementList",
    method: "POST",
    data: data,
  });
};

// 获得产品列表
export const getProductList = (data) => {
  return request({
    url: "/api/crm/product/getList",
    method: "POST",
    data: data,
  });
};

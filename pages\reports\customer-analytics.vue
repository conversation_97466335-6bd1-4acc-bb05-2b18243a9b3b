<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="flex-row items-center gap-sm">
        <text class="page-title">客户分析</text>
      </view>
      <view class="header-actions">
        <button class="action-button" @click="shareReport">
          <text class="ri-share-line"></text>
        </button>
        <view class="dropdown">
          <button class="action-button" @click="toggleReportMenu">
            <text class="ri-file-list-3-line"></text>
          </button>
          <view class="dropdown-menu" :class="{'show': showReportMenu}">
            <navigator url="/pages/reports/sales-reports" class="dropdown-item">
              <text class="ri-bar-chart-2-line"></text>
              <text>销售报表</text>
            </navigator>
            <navigator url="/pages/reports/team-performance" class="dropdown-item">
              <text class="ri-team-line"></text>
              <text>团队绩效</text>
            </navigator>
            <navigator url="/pages/reports/customer-analytics" class="dropdown-item active">
              <text class="ri-user-search-line"></text>
              <text>客户分析</text>
            </navigator>
            <navigator url="/pages/reports/custom-reports" class="dropdown-item">
              <text class="ri-file-chart-line"></text>
              <text>自定义报表</text>
            </navigator>
          </view>
        </view>
      </view>
    </view>

    <!-- 分析筛选器 -->
    <view class="filter-container">
      <scroll-view class="filter-row" scroll-x="true" show-scrollbar="false">
        <view class="filter-item" v-for="(filter, index) in filterRow1" :key="index" @click="showFilter(filter.name)">
          <text>{{filter.name}}</text>
          <text :class="filter.icon"></text>
        </view>
      </scroll-view>
      <scroll-view class="filter-row" scroll-x="true" show-scrollbar="false">
        <view class="filter-item" v-for="(filter, index) in filterRow2" :key="index" @click="showFilter(filter.name)">
          <text>{{filter.name}}</text>
          <text :class="filter.icon"></text>
        </view>
      </scroll-view>
    </view>
    
    <scroll-view scroll-y="true" class="page-content">
      <!-- 客户指标卡片 -->
      <view class="metrics-cards">
        <view class="metric-card" v-for="(metric, index) in metricsData" :key="index">
          <text class="metric-label">{{metric.label}}</text>
          <text class="metric-value">{{metric.value}}</text>
          <view class="metric-change" :class="metric.changeClass">
            <text class="ri-arrow-up-line" v-if="metric.change > 0"></text>
            <text class="ri-arrow-down-line" v-else></text>
            <text>{{metric.changeText}}</text>
          </view>
        </view>
      </view>
      
      <!-- 客户生命周期分析 -->
      <view class="chart-container">
        <view class="chart-header">
          <text class="chart-title">客户生命周期分析</text>
          <view class="chart-actions">
            <button class="chart-action-btn" @click="downloadChart('lifecycle')">
              <text class="ri-download-line"></text>
            </button>
            <button class="chart-action-btn" @click="showChartOptions('lifecycle')">
              <text class="ri-more-2-fill"></text>
            </button>
          </view>
        </view>
        <scroll-view class="lifecycle-container" scroll-x="true" show-scrollbar="false">
          <view class="lifecycle-stages">
            <view class="lifecycle-stage" v-for="(stage, index) in lifecycleData" :key="index">
              <view class="stage-connector"></view>
              <view class="stage-content">
                <text class="stage-name">{{stage.name}}</text>
                <text class="stage-count">{{stage.count}}</text>
                <text class="stage-percent">{{stage.percent}}</text>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 客户来源分布 -->
      <view class="chart-container">
        <view class="chart-header">
          <text class="chart-title">客户来源分布</text>
          <view class="chart-actions">
            <button class="chart-action-btn" @click="downloadChart('source')">
              <text class="ri-download-line"></text>
            </button>
            <button class="chart-action-btn" @click="showChartOptions('source')">
              <text class="ri-more-2-fill"></text>
            </button>
          </view>
        </view>
        <view class="chart-wrapper">
          <qiun-data-charts 
            type="pie"
            :opts="sourceChartOpts"
            :chartData="sourceChartData"
          />
        </view>
      </view>
      
      <!-- 客户行业分布 -->
      <view class="chart-container">
        <view class="chart-header">
          <text class="chart-title">客户行业分布</text>
          <view class="chart-actions">
            <button class="chart-action-btn" @click="downloadChart('industry')">
              <text class="ri-download-line"></text>
            </button>
            <button class="chart-action-btn" @click="showChartOptions('industry')">
              <text class="ri-more-2-fill"></text>
            </button>
          </view>
        </view>
        <view class="chart-wrapper">
          <qiun-data-charts 
            type="bar"
            :opts="industryChartOpts"
            :chartData="industryChartData"
          />
        </view>
      </view>
      
      <!-- 客户互动频率热图 -->
      <view class="chart-container">
        <view class="chart-header">
          <text class="chart-title">客户互动频率热图 (最近30天)</text>
          <view class="chart-actions">
            <button class="chart-action-btn" @click="showHeatmapInfo">
              <text class="ri-information-line"></text>
            </button>
          </view>
        </view>
        <view class="heatmap-grid">
          <view 
            class="heatmap-cell" 
            v-for="(cell, index) in heatmapData" 
            :key="index"
            :style="{'background-color': cell.color}"
            @click="showInteractionDetail(cell)"
          ></view>
        </view>
        <view class="heatmap-legend">
          <text>较少</text>
          <text>中等</text>
          <text>频繁</text>
        </view>
      </view>
      
      <!-- 高价值客户排行 -->
      <view class="table-container">
        <view class="table-header">
          <text class="table-title">高价值客户排行</text>
          <button class="chart-action-btn" @click="downloadTable">
            <text class="ri-download-line"></text>
          </button>
        </view>
        <view class="table-wrapper">
          <view class="table-row table-header-row">
            <text class="table-cell table-head-cell">客户名称</text>
            <text class="table-cell table-head-cell">客户价值</text>
            <text class="table-cell table-head-cell">购买频率</text>
            <text class="table-cell table-head-cell">最近互动</text>
          </view>
          <view 
            class="table-row" 
            v-for="(customer, index) in customerRanking" 
            :key="index"
            @click="navigateToCustomer(customer)"
          >
            <text class="table-cell">{{customer.name}}</text>
            <text class="table-cell">{{customer.value}}</text>
            <text class="table-cell">{{customer.frequency}}</text>
            <text class="table-cell">{{customer.lastInteraction}}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      showReportMenu: false,
      
      // 筛选器数据
      filterRow1: [
        { name: '时间范围', icon: 'ri-calendar-line' },
        { name: '客户分组', icon: 'ri-group-line' },
        { name: '客户来源', icon: 'ri-flag-line' }
      ],
      filterRow2: [
        { name: '行业', icon: 'ri-building-line' },
        { name: '区域', icon: 'ri-map-pin-line' },
        { name: '客户等级', icon: 'ri-star-line' }
      ],
      
      // 指标卡片数据
      metricsData: [
        {
          label: '活跃客户数',
          value: '1,283',
          change: 12.7,
          changeText: '12.7%',
          changeClass: 'change-positive'
        },
        {
          label: '客户留存率',
          value: '94.2%',
          change: 2.5,
          changeText: '2.5%',
          changeClass: 'change-positive'
        },
        {
          label: '客户获取成本',
          value: '¥1,236',
          change: -8.3,
          changeText: '8.3%',
          changeClass: 'change-positive'
        },
        {
          label: '客户终身价值',
          value: '¥52,840',
          change: 5.2,
          changeText: '5.2%',
          changeClass: 'change-positive'
        }
      ],
      
      // 生命周期数据
      lifecycleData: [
        { name: '潜在客户', count: '2,465', percent: '100%' },
        { name: '初次接触', count: '1,854', percent: '75.2%' },
        { name: '需求确认', count: '1,258', percent: '51.0%' },
        { name: '成交客户', count: '642', percent: '26.0%' },
        { name: '忠诚客户', count: '358', percent: '14.5%' }
      ],
      
      // 客户来源分布图表数据
      sourceChartData: {
        series: [
          {
            data: [
              { name: '线上渠道', value: 32 },
              { name: '推荐介绍', value: 25 },
              { name: '销售开发', value: 18 },
              { name: '展会活动', value: 12 },
              { name: '合作伙伴', value: 8 },
              { name: '其他', value: 5 }
            ]
          }
        ]
      },
      
      // 客户来源分布图表配置
      sourceChartOpts: {
        color: ['#4a6fff', '#34d399', '#f59e0b', '#ec4899', '#8b5cf6', '#9ca3af'],
        padding: [15, 15, 15, 15],
        enableScroll: false,
        legend: {
          position: 'right',
          itemGap: 10
        },
        extra: {
          pie: {
            activeOpacity: 0.5,
            activeRadius: 10,
            offsetAngle: 0,
            labelWidth: 15,
            border: false,
            borderWidth: 3,
            borderColor: '#FFFFFF'
          }
        }
      },
      
      // 客户行业分布图表数据
      industryChartData: {
        categories: ['信息技术', '制造业', '金融服务', '教育培训', '医疗健康', '零售商业', '其他'],
        series: [
          {
            name: '客户数量',
            data: [320, 285, 215, 180, 165, 150, 98]
          }
        ]
      },
      
      // 客户行业分布图表配置
      industryChartOpts: {
        color: ['#4a6fff'],
        padding: [15, 15, 0, 15],
        enableScroll: false,
        legend: {
          show: false
        },
        xAxis: {
          disabled: true
        },
        yAxis: {
          disableGrid: true
        },
        extra: {
          bar: {
            type: 'stack',
            width: 20,
            meterBorde: 1,
            meterFillColor: '#FFFFFF',
            barBorderCircle: false,
            linearType: 'none'
          }
        }
      },
      
      // 热图数据
      heatmapData: [],
      
      // 高价值客户排行数据
      customerRanking: [
        { name: '云科技集团', value: '¥358,600', frequency: '每月', lastInteraction: '今天' },
        { name: '星辰数据有限公司', value: '¥285,420', frequency: '每季度', lastInteraction: '昨天' },
        { name: '未来智能科技', value: '¥253,180', frequency: '每月', lastInteraction: '3天前' },
        { name: '蓝海电子商务', value: '¥184,350', frequency: '每季度', lastInteraction: '1周前' },
        { name: '绿洲环保科技', value: '¥168,720', frequency: '每半年', lastInteraction: '2周前' }
      ]
    };
  },
  
  methods: {
    toggleReportMenu() {
      this.showReportMenu = !this.showReportMenu;
    },
    
    showFilter(filterName) {
      uni.showToast({
        title: `筛选功能: ${filterName}`,
        icon: 'none'
      });
    },
    
    // 初始化热图数据
    initHeatmapData() {
      const days = 90;
      const maxInteraction = 25;
      const data = [];
      
      for (let i = 0; i < days; i++) {
        let interactions;
        
        if (i % 7 === 5 || i % 7 === 6) {
          // 周末互动少
          interactions = Math.floor(Math.random() * 8);
        } else {
          // 工作日互动多
          interactions = Math.floor(Math.random() * maxInteraction);
          
          // 有些日子特别活跃
          if (Math.random() > 0.8) {
            interactions = Math.floor(Math.random() * 10) + 15;
          }
        }
        
        // 颜色深浅表示互动频率
        const intensity = Math.min(interactions / maxInteraction, 1);
        const hue = 210; // 蓝色
        const saturation = 90;
        const lightness = 100 - (intensity * 50); // 越多互动越深
        
        data.push({
          interactions: interactions,
          color: `hsl(${hue}, ${saturation}%, ${lightness}%)`,
          day: i + 1
        });
      }
      
      this.heatmapData = data;
    },
    
    showInteractionDetail(cell) {
      uni.showToast({
        title: `第${cell.day}天: ${cell.interactions}次互动`,
        icon: 'none'
      });
    },
    
    showHeatmapInfo() {
      uni.showModal({
        title: '互动热图说明',
        content: '热图展示了最近90天内与客户的互动频率，颜色越深表示互动越频繁。周末通常互动较少，工作日互动较多。',
        showCancel: false
      });
    },
    
    shareReport() {
      uni.showActionSheet({
        itemList: ['分享到微信', '发送邮件', '导出PDF', '导出Excel'],
        success: (res) => {
          uni.showToast({
            title: '分享功能开发中',
            icon: 'none'
          });
        }
      });
    },
    
    downloadChart(chartType) {
      uni.showToast({
        title: '下载功能开发中',
        icon: 'none'
      });
    },
    
    showChartOptions(chartType) {
      uni.showActionSheet({
        itemList: ['查看大图', '下载数据', '查看明细数据'],
        success: (res) => {
          uni.showToast({
            title: '功能开发中',
            icon: 'none'
          });
        }
      });
    },
    
    downloadTable() {
      uni.showToast({
        title: '下载功能开发中',
        icon: 'none'
      });
    },
    
    navigateToCustomer(customer) {
      uni.showToast({
        title: `查看客户: ${customer.name}`,
        icon: 'none'
      });
      // 实际应用中应该跳转到客户详情页
      // uni.navigateTo({
      //   url: `/pages/customers/detail?name=${encodeURIComponent(customer.name)}`
      // });
    }
  },
  
  onLoad() {
    // 初始化热图数据
    this.initHeatmapData();
  },
  
  onShow() {
    // 重新进入页面时可能需要刷新数据
  },
  
  onHide() {
    // 隐藏菜单
    this.showReportMenu = false;
  }
};
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.page-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1px solid var(--border-color);
  background-color: #fff;
  position: relative;
  z-index: 10;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.gap-sm {
  gap: 20rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  flex-direction: row;
  gap: 20rpx;
}

.action-button {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #666;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  padding: 0;
  margin: 0;
  line-height: 1;
}

.filter-container {
  padding: 30rpx 40rpx;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.filter-row {
  display: flex;
  flex-direction: row;
  white-space: nowrap;
  margin-bottom: 20rpx;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 10rpx 30rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f5f5f5;
  margin-right: 20rpx;
}

.page-content {
  flex: 1;
  background-color: #f5f7fa;
}

.metrics-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 30rpx;
}

.metric-card {
  background-color: #fff;
  border-radius: 16rpx;
  border: 1px solid #e0e0e0;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.metric-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.metric-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.metric-change {
  font-size: 24rpx;
  margin-top: 10rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.change-positive {
  color: #34d399;
}

.change-negative {
  color: #ef4444;
}

.chart-container {
  background-color: #fff;
  border-radius: 16rpx;
  border: 1px solid #e0e0e0;
  margin: 0 30rpx 30rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.chart-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.chart-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.chart-actions {
  display: flex;
  flex-direction: row;
  gap: 10rpx;
}

.chart-action-btn {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  color: #666;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  padding: 0;
  margin: 0;
  line-height: 1;
}

.chart-wrapper {
  width: 100%;
  height: 500rpx;
  position: relative;
}

.lifecycle-container {
  width: 100%;
  overflow-x: auto;
}

.lifecycle-stages {
  display: flex;
  min-width: 1000rpx;
}

.lifecycle-stage {
  flex: 1;
  position: relative;
  padding-bottom: 20rpx;
}

.stage-content {
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  padding: 30rpx;
  text-align: center;
  margin: 0 10rpx;
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
}

.stage-name {
  font-size: 24rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
  color: #666;
}

.stage-count {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.stage-percent {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.stage-connector {
  position: absolute;
  height: 4rpx;
  background-color: #e0e0e0;
  top: 50%;
  left: 0;
  right: 0;
  z-index: 1;
}

.lifecycle-stage:first-child .stage-connector {
  left: 50%;
}

.lifecycle-stage:last-child .stage-connector {
  right: 50%;
}

.heatmap-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80rpx, 1fr));
  gap: 4rpx;
  margin-top: 30rpx;
}

.heatmap-cell {
  aspect-ratio: 1;
  border-radius: 4rpx;
}

.heatmap-legend {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #999;
}

.table-container {
  margin: 0 30rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  border: 1px solid #e0e0e0;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.table-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #e0e0e0;
}

.table-title {
  font-size: 30rpx;
  font-weight: 500;
}

.table-wrapper {
  width: 100%;
}

.table-row {
  display: flex;
  flex-direction: row;
  border-bottom: 1px solid #e0e0e0;
}

.table-row:last-child {
  border-bottom: none;
}

.table-header-row {
  background-color: #f5f7fa;
}

.table-cell {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  flex: 1;
}

.table-head-cell {
  font-weight: 500;
  color: #666;
}

.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  width: 360rpx;
  z-index: 1000;
  display: none;
  margin-top: 20rpx;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 30rpx;
  color: #333;
  font-size: 28rpx;
  border-bottom: 1px solid #e0e0e0;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item text:first-child {
  margin-right: 20rpx;
  font-size: 32rpx;
  color: #4a6fff;
}

.dropdown-item.active {
  font-weight: 500;
  background-color: rgba(74, 111, 255, 0.1);
}
</style> 
<template>
  <view class="calendar-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @tap="navBack">
        <svg-icon name="arrow-left" type="svg" size="24"></svg-icon>
      </view>
      <text class="page-title">日程安排</text>
      <view class="header-actions">
        <view class="header-icon" @tap="switchView">
          <svg-icon :name="currentView === 'month' ? 'list' : 'calendar'" type="svg" size="24"></svg-icon>
        </view>
      </view>
    </view>

    <!-- 月历视图 -->
    <view class="calendar-view" v-if="currentView === 'month'">
      <!-- 月份选择器 -->
      <view class="month-selector">
        <view class="month-nav" @tap="prevMonth">
          <svg-icon name="arrow-left" type="svg" size="20"></svg-icon>
        </view>
        <view class="current-month">{{currentYear}}年{{currentMonth}}月</view>
        <view class="month-nav" @tap="nextMonth">
          <svg-icon name="arrow-right" type="svg" size="20"></svg-icon>
        </view>
      </view>

      <!-- 星期表头 -->
      <view class="week-header">
        <text v-for="day in weekDays" :key="day">{{day}}</text>
      </view>

      <!-- 日期网格 -->
      <view class="days-grid">
        <view 
          v-for="(day, index) in daysInMonth" 
          :key="index"
          class="day-cell"
          :class="{
            'other-month': !day.currentMonth,
            'today': day.isToday,
            'has-events': day.events && day.events.length
          }"
          @tap="selectDate(day)"
        >
          <text class="day-number">{{day.date}}</text>
          <view class="event-dots" v-if="day.events && day.events.length">
            <view 
              class="event-dot"
              v-for="(event, eventIndex) in day.events.slice(0, 3)"
              :key="eventIndex"
              :style="{ backgroundColor: event.color }"
            ></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 列表视图 -->
    <view class="list-view" v-else>
      <scroll-view scroll-y class="events-list">
        <view 
          class="date-group"
          v-for="(group, index) in groupedEvents"
          :key="index"
        >
          <view class="date-header">
            <text class="date-text">{{group.date}}</text>
            <text class="weekday-text">{{group.weekday}}</text>
          </view>
          <view class="event-items">
            <view 
              class="event-item"
              v-for="(event, eventIndex) in group.events"
              :key="eventIndex"
              @tap="viewEventDetail(event)"
            >
              <view class="event-time">{{event.time}}</view>
              <view class="event-content">
                <view class="event-title">{{event.title}}</view>
                <view class="event-desc" v-if="event.description">{{event.description}}</view>
                <view class="event-location" v-if="event.location">
                  <svg-icon name="location" type="svg" size="14"></svg-icon>
                  <text>{{event.location}}</text>
                </view>
              </view>
              <view class="event-status" :class="event.status">{{event.statusText}}</view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 底部操作栏 -->
    <view class="action-bar">
      <button class="btn btn-primary" @tap="createEvent">
        <svg-icon name="plus" type="svg" size="20"></svg-icon>
        <text>新建日程</text>
      </button>
    </view>
  </view>
</template>

<script>
import SvgIcon from '@/components/svg-icon.vue';

export default {
  components: {
    SvgIcon
  },
  data() {
    return {
      currentView: 'month', // month 或 list
      currentYear: 2024,
      currentMonth: 3,
      weekDays: ['日', '一', '二', '三', '四', '五', '六'],
      daysInMonth: [],
      events: [
        {
          id: 1,
          date: '2024-03-21',
          time: '09:30',
          title: '客户会议',
          description: '与ABC公司讨论项目进展',
          location: '会议室A',
          status: 'pending',
          statusText: '待开始',
          color: '#3370ff'
        },
        {
          id: 2,
          date: '2024-03-21',
          time: '14:00',
          title: '产品演示',
          description: '新功能演示会议',
          location: '会议室B',
          status: 'completed',
          statusText: '已完成',
          color: '#52c41a'
        },
        {
          id: 3,
          date: '2024-03-22',
          time: '10:00',
          title: '团队周会',
          description: '回顾本周工作进展',
          location: '大会议室',
          status: 'pending',
          statusText: '待开始',
          color: '#722ed1'
        }
      ]
    }
  },
  computed: {
    groupedEvents() {
      // 按日期分组事件
      const groups = {};
      this.events.forEach(event => {
        if (!groups[event.date]) {
          groups[event.date] = {
            date: this.formatDate(event.date),
            weekday: this.getWeekday(event.date),
            events: []
          };
        }
        groups[event.date].events.push(event);
      });
      
      // 按日期排序
      return Object.values(groups).sort((a, b) => {
        return new Date(a.date) - new Date(b.date);
      });
    }
  },
  methods: {
    navBack() {
      uni.navigateBack();
    },
    switchView() {
      this.currentView = this.currentView === 'month' ? 'list' : 'month';
    },
    prevMonth() {
      if (this.currentMonth === 1) {
        this.currentYear--;
        this.currentMonth = 12;
      } else {
        this.currentMonth--;
      }
      this.generateCalendar();
    },
    nextMonth() {
      if (this.currentMonth === 12) {
        this.currentYear++;
        this.currentMonth = 1;
      } else {
        this.currentMonth++;
      }
      this.generateCalendar();
    },
    generateCalendar() {
      const firstDay = new Date(this.currentYear, this.currentMonth - 1, 1);
      const lastDay = new Date(this.currentYear, this.currentMonth, 0);
      const daysInMonth = lastDay.getDate();
      const firstDayWeek = firstDay.getDay();
      
      // 清空数组
      this.daysInMonth = [];
      
      // 添加上个月的日期
      const prevMonthLastDay = new Date(this.currentYear, this.currentMonth - 1, 0).getDate();
      for (let i = firstDayWeek - 1; i >= 0; i--) {
        this.daysInMonth.push({
          date: prevMonthLastDay - i,
          currentMonth: false,
          events: []
        });
      }
      
      // 添加当前月的日期
      const today = new Date();
      for (let i = 1; i <= daysInMonth; i++) {
        const date = `${this.currentYear}-${String(this.currentMonth).padStart(2, '0')}-${String(i).padStart(2, '0')}`;
        this.daysInMonth.push({
          date: i,
          currentMonth: true,
          isToday: today.getFullYear() === this.currentYear && 
                   today.getMonth() + 1 === this.currentMonth && 
                   today.getDate() === i,
          events: this.events.filter(event => event.date === date)
        });
      }
      
      // 添加下个月的日期
      const remainingDays = 42 - this.daysInMonth.length;
      for (let i = 1; i <= remainingDays; i++) {
        this.daysInMonth.push({
          date: i,
          currentMonth: false,
          events: []
        });
      }
    },
    selectDate(day) {
      if (!day.currentMonth) return;
      const date = `${this.currentYear}-${String(this.currentMonth).padStart(2, '0')}-${String(day.date).padStart(2, '0')}`;
      console.log('选择日期:', date);
      // 这里可以添加选择日期后的操作
    },
    createEvent() {
      uni.navigateTo({
        url: '/pages/actions/action-create'
      });
    },
    viewEventDetail(event) {
      uni.navigateTo({
        url: `/pages/actions/action-detail?id=${event.id}`
      });
    },
    formatDate(dateString) {
      const date = new Date(dateString);
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    },
    getWeekday(dateString) {
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      const date = new Date(dateString);
      return weekdays[date.getDay()];
    }
  },
  onLoad() {
    this.generateCalendar();
  }
}
</script>

<style>
.calendar-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e0e0e0;
}

.back-button {
  padding: 10rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 30rpx;
}

.header-icon {
  color: #666;
  padding: 10rpx;
}

.calendar-view {
  background-color: #fff;
  margin: 30rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.month-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.month-nav {
  padding: 10rpx;
  color: #666;
}

.current-month {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.week-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  margin-bottom: 20rpx;
}

.week-header text {
  font-size: 24rpx;
  color: #999;
  padding: 10rpx 0;
}

.days-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2rpx;
  background-color: #f5f7fa;
}

.day-cell {
  aspect-ratio: 1;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx;
  position: relative;
}

.day-number {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.other-month {
  color: #ccc;
}

.other-month .day-number {
  color: #ccc;
}

.today {
  background-color: #e6f7ff;
}

.today .day-number {
  color: #3370ff;
  font-weight: bold;
}

.event-dots {
  display: flex;
  gap: 4rpx;
  margin-top: 4rpx;
}

.event-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
}

.list-view {
  flex: 1;
  overflow: hidden;
}

.events-list {
  height: 100%;
  padding: 30rpx;
}

.date-group {
  margin-bottom: 40rpx;
}

.date-header {
  margin-bottom: 20rpx;
}

.date-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 16rpx;
}

.weekday-text {
  font-size: 28rpx;
  color: #999;
}

.event-items {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.event-item {
  display: flex;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.event-item:last-child {
  border-bottom: none;
}

.event-time {
  font-size: 28rpx;
  color: #666;
  width: 100rpx;
  flex-shrink: 0;
}

.event-content {
  flex: 1;
  margin: 0 20rpx;
}

.event-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.event-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.event-location {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
}

.event-location text {
  margin-left: 6rpx;
}

.event-status {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 100rpx;
  align-self: flex-start;
}

.event-status.pending {
  background-color: #e6f7ff;
  color: #3370ff;
}

.event-status.completed {
  background-color: #f6ffed;
  color: #52c41a;
}

.action-bar {
  padding: 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #e0e0e0;
}

.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  border-radius: 8rpx;
  font-size: 30rpx;
}

.btn-primary {
  background-color: #3370ff;
  color: #fff;
}

.btn text {
  margin-left: 10rpx;
}
</style> 
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-actions-action-detail"],{"44d0":function(t,i,a){var e=a("c86c");i=e(!1),i.push([t.i,".page[data-v-d95c1318]{display:flex;flex-direction:column;height:100vh;background-color:#f5f7fa}.page-header[data-v-d95c1318]{display:flex;align-items:center;justify-content:space-between;padding:%?30?% %?40?%;border-bottom:%?1?% solid #e0e0e0;background-color:#fff;position:-webkit-sticky;position:sticky;top:0;z-index:10}.back-button[data-v-d95c1318]{padding:%?10?%}.page-title[data-v-d95c1318]{font-size:%?36?%;font-weight:700;color:#333}.header-actions[data-v-d95c1318]{display:flex;gap:%?30?%}.header-icon[data-v-d95c1318]{color:#666;font-size:%?40?%;display:flex;align-items:center;justify-content:center}.page-container[data-v-d95c1318]{flex:1;padding:%?30?%}.info-section[data-v-d95c1318]{background-color:#fff;border-radius:%?16?%;padding:%?30?%;margin-bottom:%?30?%;box-shadow:0 %?2?% %?10?% rgba(0,0,0,.05)}.info-header[data-v-d95c1318]{display:flex;align-items:center;margin-bottom:%?20?%}.action-type-icon[data-v-d95c1318]{width:%?80?%;height:%?80?%;display:flex;justify-content:center;align-items:center;border-radius:50%;margin-right:%?20?%;color:#fff}.action-type-icon.high[data-v-d95c1318]{background-color:#f5222d}.action-type-icon.medium[data-v-d95c1318]{background-color:#faad14}.action-type-icon.low[data-v-d95c1318]{background-color:#52c41a}.info-title[data-v-d95c1318]{flex:1}.action-title[data-v-d95c1318]{font-size:%?32?%;font-weight:500;color:#333;display:block;margin-bottom:%?8?%}.action-time[data-v-d95c1318]{font-size:%?24?%;color:#999}.action-status[data-v-d95c1318]{display:flex;justify-content:flex-end;margin-top:%?20?%}.status-item[data-v-d95c1318]{display:flex;align-items:center;padding:%?10?% %?20?%;border-radius:%?100?%;background-color:#f5f5f5;color:#999}.status-item.active[data-v-d95c1318]{background-color:#e6f7ff;color:#1890ff}.status-item uni-text[data-v-d95c1318]{margin-left:%?8?%;font-size:%?26?%}.section-header[data-v-d95c1318]{display:flex;align-items:center;margin-bottom:%?20?%}.section-title[data-v-d95c1318]{font-size:%?30?%;font-weight:700;margin-left:%?10?%;color:#333}.section-content[data-v-d95c1318]{padding:%?10?%}.info-item[data-v-d95c1318]{display:flex;justify-content:space-between;align-items:center;padding:%?20?% 0;border-bottom:%?1?% solid #f0f0f0}.info-item[data-v-d95c1318]:last-child{border-bottom:none}.info-label[data-v-d95c1318]{font-size:%?28?%;color:#666}.info-value[data-v-d95c1318]{font-size:%?28?%;color:#333}.description-text[data-v-d95c1318]{font-size:%?28?%;line-height:1.6;color:#333}.participant-list[data-v-d95c1318]{display:flex;flex-direction:column;gap:%?20?%}.participant-item[data-v-d95c1318]{display:flex;align-items:center}.participant-avatar[data-v-d95c1318]{width:%?80?%;height:%?80?%;border-radius:50%;background-color:#e6f7ff;color:#1890ff;display:flex;align-items:center;justify-content:center;font-size:%?32?%;margin-right:%?20?%}.participant-info[data-v-d95c1318]{flex:1}.participant-name[data-v-d95c1318]{font-size:%?28?%;color:#333;display:block;margin-bottom:%?4?%}.participant-role[data-v-d95c1318]{font-size:%?24?%;color:#999}.history-list[data-v-d95c1318]{display:flex;flex-direction:column;gap:%?20?%}.history-item[data-v-d95c1318]{display:flex;flex-direction:column;gap:%?8?%}.history-time[data-v-d95c1318]{font-size:%?24?%;color:#999}.history-content[data-v-d95c1318]{font-size:%?28?%;color:#333}.action-bar[data-v-d95c1318]{position:fixed;bottom:0;left:0;right:0;padding:%?20?% %?30?%;background-color:#fff;box-shadow:0 %?-2?% %?10?% rgba(0,0,0,.05);display:flex;gap:%?30?%}.btn[data-v-d95c1318]{flex:1;height:%?88?%;border-radius:%?8?%;display:flex;align-items:center;justify-content:center;font-size:%?30?%}.btn-outline[data-v-d95c1318]{border:%?1?% solid #d9d9d9;color:#666}.btn-primary[data-v-d95c1318]{background-color:#3370ff;color:#fff}",""]),t.exports=i},"78f8":function(t,i,a){"use strict";a.r(i);var e=a("c123"),n=a.n(e);for(var o in e)["default"].indexOf(o)<0&&function(t){a.d(i,t,(function(){return e[t]}))}(o);i["default"]=n.a},"88fb":function(t,i,a){"use strict";var e=a("e4bb"),n=a.n(e);n.a},"99ae":function(t,i,a){"use strict";a.r(i);var e=a("9b86"),n=a("78f8");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(i,t,(function(){return n[t]}))}(o);a("88fb");var c=a("828b"),s=Object(c["a"])(n["default"],e["b"],e["c"],!1,null,"d95c1318",null,!1,e["a"],void 0);i["default"]=s.exports},"9b86":function(t,i,a){"use strict";a.d(i,"b",(function(){return n})),a.d(i,"c",(function(){return o})),a.d(i,"a",(function(){return e}));var e={svgIcon:a("8a0f").default},n=function(){var t=this,i=t.$createElement,a=t._self._c||i;return a("v-uni-view",{staticClass:"page"},[a("v-uni-view",{staticClass:"page-header"},[a("v-uni-view",{staticClass:"back-button",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.navBack.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"arrow-left",type:"svg",size:"24"}})],1),a("v-uni-text",{staticClass:"page-title"},[t._v("行动计划详情")]),a("v-uni-view",{staticClass:"header-actions"},[a("v-uni-view",{staticClass:"header-icon",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.showMoreActions.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"more",type:"svg",size:"28"}})],1)],1)],1),a("v-uni-scroll-view",{staticClass:"page-container",attrs:{"scroll-y":!0}},[a("v-uni-view",{staticClass:"info-section"},[a("v-uni-view",{staticClass:"info-header"},[a("v-uni-view",{staticClass:"action-type-icon",class:t.actionData.priority},[a("svg-icon",{attrs:{name:t.getTypeIconName(t.actionData),type:"svg",size:"28"}})],1),a("v-uni-view",{staticClass:"info-title"},[a("v-uni-text",{staticClass:"action-title"},[t._v(t._s(t.actionData.title))]),a("v-uni-text",{staticClass:"action-time"},[t._v(t._s(t.actionData.date)+" "+t._s(t.actionData.time))])],1)],1),"task"===t.actionData.type?a("v-uni-view",{staticClass:"action-status"},[a("v-uni-view",{staticClass:"status-item",class:{active:t.actionData.completed},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toggleStatus.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:t.actionData.completed?"checkbox-circle-fill":"checkbox-blank-circle-line",type:"svg",size:"28"}}),a("v-uni-text",[t._v(t._s(t.actionData.completed?"已完成":"未完成"))])],1)],1):t._e()],1),t.actionData.related?a("v-uni-view",{staticClass:"info-section"},[a("v-uni-view",{staticClass:"section-header"},[a("svg-icon",{attrs:{name:"link",type:"svg",size:"20"}}),a("v-uni-text",{staticClass:"section-title"},[t._v("关联信息")])],1),a("v-uni-view",{staticClass:"section-content"},[a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"info-label"},[t._v("关联对象")]),a("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.actionData.related))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"info-label"},[t._v("关联类型")]),a("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.getRelatedTypeName(t.actionData.relatedType)))])],1)],1)],1):t._e(),t.actionData.description?a("v-uni-view",{staticClass:"info-section"},[a("v-uni-view",{staticClass:"section-header"},[a("svg-icon",{attrs:{name:"align-left",type:"svg",size:"20"}}),a("v-uni-text",{staticClass:"section-title"},[t._v("详情描述")])],1),a("v-uni-view",{staticClass:"section-content"},[a("v-uni-text",{staticClass:"description-text"},[t._v(t._s(t.actionData.description))])],1)],1):t._e(),"task"!==t.actionData.type?a("v-uni-view",{staticClass:"info-section"},[a("v-uni-view",{staticClass:"section-header"},[a("svg-icon",{attrs:{name:"notification",type:"svg",size:"20"}}),a("v-uni-text",{staticClass:"section-title"},[t._v("提醒设置")])],1),a("v-uni-view",{staticClass:"section-content"},[a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"info-label"},[t._v("提醒")]),a("v-uni-switch",{attrs:{checked:t.actionData.hasReminder,color:"#3370ff"},on:{change:function(i){arguments[0]=i=t.$handleEvent(i),t.onReminderChange.apply(void 0,arguments)}}})],1),t.actionData.hasReminder?a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"info-label"},[t._v("提醒时间")]),a("v-uni-text",{staticClass:"info-value"},[t._v(t._s(t.getReminderTimeText(t.actionData.reminderTime)))])],1):t._e()],1)],1):t._e(),"task"!==t.actionData.type&&t.actionData.participants?a("v-uni-view",{staticClass:"info-section"},[a("v-uni-view",{staticClass:"section-header"},[a("svg-icon",{attrs:{name:"user-group",type:"svg",size:"20"}}),a("v-uni-text",{staticClass:"section-title"},[t._v("参与人")])],1),a("v-uni-view",{staticClass:"section-content"},[a("v-uni-view",{staticClass:"participant-list"},t._l(t.actionData.participants,(function(i,e){return a("v-uni-view",{key:e,staticClass:"participant-item"},[a("v-uni-view",{staticClass:"participant-avatar"},[a("v-uni-text",[t._v(t._s(i.name.charAt(0)))])],1),a("v-uni-view",{staticClass:"participant-info"},[a("v-uni-text",{staticClass:"participant-name"},[t._v(t._s(i.name))]),a("v-uni-text",{staticClass:"participant-role"},[t._v(t._s(i.role))])],1)],1)})),1)],1)],1):t._e(),a("v-uni-view",{staticClass:"info-section"},[a("v-uni-view",{staticClass:"section-header"},[a("svg-icon",{attrs:{name:"history",type:"svg",size:"20"}}),a("v-uni-text",{staticClass:"section-title"},[t._v("操作记录")])],1),a("v-uni-view",{staticClass:"section-content"},[a("v-uni-view",{staticClass:"history-list"},t._l(t.actionData.history,(function(i,e){return a("v-uni-view",{key:e,staticClass:"history-item"},[a("v-uni-view",{staticClass:"history-time"},[t._v(t._s(i.time))]),a("v-uni-view",{staticClass:"history-content"},[t._v(t._s(i.content))])],1)})),1)],1)],1)],1),a("v-uni-view",{staticClass:"action-bar"},[a("v-uni-button",{staticClass:"btn btn-outline",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.editAction.apply(void 0,arguments)}}},[t._v("编辑")]),"task"!==t.actionData.type||t.actionData.completed?t._e():a("v-uni-button",{staticClass:"btn btn-primary",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.completeAction.apply(void 0,arguments)}}},[t._v("完成")])],1)],1)},o=[]},c123:function(t,i,a){"use strict";a("6a54");var e=a("f5bd").default;Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var n=e(a("8a0f")),o={components:{SvgIcon:n.default},data:function(){return{actionData:{id:"",type:"task",title:"",priority:"medium",date:"",time:"",related:"",relatedType:"company",description:"",completed:!1,hasReminder:!1,reminderTime:15,participants:[],history:[]}}},methods:{navBack:function(){uni.navigateBack()},showMoreActions:function(){var t=this;uni.showActionSheet({itemList:["删除","分享","复制"],success:function(i){switch(i.tapIndex){case 0:t.deleteAction();break;case 1:t.shareAction();break;case 2:t.copyAction();break}}})},getTypeIconName:function(t){return{task:"check-circle",call:"phone",meeting:"team",visit:"navigation",email:"mail"}[t.type]||"schedule"},getRelatedTypeName:function(t){return{company:"公司",contact:"联系人",opportunity:"商机",contract:"合同",meeting:"会议",report:"报表",department:"部门"}[t]||t},getReminderTimeText:function(t){return{5:"活动开始前5分钟",15:"活动开始前15分钟",30:"活动开始前30分钟",60:"活动开始前1小时",120:"活动开始前2小时",1440:"活动开始前1天"}[t]||"".concat(t,"分钟")},toggleStatus:function(){this.actionData.completed=!this.actionData.completed,uni.showToast({title:this.actionData.completed?"已完成":"已取消",icon:"none"})},onReminderChange:function(t){this.actionData.hasReminder=t.detail.value,uni.showToast({title:this.actionData.hasReminder?"提醒已开启":"提醒已关闭",icon:"none"})},editAction:function(){uni.navigateTo({url:"/pages/actions/action-create?id=".concat(this.actionData.id)})},completeAction:function(){this.actionData.completed=!0,uni.showToast({title:"已完成",icon:"success"})},deleteAction:function(){uni.showModal({title:"确认删除",content:"确定要删除此行动计划吗？",success:function(t){t.confirm&&uni.showToast({title:"删除成功",icon:"success",duration:2e3,success:function(){setTimeout((function(){uni.navigateBack()}),1500)}})}})},shareAction:function(){uni.showToast({title:"分享功能开发中",icon:"none"})},copyAction:function(){uni.showToast({title:"复制功能开发中",icon:"none"})},loadActionDetail:function(t){this.actionData={id:t,type:"task",title:"完成销售报表",priority:"high",date:"2023-10-25",time:"16:30",related:"销售部",relatedType:"department",description:"需要完成本月销售报表，包括销售额、客户增长、产品分析等内容。",completed:!1,hasReminder:!0,reminderTime:15,participants:[{name:"张三",role:"销售经理"},{name:"李四",role:"数据分析师"}],history:[{time:"2023-10-24 10:00",content:"创建任务"},{time:"2023-10-24 14:30",content:"添加了参与人"}]}}},onLoad:function(t){t.id&&this.loadActionDetail(t.id)}};i.default=o},e4bb:function(t,i,a){var e=a("44d0");e.__esModule&&(e=e.default),"string"===typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);var n=a("967d").default;n("e9513688",e,!0,{sourceMap:!1,shadowMode:!1})}}]);
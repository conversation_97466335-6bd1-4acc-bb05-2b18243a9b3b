<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="goBack">
        <svg-icon name="arrow-left" type="svg" size="24"></svg-icon>
      </view>
      <text class="page-title">报价单详情</text>
      <view class="header-actions">
        <view class="action-button" @click="shareQuotation">
          <svg-icon name="share" type="svg" size="24"></svg-icon>
        </view>
        <view class="menu-container">
          <view class="action-button" @click="toggleMenu">
            <svg-icon name="more" type="svg" size="24"></svg-icon>
          </view>
          <view class="dropdown-menu" v-if="showMenu">
            <view class="dropdown-item" @click="exportPDF">
              <svg-icon name="file-pdf" type="svg" size="20"></svg-icon>
              <text>导出PDF</text>
            </view>
            <view class="dropdown-item" @click="printQuotation">
              <svg-icon name="printer" type="svg" size="20"></svg-icon>
              <text>打印</text>
            </view>
            <view class="dropdown-item" @click="copyQuotation">
              <svg-icon name="file-copy" type="svg" size="20"></svg-icon>
              <text>复制</text>
            </view>
            <view class="dropdown-divider"></view>
            <view class="dropdown-item delete" @click="deleteQuotation">
              <svg-icon name="delete-bin" type="svg" size="20"></svg-icon>
              <text>删除</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <scroll-view scroll-y class="content-scroll">
      <!-- 报价单头部信息 -->
      <view class="quotation-header">
        <text class="quotation-title">{{quotation.title}}</text>
        <text class="quotation-id">报价单号: {{quotation.code}}</text>
        
        <view class="quotation-meta">
          <view class="meta-item">
            <text class="meta-label">创建日期</text>
            <text class="meta-value">{{quotation.createDate}}</text>
          </view>
          <view class="meta-item">
            <text class="meta-label">有效期至</text>
            <text class="meta-value">{{quotation.validUntil}}</text>
          </view>
          <view class="meta-item">
            <text class="meta-label">负责人</text>
            <text class="meta-value">{{quotation.owner}}</text>
          </view>
        </view>
        
        <view :class="['quotation-status', 'status-' + quotation.status.code]">
          {{quotation.status.name}}
        </view>
        
        <view class="action-buttons">
          <button class="btn btn-outline" @click="editQuotation" v-if="quotation.status.code === 'draft'">
            <svg-icon name="edit" type="svg" size="18"></svg-icon>
            <text>编辑</text>
          </button>
          <button class="btn btn-outline" @click="sendQuotation" v-if="quotation.status.code !== 'rejected'">
            <svg-icon name="mail" type="svg" size="18"></svg-icon>
            <text>发送</text>
          </button>
          <button class="btn btn-primary" @click="convertToContract" v-if="['accepted', 'sent'].includes(quotation.status.code)">
            <svg-icon name="contract" type="svg" size="18"></svg-icon>
            <text>转为合同</text>
          </button>
        </view>
      </view>
      
      <!-- 客户信息 -->
      <view class="info-section">
        <view class="section-title">
          <text>客户信息</text>
        </view>
        <view class="info-content">
          <view class="info-row">
            <text class="info-label">客户名称</text>
            <text class="info-value">{{quotation.customer}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">联系人</text>
            <text class="info-value">{{quotation.contact || '未指定'}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">联系电话</text>
            <text class="info-value">{{quotation.phone || '未提供'}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">电子邮箱</text>
            <text class="info-value">{{quotation.email || '未提供'}}</text>
          </view>
        </view>
      </view>
      
      <!-- 产品和服务明细 -->
      <view class="info-section">
        <view class="section-title">
          <text>产品与服务</text>
        </view>
        <view class="product-list">
          <view class="product-item" v-for="(item, index) in quotation.items" :key="index">
            <view class="product-header">
              <text class="product-name">{{item.name}}</text>
              <text class="product-price">¥{{formatPrice(item.amount)}}</text>
            </view>
            <view class="product-details" v-if="item.description">
              <text class="product-description">{{item.description}}</text>
            </view>
          </view>
          
          <!-- 合计 -->
          <view class="total-row">
            <text class="total-label">总计</text>
            <text class="total-value">¥{{formatPrice(quotation.totalAmount)}}</text>
          </view>
        </view>
      </view>
      
      <!-- 备注说明 -->
      <view class="info-section">
        <view class="section-title">
          <text>备注说明</text>
        </view>
        <view class="remark-content">
          <text class="remark-text">{{quotation.remark || '暂无备注信息'}}</text>
        </view>
      </view>
      
      <!-- 附加信息 -->
      <view class="info-section">
        <view class="section-title">
          <text>附加信息</text>
        </view>
        <view class="info-content">
          <view class="info-row">
            <text class="info-label">创建人</text>
            <text class="info-value">{{quotation.creator || '系统'}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">创建时间</text>
            <text class="info-value">{{quotation.createTime || quotation.createDate}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">最后更新</text>
            <text class="info-value">{{quotation.updateTime || '无更新记录'}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">关联商机</text>
            <text class="info-value">{{quotation.opportunity || '无关联商机'}}</text>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 自定义TabBar组件 -->
    <custom-tab-bar ref="customTabBar"></custom-tab-bar>
  </view>
</template>

<script>
import CustomTabBar from '@/components/CustomTabBar.vue';
import SvgIcon from '@/components/svg-icon.vue';

export default {
  components: {
    CustomTabBar,
    SvgIcon
  },
  data() {
    return {
      id: '',
      showMenu: false,
      quotation: {
        id: '1',
        title: '云数据分析平台解决方案',
        code: 'QT-2023-10-001',
        customer: '北京科技有限公司',
        contact: '张经理',
        phone: '13800138000',
        email: '<EMAIL>',
        createDate: '2023-10-10',
        createTime: '2023-10-10 14:30',
        validUntil: '2023-11-10',
        owner: '李销售',
        creator: '李销售',
        updateTime: '2023-10-12 09:45',
        opportunity: '云平台建设项目',
        remark: '1. 本报价单有效期为30天；\n2. 不包含硬件设备费用；\n3. 可根据客户需求调整配置和价格。',
        items: [
          { 
            name: '产品费用', 
            amount: 280000,
            description: '包含基础模块、数据分析模块、报表模块的授权费用'
          },
          { 
            name: '实施费用', 
            amount: 50000,
            description: '包含需求调研、系统部署、数据迁移和系统测试'
          },
          { 
            name: '年度维护', 
            amount: 20000,
            description: '包含系统升级、故障处理和技术支持'
          }
        ],
        totalAmount: 350000,
        status: {
          code: 'sent',
          name: '已发送'
        }
      }
    }
  },
  onLoad(options) {
    if (options.id) {
      this.id = options.id;
      // 实际项目中应该通过API获取报价单详情
      console.log('加载报价单ID:', this.id);
    }
  },
  methods: {
    formatPrice(price) {
      return price.toLocaleString('zh-CN');
    },
    goBack() {
      uni.navigateBack();
    },
    toggleMenu() {
      this.showMenu = !this.showMenu;
    },
    editQuotation() {
      uni.navigateTo({
        url: `/pages/sales/quotation-edit?id=${this.quotation.id}`
      });
    },
    deleteQuotation() {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除该报价单吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '删除成功',
              icon: 'success',
              success: () => {
                setTimeout(() => {
                  uni.navigateBack();
                }, 1500);
              }
            });
          }
        }
      });
      this.showMenu = false;
    },
    shareQuotation() {
      uni.showToast({
        title: '分享功能开发中...',
        icon: 'none'
      });
    },
    exportPDF() {
      uni.showToast({
        title: '导出PDF功能开发中...',
        icon: 'none'
      });
      this.showMenu = false;
    },
    printQuotation() {
      uni.showToast({
        title: '打印功能开发中...',
        icon: 'none'
      });
      this.showMenu = false;
    },
    copyQuotation() {
      uni.showToast({
        title: '复制功能开发中...',
        icon: 'none'
      });
      this.showMenu = false;
    },
    sendQuotation() {
      uni.showToast({
        title: '发送功能开发中...',
        icon: 'none'
      });
    },
    convertToContract() {
      uni.navigateTo({
        url: `/pages/sales/contract-create?quotationId=${this.quotation.id}`
      });
    }
  },
  onShow() {
    // 设置TabBar当前选中项
    if (typeof this.$refs.customTabBar !== 'undefined') {
      this.$refs.customTabBar.current = 2; // 对应"销售"菜单
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #f5f5f5;
  box-sizing: border-box;
  overflow: hidden;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  height: 44px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  width: 100%;
  box-sizing: border-box;
}

.page-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.back-button {
  color: #666;
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #666;
  background-color: #f5f5f5;
  border: 1px solid #eee;
}

.content-scroll {
  flex: 1;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-bottom: 100rpx;
}

.quotation-header {
  background-color: white;
  padding: 20px 16px;
  border-bottom: 1px solid #eee;
}

.quotation-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
}

.quotation-id {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
  display: block;
}

.quotation-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 16px;
}

.meta-item {
  display: flex;
  flex-direction: column;
}

.meta-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.meta-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.quotation-status {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 100px;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 16px;
}

.status-draft {
  background-color: #f3f4f6;
  color: #6b7280;
}

.status-sent {
  background-color: #dbeafe;
  color: #2563eb;
}

.status-accepted {
  background-color: #d1fae5;
  color: #059669;
}

.status-rejected {
  background-color: #fee2e2;
  color: #dc2626;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.btn-outline {
  border: 1px solid #ddd;
  background-color: white;
  color: #333;
}

.btn-primary {
  background-color: #3a86ff;
  color: white;
  border: none;
}

.info-section {
  background-color: white;
  margin: 12px 0;
  padding: 16px;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-row {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.info-value {
  font-size: 14px;
  color: #333;
}

.product-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.product-item {
  padding: 12px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #eee;
}

.product-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.product-name {
  font-size: 15px;
  font-weight: 500;
  color: #333;
}

.product-price {
  font-size: 15px;
  font-weight: 600;
  color: #333;
}

.product-description {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}

.total-row {
  display: flex;
  justify-content: space-between;
  padding: 16px 0;
  border-top: 1px solid #eee;
  margin-top: 8px;
}

.total-label {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.total-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.remark-content {
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #eee;
}

.remark-text {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  white-space: pre-line;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  border: 1px solid #eee;
  padding: 4px 0;
  z-index: 1000;
  min-width: 180px;
}

.dropdown-item {
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #333;
}

.dropdown-item.delete {
  color: #dc2626;
}

.dropdown-divider {
  height: 1px;
  background-color: #eee;
  margin: 4px 0;
}

.menu-container {
  position: relative;
}
</style> 
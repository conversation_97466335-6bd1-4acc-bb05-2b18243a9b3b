<template>
  <view class="container">
    <!-- 头部导航 -->
    <view class="page-header">
      <view class="back-button" @click="goBack">
        <text class="ri-arrow-left-line"></text>
      </view>
      <view class="page-title">编辑合同</view>
      <view class="header-actions">
        <button @click="saveContract">保存</button>
      </view>
    </view>

    <scroll-view scroll-y class="form-container">
      <!-- 基本信息部分 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>
        
        <view class="form-group">
          <text class="form-label">合同名称</text>
          <input type="text" class="form-input" v-model="formData.title" placeholder="输入合同名称" />
        </view>
        
        <view class="form-group">
          <text class="form-label">合同编号</text>
          <input type="text" class="form-input" v-model="formData.code" placeholder="自动生成" disabled />
        </view>
        
        <view class="form-group">
          <text class="form-label">关联报价单</text>
          <picker @change="onQuotationChange" :value="quotationIndex" :range="quotations" range-key="name">
            <view class="picker-view">
              <text v-if="formData.quotation">{{formData.quotation.name}}</text>
              <text v-else class="placeholder">请选择关联报价单</text>
              <text class="ri-arrow-down-s-line"></text>
            </view>
          </picker>
        </view>
        
        <view class="form-group">
          <text class="form-label">签署日期</text>
          <view class="form-date">
            <picker mode="date" @change="onSigningDateChange" :value="formData.signingDate">
              <view class="picker-view">
                <text v-if="formData.signingDate">{{formData.signingDate}}</text>
                <text v-else class="placeholder">请选择签署日期</text>
                <text class="ri-calendar-line"></text>
              </view>
            </picker>
          </view>
        </view>
        
        <view class="form-group">
          <text class="form-label">有效期至</text>
          <view class="form-date">
            <picker mode="date" @change="onExpiryDateChange" :value="formData.expiryDate">
              <view class="picker-view">
                <text v-if="formData.expiryDate">{{formData.expiryDate}}</text>
                <text v-else class="placeholder">请选择有效期</text>
                <text class="ri-calendar-line"></text>
              </view>
            </picker>
          </view>
        </view>
        
        <view class="form-group">
          <text class="form-label">合同类型</text>
          <picker @change="onContractTypeChange" :value="contractTypeIndex" :range="contractTypes" range-key="name">
            <view class="picker-view">
              <text v-if="formData.contractType">{{formData.contractType.name}}</text>
              <text v-else class="placeholder">请选择合同类型</text>
              <text class="ri-arrow-down-s-line"></text>
            </view>
          </picker>
        </view>
        
        <view class="form-group">
          <text class="form-label">负责人</text>
          <picker @change="onOwnerChange" :value="ownerIndex" :range="owners" range-key="name">
            <view class="picker-view">
              <text v-if="formData.owner">{{formData.owner.name}}</text>
              <text v-else class="placeholder">请选择负责人</text>
              <text class="ri-arrow-down-s-line"></text>
            </view>
          </picker>
        </view>
        
        <view class="form-group">
          <text class="form-label">状态</text>
          <picker @change="onStatusChange" :value="statusIndex" :range="statuses" range-key="name">
            <view class="picker-view">
              <text v-if="formData.status">{{formData.status.name}}</text>
              <text v-else class="placeholder">请选择状态</text>
              <text class="ri-arrow-down-s-line"></text>
            </view>
          </picker>
        </view>
      </view>
      
      <!-- 客户信息部分 -->
      <view class="form-section">
        <view class="section-title">客户信息</view>
        
        <view class="customer-selector" @click="selectCustomer">
          <view class="customer-avatar" v-if="formData.customer">{{formData.customer.name.charAt(0)}}</view>
          <view class="customer-avatar" v-else>选</view>
          <view class="customer-info">
            <view class="customer-name" v-if="formData.customer">{{formData.customer.name}}</view>
            <view class="customer-name" v-else>请选择客户</view>
            <view class="customer-meta" v-if="formData.customer">{{formData.customer.industry}} · {{formData.customer.size}}</view>
          </view>
          <view class="customer-action">
            <text class="customer-select-button">
              更换客户
              <text class="ri-arrow-right-s-line"></text>
            </text>
          </view>
        </view>
        
        <view class="form-group">
          <text class="form-label">联系人</text>
          <picker @change="onContactChange" :value="contactIndex" :range="contacts" range-key="name" :disabled="!formData.customer">
            <view class="picker-view">
              <text v-if="formData.contact">{{formData.contact.name}} ({{formData.contact.title}})</text>
              <text v-else class="placeholder">请选择联系人</text>
              <text class="ri-arrow-down-s-line"></text>
            </view>
          </picker>
        </view>
      </view>
      
      <!-- 合同金额部分 -->
      <view class="form-section">
        <view class="section-title">合同金额</view>
        
        <view class="summary-table">
          <view class="summary-row" v-for="(item, index) in formData.items" :key="index">
            <text class="label">{{item.name}}</text>
            <view class="value">
              <input type="digit" v-model="item.amount" placeholder="0" @input="calculateTotal" />
            </view>
          </view>
          <view class="summary-row">
            <text class="label">增值税 ({{formData.taxRate}}%)</text>
            <text class="value">¥{{formatNumber(formData.tax)}}</text>
          </view>
          <view class="total-row">
            <text class="label">总计</text>
            <text class="value">¥{{formatNumber(formData.totalAmount)}}</text>
          </view>
        </view>
      </view>
      
      <!-- 付款计划部分 -->
      <view class="form-section">
        <view class="section-title">付款计划</view>
        
        <!-- 付款计划项 -->
        <view class="payment-plan-item" v-for="(payment, index) in formData.paymentPlan" :key="index">
          <view class="payment-item-header">
            <view class="payment-item-title">第{{index+1}}期付款</view>
            <view class="payment-item-actions">
              <view class="payment-action-button delete-button" @click="removePaymentPlan(index)">
                <view class="delete-icon">×</view>
              </view>
            </view>
          </view>
          <view class="payment-item-body">
            <view class="form-group">
              <text class="form-label">付款金额</text>
              <input type="digit" class="form-input" v-model="payment.amount" placeholder="0" @input="calculatePercentage(index)" />
            </view>
            <view class="form-group">
              <text class="form-label">付款比例</text>
              <input type="text" class="form-input" :value="payment.percentage + '%'" disabled />
            </view>
            <view class="form-group">
              <text class="form-label">预计日期</text>
              <view class="form-date">
                <picker mode="date" @change="(e) => onPaymentDateChange(e, index)" :value="payment.date">
                  <view class="picker-view">
                    <text>{{payment.date || '请选择日期'}}</text>
                    <text class="ri-calendar-line"></text>
                  </view>
                </picker>
              </view>
            </view>
            <view class="form-group">
              <text class="form-label">状态</text>
              <picker @change="(e) => onPaymentStatusChange(e, index)" :value="getPaymentStatusIndex(payment.status)" :range="paymentStatuses" range-key="name">
                <view class="picker-view">
                  <text>{{getPaymentStatusName(payment.status)}}</text>
                  <text class="ri-arrow-down-s-line"></text>
                </view>
              </picker>
            </view>
          </view>
          <view class="form-group">
            <text class="form-label">付款说明</text>
            <input type="text" class="form-input" v-model="payment.description" placeholder="输入付款说明" />
          </view>
        </view>
        
        <button type="button" class="add-button" @click="addPaymentPlan">
          <text class="ri-add-line"></text>
          添加付款计划
        </button>
      </view>
      
      <!-- 合同条款 -->
      <view class="form-section">
        <view class="section-title">合同条款</view>
        
        <view class="form-group">
          <text class="form-label">项目周期</text>
          <input type="number" class="form-input" v-model="formData.terms.projectDuration" placeholder="输入项目周期" />
          <text class="form-hint">天</text>
        </view>
        
        <view class="form-group">
          <text class="form-label">质保期</text>
          <input type="number" class="form-input" v-model="formData.terms.warrantyPeriod" placeholder="输入质保期" />
          <text class="form-hint">月</text>
        </view>
        
        <view class="form-group">
          <text class="form-label">详细条款</text>
          <textarea class="form-textarea" v-model="formData.terms.content" placeholder="输入详细条款"></textarea>
        </view>
      </view>
      
      <!-- 相关文档部分 -->
      <view class="form-section">
        <view class="section-title">相关文档</view>
        
        <view class="file-item" v-for="(doc, index) in formData.documents" :key="index">
          <text class="ri-file-pdf-line"></text>
          <text>{{doc.name}}</text>
          <view class="file-action-button delete-button" @click="removeDocument(index)">
            <view class="delete-icon">×</view>
          </view>
        </view>
        
        <button type="button" class="add-button" @click="uploadDocument">
          <text class="ri-add-line"></text>
          上传文档
        </button>
      </view>
    </scroll-view>

    <view class="action-bar">
      <button type="button" class="btn btn-outline" @click="cancel">
        取消
      </button>
      <button type="button" class="btn btn-primary" @click="saveContract">
        保存
      </button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        title: '企业系统集成项目合同',
        code: 'CT-2023-09-001',
        quotation: { id: 'QT-2023-09-005', name: 'QT-2023-09-005 (企业系统集成项目)' },
        signingDate: '2023-10-25',
        expiryDate: '2024-10-24',
        contractType: { id: 'system-integration', name: '系统集成' },
        owner: { id: '2', name: '王销售' },
        status: { code: 'signed', name: '已签署' },
        customer: {
          id: '1',
          name: '上海智能科技',
          industry: '科技行业',
          size: '大型企业'
        },
        contact: { id: '1', name: '张总监', title: '技术总监' },
        items: [
          { name: '系统集成', amount: '320000' },
          { name: '定制开发', amount: '150000' },
          { name: '培训与支持', amount: '45000' }
        ],
        taxRate: 13,
        tax: 66950,
        totalAmount: 581950,
        paymentPlan: [
          {
            amount: '290975',
            percentage: 50,
            date: '2023-10-25',
            status: 'paid',
            description: '合同签署后7天内支付'
          },
          {
            amount: '174585',
            percentage: 30,
            date: '2023-12-25',
            status: 'pending',
            description: '项目交付后7天内支付'
          },
          {
            amount: '116390',
            percentage: 20,
            date: '2024-01-25',
            status: 'pending',
            description: '项目验收通过后7天内支付'
          }
        ],
        terms: {
          projectDuration: '90',
          warrantyPeriod: '12',
          content: `1. 项目范围
   - 系统集成服务包括硬件部署、软件安装与配置、系统联调
   - 定制开发服务包括需求分析、设计、开发、测试和部署
   - 培训与支持服务包括用户培训、管理员培训和远程技术支持

2. 交付时间与验收
   - 项目预计于签约后90天内完成交付
   - 客户有10个工作日进行验收测试
   - 验收标准详见附件1《验收标准说明》

3. 保密条款
   - 双方对项目过程中获知的对方商业秘密负有保密义务
   - 未经授权不得向第三方透露项目相关信息

4. 知识产权
   - 定制开发部分的知识产权归客户所有
   - 供应商预先存在的技术和工具的知识产权仍归供应商所有

5. 违约责任
   - 任何一方违约，应承担由此给对方造成的损失

6. 不可抗力
   - 因不可抗力导致合同无法履行的，双方可协商解除合同`
        },
        documents: [
          { id: '1', name: '企业系统集成项目合同.pdf', url: '' },
          { id: '2', name: '技术方案附件.pdf', url: '' }
        ]
      },
      
      // 选项数据
      quotations: [
        { id: '', name: '无关联报价单' },
        { id: 'QT-2023-09-005', name: 'QT-2023-09-005 (企业系统集成项目)' },
        { id: 'QT-2023-10-001', name: 'QT-2023-10-001 (云数据分析平台解决方案)' }
      ],
      contractTypes: [
        { id: '', name: '请选择合同类型' },
        { id: 'system-integration', name: '系统集成' },
        { id: 'product-sales', name: '产品销售' },
        { id: 'service', name: '服务协议' },
        { id: 'maintenance', name: '维护合同' },
        { id: 'custom', name: '自定义' }
      ],
      owners: [
        { id: '', name: '请选择负责人' },
        { id: '1', name: '李销售' },
        { id: '2', name: '王销售' },
        { id: '3', name: '张总监' }
      ],
      statuses: [
        { code: 'draft', name: '草稿' },
        { code: 'review', name: '审批中' },
        { code: 'signed', name: '已签署' },
        { code: 'completed', name: '已履行' },
        { code: 'terminated', name: '已终止' }
      ],
      contacts: [
        { id: '', name: '请选择联系人', title: '' },
        { id: '1', name: '张总监', title: '技术总监' },
        { id: '2', name: '李经理', title: '采购经理' },
        { id: '3', name: '王总', title: '总经理' }
      ],
      paymentStatuses: [
        { code: 'pending', name: '待付款' },
        { code: 'paid', name: '已付款' },
        { code: 'overdue', name: '逾期' }
      ],
      
      // 索引值
      quotationIndex: 1,
      contractTypeIndex: 1,
      ownerIndex: 2,
      statusIndex: 2,
      contactIndex: 1
    }
  },
  
  onLoad(options) {
    // 如果有传递合同ID，则加载合同数据
    const contractId = options.id;
    if (contractId) {
      // 这里应该调用API加载合同数据
      // 现在使用模拟数据
    }
  },
  
  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    // 保存合同
    saveContract() {
      // 验证表单
      if (!this.validateForm()) {
        return;
      }
      
      // 显示加载提示
      uni.showLoading({
        title: '保存中...'
      });
      
      // 模拟API调用
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '合同已保存',
          icon: 'success'
        });
        
        // 返回详情页
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }, 1000);
    },
    
    // 取消编辑
    cancel() {
      uni.showModal({
        title: '提示',
        content: '确定要取消编辑吗？未保存的内容将丢失',
        success: (res) => {
          if (res.confirm) {
            uni.navigateBack();
          }
        }
      });
    },
    
    // 表单验证
    validateForm() {
      if (!this.formData.title) {
        uni.showToast({
          title: '请输入合同名称',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.formData.customer) {
        uni.showToast({
          title: '请选择客户',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.formData.signingDate) {
        uni.showToast({
          title: '请选择签署日期',
          icon: 'none'
        });
        return false;
      }
      
      return true;
    },
    
    // 格式化数字
    formatNumber(num) {
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    
    // 计算总金额
    calculateTotal() {
      let subtotal = 0;
      this.formData.items.forEach(item => {
        subtotal += parseFloat(item.amount || 0);
      });
      
      // 计算税额
      this.formData.tax = subtotal * (this.formData.taxRate / 100);
      // 计算总金额
      this.formData.totalAmount = subtotal + this.formData.tax;
      
      // 更新付款计划百分比
      this.updatePaymentPercentages();
    },
    
    // 更新所有付款计划的百分比
    updatePaymentPercentages() {
      const total = this.formData.totalAmount;
      if (total > 0) {
        this.formData.paymentPlan.forEach(payment => {
          payment.percentage = Math.round((parseFloat(payment.amount) / total) * 100);
        });
      }
    },
    
    // 计算单个付款计划的百分比
    calculatePercentage(index) {
      const payment = this.formData.paymentPlan[index];
      const amount = parseFloat(payment.amount || 0);
      const total = this.formData.totalAmount;
      
      if (total > 0) {
        payment.percentage = Math.round((amount / total) * 100);
      } else {
        payment.percentage = 0;
      }
    },
    
    // 添加付款计划
    addPaymentPlan() {
      this.formData.paymentPlan.push({
        amount: '0',
        percentage: 0,
        date: '',
        status: 'pending',
        description: ''
      });
    },
    
    // 删除付款计划
    removePaymentPlan(index) {
      uni.showModal({
        title: '提示',
        content: '确定要删除此付款计划吗？',
        success: (res) => {
          if (res.confirm) {
            this.formData.paymentPlan.splice(index, 1);
            this.updatePaymentPercentages();
          }
        }
      });
    },
    
    // 获取付款状态名称
    getPaymentStatusName(statusCode) {
      const status = this.paymentStatuses.find(s => s.code === statusCode);
      return status ? status.name : '待付款';
    },
    
    // 获取付款状态索引
    getPaymentStatusIndex(statusCode) {
      return this.paymentStatuses.findIndex(s => s.code === statusCode);
    },
    
    // 选择客户
    selectCustomer() {
      uni.navigateTo({
        url: '/pages/customers/customer-select',
        events: {
          // 选择客户后的回调
          customerSelected: (customer) => {
            this.formData.customer = customer;
            this.formData.contact = null; // 清空联系人，因为联系人与客户关联
          }
        }
      });
    },
    
    // 上传文档
    uploadDocument() {
      uni.showToast({
        title: '上传文档功能开发中...',
        icon: 'none'
      });
    },
    
    // 删除文档
    removeDocument(index) {
      uni.showModal({
        title: '提示',
        content: '确定要删除此文档吗？',
        success: (res) => {
          if (res.confirm) {
            this.formData.documents.splice(index, 1);
          }
        }
      });
    },
    
    // 选择器事件处理
    onQuotationChange(e) {
      const index = e.detail.value;
      this.quotationIndex = index;
      this.formData.quotation = this.quotations[index];
    },
    
    onSigningDateChange(e) {
      this.formData.signingDate = e.detail.value;
    },
    
    onExpiryDateChange(e) {
      this.formData.expiryDate = e.detail.value;
    },
    
    onContractTypeChange(e) {
      const index = e.detail.value;
      this.contractTypeIndex = index;
      this.formData.contractType = this.contractTypes[index];
    },
    
    onOwnerChange(e) {
      const index = e.detail.value;
      this.ownerIndex = index;
      this.formData.owner = this.owners[index];
    },
    
    onStatusChange(e) {
      const index = e.detail.value;
      this.statusIndex = index;
      this.formData.status = this.statuses[index];
    },
    
    onContactChange(e) {
      const index = e.detail.value;
      this.contactIndex = index;
      this.formData.contact = this.contacts[index];
    },
    
    onPaymentDateChange(e, index) {
      this.formData.paymentPlan[index].date = e.detail.value;
    },
    
    onPaymentStatusChange(e, paymentIndex) {
      const index = e.detail.value;
      this.formData.paymentPlan[paymentIndex].status = this.paymentStatuses[index].code;
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  background-color: #fff;
  z-index: 10;
}

.page-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.back-button {
  color: #666;
  display: flex;
  align-items: center;
}

.header-actions button {
  color: #3a86ff;
  font-weight: 500;
  background: none;
  border: none;
  font-size: 16px;
  padding: 0;
}

.form-container {
  flex: 1;
  padding: 12px;
  margin-bottom: 100px;
}

.form-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid #eee;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-group {
  margin-bottom: 12px;
}

.form-label {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.form-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  color: #333;
  background-color: #fff;
  box-sizing: border-box;
  height: 44px;
  line-height: 24px;
}

.form-hint {
  font-size: 12px;
  color: #999;
  margin-left: 4px;
}

.picker-view {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  color: #333;
  background-color: #fff;
  font-size: 14px;
  height: 44px;
  box-sizing: border-box;
}

.placeholder {
  color: #999;
}

.form-date {
  position: relative;
}

.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  color: #333;
  background-color: #fff;
  min-height: 100px;
  box-sizing: border-box;
  line-height: 24px;
}

/* 客户选择器样式 */
.customer-selector {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 12px;
  border: 1px solid #eee;
  min-height: 64px;
  box-sizing: border-box;
}

.customer-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #e0f0ff;
  color: #3a86ff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

.customer-info {
  flex: 1;
}

.customer-name {
  font-size: 15px;
  font-weight: 500;
  color: #333;
}

.customer-meta {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.customer-action {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.customer-select-button {
  color: #3a86ff;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 添加按钮 */
.add-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 12px;
  border: 1px dashed #ddd;
  border-radius: 8px;
  color: #3a86ff;
  background-color: #fff;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 12px;
  height: 44px;
  box-sizing: border-box;
}

/* 摘要部分 */
.summary-table {
  width: 100%;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
}

.summary-table .label {
  font-size: 14px;
  color: #666;
  text-align: left;
}

.summary-table .value {
  font-size: 14px;
  color: #333;
  text-align: right;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.summary-table input {
  text-align: right;
  border: 1px solid transparent;
  background-color: transparent;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  font-weight: 500;
  width: 120px;
  height: 36px;
  line-height: 24px;
  box-sizing: border-box;
}

.summary-table input:focus {
  border-color: #ddd;
  background-color: #fff;
  outline: none;
}

.total-row {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #eee;
  margin-top: 8px;
  padding-top: 8px;
}

.total-row .label {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.total-row .value {
  font-size: 18px;
  font-weight: 600;
  color: #3a86ff;
}

/* 付款计划样式 */
.payment-plan-item {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid #eee;
  box-sizing: border-box;
}

.payment-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.payment-item-title {
  font-weight: 500;
  color: #333;
}

.payment-item-actions {
  display: flex;
  gap: 4px;
}

.payment-action-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 1px solid #eee;
  background-color: #fff;
}

.delete-icon {
  color: #ff4d4f;
  font-size: 28px;
  font-weight: bold;
  line-height: 28px;
}

.payment-item-body {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

/* 文件项样式 */
.file-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 12px;
  height: 48px;
  box-sizing: border-box;
}

.file-item text:first-child {
  margin-right: 8px;
  font-size: 20px;
  color: #ff5a5f;
}

.file-item text:nth-child(2) {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.file-action-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 1px solid #eee;
  background-color: #fff;
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 12px;
  display: flex;
  justify-content: space-between;
  gap: 12px;
  border-top: 1px solid #eee;
  z-index: 100;
}

.action-bar .btn {
  flex: 1;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

.btn-primary {
  background-color: #3a86ff;
  color: #fff;
  border: none;
}

.btn-outline {
  background-color: #fff;
  color: #333;
  border: 1px solid #ddd;
}

/* 添加删除按钮样式 */
.delete-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 1px solid #ff4d4f;
  background-color: #fff0f0;
}

.delete-icon {
  color: #ff4d4f;
  font-size: 28px;
  font-weight: bold;
  line-height: 28px;
}

.delete-button:active {
  background-color: #ffccc7;
}
</style> 
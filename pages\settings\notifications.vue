<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="flex-row items-center gap-sm">
        <text class="page-title">通知设置</text>
      </view>
    </view>
    
    <scroll-view scroll-y class="page-content">
      <!-- 通知开关总控 -->
      <view class="settings-card">
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-title">推送通知</text>
            <text class="setting-desc">开启后接收应用的推送通知</text>
          </view>
          <switch :checked="masterSwitch" @change="toggleMasterSwitch" color="#4a6fff" />
        </view>
      </view>
      
      <!-- 通知类型组 -->
      <text class="section-title">销售相关</text>
      <view class="settings-card">
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-title">商机进展提醒</text>
            <text class="setting-desc">商机阶段变更时通知我</text>
          </view>
          <switch :checked="settings.salesOpportunities" :disabled="!masterSwitch" @change="toggleSetting('salesOpportunities', $event)" color="#4a6fff" />
        </view>
        
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-title">销售目标提醒</text>
            <text class="setting-desc">接近或达成销售目标时通知我</text>
          </view>
          <switch :checked="settings.salesGoals" :disabled="!masterSwitch" @change="toggleSetting('salesGoals', $event)" color="#4a6fff" />
        </view>
        
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-title">新分配商机提醒</text>
            <text class="setting-desc">有新商机分配给我时通知我</text>
          </view>
          <switch :checked="settings.newOpportunities" :disabled="!masterSwitch" @change="toggleSetting('newOpportunities', $event)" color="#4a6fff" />
        </view>
      </view>
      
      <text class="section-title">客户相关</text>
      <view class="settings-card">
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-title">客户生日提醒</text>
            <text class="setting-desc">客户生日当天提前通知我</text>
          </view>
          <switch :checked="settings.customerBirthday" :disabled="!masterSwitch" @change="toggleSetting('customerBirthday', $event)" color="#4a6fff" />
        </view>
        
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-title">客户跟进提醒</text>
            <text class="setting-desc">提醒我定期跟进客户</text>
          </view>
          <switch :checked="settings.customerFollowUp" :disabled="!masterSwitch" @change="toggleSetting('customerFollowUp', $event)" color="#4a6fff" />
        </view>
        
        <view class="setting-item setting-with-options" :class="{'disabled': !masterSwitch || !settings.customerFollowUp}">
          <view class="setting-info">
            <text class="setting-title">跟进频率设置</text>
          </view>
          <picker :range="followUpTimeOptions" :value="followUpTimeIndex" :disabled="!masterSwitch || !settings.customerFollowUp" @change="onFollowUpTimeChange">
            <view class="picker-text">
              <text>{{followUpTimeOptions[followUpTimeIndex]}}</text>
              <svg-icon name="arrow-right" type="svg" size="24" color="#4a6fff"></svg-icon>
            </view>
          </picker>
        </view>
      </view>
      
      <text class="section-title">任务相关</text>
      <view class="settings-card">
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-title">任务到期提醒</text>
            <text class="setting-desc">任务临近截止时通知我</text>
          </view>
          <switch :checked="settings.taskDeadline" :disabled="!masterSwitch" @change="toggleSetting('taskDeadline', $event)" color="#4a6fff" />
        </view>
        
        <view class="setting-item setting-with-options" :class="{'disabled': !masterSwitch || !settings.taskDeadline}">
          <view class="setting-info">
            <text class="setting-title">提前提醒时间</text>
          </view>
          <picker :range="reminderTimeOptions" :value="reminderTimeIndex" :disabled="!masterSwitch || !settings.taskDeadline" @change="onReminderTimeChange">
            <view class="picker-text">
              <text>{{reminderTimeOptions[reminderTimeIndex]}}</text>
              <svg-icon name="arrow-right" type="svg" size="24" color="#4a6fff"></svg-icon>
            </view>
          </picker>
        </view>
        
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-title">任务指派提醒</text>
            <text class="setting-desc">有新任务指派给我时通知我</text>
          </view>
          <switch :checked="settings.taskAssignment" :disabled="!masterSwitch" @change="toggleSetting('taskAssignment', $event)" color="#4a6fff" />
        </view>
        
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-title">任务完成提醒</text>
            <text class="setting-desc">我创建的任务被完成时通知我</text>
          </view>
          <switch :checked="settings.taskCompletion" :disabled="!masterSwitch" @change="toggleSetting('taskCompletion', $event)" color="#4a6fff" />
        </view>
      </view>
      
      <text class="section-title">系统与安全</text>
      <view class="settings-card">
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-title">账号安全提醒</text>
            <text class="setting-desc">账号异常登录时通知我</text>
          </view>
          <switch :checked="settings.securityAlerts" @change="toggleSetting('securityAlerts', $event)" color="#4a6fff" />
        </view>
        
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-title">系统公告</text>
            <text class="setting-desc">接收系统更新、维护等公告</text>
          </view>
          <switch :checked="settings.systemAnnouncements" :disabled="!masterSwitch" @change="toggleSetting('systemAnnouncements', $event)" color="#4a6fff" />
        </view>
      </view>
      
      <text class="section-title">邮件订阅</text>
      <view class="settings-card">
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-title">每日工作摘要</text>
            <text class="setting-desc">每天发送工作完成情况摘要</text>
          </view>
          <switch :checked="settings.dailyDigest" :disabled="!masterSwitch" @change="toggleSetting('dailyDigest', $event)" color="#4a6fff" />
        </view>
        
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-title">周报</text>
            <text class="setting-desc">每周发送工作总结和下周计划</text>
          </view>
          <switch :checked="settings.weeklyReport" :disabled="!masterSwitch" @change="toggleSetting('weeklyReport', $event)" color="#4a6fff" />
        </view>
        
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-title">业绩提醒</text>
            <text class="setting-desc">定期发送业绩报告和提醒</text>
          </view>
          <switch :checked="settings.performanceAlerts" :disabled="!masterSwitch" @change="toggleSetting('performanceAlerts', $event)" color="#4a6fff" />
        </view>
      </view>
      
      <!-- 保存按钮 -->
      <button class="save-btn" @tap="saveSettings">保存设置</button>
      
      <!-- 底部空间 -->
      <view class="bottom-space"></view>
    </scroll-view>
    
    <!-- 自定义TabBar组件 -->
    <custom-tab-bar></custom-tab-bar>
  </view>
</template>

<script>
import SvgIcon from '@/components/svg-icon.vue';
import CustomTabBar from '@/components/CustomTabBar.vue';

export default {
  components: {
    SvgIcon,
    CustomTabBar
  },
  data() {
    return {
      masterSwitch: true,
      settings: {
        // 销售相关
        salesOpportunities: true,
        salesGoals: true,
        newOpportunities: true,
        
        // 客户相关
        customerBirthday: true,
        customerFollowUp: true,
        
        // 任务相关
        taskDeadline: true,
        taskAssignment: true,
        taskCompletion: false,
        
        // 系统与安全
        securityAlerts: true, // 安全提醒始终开启
        systemAnnouncements: true,
        
        // 邮件订阅
        dailyDigest: false,
        weeklyReport: true,
        performanceAlerts: true
      },
      
      // 提醒时间选项
      reminderTimeOptions: ['提前1小时', '提前3小时', '提前6小时', '提前12小时', '提前1天', '提前2天'],
      reminderTimeIndex: 4, // 默认提前1天
      
      // 跟进频率选项
      followUpTimeOptions: ['每周', '每两周', '每月', '每季度', '每半年'],
      followUpTimeIndex: 2 // 默认每月
    }
  },
  onLoad() {
    // 加载用户已保存的通知设置
    this.loadSettings();
  },
  onShow() {
    // 设置TabBar选中项
    if (typeof this.$refs.customTabBar !== 'undefined') {
      this.$refs.customTabBar.current = 4;
    }
  },
  methods: {
    loadSettings() {
      // 这里可以替换为实际的API调用来获取用户设置
      uni.showLoading({
        title: '加载设置...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        // 模拟从服务器获取设置
        console.log('设置已加载');
      }, 500);
    },
    
    toggleMasterSwitch(e) {
      this.masterSwitch = e.detail.value;
      
      // 如果总开关关闭，显示确认对话框
      if (!this.masterSwitch) {
        uni.showModal({
          title: '关闭所有通知',
          content: '关闭后将不会收到任何应用内推送通知，确定要关闭吗？',
          success: (res) => {
            if (res.cancel) {
              // 用户取消了，恢复开关状态
              this.masterSwitch = true;
            }
          }
        });
      }
    },
    
    toggleSetting(key, e) {
      // 如果是安全提醒且尝试关闭
      if (key === 'securityAlerts' && !e.detail.value) {
        uni.showModal({
          title: '安全提醒',
          content: '为了您的账号安全，安全提醒不建议关闭。确定要关闭吗？',
          success: (res) => {
            if (res.confirm) {
              this.settings[key] = e.detail.value;
            }
          }
        });
      } else {
        this.settings[key] = e.detail.value;
      }
    },
    
    onReminderTimeChange(e) {
      this.reminderTimeIndex = e.detail.value;
    },
    
    onFollowUpTimeChange(e) {
      this.followUpTimeIndex = e.detail.value;
    },
    
    saveSettings() {
      // 这里可以替换为实际的API调用来保存用户设置
      uni.showLoading({
        title: '保存中...'
      });
      
      // 构建要保存的设置对象
      const settingsToSave = {
        masterSwitch: this.masterSwitch,
        settings: this.settings,
        reminderTimeOption: this.reminderTimeOptions[this.reminderTimeIndex],
        followUpTimeOption: this.followUpTimeOptions[this.followUpTimeIndex]
      };
      
      console.log('保存设置:', settingsToSave);
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '设置已保存',
          icon: 'success'
        });
      }, 1000);
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
  position: relative;
}

.page-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e0e0e0;
  position: relative;
  z-index: 10;
  width: 100%;
  box-sizing: border-box;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.gap-sm {
  gap: 20rpx;
}

.page-content {
  flex: 1;
  padding: 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.section-title {
  font-size: 28rpx;
  color: #666666;
  margin: 30rpx 0 15rpx 10rpx;
  display: block;
  width: 100%;
  box-sizing: border-box;
}

.settings-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #e0e0e0;
  margin-bottom: 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.setting-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 20rpx;
  border-bottom: 1rpx solid #e0e0e0;
  width: 100%;
  box-sizing: border-box;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-with-options {
  padding-top: 20rpx;
  padding-bottom: 20rpx;
  background-color: #f9fafb;
}

.setting-with-options.disabled {
  opacity: 0.6;
}

.setting-info {
  flex: 1;
  margin-right: 20rpx;
  min-width: 0;
  overflow: hidden;
}

.setting-title {
  font-size: 30rpx;
  color: #333333;
  margin-bottom: 8rpx;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
}

.setting-desc {
  font-size: 24rpx;
  color: #666666;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
}

.picker-text {
  display: flex;
  align-items: center;
  color: #4a6fff;
  font-size: 28rpx;
  min-width: 120rpx;
  flex-shrink: 0;
}

.picker-text svg-icon {
  margin-left: 10rpx;
  flex-shrink: 0;
}

.save-btn {
  background-color: #4a6fff;
  color: #ffffff;
  border-radius: 8rpx;
  margin: 40rpx 0 40rpx;
  padding: 24rpx 0;
  font-size: 32rpx;
  width: 100%;
  box-sizing: border-box;
}

.bottom-space {
  height: 120rpx;
  width: 100%;
}
</style> 
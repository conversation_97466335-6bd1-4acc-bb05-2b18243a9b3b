(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-dashboard-main-dashboard"],{"086d":function(t,e,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("c223"),a("4626");var r=n(a("2634")),o=n(a("2fdc")),i=n(a("eab4")),s=a("a9a0"),c={components:{CustomTabBar:i.default},data:function(){return{todayDate:"2024年3月21日 星期四",userName:"",salePercentData:{percent:0,text:"0%",color:"#999"},followedBusiness:0,lastMonthNewCustomers:0,lastMonthSales:0,thisMonthNewCustomers:0,thisMonthSales:0,todoTasks:0,features:[{name:"线索管理",iconName:"leads",iconType:"svg",url:"/pages/marketing/leads"},{name:"客户管理",iconName:"customer",iconType:"svg",url:"/pages/customers/customer-list"},{name:"商机管理",iconName:"opportunity",iconType:"svg",url:"/pages/sales/opportunity-list"}],activities:[{iconName:"user-add",iconType:"svg",title:"新增客户：上海科技有限公司",desc:"客户经理：李经理",time:"10分钟前"},{iconName:"line-chart",iconType:"svg",title:"商机更新：智能家居项目",desc:"金额：¥500,000",time:"30分钟前"},{iconName:"message",iconType:"svg",title:"新增沟通记录",desc:"客户：北京科技有限公司",time:"1小时前"},{iconName:"task",iconType:"svg",title:"任务完成：产品演示",desc:"负责人：王经理",time:"2小时前"}]}},onLoad:function(){var t=this;return(0,o.default)((0,r.default)().mark((function e(){return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.updateTodayDate();case 2:return e.next=4,t.loadDashboardData();case 4:return e.next=6,t.getUserInfo();case 6:case"end":return e.stop()}}),e)})))()},onShow:function(){var t=this;"undefined"!==typeof this.$refs.customTabBar?this.$refs.customTabBar.current=0:setTimeout((function(){"undefined"!==typeof t.$refs.customTabBar&&(t.$refs.customTabBar.current=3,console.log("线索页面设置TabBar当前项为0"))}),300)},methods:{updateTodayDate:function(){var t=new Date,e=t.getFullYear(),a=t.getMonth()+1,n=t.getDate(),r=["日","一","二","三","四","五","六"][t.getDay()];this.todayDate="".concat(e,"年").concat(a,"月").concat(n,"日 星期").concat(r)},getUserInfo:function(){var t=uni.getStorageSync("userInfo");this.userName=t.name},loadDashboardData:function(){var t=this;return(0,o.default)((0,r.default)().mark((function e(){var a;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,s.getHomeData)();case 2:a=e.sent,t.followedBusiness=a.followedBusiness,t.lastMonthNewCustomers=a.lastMonthNewCustomers,t.lastMonthSales=a.lastMonthSales,t.thisMonthNewCustomers=a.thisMonthNewCustomers,t.thisMonthSales=a.thisMonthSales,t.todoTasks=a.todoTasks;case 9:case"end":return e.stop()}}),e)})))()},viewAllActivities:function(){uni.showToast({title:"查看全部动态功能开发中...",icon:"none"})},handleRoute:function(t){["/pages/marketing/leads","/pages/interactions/interaction-list","/pages/sales/quotation-list"].includes(t)?uni.navigateTo({url:t}):uni.switchTab({url:t})}},computed:{getSalesTrend:function(){return this.lastMonthSales?(this.thisMonthSales-this.lastMonthSales)/this.lastMonthSales*100:0},getSalesTrendText:function(){return"".concat((this.getSalesTrend>0?this.getSalesTrend:-this.getSalesTrend).toFixed(2),"%")},getNewCustomersTrend:function(){return this.lastMonthNewCustomers?(this.thisMonthNewCustomers-this.lastMonthNewCustomers)/this.lastMonthNewCustomers*100:0},getNewCustomersTrendText:function(){return"".concat((this.getNewCustomersTrend>0?this.getNewCustomersTrend:-this.getNewCustomersTrend).toFixed(2),"%")},updatedStats:function(){return{1:{title:"本月销售额",value:this.thisMonthSales,trend:this.getSalesTrend,text:this.getSalesTrendText},2:{title:"新增客户",value:this.thisMonthNewCustomers,trend:this.getNewCustomersTrend,text:this.getNewCustomersTrendText},3:{title:"待处理业务",value:this.todoTasks},4:{title:"跟进商机",value:this.followedBusiness}}}}};e.default=c},1880:function(t,e,a){"use strict";a.r(e);var n=a("1a37"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},"1a37":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4626"),a("5ac7"),a("bf0f"),a("bd06");var n={name:"CustomTabBar",data:function(){return{current:0,color:"#333333",activeColor:"#007AFF",showMoreMenu:!1,tabList:[{pagePath:"/pages/dashboard/main-dashboard",text:"首页",iconPath:"dashboard",selectedIconPath:"dashboard"},{pagePath:"/pages/customers/customer-list",text:"客户",iconPath:"customer",selectedIconPath:"customer"},{pagePath:"/pages/sales/opportunity-list",text:"销售",iconPath:"sales",selectedIconPath:"sales"},{type:"more",text:"更多",iconPath:"more",selectedIconPath:"more"},{pagePath:"/pages/settings/profile",text:"我的",iconPath:"user",selectedIconPath:"user"}],moreMenuList:[{pagePath:"/pages/marketing/leads",text:"线索",iconPath:"lead"},{pagePath:"/pages/interactions/interaction-list",text:"沟通",iconPath:"communication"},{pagePath:"/pages/sales/quotation-list",text:"报价",iconPath:"quotation"},{pagePath:"/pages/contracts/contract-list",text:"合同",iconPath:"contract"},{pagePath:"/pages/contracts/invoice-list",text:"发票",iconPath:"file-text"},{pagePath:"/pages/contracts/payment-list",text:"收款",iconPath:"money"},{pagePath:"/pages/reports/report-list",text:"报表",iconPath:"report"}]}},created:function(){this.updateCurrentTab()},onLoad:function(){this.updateCurrentTab()},onShow:function(){var t=this;setTimeout((function(){t.updateCurrentTab()}),100)},methods:{updateCurrentTab:function(){try{var t=getCurrentPages(),e=t[t.length-1];if(!e||!e.route)return;var a=e.route;console.log("当前路由:",a),a.includes("/pages/dashboard/")?this.current=0:a.includes("/pages/customers/")?this.current=1:a.includes("/pages/sales/")?this.current=2:a.includes("/pages/actions/")?this.current=3:a.includes("/pages/settings/")&&(this.current=5)}catch(n){console.error("更新Tab出错:",n)}},handleTabClick:function(t,e){"more"===t.type?(this.toggleMoreMenu(),this.current=e):this.switchTab(t.pagePath,e)},switchTab:function(t,e){this.current!==e&&(this.current=e,uni.switchTab({url:t}))},toggleMoreMenu:function(){this.showMoreMenu=!this.showMoreMenu},closeMoreMenu:function(){this.showMoreMenu=!1},navigateToPage:function(t){var e=this.tabList.some((function(e){return e.pagePath===t}));if(e){uni.switchTab({url:t});var a=this.tabList.findIndex((function(e){return e.pagePath===t}));-1!==a&&(this.current=a)}else uni.navigateTo({url:t});this.closeMoreMenu()}},watch:{$route:{handler:function(){this.updateCurrentTab()},immediate:!0}}};e.default=n},2634:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
e.default=function(){return t};var t={},a=Object.prototype,r=a.hasOwnProperty,o=Object.defineProperty||function(t,e,a){t[e]=a.value},i="function"==typeof Symbol?Symbol:{},s=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function l(t,e,a){return Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(S){l=function(t,e,a){return t[e]=a}}function d(t,e,a,n){var r=e&&e.prototype instanceof p?e:p,i=Object.create(r.prototype),s=new P(n||[]);return o(i,"_invoke",{value:k(t,a,s)}),i}function f(t,e,a){try{return{type:"normal",arg:t.call(e,a)}}catch(S){return{type:"throw",arg:S}}}t.wrap=d;var h={};function p(){}function v(){}function m(){}var g={};l(g,s,(function(){return this}));var b=Object.getPrototypeOf,x=b&&b(b(N([])));x&&x!==a&&r.call(x,s)&&(g=x);var y=m.prototype=p.prototype=Object.create(g);function w(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function T(t,e){var a;o(this,"_invoke",{value:function(o,i){function s(){return new e((function(a,s){(function a(o,i,s,c){var u=f(t[o],t,i);if("throw"!==u.type){var l=u.arg,d=l.value;return d&&"object"==(0,n.default)(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){a("next",t,s,c)}),(function(t){a("throw",t,s,c)})):e.resolve(d).then((function(t){l.value=t,s(l)}),(function(t){return a("throw",t,s,c)}))}c(u.arg)})(o,i,a,s)}))}return a=a?a.then(s,s):s()}})}function k(t,e,a){var n="suspendedStart";return function(r,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===r)throw o;return L()}for(a.method=r,a.arg=o;;){var i=a.delegate;if(i){var s=_(i,a);if(s){if(s===h)continue;return s}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if("suspendedStart"===n)throw n="completed",a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);n="executing";var c=f(t,e,a);if("normal"===c.type){if(n=a.done?"completed":"suspendedYield",c.arg===h)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(n="completed",a.method="throw",a.arg=c.arg)}}}function _(t,e){var a=e.method,n=t.iterator[a];if(void 0===n)return e.delegate=null,"throw"===a&&t.iterator["return"]&&(e.method="return",e.arg=void 0,_(t,e),"throw"===e.method)||"return"!==a&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+a+"' method")),h;var r=f(n,t.iterator,e.arg);if("throw"===r.type)return e.method="throw",e.arg=r.arg,e.delegate=null,h;var o=r.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,h):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function M(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(M,this),this.reset(!0)}function N(t){if(t){var e=t[s];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,n=function e(){for(;++a<t.length;)if(r.call(t,a))return e.value=t[a],e.done=!1,e;return e.value=void 0,e.done=!0,e};return n.next=n}}return{next:L}}function L(){return{value:void 0,done:!0}}return v.prototype=m,o(y,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:v,configurable:!0}),v.displayName=l(m,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,l(t,u,"GeneratorFunction")),t.prototype=Object.create(y),t},t.awrap=function(t){return{__await:t}},w(T.prototype),l(T.prototype,c,(function(){return this})),t.AsyncIterator=T,t.async=function(e,a,n,r,o){void 0===o&&(o=Promise);var i=new T(d(e,a,n,r),o);return t.isGeneratorFunction(a)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},w(y),l(y,u,"Generator"),l(y,s,(function(){return this})),l(y,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),a=[];for(var n in e)a.push(n);return a.reverse(),function t(){for(;a.length;){var n=a.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=N,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function a(a,n){return i.type="throw",i.arg=t,e.next=a,n&&(e.method="next",e.arg=void 0),!!n}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],i=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var s=r.call(o,"catchLoc"),c=r.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var a=this.tryEntries.length-1;a>=0;--a){var n=this.tryEntries[a];if(n.tryLoc<=this.prev&&r.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,h):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.finallyLoc===t)return this.complete(a.completion,a.afterLoc),C(a),h}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.tryLoc===t){var n=a.completion;if("throw"===n.type){var r=n.arg;C(a)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,a){return this.delegate={iterator:N(t),resultName:e,nextLoc:a},"next"===this.method&&(this.arg=void 0),h}},t},a("6a54"),a("01a2"),a("e39c"),a("bf0f"),a("844d"),a("18f7"),a("de6c"),a("3872e"),a("4e9b"),a("114e"),a("c240"),a("926e"),a("7a76"),a("c9b5"),a("aa9c"),a("2797"),a("8a8d"),a("dc69"),a("f7a5");var n=function(t){return t&&t.__esModule?t:{default:t}}(a("fcf3"))},"2fdc":function(t,e,a){"use strict";function n(t,e,a,n,r,o,i){try{var s=t[o](i),c=s.value}catch(u){return void a(u)}s.done?e(c):Promise.resolve(c).then(n,r)}a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return function(){var e=this,a=arguments;return new Promise((function(r,o){var i=t.apply(e,a);function s(t){n(i,r,o,s,c,"next",t)}function c(t){n(i,r,o,s,c,"throw",t)}s(void 0)}))}},a("bf0f")},"326d":function(t,e,a){"use strict";a.r(e);var n=a("ba84"),r=a("4b92");for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);a("8d01");var i=a("828b"),s=Object(i["a"])(r["default"],n["b"],n["c"],!1,null,"16aec813",null,!1,n["a"],void 0);e["default"]=s.exports},"4b92":function(t,e,a){"use strict";a.r(e);var n=a("086d"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},7775:function(t,e,a){"use strict";a.d(e,"b",(function(){return r})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n}));var n={svgIcon:a("8a0f").default},r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"custom-tab-bar"},[t._l(t.tabList,(function(e,n){return a("v-uni-view",{key:n,staticClass:"tab-item",class:{active:t.current===n},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.handleTabClick(e,n)}}},[a("svg-icon",{attrs:{name:t.current===n?e.selectedIconPath:e.iconPath,type:"svg",size:24,color:t.current===n?t.activeColor:t.color}}),a("v-uni-text",{staticClass:"tab-text",class:{"active-text":t.current===n}},[t._v(t._s(e.text))])],1)})),t.showMoreMenu?a("v-uni-view",{staticClass:"more-menu"},[a("v-uni-view",{staticClass:"menu-overlay",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeMoreMenu.apply(void 0,arguments)}}}),a("v-uni-view",{staticClass:"menu-content"},[a("v-uni-view",{staticClass:"menu-header"},[a("v-uni-text",{staticClass:"menu-title"},[t._v("更多功能")]),a("v-uni-view",{staticClass:"menu-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeMoreMenu.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"close",type:"svg",size:32,color:"#666"}})],1)],1),a("v-uni-view",{staticClass:"menu-list"},t._l(t.moreMenuList,(function(e,n){return a("v-uni-view",{key:n,staticClass:"menu-item",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.navigateToPage(e.pagePath)}}},[a("svg-icon",{attrs:{name:e.iconPath,type:"svg",size:24,color:"#333333"}}),a("v-uni-text",{staticClass:"menu-item-text"},[t._v(t._s(e.text))])],1)})),1)],1)],1):t._e()],2)},o=[]},"8d01":function(t,e,a){"use strict";var n=a("d0ea"),r=a.n(n);r.a},a9a0:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.getHomeData=void 0;var n=a("c475");e.getHomeData=function(){return(0,n.request)({url:"/api/crm/report/getMobileHomeNumber",method:"POST"})}},ad8a:function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,".dashboard-container[data-v-16aec813]{padding:16px;padding-bottom:120px;background-color:#f5f7fa}.welcome-section[data-v-16aec813]{margin-bottom:20px}.welcome-text[data-v-16aec813]{font-size:24px;font-weight:700;color:#333;display:block;margin-bottom:4px}.date-text[data-v-16aec813]{font-size:14px;color:#666;display:block}.quick-stats[data-v-16aec813]{display:grid;grid-template-columns:repeat(2,1fr);gap:12px;margin-bottom:20px}.stat-card[data-v-16aec813]{background:#fff;border-radius:12px;padding:16px;box-shadow:0 2px 8px rgba(0,0,0,.05)}.stat-title[data-v-16aec813]{font-size:14px;color:#666;display:block;margin-bottom:6px}.stat-value[data-v-16aec813]{font-size:24px;font-weight:700;color:#333;display:block}.stat-trend[data-v-16aec813]{font-size:12px;color:#00b578;margin-top:6px;display:flex;align-items:center}.stat-trend.negative[data-v-16aec813]{color:#ff4d4f}.stat-trend uni-text[data-v-16aec813]{margin-right:4px}.feature-grid[data-v-16aec813]{display:grid;grid-template-columns:repeat(3,1fr);gap:12px;margin-bottom:20px}.feature-item[data-v-16aec813]{background:#fff;border-radius:12px;padding:16px;text-align:center;box-shadow:0 2px 8px rgba(0,0,0,.05);transition:-webkit-transform .2s;transition:transform .2s;transition:transform .2s,-webkit-transform .2s}.feature-item[data-v-16aec813]:active{-webkit-transform:scale(.98);transform:scale(.98)}.feature-icon[data-v-16aec813]{width:48px;height:48px;background:#e6f3ff;border-radius:12px;display:flex;align-items:center;justify-content:center;margin:0 auto 8px}.feature-name[data-v-16aec813]{font-size:14px;color:#333;display:block}.recent-activities[data-v-16aec813]{background:#fff;border-radius:12px;padding:16px;box-shadow:0 2px 8px rgba(0,0,0,.05)}.section-title[data-v-16aec813]{font-size:16px;font-weight:500;color:#333;margin-bottom:16px;display:flex;justify-content:space-between;align-items:center}.view-all[data-v-16aec813]{font-size:14px;color:#3a86ff}.activity-list[data-v-16aec813]{display:flex;flex-direction:column;gap:16px}.activity-item[data-v-16aec813]{display:flex;align-items:flex-start;gap:12px;padding-bottom:16px;border-bottom:1px solid #eee}.activity-item[data-v-16aec813]:last-child{padding-bottom:8px;border-bottom:none}.activity-icon[data-v-16aec813]{width:32px;height:32px;background:#e6f3ff;border-radius:8px;display:flex;align-items:center;justify-content:center;flex-shrink:0}.activity-content[data-v-16aec813]{flex:1}.activity-title[data-v-16aec813]{font-size:14px;color:#333;margin-bottom:4px;display:block}.activity-desc[data-v-16aec813]{font-size:12px;color:#666;display:block}.activity-time[data-v-16aec813]{font-size:12px;color:#999;margin-top:4px;display:block}",""]),t.exports=e},ba84:function(t,e,a){"use strict";a.d(e,"b",(function(){return r})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n}));var n={svgIcon:a("8a0f").default},r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"dashboard-container"},[a("v-uni-view",{staticClass:"welcome-section"},[a("v-uni-text",{staticClass:"welcome-text"},[t._v("您好 "+t._s(t.userName))]),a("v-uni-text",{staticClass:"date-text"},[t._v("今天是 "+t._s(t.todayDate))])],1),a("v-uni-view",{staticClass:"quick-stats"},t._l(t.updatedStats,(function(e,n){return a("v-uni-view",{key:n,staticClass:"stat-card"},[a("v-uni-text",{staticClass:"stat-title"},[t._v(t._s(e.title))]),a("v-uni-text",{staticClass:"stat-value"},[t._v(t._s(e.value))]),"3"!==n&&"4"!==n?a("v-uni-view",{class:["stat-trend",e.trend<0?"negative":""]},[a("svg-icon",{attrs:{name:e.trend>=0?"arrow-up":"arrow-down",type:"svg",size:"24"}}),a("v-uni-text",[t._v(t._s(e.text))])],1):t._e()],1)})),1),a("v-uni-view",{staticClass:"feature-grid"},t._l(t.features,(function(e,n){return a("v-uni-view",{key:n,staticClass:"feature-item",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.handleRoute(e.url)}}},[a("v-uni-view",{staticClass:"feature-icon"},[a("svg-icon",{attrs:{name:e.iconName,type:e.iconType,size:"48"}})],1),a("v-uni-text",{staticClass:"feature-name"},[t._v(t._s(e.name))])],1)})),1),a("custom-tab-bar")],1)},o=[]},bf69:function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,".custom-tab-bar[data-v-6a709636]{display:flex;justify-content:space-around;align-items:center;background-color:#fff;box-shadow:0 -1px 5px rgba(0,0,0,.1);height:%?100?%;position:fixed;bottom:0;left:0;right:0;z-index:999;padding-bottom:env(safe-area-inset-bottom)}.tab-item[data-v-6a709636]{display:flex;flex-direction:column;align-items:center;justify-content:center;flex:1;height:100%;padding:%?10?% 0}.tab-text[data-v-6a709636]{font-size:%?22?%;color:#333;margin-top:%?4?%}.active-text[data-v-6a709636]{color:#007aff}\n\n/* 更多菜单样式 */.more-menu[data-v-6a709636]{position:fixed;top:0;right:0;bottom:0;left:0;z-index:1000}.menu-overlay[data-v-6a709636]{position:absolute;top:0;right:0;bottom:0;left:0;background-color:rgba(0,0,0,.5)}.menu-content[data-v-6a709636]{position:absolute;bottom:%?100?%;left:0;right:0;background-color:#fff;border-top-left-radius:%?20?%;border-top-right-radius:%?20?%;overflow:hidden;-webkit-animation:slideUp-data-v-6a709636 .3s ease;animation:slideUp-data-v-6a709636 .3s ease;box-shadow:0 -2px 10px rgba(0,0,0,.1)}.menu-header[data-v-6a709636]{display:flex;justify-content:space-between;align-items:center;padding:%?30?%;border-bottom:1px solid #f0f0f0}.menu-title[data-v-6a709636]{font-size:%?32?%;font-weight:500;color:#333}.menu-close[data-v-6a709636]{padding:%?10?%}.menu-list[data-v-6a709636]{display:flex;flex-wrap:wrap;padding:%?20?%}.menu-item[data-v-6a709636]{width:25%;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?20?% 0}.menu-item-text[data-v-6a709636]{font-size:%?24?%;color:#333;margin-top:%?10?%;text-align:center}@-webkit-keyframes slideUp-data-v-6a709636{from{-webkit-transform:translateY(100%);transform:translateY(100%)}to{-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes slideUp-data-v-6a709636{from{-webkit-transform:translateY(100%);transform:translateY(100%)}to{-webkit-transform:translateY(0);transform:translateY(0)}}",""]),t.exports=e},c475:function(t,e,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.request=e.getTenantInfo=void 0;var r=n(a("9b1b"));a("bf0f"),a("4626"),a("5ac7");var o=null;e.getTenantInfo=function(t){return new Promise((function(e,a){uni.request({url:"/api/Tenants/getTenantsByLogin",method:"POST",data:t,header:{"Content-Type":"application/json"},success:function(t){200===t.statusCode?(o=t.data,e(t.data)):a(t.data)},fail:function(t){a(t)}})}))};e.request=function(t){return t.url.includes("/login")&&o&&(t.header=(0,r.default)((0,r.default)({},t.header),{},{__tenant:o[0].id})),new Promise((function(e,a){uni.request({url:t.url,method:t.method||"GET",data:t.data||{},header:(0,r.default)({Authorization:"Bearer "+uni.getStorageSync("token"),"Content-Type":"application/json"},t.header),success:function(t){200===t.statusCode?e(t.data):a(t.data)},fail:function(t){a(t)}})}))}},c9cf:function(t,e,a){"use strict";var n=a("e9f7"),r=a.n(n);r.a},d0ea:function(t,e,a){var n=a("ad8a");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=a("967d").default;r("98c567ce",n,!0,{sourceMap:!1,shadowMode:!1})},e9f7:function(t,e,a){var n=a("bf69");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=a("967d").default;r("9e21a296",n,!0,{sourceMap:!1,shadowMode:!1})},eab4:function(t,e,a){"use strict";a.r(e);var n=a("7775"),r=a("1880");for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);a("c9cf");var i=a("828b"),s=Object(i["a"])(r["default"],n["b"],n["c"],!1,null,"6a709636",null,!1,n["a"],void 0);e["default"]=s.exports}}]);
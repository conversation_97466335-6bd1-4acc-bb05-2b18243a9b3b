import dayjs from "dayjs";
export const DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';
export const DATE_TIME = 'YYYY-MM-DD HH:mm';
export const DATE_FORMAT = 'YYYY-MM-DD';

export function formatToDateTime(date, format = DATE_TIME_FORMAT) {
  return dayjs(date)?.format(format);
}

export function formatToDate(date, format = DATE_FORMAT) {
  if (!date) return '--'
  // 处理一些常见的特殊格式
  if (typeof date === 'string' && date.includes('T')) {
    return dayjs(new Date(date)).format(format);
  }
  return dayjs(date).format(format);
}

export function formatToTime(date, format = DATE_TIME) {
  return dayjs(date).format(format);
}
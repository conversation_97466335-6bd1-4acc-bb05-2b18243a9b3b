(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-sales-opportunity-select"],{"17c4":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.getDictionaryPageDetail=e.getDictionaryPage=void 0;var r=n("c475");e.getDictionaryPage=function(t){return(0,r.request)({url:"/api/DataDictionary/page",method:"POST",data:t})};e.getDictionaryPageDetail=function(t){return(0,r.request)({url:"/api/DataDictionary/pageDetail",method:"POST",data:t})}},"18ee":function(t,e,n){"use strict";var r=n("ca14"),a=n.n(r);a.a},2634:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
e.default=function(){return t};var t={},n=Object.prototype,a=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},c=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function f(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(j){f=function(t,e,n){return t[e]=n}}function l(t,e,n,r){var a=e&&e.prototype instanceof h?e:h,i=Object.create(a.prototype),c=new E(r||[]);return o(i,"_invoke",{value:_(t,n,c)}),i}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(j){return{type:"throw",arg:j}}}t.wrap=l;var p={};function h(){}function v(){}function g(){}var y={};f(y,c,(function(){return this}));var m=Object.getPrototypeOf,b=m&&m(m(C([])));b&&b!==n&&a.call(b,c)&&(y=b);var w=g.prototype=h.prototype=Object.create(y);function x(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function L(t,e){var n;o(this,"_invoke",{value:function(o,i){function c(){return new e((function(n,c){(function n(o,i,c,u){var s=d(t[o],t,i);if("throw"!==s.type){var f=s.arg,l=f.value;return l&&"object"==(0,r.default)(l)&&a.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,c,u)}),(function(t){n("throw",t,c,u)})):e.resolve(l).then((function(t){f.value=t,c(f)}),(function(t){return n("throw",t,c,u)}))}u(s.arg)})(o,i,n,c)}))}return n=n?n.then(c,c):c()}})}function _(t,e,n){var r="suspendedStart";return function(a,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===a)throw o;return S()}for(n.method=a,n.arg=o;;){var i=n.delegate;if(i){var c=k(i,n);if(c){if(c===p)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var u=d(t,e,n);if("normal"===u.type){if(r=n.done?"completed":"suspendedYield",u.arg===p)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(r="completed",n.method="throw",n.arg=u.arg)}}}function k(t,e){var n=e.method,r=t.iterator[n];if(void 0===r)return e.delegate=null,"throw"===n&&t.iterator["return"]&&(e.method="return",e.arg=void 0,k(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),p;var a=d(r,t.iterator,e.arg);if("throw"===a.type)return e.method="throw",e.arg=a.arg,e.delegate=null,p;var o=a.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,p):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function C(t){if(t){var e=t[c];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,r=function e(){for(;++n<t.length;)if(a.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return r.next=r}}return{next:S}}function S(){return{value:void 0,done:!0}}return v.prototype=g,o(w,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:v,configurable:!0}),v.displayName=f(g,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,f(t,s,"GeneratorFunction")),t.prototype=Object.create(w),t},t.awrap=function(t){return{__await:t}},x(L.prototype),f(L.prototype,u,(function(){return this})),t.AsyncIterator=L,t.async=function(e,n,r,a,o){void 0===o&&(o=Promise);var i=new L(l(e,n,r,a),o);return t.isGeneratorFunction(n)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},x(w),f(w,s,"Generator"),f(w,c,(function(){return this})),f(w,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},t.values=C,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(O),!t)for(var e in this)"t"===e.charAt(0)&&a.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(n,r){return i.type="throw",i.arg=t,e.next=n,r&&(e.method="next",e.arg=void 0),!!r}for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r],i=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var c=a.call(o,"catchLoc"),u=a.call(o,"finallyLoc");if(c&&u){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&a.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,p):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),O(n),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var a=r.arg;O(n)}return a}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:C(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),p}},t},n("6a54"),n("01a2"),n("e39c"),n("bf0f"),n("844d"),n("18f7"),n("de6c"),n("3872e"),n("4e9b"),n("114e"),n("c240"),n("926e"),n("7a76"),n("c9b5"),n("aa9c"),n("2797"),n("8a8d"),n("dc69"),n("f7a5");var r=function(t){return t&&t.__esModule?t:{default:t}}(n("fcf3"))},"28a6":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return r}));var r={svgIcon:n("8a0f").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"page"},[n("v-uni-view",{staticClass:"page-header"},[n("v-uni-view",{staticClass:"back-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.navBack.apply(void 0,arguments)}}},[n("svg-icon",{attrs:{name:"arrow-left",type:"svg",size:"24"}})],1),n("v-uni-text",{staticClass:"page-title"},[t._v("选择商机")])],1),n("v-uni-view",{staticClass:"search-container"},[n("v-uni-view",{staticClass:"search-box"},[n("v-uni-view",{staticClass:"search-icon"},[n("svg-icon",{attrs:{name:"search",type:"svg",size:"20"}})],1),n("v-uni-input",{staticClass:"search-input",attrs:{type:"text",placeholder:"搜索商机名称"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.onSearch.apply(void 0,arguments)}},model:{value:t.searchKeyword,callback:function(e){t.searchKeyword=e},expression:"searchKeyword"}})],1)],1),n("v-uni-scroll-view",{staticClass:"opportunity-list",style:{height:t.listHeight+"px"},attrs:{"scroll-y":!0}},t._l(t.filterBusiness,(function(e){return n("v-uni-view",{key:e.id,staticClass:"opportunity-item",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.selectOpportunity(e)}}},[n("v-uni-view",{staticClass:"opportunity-info"},[n("v-uni-text",{staticClass:"opportunity-name"},[t._v(t._s(e.name))]),n("v-uni-text",{staticClass:"opportunity-customer"})],1),n("v-uni-view",{staticClass:"opportunity-meta"},[n("v-uni-text",{staticClass:"opportunity-amount"},[t._v("￥"+t._s(e.expectedTransNoRateAmount))]),n("v-uni-text",{staticClass:"opportunity-stage",class:"stage-"+e.businessProcessName},[t._v(t._s(e.businessProcessName))])],1)],1)})),1)],1)},o=[]},"2fdc":function(t,e,n){"use strict";function r(t,e,n,r,a,o,i){try{var c=t[o](i),u=c.value}catch(s){return void n(s)}c.done?e(u):Promise.resolve(u).then(r,a)}n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return function(){var e=this,n=arguments;return new Promise((function(a,o){var i=t.apply(e,n);function c(t){r(i,a,o,c,u,"next",t)}function u(t){r(i,a,o,c,u,"throw",t)}c(void 0)}))}},n("bf0f")},"363b":function(t,e,n){"use strict";n.r(e);var r=n("8de0"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=a.a},"8de0":function(t,e,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("aa77"),n("bf0f"),n("0c26"),n("8f71"),n("4626"),n("5ac7");var a=r(n("2634")),o=r(n("2fdc")),i=r(n("8a0f")),c=n("d86f"),u=r(n("c780")),s={components:{SvgIcon:i.default},data:function(){return{searchKeyword:"",businessProcessList:[],opportunities:[],isLoading:!1,hasMore:!0,page:1,listHeight:0}},methods:{getList:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var n;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,u.default)("BusinessProcess");case 2:return t.businessProcessList=e.sent,n=t.businessProcessList.find((function(t){return"Win"===t.code})),e.next=6,(0,c.getOpportunityList)({pageIndex:1,pageSize:10,filter:{hasContract:!1,businessProcessId:n.id}}).then((function(e){t.opportunities=e}));case 6:case"end":return e.stop()}}),e)})))()},navBack:function(){uni.navigateBack()},onSearch:function(){console.log("搜索关键词:",this.searchKeyword)},selectOpportunity:function(t){var e=this.getOpenerEventChannel();e.emit("opportunitySelected",{id:t.id,name:t.name}),console.log("selectOpportunity",t),uni.navigateBack()},calculateListHeight:function(){var t=uni.getSystemInfoSync();this.listHeight=t.windowHeight-uni.upx2px(200)}},computed:{filterBusiness:function(){var t=this.searchKeyword.toLowerCase().trim(),e=this.opportunities;return t&&(e=e.filter((function(e){var n=e.name?e.name.toLowerCase():"";return!!n.includes(t)}))),e}},onLoad:function(){this.calculateListHeight(),this.getList()},onReady:function(){}};e.default=s},c475:function(t,e,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.request=e.getTenantInfo=void 0;var a=r(n("9b1b"));n("bf0f"),n("4626"),n("5ac7");var o=null;e.getTenantInfo=function(t){return new Promise((function(e,n){uni.request({url:"/api/Tenants/getTenantsByLogin",method:"POST",data:t,header:{"Content-Type":"application/json"},success:function(t){200===t.statusCode?(o=t.data,e(t.data)):n(t.data)},fail:function(t){n(t)}})}))};e.request=function(t){return t.url.includes("/login")&&o&&(t.header=(0,a.default)((0,a.default)({},t.header),{},{__tenant:o[0].id})),new Promise((function(e,n){uni.request({url:t.url,method:t.method||"GET",data:t.data||{},header:(0,a.default)({Authorization:"Bearer "+uni.getStorageSync("token"),"Content-Type":"application/json"},t.header),success:function(t){200===t.statusCode?e(t.data):n(t.data)},fail:function(t){n(t)}})}))}},c780:function(t,e,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return c.apply(this,arguments)},n("8f71"),n("bf0f");var a=r(n("2634")),o=r(n("2fdc")),i=n("17c4");function c(){return c=(0,o.default)((0,a.default)().mark((function t(e){var n,r,o,c,u,s,f,l,d=arguments;return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n=d.length>1&&void 0!==d[1]?d[1]:{},r=n.pageIndex,o=void 0===r?1:r,c=n.pageSize,u=void 0===c?100:c,t.prev=2,t.next=5,(0,i.getDictionaryPage)({pageIndex:o,pageSize:u,filter:e});case 5:if(f=t.sent,null!==f&&void 0!==f&&null!==(s=f.items)&&void 0!==s&&s.length){t.next=8;break}return t.abrupt("return",[]);case 8:return t.next=10,(0,i.getDictionaryPageDetail)({pageIndex:o,pageSize:u,dataDictionaryId:f.items[0].id});case 10:return l=t.sent,t.abrupt("return",l.items.filter((function(t){return t.isEnabled})));case 14:throw t.prev=14,t.t0=t["catch"](2),console.error("Error fetching select options:",t.t0),t.t0;case 18:case"end":return t.stop()}}),t,null,[[2,14]])}))),c.apply(this,arguments)}},ca14:function(t,e,n){var r=n("fec5");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var a=n("967d").default;a("32c6da08",r,!0,{sourceMap:!1,shadowMode:!1})},d2a7:function(t,e,n){"use strict";n.r(e);var r=n("28a6"),a=n("363b");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("18ee");var i=n("828b"),c=Object(i["a"])(a["default"],r["b"],r["c"],!1,null,"7af46120",null,!1,r["a"],void 0);e["default"]=c.exports},d86f:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.getUserList=e.getProductList=e.getOpportunityList=e.getContactList=e.getCompanyList=e.getAgreementList=void 0;var r=n("c475");e.getContactList=function(t){return(0,r.request)({url:"/api/crm/contract/getList",method:"POST",data:t})};e.getCompanyList=function(){return(0,r.request)({url:"/api/crm/contract/getAllCompanys",method:"GET"})};e.getUserList=function(t){return(0,r.request)({url:"/api/Users/<USER>",method:"POST",data:t})};e.getOpportunityList=function(t){return(0,r.request)({url:"/api/crm/business/getKanbanList",method:"POST",data:t})};e.getAgreementList=function(t){return(0,r.request)({url:"/api/crm/contract/getAgreementList",method:"POST",data:t})};e.getProductList=function(t){return(0,r.request)({url:"/api/crm/product/getList",method:"POST",data:t})}},fec5:function(t,e,n){var r=n("c86c");e=r(!1),e.push([t.i,".page[data-v-7af46120]{background-color:#f5f7fa;min-height:100vh}.page-header[data-v-7af46120]{display:flex;align-items:center;padding:%?20?% %?30?%;background-color:#fff;border-bottom:%?1?% solid #e0e0e0}.back-button[data-v-7af46120]{padding:%?10?%;margin-right:%?20?%}.page-title[data-v-7af46120]{font-size:%?32?%;font-weight:700;color:#333}.search-container[data-v-7af46120]{padding:%?20?% %?30?%;background-color:#fff;border-bottom:%?1?% solid #e0e0e0}.search-box[data-v-7af46120]{display:flex;align-items:center;background-color:#f5f7fa;border-radius:%?8?%;padding:0 %?20?%}.search-icon[data-v-7af46120]{color:#999;margin-right:%?10?%}.search-input[data-v-7af46120]{flex:1;height:%?72?%;font-size:%?28?%}.opportunity-list[data-v-7af46120]{padding:%?16?% %?20?% 0 %?20?%;width:100%;box-sizing:border-box}.opportunity-item[data-v-7af46120]{background-color:#fff;border-radius:%?12?%;padding:%?24?%;margin-bottom:%?20?%;box-shadow:0 %?2?% %?8?% rgba(0,0,0,.05);width:100%;box-sizing:border-box;overflow:hidden}.opportunity-info[data-v-7af46120],\n.opportunity-meta[data-v-7af46120]{width:100%;box-sizing:border-box}.opportunity-name[data-v-7af46120],\n.opportunity-customer[data-v-7af46120],\n.opportunity-amount[data-v-7af46120],\n.opportunity-stage[data-v-7af46120]{word-break:break-all;white-space:normal}.opportunity-name[data-v-7af46120]{font-size:%?30?%;font-weight:500;color:#333;margin-bottom:%?8?%;display:block}.opportunity-customer[data-v-7af46120]{font-size:%?26?%;color:#666}.opportunity-meta[data-v-7af46120]{display:flex;justify-content:space-between;align-items:center}.opportunity-amount[data-v-7af46120]{font-size:%?28?%;color:#3370ff;font-weight:500}.opportunity-stage[data-v-7af46120]{font-size:%?24?%;padding:%?4?% %?16?%;border-radius:%?100?%}.stage-proposal[data-v-7af46120]{background-color:#fef3c7;color:#d97706}.stage-qualifying[data-v-7af46120]{background-color:#dbeafe;color:#2563eb}.stage-赢单[data-v-7af46120]{background-color:#fee2e2;color:#dc2626}.loading-state[data-v-7af46120],\n.empty-state[data-v-7af46120]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?60?% 0;color:#999}.loading-icon[data-v-7af46120]{margin-bottom:%?20?%;-webkit-animation:rotate-data-v-7af46120 1s linear infinite;animation:rotate-data-v-7af46120 1s linear infinite}@-webkit-keyframes rotate-data-v-7af46120{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes rotate-data-v-7af46120{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}.load-more[data-v-7af46120]{text-align:center;padding:%?20?% 0;color:#999;font-size:%?26?%}",""]),t.exports=e}}]);
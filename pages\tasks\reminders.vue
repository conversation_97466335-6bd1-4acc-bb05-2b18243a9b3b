<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="back-button" @click="goBack">
        <text class="ri-arrow-left-line"></text>
      </view>
      <view class="title">{{showSettings ? '提醒设置' : '提醒'}}</view>
      <view class="setting-button" @click="toggleSettings">
        <text class="ri-settings-3-line"></text>
      </view>
    </view>
    
    <!-- 提醒列表内容 -->
    <view v-show="!showSettings">
      <!-- 筛选器 -->
      <scroll-view scroll-x class="filters">
        <view 
          v-for="(filter, index) in filters" 
          :key="index"
          :class="['filter', { active: currentFilter === filter }]"
          @click="setFilter(filter)"
        >
          {{filter}}
        </view>
      </scroll-view>
      
      <!-- 提醒列表 -->
      <scroll-view scroll-y class="reminder-list" v-if="filteredReminders.length > 0">
        <!-- 即将到期提醒组 -->
        <view class="reminder-group" v-if="upcomingReminders.length > 0">
          <view class="group-header">即将到期</view>
          
          <view 
            class="reminder-card" 
            v-for="(reminder, index) in upcomingReminders" 
            :key="'upcoming-'+index"
          >
            <view class="reminder-status upcoming"></view>
            <view class="reminder-content">
              <view class="reminder-time upcoming">
                <text class="ri-time-line"></text>
                <text>{{reminder.timeText}}</text>
              </view>
              <view class="reminder-title">{{reminder.title}}</view>
              <view class="reminder-meta">
                <view class="reminder-source">
                  <text :class="getSourceIcon(reminder.source)"></text>
                  <text>{{reminder.source}}</text>
                </view>
                <view class="reminder-source" v-if="reminder.customer">
                  <text class="ri-building-line"></text>
                  <text>{{reminder.customer}}</text>
                </view>
              </view>
              <view class="reminder-actions">
                <view class="reminder-action" @click="postponeReminder(reminder)">稍后提醒</view>
                <view class="reminder-action" @click="dismissReminder(reminder)">关闭</view>
                <view class="reminder-action primary" @click="viewReminderDetail(reminder)">查看详情</view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 今日提醒组 -->
        <view class="reminder-group" v-if="todayReminders.length > 0">
          <view class="group-header">今日</view>
          
          <view 
            class="reminder-card" 
            v-for="(reminder, index) in todayReminders" 
            :key="'today-'+index"
          >
            <view class="reminder-status"></view>
            <view class="reminder-content">
              <view class="reminder-time">
                <text class="ri-time-line"></text>
                <text>{{reminder.timeText}}</text>
              </view>
              <view class="reminder-title">{{reminder.title}}</view>
              <view class="reminder-meta">
                <view class="reminder-source">
                  <text :class="getSourceIcon(reminder.source)"></text>
                  <text>{{reminder.source}}</text>
                </view>
                <view class="reminder-source" v-if="reminder.customer">
                  <text class="ri-building-line"></text>
                  <text>{{reminder.customer}}</text>
                </view>
              </view>
              <view class="reminder-actions">
                <view class="reminder-action" @click="postponeReminder(reminder)">稍后提醒</view>
                <view class="reminder-action" @click="dismissReminder(reminder)">关闭</view>
                <view class="reminder-action primary" @click="viewReminderDetail(reminder)">查看详情</view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- Overdue reminders -->
        <view class="reminder-group" v-if="overdueReminders.length > 0">
          <view class="group-header">已过期</view>
          
          <view 
            class="reminder-card" 
            v-for="(reminder, index) in overdueReminders" 
            :key="'overdue-'+index"
          >
            <view class="reminder-status overdue"></view>
            <view class="reminder-content">
              <view class="reminder-time overdue">
                <text class="ri-time-line"></text>
                <text>{{reminder.timeText}}</text>
              </view>
              <view class="reminder-title">{{reminder.title}}</view>
              <view class="reminder-meta">
                <view class="reminder-source">
                  <text :class="getSourceIcon(reminder.source)"></text>
                  <text>{{reminder.source}}</text>
                </view>
                <view class="reminder-source" v-if="reminder.customer">
                  <text class="ri-building-line"></text>
                  <text>{{reminder.customer}}</text>
                </view>
              </view>
              <view class="reminder-actions">
                <view class="reminder-action" @click="postponeTomorrow(reminder)">推迟到明天</view>
                <view class="reminder-action" @click="dismissReminder(reminder)">关闭</view>
                <view class="reminder-action primary" @click="completeReminder(reminder)">完成</view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="filteredReminders.length === 0">
        <text class="empty-icon ri-notification-line"></text>
        <view class="empty-title">暂无提醒</view>
        <view class="empty-text">您当前没有任何待处理的提醒。创建任务或日程时可以设置提醒。</view>
      </view>
    </view>
    
    <!-- 设置页面 -->
    <view class="settings-container" v-show="showSettings">
      <view class="setting-section">
        <view class="section-header">提醒设置</view>
        
        <view class="setting-item" v-for="(setting, index) in notificationSettings" :key="index">
          <view class="setting-info">
            <view class="setting-title">{{setting.title}}</view>
            <view class="setting-desc">{{setting.desc}}</view>
          </view>
          <view class="setting-action">
            <switch :checked="setting.enabled" @change="toggleSetting(index)" color="#3c8dbc" />
          </view>
        </view>
      </view>
      
      <view class="setting-section">
        <view class="section-header">默认提醒时间</view>
        
        <view class="setting-item" v-for="(timing, index) in reminderTimings" :key="index" @click="editReminderTiming(index)">
          <view class="setting-info">
            <view class="setting-title">{{timing.title}}</view>
            <view class="setting-desc">{{timing.desc}}</view>
          </view>
          <view class="setting-action">
            <text class="ri-arrow-right-s-line"></text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      showSettings: false,
      currentFilter: '全部',
      filters: ['全部', '今日', '任务', '会议', '已过期'],
      reminders: [
        {
          id: 1,
          title: '与王总进行项目方案讨论',
          timeText: '15分钟后 (15:00)',
          time: new Date(new Date().setHours(15, 0, 0)),
          source: '会议',
          customer: '创新科技有限公司',
          status: 'upcoming'
        },
        {
          id: 2,
          title: '回电广州未来科技公司',
          timeText: '1小时后 (16:30)',
          time: new Date(new Date().setHours(16, 30, 0)),
          source: '电话',
          customer: '未来科技有限公司',
          status: 'upcoming'
        },
        {
          id: 3,
          title: '完成月度销售报表',
          timeText: '今日 17:00',
          time: new Date(new Date().setHours(17, 0, 0)),
          source: '任务',
          status: 'today'
        },
        {
          id: 4,
          title: '跟进新客户需求',
          timeText: '昨日 14:30 已过期',
          time: new Date(new Date().setDate(new Date().getDate() - 1)),
          source: '任务',
          customer: '星辰科技',
          status: 'overdue'
        }
      ],
      notificationSettings: [
        {
          title: '允许推送通知',
          desc: '接收任务和活动的推送通知',
          enabled: true
        },
        {
          title: '声音提醒',
          desc: '提醒时播放声音',
          enabled: true
        },
        {
          title: '振动提醒',
          desc: '提醒时振动',
          enabled: true
        }
      ],
      reminderTimings: [
        {
          title: '任务提醒',
          desc: '提前15分钟',
          value: 15
        },
        {
          title: '会议提醒',
          desc: '提前30分钟',
          value: 30
        },
        {
          title: '客户跟进提醒',
          desc: '提前1小时',
          value: 60
        }
      ]
    }
  },
  computed: {
    filteredReminders() {
      if (this.currentFilter === '全部') {
        return this.reminders;
      } else if (this.currentFilter === '今日') {
        return this.reminders.filter(r => r.timeText.includes('今日') || r.status === 'upcoming');
      } else if (this.currentFilter === '任务') {
        return this.reminders.filter(r => r.source === '任务');
      } else if (this.currentFilter === '会议') {
        return this.reminders.filter(r => r.source === '会议');
      } else if (this.currentFilter === '已过期') {
        return this.reminders.filter(r => r.status === 'overdue');
      }
      return this.reminders;
    },
    upcomingReminders() {
      return this.filteredReminders.filter(r => r.status === 'upcoming');
    },
    todayReminders() {
      return this.filteredReminders.filter(r => r.status === 'today');
    },
    overdueReminders() {
      return this.filteredReminders.filter(r => r.status === 'overdue');
    }
  },
  methods: {
    goBack() {
      if (this.showSettings) {
        this.showSettings = false;
      } else {
        uni.navigateBack();
      }
    },
    toggleSettings() {
      this.showSettings = !this.showSettings;
    },
    setFilter(filter) {
      this.currentFilter = filter;
    },
    getSourceIcon(source) {
      const iconMap = {
        '任务': 'ri-task-line',
        '会议': 'ri-calendar-event-line',
        '电话': 'ri-phone-line',
        '客户': 'ri-user-line'
      };
      return iconMap[source] || 'ri-notification-line';
    },
    dismissReminder(reminder) {
      const index = this.reminders.findIndex(r => r.id === reminder.id);
      if (index > -1) {
        this.reminders.splice(index, 1);
      }
      uni.showToast({
        title: '已关闭提醒',
        icon: 'none'
      });
    },
    postponeReminder(reminder) {
      uni.showActionSheet({
        itemList: ['30分钟后', '1小时后', '明天同一时间', '自定义时间'],
        success: (res) => {
          uni.showToast({
            title: '已推迟提醒',
            icon: 'success'
          });
        }
      });
    },
    postponeTomorrow(reminder) {
      uni.showToast({
        title: '已推迟到明天',
        icon: 'success'
      });
      const index = this.reminders.findIndex(r => r.id === reminder.id);
      if (index > -1) {
        this.reminders.splice(index, 1);
      }
    },
    completeReminder(reminder) {
      uni.showToast({
        title: '已完成',
        icon: 'success'
      });
      const index = this.reminders.findIndex(r => r.id === reminder.id);
      if (index > -1) {
        this.reminders.splice(index, 1);
      }
    },
    viewReminderDetail(reminder) {
      uni.navigateTo({
        url: `/pages/tasks/task-detail?id=${reminder.id}`
      });
    },
    toggleSetting(index) {
      this.notificationSettings[index].enabled = !this.notificationSettings[index].enabled;
    },
    editReminderTiming(index) {
      uni.showActionSheet({
        itemList: ['不提醒', '提前5分钟', '提前15分钟', '提前30分钟', '提前1小时', '提前1天'],
        success: (res) => {
          const times = [0, 5, 15, 30, 60, 1440];
          const descriptions = ['不提醒', '提前5分钟', '提前15分钟', '提前30分钟', '提前1小时', '提前1天'];
          
          this.reminderTimings[index].value = times[res.tapIndex];
          this.reminderTimings[index].desc = descriptions[res.tapIndex];
        }
      });
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #3c8dbc;
  color: #fff;
}

.back-button, .setting-button {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
}

.filters {
  display: flex;
  white-space: nowrap;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.filter {
  display: inline-block;
  padding: 12rpx 30rpx;
  margin-right: 20rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  background-color: #f0f0f0;
  color: #666;
}

.filter.active {
  background-color: #3c8dbc;
  color: #fff;
}

.reminder-list {
  flex: 1;
  padding: 20rpx 30rpx;
  height: calc(100vh - 200rpx);
}

.reminder-group {
  margin-bottom: 40rpx;
}

.group-header {
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #999;
  font-weight: 500;
}

.reminder-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.reminder-status {
  height: 8rpx;
  background-color: #3c8dbc;
}

.reminder-status.upcoming {
  background-color: #e6a23c;
}

.reminder-status.overdue {
  background-color: #f56c6c;
}

.reminder-content {
  padding: 20rpx 30rpx;
}

.reminder-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
}

.reminder-time text:first-child {
  margin-right: 10rpx;
}

.reminder-time.upcoming {
  color: #e6a23c;
}

.reminder-time.overdue {
  color: #f56c6c;
}

.reminder-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  color: #333;
}

.reminder-meta {
  display: flex;
  flex-wrap: wrap;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.reminder-source {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 10rpx;
}

.reminder-source text:first-child {
  margin-right: 10rpx;
}

.reminder-actions {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}

.reminder-action {
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  color: #666;
}

.reminder-action.primary {
  background-color: #3c8dbc;
  color: #fff;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 30rpx;
  text-align: center;
}

.empty-icon {
  font-size: 100rpx;
  color: #ddd;
  margin-bottom: 30rpx;
}

.empty-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  max-width: 80%;
}

.settings-container {
  flex: 1;
}

.setting-section {
  margin-bottom: 30rpx;
}

.section-header {
  padding: 20rpx 30rpx;
  font-size: 26rpx;
  color: #999;
  text-transform: uppercase;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.setting-info {
  flex: 1;
}

.setting-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.setting-desc {
  font-size: 26rpx;
  color: #999;
}

.setting-action {
  padding-left: 30rpx;
  color: #999;
}
</style> 
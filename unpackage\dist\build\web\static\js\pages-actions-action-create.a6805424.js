(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-actions-action-create"],{"0200":function(t,e,a){"use strict";a.r(e);var i=a("06a7"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},"06a7":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("795c"),a("c223"),a("bd06");var n=i(a("8a0f")),o={components:{SvgIcon:n.default},data:function(){var t=new Date;return{formData:{type:"task",title:"",priority:"medium",date:this.formatDate(t),time:this.formatTime(t),related:"",relatedType:"company",description:"",hasReminder:!1,reminderTime:15},actionTypes:[{label:"任务",value:"task",icon:"check-circle"},{label:"电话",value:"call",icon:"phone"},{label:"会议",value:"meeting",icon:"team"},{label:"拜访",value:"visit",icon:"navigation"},{label:"邮件",value:"email",icon:"mail"}],priorities:[{label:"低",value:"low"},{label:"中",value:"medium"},{label:"高",value:"high"}],reminderOptions:[{label:"活动开始前5分钟",value:5},{label:"活动开始前15分钟",value:15},{label:"活动开始前30分钟",value:30},{label:"活动开始前1小时",value:60},{label:"活动开始前2小时",value:120},{label:"活动开始前1天",value:1440}],reminderIndex:1,relatedTypes:[{label:"公司",value:"company"},{label:"联系人",value:"contact"},{label:"商机",value:"opportunity"},{label:"合同",value:"contract"},{label:"会议",value:"meeting"},{label:"报表",value:"report"}],relatedTypeIndex:0,startDate:this.formatDate(t)}},methods:{navBack:function(){uni.navigateBack()},formatDate:function(t){var e=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0"),i=String(t.getDate()).padStart(2,"0");return"".concat(e,"-").concat(a,"-").concat(i)},formatTime:function(t){var e=String(t.getHours()).padStart(2,"0"),a=String(t.getMinutes()).padStart(2,"0");return"".concat(e,":").concat(a)},onDateChange:function(t){this.formData.date=t.detail.value},onTimeChange:function(t){this.formData.time=t.detail.value},onReminderChange:function(t){this.formData.hasReminder=t.detail.value},onReminderTimeChange:function(t){this.reminderIndex=t.detail.value,this.formData.reminderTime=this.reminderOptions[this.reminderIndex].value},onRelatedTypeChange:function(t){this.relatedTypeIndex=t.detail.value,this.formData.relatedType=this.relatedTypes[this.relatedTypeIndex].value},showParticipantModal:function(){uni.showToast({title:"添加参与人功能开发中",icon:"none"})},saveAction:function(){this.formData.title?(uni.showLoading({title:"保存中..."}),setTimeout((function(){uni.hideLoading(),uni.showToast({title:"创建成功",icon:"success",duration:2e3,success:function(){setTimeout((function(){uni.navigateBack()}),1500)}})}),1e3)):uni.showToast({title:"请输入标题",icon:"none"})}},onLoad:function(t){if(t.type&&(this.formData.type=t.type),t.relatedId&&t.relatedType){this.formData.relatedType=t.relatedType;var e=this.relatedTypes.findIndex((function(e){return e.value===t.relatedType}));-1!==e&&(this.relatedTypeIndex=e)}}};e.default=o},3095:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'.page[data-v-093f2853]{display:flex;flex-direction:column;height:100vh;background-color:#f5f7fa}.page-header[data-v-093f2853]{display:flex;align-items:center;justify-content:space-between;padding:%?30?% %?40?%;border-bottom:%?1?% solid #e0e0e0;background-color:#fff;position:-webkit-sticky;position:sticky;top:0;z-index:10}.back-button[data-v-093f2853]{padding:%?10?%}.page-title[data-v-093f2853]{font-size:%?36?%;font-weight:700;color:#333}.header-spacer[data-v-093f2853]{width:%?44?%}.page-container[data-v-093f2853]{flex:1;padding:%?30?%}.form-section[data-v-093f2853]{background-color:#fff;border-radius:%?16?%;padding:%?30?%;margin-bottom:%?30?%;box-shadow:0 %?2?% %?10?% rgba(0,0,0,.05)}.section-header[data-v-093f2853]{display:flex;align-items:center;margin-bottom:%?20?%}.section-title[data-v-093f2853]{font-size:%?30?%;font-weight:700;margin-left:%?10?%;color:#333}.section-content[data-v-093f2853]{padding:%?10?%}.type-selector[data-v-093f2853]{display:grid;grid-template-columns:repeat(3,1fr);gap:%?20?%}.type-option[data-v-093f2853]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?30?% 0;border-radius:%?12?%;border:%?1?% solid #e0e0e0;transition:all .3s}.type-option.selected[data-v-093f2853]{border-color:#3370ff;background-color:#f0f7ff}.type-option uni-text[data-v-093f2853]{margin-top:%?16?%;font-size:%?28?%;color:#666}.type-option.selected uni-text[data-v-093f2853]{color:#3370ff;font-weight:500}.form-group[data-v-093f2853]{margin-bottom:%?30?%}.form-label[data-v-093f2853]{display:block;font-size:%?28?%;color:#666;margin-bottom:%?16?%}.form-label.required[data-v-093f2853]:after{content:"*";color:#f5222d;margin-left:%?8?%}.form-control[data-v-093f2853]{width:100%;height:%?80?%;padding:0 %?20?%;font-size:%?28?%;color:#333;border:%?1?% solid #d9d9d9;border-radius:%?8?%;box-sizing:border-box}uni-textarea.form-control[data-v-093f2853]{height:%?240?%;padding:%?20?%}.priority-group[data-v-093f2853]{display:flex;gap:%?20?%}.priority-item[data-v-093f2853]{flex:1;text-align:center;padding:%?20?% 0;border:%?1?% solid #d9d9d9;border-radius:%?8?%;font-size:%?28?%;color:#666}.priority-item.active[data-v-093f2853]{border-color:currentColor;background-color:rgba(0,0,0,.05);font-weight:500}.priority-item.low.active[data-v-093f2853]{color:#52c41a}.priority-item.medium.active[data-v-093f2853]{color:#faad14}.priority-item.high.active[data-v-093f2853]{color:#f5222d}.uni-input[data-v-093f2853]{position:relative;height:%?80?%;padding:0 %?20?%;line-height:%?80?%;border:%?1?% solid #d9d9d9;border-radius:%?8?%}.input-icon[data-v-093f2853]{position:absolute;right:%?20?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#999}.toggle-switch[data-v-093f2853]{display:flex;justify-content:space-between;align-items:center;margin-bottom:%?30?%}.toggle-label[data-v-093f2853]{font-size:%?28?%;color:#666}.form-select[data-v-093f2853]{width:100%}.add-participant[data-v-093f2853]{display:flex;align-items:center;padding:%?20?%;border:%?1?% dashed #d9d9d9;border-radius:%?8?%;color:#3370ff}.add-participant uni-text[data-v-093f2853]{margin-left:%?10?%}.bottom-spacer[data-v-093f2853]{height:%?120?%}.action-bar[data-v-093f2853]{position:fixed;bottom:0;left:0;right:0;padding:%?20?% %?30?%;background-color:#fff;box-shadow:0 %?-2?% %?10?% rgba(0,0,0,.05);display:flex;gap:%?30?%}.btn[data-v-093f2853]{flex:1;height:%?88?%;border-radius:%?8?%;display:flex;align-items:center;justify-content:center;font-size:%?30?%}.btn-outline[data-v-093f2853]{border:%?1?% solid #d9d9d9;color:#666}.btn-primary[data-v-093f2853]{background-color:#3370ff;color:#fff}',""]),t.exports=e},c790:function(t,e,a){var i=a("3095");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("4919bca0",i,!0,{sourceMap:!1,shadowMode:!1})},cf63:function(t,e,a){"use strict";a.r(e);var i=a("dc2b"),n=a("0200");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("dea0");var s=a("828b"),r=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"093f2853",null,!1,i["a"],void 0);e["default"]=r.exports},dc2b:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={svgIcon:a("8a0f").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"page"},[a("v-uni-view",{staticClass:"page-header"},[a("v-uni-view",{staticClass:"back-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.navBack.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"arrow-left",type:"svg",size:"24"}})],1),a("v-uni-text",{staticClass:"page-title"},[t._v("创建行动计划")]),a("v-uni-view",{staticClass:"header-spacer"})],1),a("v-uni-scroll-view",{staticClass:"page-container",attrs:{"scroll-y":!0}},[a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-header"},[a("svg-icon",{attrs:{name:"information",type:"svg",size:"20"}}),a("v-uni-text",{staticClass:"section-title"},[t._v("行动类型")])],1),a("v-uni-view",{staticClass:"section-content"},[a("v-uni-view",{staticClass:"type-selector"},t._l(t.actionTypes,(function(e,i){return a("v-uni-view",{key:i,staticClass:"type-option",class:{selected:t.formData.type===e.value},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.formData.type=e.value}}},[a("svg-icon",{attrs:{name:e.icon,type:e.iconType||"svg",size:"24",color:t.formData.type===e.value?"#3370ff":""}}),a("v-uni-text",[t._v(t._s(e.label))])],1)})),1)],1)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-header"},[a("svg-icon",{attrs:{name:"file-list",type:"svg",size:"20"}}),a("v-uni-text",{staticClass:"section-title"},[t._v("基本信息")])],1),a("v-uni-view",{staticClass:"section-content"},[a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label required"},[t._v("标题")]),a("v-uni-input",{staticClass:"form-control",attrs:{type:"text",placeholder:"请输入标题"},model:{value:t.formData.title,callback:function(e){t.$set(t.formData,"title",e)},expression:"formData.title"}})],1),"task"===t.formData.type?a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("优先级")]),a("v-uni-view",{staticClass:"priority-group"},t._l(t.priorities,(function(e,i){return a("v-uni-view",{key:i,class:["priority-item",e.value,{active:t.formData.priority===e.value}],on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.formData.priority=e.value}}},[a("v-uni-text",[t._v(t._s(e.label))])],1)})),1)],1):t._e(),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label required"},[t._v("日期")]),a("v-uni-view",{staticClass:"date-picker"},[a("v-uni-picker",{attrs:{mode:"date",value:t.formData.date,start:t.startDate},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onDateChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"uni-input"},[t._v(t._s(t.formData.date)),a("v-uni-view",{staticClass:"input-icon"},[a("svg-icon",{attrs:{name:"calendar",type:"svg",size:"20"}})],1)],1)],1)],1)],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("时间")]),a("v-uni-view",{staticClass:"date-picker"},[a("v-uni-picker",{attrs:{mode:"time",value:t.formData.time},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onTimeChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"uni-input"},[t._v(t._s(t.formData.time)),a("v-uni-view",{staticClass:"input-icon"},[a("svg-icon",{attrs:{name:"clock",type:"svg",size:"20"}})],1)],1)],1)],1)],1),"task"!==t.formData.type?a("v-uni-view",{staticClass:"toggle-switch"},[a("v-uni-view",{staticClass:"toggle-label"},[t._v("设置提醒")]),a("v-uni-switch",{attrs:{checked:t.formData.hasReminder,color:"#3370ff"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onReminderChange.apply(void 0,arguments)}}})],1):t._e(),t.formData.hasReminder&&"task"!==t.formData.type?a("v-uni-view",{staticClass:"form-group"},[a("v-uni-picker",{attrs:{value:t.reminderIndex,range:t.reminderOptions.map((function(t){return t.label})),mode:"selector"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onReminderTimeChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"form-select"},[a("v-uni-view",{staticClass:"uni-input"},[t._v(t._s(t.reminderOptions[t.reminderIndex].label)),a("v-uni-view",{staticClass:"input-icon"},[a("svg-icon",{attrs:{name:"notification",type:"svg",size:"20"}})],1)],1)],1)],1)],1):t._e()],1)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-header"},[a("svg-icon",{attrs:{name:"link",type:"svg",size:"20"}}),a("v-uni-text",{staticClass:"section-title"},[t._v("关联信息")])],1),a("v-uni-view",{staticClass:"section-content"},[a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("关联对象")]),a("v-uni-picker",{attrs:{value:t.relatedTypeIndex,range:t.relatedTypes,"range-key":"label"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onRelatedTypeChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"uni-input"},[t._v(t._s(t.relatedTypes[t.relatedTypeIndex].label)),a("v-uni-view",{staticClass:"input-icon"},[a("svg-icon",{attrs:{name:"down",type:"svg",size:"20"}})],1)],1)],1)],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"form-label"},[t._v("关联内容")]),a("v-uni-input",{staticClass:"form-control",attrs:{type:"text",placeholder:"输入或选择关联内容"},model:{value:t.formData.related,callback:function(e){t.$set(t.formData,"related",e)},expression:"formData.related"}})],1)],1)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-header"},[a("svg-icon",{attrs:{name:"align-left",type:"svg",size:"20"}}),a("v-uni-text",{staticClass:"section-title"},[t._v("详情描述")])],1),a("v-uni-view",{staticClass:"section-content"},[a("v-uni-view",{staticClass:"form-group"},[a("v-uni-textarea",{staticClass:"form-control",attrs:{placeholder:"请输入详情描述..."},model:{value:t.formData.description,callback:function(e){t.$set(t.formData,"description",e)},expression:"formData.description"}})],1)],1)],1),"task"!==t.formData.type?a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-header"},[a("svg-icon",{attrs:{name:"user-group",type:"svg",size:"20"}}),a("v-uni-text",{staticClass:"section-title"},[t._v("参与人")])],1),a("v-uni-view",{staticClass:"section-content"},[a("v-uni-view",{staticClass:"add-participant",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showParticipantModal.apply(void 0,arguments)}}},[a("svg-icon",{attrs:{name:"user-add",type:"svg",size:"20"}}),a("v-uni-text",[t._v("添加参与人")])],1)],1)],1):t._e(),a("v-uni-view",{staticClass:"bottom-spacer"})],1),a("v-uni-view",{staticClass:"action-bar"},[a("v-uni-button",{staticClass:"btn btn-outline",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.navBack.apply(void 0,arguments)}}},[t._v("取消")]),a("v-uni-button",{staticClass:"btn btn-primary",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveAction.apply(void 0,arguments)}}},[t._v("保存")])],1)],1)},o=[]},dea0:function(t,e,a){"use strict";var i=a("c790"),n=a.n(i);n.a}}]);
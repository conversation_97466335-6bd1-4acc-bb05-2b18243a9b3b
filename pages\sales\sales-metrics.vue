<template>
  <view class="metrics-container">
    <view class="page-header">
      <view class="header-left">
        <view class="back-button" @click="navigateBack">
          <text class="ri-arrow-left-line"></text>
        </view>
        <text class="page-title">销售指标</text>
      </view>
      <view class="time-selector">
        <view 
          v-for="(item, index) in timeOptions" 
          :key="index" 
          class="time-btn" 
          :class="{ active: currentTimeFrame === item.value }"
          @click="changeTimeFrame(item.value)"
        >
          {{item.label}}
        </view>
      </view>
    </view>
    
    <view class="chart-card">
      <view class="chart-header">
        <text class="chart-title">销售趋势</text>
      </view>
      <view class="chart-container">
        <text class="ri-line-chart-line chart-icon"></text>
        <text class="chart-loading">图表加载中...</text>
      </view>
    </view>
    
    <view class="metrics-grid">
      <view class="metric-card" v-for="(metric, index) in metrics" :key="index">
        <text class="metric-title">{{metric.title}}</text>
        <text class="metric-value">{{metric.value}}</text>
        <view class="metric-trend" :class="{ negative: metric.trend < 0 }">
          <text :class="metric.trend >= 0 ? 'ri-arrow-up-line' : 'ri-arrow-down-line'"></text>
          <text>{{Math.abs(metric.trend)}}%</text>
        </view>
      </view>
    </view>
    
    <view class="team-performance">
      <text class="chart-title">团队业绩</text>
      <view class="team-member" v-for="(member, index) in teamMembers" :key="index">
        <view class="member-avatar">
          <text class="ri-user-line"></text>
        </view>
        <view class="member-info">
          <text class="member-name">{{member.name}}</text>
          <view class="member-performance">
            <text>销售额：</text>
            <text class="performance-value">{{member.sales}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentTimeFrame: 'today',
      timeOptions: [
        { label: '今日', value: 'today' },
        { label: '本周', value: 'week' },
        { label: '本月', value: 'month' }
      ],
      metrics: [
        { title: '销售额', value: '¥128,560', trend: 12.5 },
        { title: '订单数', value: '24', trend: 8.3 },
        { title: '客单价', value: '¥5,357', trend: -3.2 },
        { title: '转化率', value: '32.5%', trend: 2.1 }
      ],
      teamMembers: [
        { name: '张经理', sales: '¥45,280' },
        { name: '李经理', sales: '¥38,920' },
        { name: '王经理', sales: '¥44,360' }
      ]
    }
  },
  methods: {
    navigateBack() {
      uni.navigateBack();
    },
    changeTimeFrame(timeFrame) {
      this.currentTimeFrame = timeFrame;
      this.fetchMetricsData(timeFrame);
    },
    fetchMetricsData(timeFrame) {
      // 模拟获取不同时间范围的数据
      console.log(`请求${timeFrame}的销售指标数据`);
      
      // 实际开发中这里应该通过API获取对应时间段的数据
      // 现在只做模拟展示
      switch(timeFrame) {
        case 'today':
          this.metrics = [
            { title: '销售额', value: '¥128,560', trend: 12.5 },
            { title: '订单数', value: '24', trend: 8.3 },
            { title: '客单价', value: '¥5,357', trend: -3.2 },
            { title: '转化率', value: '32.5%', trend: 2.1 }
          ];
          break;
        case 'week':
          this.metrics = [
            { title: '销售额', value: '¥589,420', trend: 8.2 },
            { title: '订单数', value: '98', trend: 10.5 },
            { title: '客单价', value: '¥6,014', trend: -2.1 },
            { title: '转化率', value: '35.2%', trend: 4.3 }
          ];
          break;
        case 'month':
          this.metrics = [
            { title: '销售额', value: '¥2,156,800', trend: 15.6 },
            { title: '订单数', value: '312', trend: 12.3 },
            { title: '客单价', value: '¥6,913', trend: 3.5 },
            { title: '转化率', value: '38.7%', trend: 6.8 }
          ];
          break;
      }
      
      // 未来可对接echarts等图表组件展示图表
    }
  }
}
</script>

<style>
.metrics-container {
  padding: 15px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
}

.back-button {
  margin-right: 10px;
  color: #666;
  font-size: 24px;
}

.page-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.time-selector {
  display: flex;
  gap: 8px;
}

.time-btn {
  padding: 6px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  color: #666;
  background-color: white;
}

.time-btn.active {
  background-color: #3370ff;
  color: white;
  border-color: #3370ff;
}

.chart-card {
  background-color: white;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.chart-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.chart-container {
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
}

.chart-icon {
  font-size: 40px;
  margin-bottom: 10px;
}

.chart-loading {
  font-size: 14px;
}

.metrics-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
  margin-bottom: 20px;
}

.metric-card {
  background-color: white;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  width: calc(50% - 10px);
  margin: 0 5px 10px 5px;
  box-sizing: border-box;
}

.metric-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.metric-value {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.metric-trend {
  font-size: 12px;
  color: #4caf50;
  display: flex;
  align-items: center;
}

.metric-trend.negative {
  color: #f44336;
}

.team-performance {
  background-color: white;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.team-member {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #e0e0e0;
}

.team-member:last-child {
  border-bottom: none;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #e6f0ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.member-avatar text {
  font-size: 20px;
  color: #3370ff;
}

.member-info {
  flex: 1;
}

.member-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
}

.member-performance {
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
}

.performance-value {
  font-weight: 500;
  color: #3370ff;
}
</style> 
<template>
	<view class="product-create-page">
		<!-- 头部 -->
		<view class="header">
			<view class="left" @click="goBack">
				<svg-icon name="back" type="svg" size="32" color="#FFFFFF"></svg-icon>
			</view>
			<view class="title">新建产品</view>
			<view class="right"></view>
		</view>
		
		<!-- 表单内容 -->
		<scroll-view scroll-y class="content-container">
			<view class="form-section">
				<view class="section-title">
					<svg-icon name="info" type="svg" size="32" color="#3a86ff"></svg-icon>
					<text>基本信息</text>
				</view>
				<view class="form-group">
					<view class="form-item">
						<text class="label required">产品名称</text>
						<input type="text" v-model="formData.name" placeholder="请输入产品名称" />
					</view>
					<view class="form-item">
						<text class="label required">产品编号</text>
						<input type="text" v-model="formData.code" placeholder="请输入产品编号" />
					</view>
					<view class="form-item">
						<text class="label required">产品类别</text>
						<picker mode="selector" :range="categories" range-key="name" @change="onCategoryChange">
							<view class="picker-view">
								<text v-if="formData.category">{{formData.category.name}}</text>
								<text v-else class="placeholder">请选择产品类别</text>
								<svg-icon name="right" type="svg" size="28" color="#999999"></svg-icon>
							</view>
						</picker>
					</view>
					<view class="form-item">
						<text class="label">产品描述</text>
						<textarea v-model="formData.description" placeholder="请输入产品描述" />
					</view>
				</view>
			</view>
			
			<view class="form-section">
				<view class="section-title">
					<svg-icon name="money" type="svg" size="32" color="#3a86ff"></svg-icon>
					<text>价格信息</text>
				</view>
				<view class="form-group">
					<view class="form-item">
						<text class="label required">销售价格</text>
						<input type="digit" v-model="formData.price" placeholder="请输入销售价格" />
					</view>
					<view class="form-item">
						<text class="label required">计量单位</text>
						<input type="text" v-model="formData.unit" placeholder="如：个、套、台、次" />
					</view>
					<view class="form-item">
						<text class="label">税率</text>
						<picker mode="selector" :range="taxRates" range-key="name" @change="onTaxRateChange">
							<view class="picker-view">
								<text v-if="formData.taxRate">{{formData.taxRate.name}}</text>
								<text v-else class="placeholder">请选择税率</text>
								<svg-icon name="right" type="svg" size="28" color="#999999"></svg-icon>
							</view>
						</picker>
					</view>
					<view class="form-item">
						<text class="label">成本价格</text>
						<input type="digit" v-model="formData.costPrice" placeholder="请输入成本价格(选填)" />
					</view>
				</view>
			</view>
			
			<view class="form-section">
				<view class="section-title">
					<svg-icon name="settings" type="svg" size="32" color="#3a86ff"></svg-icon>
					<text>其他信息</text>
				</view>
				<view class="form-group">
					<view class="form-item">
						<text class="label">产品状态</text>
						<picker mode="selector" :range="statusOptions" range-key="name" @change="onStatusChange">
							<view class="picker-view">
								<text v-if="formData.status">{{formData.status.name}}</text>
								<text v-else class="placeholder">请选择产品状态</text>
								<svg-icon name="right" type="svg" size="28" color="#999999"></svg-icon>
							</view>
						</picker>
					</view>
					<view class="form-item">
						<text class="label">标签</text>
						<input type="text" v-model="formData.tags" placeholder="多个标签用逗号分隔" />
					</view>
					<view class="form-item">
						<text class="label">备注</text>
						<textarea v-model="formData.remarks" placeholder="请输入备注信息" />
					</view>
				</view>
			</view>
		</scroll-view>
		
		<!-- 底部按钮 -->
		<view class="action-buttons">
			<button class="btn btn-primary" @click="saveProduct">保存</button>
			<button class="btn btn-outline" @click="goBack">取消</button>
		</view>
	</view>
</template>

<script>
import SvgIcon from '@/components/svg-icon.vue';

export default {
	components: {
		SvgIcon
	},
	data() {
		return {
			formData: {
				name: '',
				code: '',
				category: null,
				description: '',
				price: '',
				unit: '',
				taxRate: null,
				costPrice: '',
				status: null,
				tags: '',
				remarks: ''
			},
			categories: [
				{ id: 1, value: 'software', name: '软件产品' },
				{ id: 2, value: 'hardware', name: '硬件产品' },
				{ id: 3, value: 'tech_service', name: '技术服务' },
				{ id: 4, value: 'consulting', name: '咨询服务' },
				{ id: 5, value: 'maintenance', name: '维护服务' }
			],
			taxRates: [
				{ id: 1, name: '13%', value: 0.13 },
				{ id: 2, name: '6%', value: 0.06 },
				{ id: 3, name: '3%', value: 0.03 },
				{ id: 4, name: '0%', value: 0 }
			],
			statusOptions: [
				{ id: 1, value: 'active', name: '上架' },
				{ id: 2, value: 'inactive', name: '下架' },
				{ id: 3, value: 'discontinued', name: '停产' }
			]
		}
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		
		onCategoryChange(e) {
			const index = e.detail.value;
			this.formData.category = this.categories[index];
			
			// 根据类别自动生成编号前缀
			if (this.formData.code === '') {
				const prefix = this.getCodePrefix(this.formData.category.value);
				this.formData.code = prefix + this.getRandomCode();
			}
		},
		
		onTaxRateChange(e) {
			const index = e.detail.value;
			this.formData.taxRate = this.taxRates[index];
		},
		
		onStatusChange(e) {
			const index = e.detail.value;
			this.formData.status = this.statusOptions[index];
		},
		
		getCodePrefix(categoryValue) {
			const prefixMap = {
				'software': 'SW',
				'hardware': 'HW',
				'tech_service': 'TS',
				'consulting': 'CS',
				'maintenance': 'MS'
			};
			
			return prefixMap[categoryValue] || 'PD';
		},
		
		getRandomCode() {
			// 生成三位随机数字
			return (Math.floor(Math.random() * 900) + 100).toString();
		},
		
		validateForm() {
			if (!this.formData.name) {
				uni.showToast({
					title: '请输入产品名称',
					icon: 'none'
				});
				return false;
			}
			
			if (!this.formData.code) {
				uni.showToast({
					title: '请输入产品编号',
					icon: 'none'
				});
				return false;
			}
			
			if (!this.formData.category) {
				uni.showToast({
					title: '请选择产品类别',
					icon: 'none'
				});
				return false;
			}
			
			if (!this.formData.price) {
				uni.showToast({
					title: '请输入销售价格',
					icon: 'none'
				});
				return false;
			}
			
			if (!this.formData.unit) {
				uni.showToast({
					title: '请输入计量单位',
					icon: 'none'
				});
				return false;
			}
			
			return true;
		},
		
		saveProduct() {
			if (!this.validateForm()) {
				return;
			}
			
			// 设置默认值
			if (!this.formData.taxRate) {
				this.formData.taxRate = this.taxRates[0];
			}
			
			if (!this.formData.status) {
				this.formData.status = this.statusOptions[0];
			}
			
			// 模拟保存到后台
			uni.showLoading({
				title: '保存中...'
			});
			
			setTimeout(() => {
				uni.hideLoading();
				
				// 将新产品添加到本地存储中
				const newProduct = {
					id: new Date().getTime().toString(),
					name: this.formData.name,
					code: this.formData.code,
					price: this.formData.price,
					category: this.formData.category.value,
					categoryName: this.formData.category.name,
					unit: this.formData.unit,
					description: this.formData.description,
					taxRate: this.formData.taxRate,
					status: this.formData.status.value
				};
				
				// 保存并返回
				uni.setStorageSync('selected_product', newProduct);
				uni.showToast({
					title: '保存成功',
					icon: 'success'
				});
				
				setTimeout(() => {
					uni.navigateBack();
				}, 1000);
			}, 1000);
		}
	}
}
</script>

<style lang="scss">
.product-create-page {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f5f5f5;
	
	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 90rpx;
		background-color: #3a86ff;
		color: #fff;
		padding: 0 30rpx;
		position: sticky;
		top: 0;
		z-index: 100;
		
		.left {
			display: flex;
			align-items: center;
		}
		
		.title {
			font-size: 34rpx;
			font-weight: bold;
		}
		
		.right {
			width: 60rpx;
		}
	}
	
	.content-container {
		flex: 1;
		padding-bottom: 120rpx;
	}
	
	.form-section {
		background-color: #fff;
		margin: 20rpx 20rpx 0;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);
		
		.section-title {
			display: flex;
			align-items: center;
			padding: 24rpx 30rpx;
			border-bottom: 1rpx solid #eee;
			
			text {
				font-size: 30rpx;
				font-weight: bold;
				color: #333;
				margin-left: 12rpx;
			}
		}
		
		.form-group {
			padding: 0 30rpx;
		}
		
		.form-item {
			padding: 24rpx 0;
			border-bottom: 1rpx solid #eee;
			
			&:last-child {
				border-bottom: none;
			}
			
			.label {
				display: block;
				font-size: 28rpx;
				color: #666;
				margin-bottom: 12rpx;
				
				&.required:after {
					content: '*';
					color: #ff4d4f;
					margin-left: 6rpx;
				}
			}
			
			input {
				width: 100%;
				height: 88rpx;
				font-size: 28rpx;
				color: #333;
				background-color: #f9f9f9;
				border-radius: 8rpx;
				padding: 0 20rpx;
				box-sizing: border-box;
				border: 1rpx solid #eee;
			}
			
			textarea {
				width: 100%;
				height: 200rpx;
				padding: 20rpx;
				border: 1rpx solid #eee;
				border-radius: 8rpx;
				font-size: 28rpx;
				background-color: #f9f9f9;
				box-sizing: border-box;
			}
			
			.picker-view {
				display: flex;
				justify-content: space-between;
				align-items: center;
				height: 88rpx;
				font-size: 28rpx;
				color: #333;
				background-color: #f9f9f9;
				border-radius: 8rpx;
				padding: 0 20rpx;
				box-sizing: border-box;
				border: 1rpx solid #eee;
				
				.placeholder {
					color: #999;
				}
			}
		}
	}
	
	.action-buttons {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		display: flex;
		justify-content: space-between;
		padding: 20rpx;
		background-color: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
		
		.btn {
			flex: 1;
			margin: 0 10rpx;
			height: 88rpx;
			line-height: 88rpx;
			text-align: center;
			border-radius: 8rpx;
			font-size: 28rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		.btn-primary {
			background-color: #3a86ff;
			color: #fff;
		}
		
		.btn-outline {
			background-color: #fff;
			color: #666;
			border: 1rpx solid #ddd;
		}
	}
}
</style> 
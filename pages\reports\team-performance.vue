<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="flex-row items-center gap-sm">
        <text class="page-title">团队绩效</text>
      </view>
      <view class="header-actions">
        <button class="action-button" @click="shareReport">
          <text class="ri-share-line"></text>
        </button>
        <view class="dropdown">
          <button class="action-button" @click="toggleReportMenu">
            <text class="ri-file-list-3-line"></text>
          </button>
          <view class="dropdown-menu" :class="{'show': showReportMenu}">
            <navigator url="/pages/reports/sales-reports" class="dropdown-item">
              <text class="ri-bar-chart-2-line"></text>
              <text>销售报表</text>
            </navigator>
            <navigator url="/pages/reports/team-performance" class="dropdown-item active">
              <text class="ri-team-line"></text>
              <text>团队绩效</text>
            </navigator>
            <navigator url="/pages/reports/customer-analytics" class="dropdown-item">
              <text class="ri-user-search-line"></text>
              <text>客户分析</text>
            </navigator>
            <navigator url="/pages/reports/custom-reports" class="dropdown-item">
              <text class="ri-file-chart-line"></text>
              <text>自定义报表</text>
            </navigator>
          </view>
        </view>
      </view>
    </view>

    <!-- 团队选择器 -->
    <view class="team-selector">
      <view class="team-dropdown" @click="showTeamSelect">
        <text>{{selectedTeam}}</text>
        <text class="ri-arrow-down-s-line"></text>
      </view>
    </view>
    
    <!-- 时间筛选器 -->
    <scroll-view class="time-filter" scroll-x="true" show-scrollbar="false">
      <view 
        v-for="(option, index) in timeOptions" 
        :key="index" 
        class="time-option" 
        :class="{'active': selectedTimeIndex === index}"
        @click="selectTimeOption(index)"
      >
        <text>{{option}}</text>
      </view>
    </scroll-view>
    
    <scroll-view scroll-y="true" class="page-content">
      <!-- 团队绩效指标卡片 -->
      <view class="metrics-cards">
        <view class="metric-card" v-for="(metric, index) in metricsData" :key="index">
          <text class="metric-label">{{metric.label}}</text>
          <text class="metric-value">{{metric.value}}</text>
          <view class="metric-change" :class="metric.changeClass">
            <text class="ri-arrow-up-line" v-if="metric.change > 0"></text>
            <text class="ri-arrow-down-line" v-else></text>
            <text>{{metric.changeText}}</text>
          </view>
        </view>
      </view>
      
      <!-- 团队目标达成进度 -->
      <view class="chart-container">
        <view class="chart-header">
          <text class="chart-title">团队目标达成进度</text>
          <view class="chart-actions">
            <button class="chart-action-btn" @click="downloadChart('teamTarget')">
              <text class="ri-download-line"></text>
            </button>
            <button class="chart-action-btn" @click="showChartOptions('teamTarget')">
              <text class="ri-more-2-fill"></text>
            </button>
          </view>
        </view>
        <view class="chart-wrapper">
          <qiun-data-charts 
            type="column"
            :opts="teamTargetOpts"
            :chartData="teamTargetData"
          />
        </view>
      </view>
      
      <!-- 团队销售趋势 -->
      <view class="chart-container">
        <view class="chart-header">
          <text class="chart-title">团队销售趋势</text>
          <view class="chart-actions">
            <button class="chart-action-btn" @click="downloadChart('teamTrend')">
              <text class="ri-download-line"></text>
            </button>
            <button class="chart-action-btn" @click="showChartOptions('teamTrend')">
              <text class="ri-more-2-fill"></text>
            </button>
          </view>
        </view>
        <view class="chart-wrapper">
          <qiun-data-charts 
            type="line"
            :opts="teamTrendOpts"
            :chartData="teamTrendData"
          />
        </view>
      </view>
      
      <!-- 个人绩效排行 -->
      <view class="performance-table">
        <view class="table-header">
          <text class="table-title">个人绩效排行</text>
          <button class="chart-action-btn" @click="downloadPerformance">
            <text class="ri-download-line"></text>
          </button>
        </view>
        <view class="table-wrapper">
          <view class="table-row table-header-row">
            <text class="table-cell table-head-cell">员工</text>
            <text class="table-cell table-head-cell">销售目标达成</text>
            <text class="table-cell table-head-cell">销售额</text>
            <text class="table-cell table-head-cell">成单数</text>
          </view>
          <view 
            class="table-row" 
            v-for="(employee, index) in performanceData" 
            :key="index"
          >
            <view class="table-cell">
              <view class="employee-info">
                <view class="employee-avatar" :style="{'background-color': employee.avatarBg || '#4a6fff20', 'color': employee.avatarColor || '#4a6fff'}">
                  <text>{{employee.avatar}}</text>
                </view>
                <text class="employee-name">{{employee.name}}</text>
              </view>
            </view>
            <view class="table-cell">
              <view class="achievement-cell">
                <view class="achievement-label">
                  <text>{{employee.percentage}}%</text>
                  <text>{{employee.achieved}} / {{employee.target}}</text>
                </view>
                <view class="progress-bar">
                  <view class="progress-fill" :class="employee.progressClass" :style="{ width: employee.percentage + '%' }"></view>
                </view>
              </view>
            </view>
            <text class="table-cell">{{employee.sales}}</text>
            <text class="table-cell">{{employee.deals}}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      showReportMenu: false,
      selectedTeam: '销售一部',
      timeOptions: ['本月', '上个月', '本季度', '上季度', '本年度', '自定义'],
      selectedTimeIndex: 0,
      
      // 指标卡片数据
      metricsData: [
        {
          label: '团队销售额',
          value: '¥1,284,562',
          change: 15.2,
          changeText: '15.2%',
          changeClass: 'change-positive'
        },
        {
          label: '目标完成率',
          value: '78.6%',
          change: 5.3,
          changeText: '5.3%',
          changeClass: 'change-positive'
        },
        {
          label: '平均成单周期',
          value: '18.5天',
          change: -2.3,
          changeText: '2.3天',
          changeClass: 'change-positive'
        },
        {
          label: '平均客单价',
          value: '¥35,682',
          change: 8.7,
          changeText: '8.7%',
          changeClass: 'change-positive'
        }
      ],
      
      // 团队目标达成进度图表数据
      teamTargetData: {
        categories: ['销售额', '新增客户', '跟进次数', '商机转化'],
        series: [
          {
            name: '目标',
            data: [1500000, 100, 500, 50]
          },
          {
            name: '已完成',
            data: [1284562, 86, 472, 38]
          }
        ]
      },
      
      // 团队目标达成进度图表配置
      teamTargetOpts: {
        color: ['#9ca3af', '#4a6fff'],
        padding: [15, 15, 0, 5],
        enableScroll: false,
        legend: {
          position: 'top',
          itemGap: 20,
        },
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          data: [
            {
              position: 'left',
              axisLine: true,
              formatter: (val) => {
                if (val >= 1000000) {
                  return '¥' + (val / 1000000).toFixed(1) + 'M';
                } else if (val >= 1000) {
                  return '¥' + (val / 1000).toFixed(0) + 'K';
                }
                return val;
              }
            }
          ]
        },
        extra: {
          column: {
            width: 30,
            categoryGap: 4
          }
        }
      },
      
      // 团队销售趋势图表数据
      teamTrendData: {
        categories: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        series: [
          {
            name: '销售额',
            data: [850000, 920000, 880000, 950000, 980000, 1050000, 1120000, 1090000, 1180000, 1230000, 1260000, 1280000]
          }
        ]
      },
      
      // 团队销售趋势图表配置
      teamTrendOpts: {
        color: ['#4a6fff'],
        padding: [15, 15, 0, 5],
        enableScroll: false,
        legend: {
          show: false
        },
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          gridType: 'dash',
          data: [
            {
              formatter: (val) => {
                return '¥' + (val / 10000) + '万';
              }
            }
          ]
        },
        extra: {
          line: {
            type: 'curve',
            width: 2,
            activeType: 'hollow'
          },
          tooltip: {
            showCategory: true
          }
        }
      },
      
      // 个人绩效排行数据
      performanceData: [
        {
          avatar: 'ZL',
          name: '张丽',
          percentage: 95,
          achieved: '¥475K',
          target: '¥500K',
          sales: '¥475,230',
          deals: 28,
          progressClass: 'progress-fill-success'
        },
        {
          avatar: 'WM',
          name: '王明',
          percentage: 82,
          achieved: '¥410K',
          target: '¥500K',
          sales: '¥410,625',
          deals: 25,
          progressClass: 'progress-fill-primary'
        },
        {
          avatar: 'LJ',
          name: '李军',
          percentage: 72,
          achieved: '¥289K',
          target: '¥400K',
          sales: '¥289,125',
          deals: 19,
          progressClass: 'progress-fill-primary'
        },
        {
          avatar: 'CY',
          name: '陈阳',
          percentage: 63,
          achieved: '¥252K',
          target: '¥400K',
          sales: '¥252,350',
          deals: 17,
          progressClass: 'progress-fill-warning'
        },
        {
          avatar: 'ZX',
          name: '赵小红',
          percentage: 42,
          achieved: '¥168K',
          target: '¥400K',
          sales: '¥168,420',
          deals: 11,
          progressClass: 'progress-fill-danger'
        }
      ]
    };
  },
  
  methods: {
    toggleReportMenu() {
      this.showReportMenu = !this.showReportMenu;
    },
    
    selectTimeOption(index) {
      this.selectedTimeIndex = index;
      // 这里添加数据刷新逻辑
      this.loadReportData(this.timeOptions[index]);
    },
    
    loadReportData(timeFrame) {
      // 根据选择的时间框架加载数据
      console.log('加载时间框架数据:', timeFrame);
      // 实际项目中，这里应该调用API获取数据
    },
    
    showTeamSelect() {
      uni.showActionSheet({
        itemList: ['销售一部', '销售二部', '销售三部', '销售四部'],
        success: (res) => {
          this.selectedTeam = ['销售一部', '销售二部', '销售三部', '销售四部'][res.tapIndex];
          this.loadTeamData();
        }
      });
    },
    
    loadTeamData() {
      // 加载选中团队的数据
      console.log('加载团队数据:', this.selectedTeam);
      // 实际项目中，这里应该调用API获取数据
    },
    
    shareReport() {
      uni.showActionSheet({
        itemList: ['分享到微信', '发送邮件', '导出PDF', '导出Excel'],
        success: (res) => {
          uni.showToast({
            title: '分享功能开发中',
            icon: 'none'
          });
        }
      });
    },
    
    downloadChart(chartType) {
      uni.showToast({
        title: '下载功能开发中',
        icon: 'none'
      });
    },
    
    showChartOptions(chartType) {
      uni.showActionSheet({
        itemList: ['查看大图', '下载数据', '查看明细数据'],
        success: (res) => {
          uni.showToast({
            title: '功能开发中',
            icon: 'none'
          });
        }
      });
    },
    
    downloadPerformance() {
      uni.showToast({
        title: '下载功能开发中',
        icon: 'none'
      });
    }
  },
  
  onLoad() {
    // 页面加载时初始化数据
    this.loadReportData(this.timeOptions[this.selectedTimeIndex]);
    this.loadTeamData();
  },
  
  onShow() {
    // 重新进入页面时刷新数据
    this.loadReportData(this.timeOptions[this.selectedTimeIndex]);
  },
  
  onHide() {
    // 隐藏菜单
    this.showReportMenu = false;
  }
};
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.page-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1px solid var(--border-color);
  background-color: #fff;
  position: relative;
  z-index: 10;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.gap-sm {
  gap: 20rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  flex-direction: row;
  gap: 20rpx;
}

.action-button {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #666;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  padding: 0;
  margin: 0;
  line-height: 1;
}

.team-selector {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.team-dropdown {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 20rpx 30rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f5f5f5;
}

.time-filter {
  display: flex;
  flex-direction: row;
  white-space: nowrap;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  padding: 0 30rpx;
}

.time-option {
  padding: 30rpx;
  color: #666;
  font-size: 28rpx;
  position: relative;
}

.time-option.active {
  color: #4a6fff;
  font-weight: 500;
}

.time-option.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background-color: #4a6fff;
  border-radius: 4rpx;
}

.page-content {
  flex: 1;
  background-color: #f5f7fa;
}

.metrics-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 30rpx;
}

.metric-card {
  background-color: #fff;
  border-radius: 16rpx;
  border: 1px solid #e0e0e0;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.metric-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.metric-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.metric-change {
  font-size: 24rpx;
  margin-top: 10rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.change-positive {
  color: #34d399;
}

.change-negative {
  color: #ef4444;
}

.chart-container {
  background-color: #fff;
  border-radius: 16rpx;
  border: 1px solid #e0e0e0;
  margin: 0 30rpx 30rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.chart-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.chart-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.chart-actions {
  display: flex;
  flex-direction: row;
  gap: 10rpx;
}

.chart-action-btn {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  color: #666;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  padding: 0;
  margin: 0;
  line-height: 1;
}

.chart-wrapper {
  width: 100%;
  height: 500rpx;
  position: relative;
}

.performance-table {
  margin: 0 30rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  border: 1px solid #e0e0e0;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.table-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #e0e0e0;
}

.table-title {
  font-size: 30rpx;
  font-weight: 500;
}

.table-wrapper {
  width: 100%;
}

.table-row {
  display: flex;
  flex-direction: row;
  border-bottom: 1px solid #e0e0e0;
}

.table-row:last-child {
  border-bottom: none;
}

.table-header-row {
  background-color: #f5f7fa;
}

.table-cell {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  flex: 1;
}

.table-head-cell {
  font-weight: 500;
  color: #666;
}

.employee-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 20rpx;
}

.employee-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 24rpx;
}

.employee-name {
  font-weight: 500;
}

.achievement-cell {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.achievement-label {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
}

.progress-bar {
  height: 16rpx;
  background-color: #e0e0e0;
  border-radius: 8rpx;
  overflow: hidden;
  width: 100%;
}

.progress-fill {
  height: 100%;
  border-radius: 8rpx;
}

.progress-fill-primary {
  background-color: #4a6fff;
}

.progress-fill-success {
  background-color: #34d399;
}

.progress-fill-warning {
  background-color: #f59e0b;
}

.progress-fill-danger {
  background-color: #ef4444;
}

.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  width: 360rpx;
  z-index: 1000;
  display: none;
  margin-top: 20rpx;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 30rpx;
  color: #333;
  font-size: 28rpx;
  border-bottom: 1px solid #e0e0e0;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item text:first-child {
  margin-right: 20rpx;
  font-size: 32rpx;
  color: #4a6fff;
}

.dropdown-item.active {
  font-weight: 500;
  background-color: rgba(74, 111, 255, 0.1);
}
</style> 
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-contracts-contract-detail"],{"009f":function(t,n,i){"use strict";i.r(n);var a=i("d27a"),e=i("25bd");for(var o in e)["default"].indexOf(o)<0&&function(t){i.d(n,t,(function(){return e[t]}))}(o);i("4ea8");var s=i("828b"),c=Object(s["a"])(e["default"],a["b"],a["c"],!1,null,"18e0d337",null,!1,a["a"],void 0);n["default"]=c.exports},"25bd":function(t,n,i){"use strict";i.r(n);var a=i("e770"),e=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(n,t,(function(){return a[t]}))}(o);n["default"]=e.a},"4ea8":function(t,n,i){"use strict";var a=i("8608"),e=i.n(a);e.a},8608:function(t,n,i){var a=i("b07c");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var e=i("967d").default;e("86f0ee8e",a,!0,{sourceMap:!1,shadowMode:!1})},b07c:function(t,n,i){var a=i("c86c");n=a(!1),n.push([t.i,'\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* 页面容器 */.container[data-v-18e0d337]{display:flex;flex-direction:column;height:100vh;background-color:#f5f7fa;box-sizing:border-box}\n/* 头部导航样式 */.page-header[data-v-18e0d337]{display:flex;align-items:center;justify-content:space-between;padding:%?24?% %?32?%;background-color:#fff;position:-webkit-sticky;position:sticky;top:0;z-index:10;box-shadow:0 %?2?% %?8?% rgba(0,0,0,.05);box-sizing:border-box}.page-title[data-v-18e0d337]{font-size:%?36?%;font-weight:600;color:#333}.back-button[data-v-18e0d337]{color:#666;display:flex;align-items:center;padding:%?16?%;margin:%?-16?%}.header-actions[data-v-18e0d337]{display:flex;gap:%?16?%}.action-button[data-v-18e0d337]{width:%?72?%;height:%?72?%;display:flex;align-items:center;justify-content:center;border-radius:50%;color:#666;background-color:#f5f7fa;border:%?2?% solid #eaeaea;transition:all .3s ease}.action-button[data-v-18e0d337]:active{background-color:#e6e6e6}\n/* 合同内容区域 */.contract-container[data-v-18e0d337]{padding:%?24?% %?32?%;flex:1;margin-bottom:%?160?%;box-sizing:border-box}\n/* 卡片通用样式 */.section-card[data-v-18e0d337]{background-color:#fff;border-radius:%?16?%;padding:%?24?%;margin-bottom:%?24?%;border:%?2?% solid #eaeaea;box-shadow:0 %?4?% %?16?% rgba(0,0,0,.05);box-sizing:border-box}.section-title[data-v-18e0d337]{font-size:%?32?%;font-weight:600;margin-bottom:%?24?%;padding:0;color:#333;display:flex;align-items:center;justify-content:space-between;box-sizing:border-box}\n/* 状态标签样式 */.status-badge[data-v-18e0d337]{padding:%?8?% %?24?%;border-radius:%?32?%;font-size:%?24?%;font-weight:500}.status-draft[data-v-18e0d337]{background-color:#f3f4f6;color:#6b7280}.status-review[data-v-18e0d337]{background-color:#fff7ed;color:#ea580c}.status-signed[data-v-18e0d337]{background-color:#ecfdf5;color:#059669}.status-completed[data-v-18e0d337]{background-color:#eff6ff;color:#3b82f6}.status-terminated[data-v-18e0d337]{background-color:#fef2f2;color:#dc2626}\n/* 信息网格布局 */.info-grid[data-v-18e0d337]{display:grid;grid-template-columns:repeat(2,1fr);gap:%?24?%;padding:0;box-sizing:border-box}.info-item[data-v-18e0d337]{min-width:0;padding:0;box-sizing:border-box}.info-label[data-v-18e0d337]{font-size:%?26?%;color:#666;margin-bottom:%?8?%;padding:0;box-sizing:border-box}.info-value[data-v-18e0d337]{font-size:%?28?%;color:#333;font-weight:500;word-break:break-all;padding:0;box-sizing:border-box}\n/* 金额表格样式 */.amount-table[data-v-18e0d337]{width:100%}.amount-row[data-v-18e0d337]{display:flex;justify-content:space-between;padding:%?16?% 0;font-size:%?28?%}.amount-table .label[data-v-18e0d337]{color:#666}.amount-table .value[data-v-18e0d337]{font-weight:500;color:#333}.total-row[data-v-18e0d337]{display:flex;justify-content:space-between;border-top:%?2?% solid #eaeaea;margin-top:%?16?%;padding-top:%?16?%}.total-row .label[data-v-18e0d337]{font-size:%?32?%;font-weight:600;color:#333}.total-row .value[data-v-18e0d337]{font-size:%?36?%;font-weight:600;color:#3a86ff}\n/* 历史记录样式 */.history-summary[data-v-18e0d337]{margin-bottom:%?24?%}.history-item[data-v-18e0d337]{display:flex;padding:%?16?% 0;border-bottom:%?2?% solid #f0f0f0}.history-item[data-v-18e0d337]:last-child{border-bottom:none}.history-date[data-v-18e0d337]{flex:0 0 %?140?%;font-size:%?24?%;color:#666;padding-top:%?4?%}.history-content[data-v-18e0d337]{flex:1}.history-title[data-v-18e0d337]{font-size:%?28?%;color:#333;margin-bottom:%?4?%}.history-user[data-v-18e0d337]{font-size:%?24?%;color:#666}\n/* 底部操作按钮 */.float-actions[data-v-18e0d337]{position:fixed;bottom:0;left:0;right:0;display:flex;padding:%?24?%;background-color:#fff;border-top:%?2?% solid #eaeaea;z-index:100;box-shadow:0 %?-4?% %?16?% rgba(0,0,0,.05)}.action-btn[data-v-18e0d337]{flex:1;display:flex;align-items:center;justify-content:center;gap:%?8?%;padding:%?24?%;border-radius:%?16?%;font-size:%?28?%;font-weight:500;transition:all .3s ease}.primary-action[data-v-18e0d337]{background:linear-gradient(135deg,#3a86ff,#0057ff);color:#fff;border:none;box-shadow:0 %?4?% %?12?% rgba(58,134,255,.3)}.primary-action[data-v-18e0d337]:active{-webkit-transform:scale(.98);transform:scale(.98);box-shadow:0 %?2?% %?8?% rgba(58,134,255,.2)}.secondary-action[data-v-18e0d337]{background-color:#f5f7fa;color:#333;border:%?2?% solid #eaeaea;margin-right:%?24?%}.secondary-action[data-v-18e0d337]:active{background-color:#e6e6e6}\n/* 添加折叠切换按钮样式 */.collapse-toggle[data-v-18e0d337]{display:flex;align-items:center;justify-content:center;gap:%?8?%;padding:%?16?% 0;color:#3a86ff;font-size:%?28?%;font-weight:500}.collapse-toggle uni-text[data-v-18e0d337]:last-child{font-size:%?32?%}.timeline[data-v-18e0d337]{margin-top:12px;padding-left:12px;border-left:2px solid #eaeaea}.timeline-item[data-v-18e0d337]{position:relative;margin-bottom:12px;padding-left:16px}.timeline-item[data-v-18e0d337]::before{content:"";position:absolute;left:-6px;top:0;width:10px;height:10px;border-radius:50%;background-color:#007aff}.timeline-item.done[data-v-18e0d337]::before{background-color:#10b981}.timeline-item.pending[data-v-18e0d337]::before{background-color:#f59e0b}.timeline-timestamp[data-v-18e0d337]{font-size:12px;color:#666;margin-bottom:4px}.timeline-title[data-v-18e0d337]{font-size:14px;font-weight:500;color:#333;margin-bottom:4px}.timeline-desc[data-v-18e0d337]{font-size:13px;color:#666}.file-list[data-v-18e0d337]{margin-top:8px}.file-item[data-v-18e0d337]{display:flex;align-items:center;padding:8px 0;border-bottom:1px solid #f0f0f0}.file-item[data-v-18e0d337]:last-child{border-bottom:none}.file-item uni-text[data-v-18e0d337]:first-child{margin-right:8px;color:#666}.file-item uni-text[data-v-18e0d337]:nth-child(2){flex:1;font-size:14px;color:#333}.download-button[data-v-18e0d337]{color:#007aff;background:none;border:none;padding:4px}',""]),t.exports=n},d27a:function(t,n,i){"use strict";i.d(n,"b",(function(){return a})),i.d(n,"c",(function(){return e})),i.d(n,"a",(function(){}));var a=function(){var t=this,n=t.$createElement,i=t._self._c||n;return i("v-uni-view",{staticClass:"container"},[i("v-uni-view",{staticClass:"page-header"},[i("v-uni-view",{staticClass:"back-button",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.navigateBack.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"ri-arrow-left-line"})],1),i("v-uni-view",{staticClass:"page-title"},[t._v("合同详情")]),i("v-uni-view",{staticClass:"header-actions"},[i("v-uni-button",{staticClass:"action-button",attrs:{type:"button"},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.showActionMenu.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"ri-more-2-fill"})],1)],1)],1),i("v-uni-scroll-view",{staticClass:"contract-container",attrs:{"scroll-y":!0}},[i("v-uni-view",{staticClass:"section-card"},[i("v-uni-view",{staticClass:"section-title"},[i("v-uni-text",[t._v(t._s(t.contract.title))]),i("v-uni-text",{class:["status-badge","status-"+t.contract.status.code]},[t._v(t._s(t.contract.status.name))])],1),i("v-uni-view",{staticClass:"info-grid"},[i("v-uni-view",{staticClass:"info-item"},[i("v-uni-view",{staticClass:"info-label"},[t._v("合同编号")]),i("v-uni-view",{staticClass:"info-value"},[t._v(t._s(t.contract.code))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-view",{staticClass:"info-label"},[t._v("关联报价单")]),i("v-uni-view",{staticClass:"info-value"},[t._v(t._s(t.contract.quoteCode))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-view",{staticClass:"info-label"},[t._v("签署日期")]),i("v-uni-view",{staticClass:"info-value"},[t._v(t._s(t.contract.signDate))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-view",{staticClass:"info-label"},[t._v("有效期至")]),i("v-uni-view",{staticClass:"info-value"},[t._v(t._s(t.contract.expiryDate))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-view",{staticClass:"info-label"},[t._v("合同类型")]),i("v-uni-view",{staticClass:"info-value"},[t._v(t._s(t.contract.type))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-view",{staticClass:"info-label"},[t._v("负责人")]),i("v-uni-view",{staticClass:"info-value"},[t._v(t._s(t.contract.owner))])],1)],1)],1),i("v-uni-view",{staticClass:"section-card"},[i("v-uni-view",{staticClass:"section-title"},[t._v("客户信息")]),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-view",{staticClass:"info-label"},[t._v("客户名称")]),i("v-uni-view",{staticClass:"info-value"},[t._v(t._s(t.contract.customer.name))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-view",{staticClass:"info-label"},[t._v("联系人")]),i("v-uni-view",{staticClass:"info-value"},[t._v(t._s(t.contract.customer.contact)+" ("+t._s(t.contract.customer.title)+")")])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-view",{staticClass:"info-label"},[t._v("联系电话")]),i("v-uni-view",{staticClass:"info-value"},[t._v(t._s(t.contract.customer.phone))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-view",{staticClass:"info-label"},[t._v("电子邮箱")]),i("v-uni-view",{staticClass:"info-value"},[t._v(t._s(t.contract.customer.email))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-view",{staticClass:"info-label"},[t._v("公司地址")]),i("v-uni-view",{staticClass:"info-value"},[t._v(t._s(t.contract.customer.address))])],1)],1),i("v-uni-view",{staticClass:"section-card"},[i("v-uni-view",{staticClass:"section-title"},[t._v("合同金额")]),i("v-uni-view",{staticClass:"amount-table"},[t._l(t.contract.items,(function(n,a){return i("v-uni-view",{key:a,staticClass:"amount-row"},[i("v-uni-text",{staticClass:"label"},[t._v(t._s(n.name))]),i("v-uni-text",{staticClass:"value"},[t._v("¥"+t._s(t.formatNumber(n.amount)))])],1)})),i("v-uni-view",{staticClass:"amount-row"},[i("v-uni-text",{staticClass:"label"},[t._v("增值税 ("+t._s(t.contract.taxRate)+"%)")]),i("v-uni-text",{staticClass:"value"},[t._v("¥"+t._s(t.formatNumber(t.contract.tax)))])],1),i("v-uni-view",{staticClass:"total-row"},[i("v-uni-text",{staticClass:"label"},[t._v("总计")]),i("v-uni-text",{staticClass:"value"},[t._v("¥"+t._s(t.formatNumber(t.contract.totalAmount)))])],1)],2)],1),i("v-uni-view",{staticClass:"section-card"},[i("v-uni-view",{staticClass:"section-title"},[t._v("付款计划")]),i("v-uni-view",{staticClass:"timeline"},t._l(t.contract.paymentPlan,(function(n,a){return i("v-uni-view",{key:a,class:["timeline-item",n.status]},[i("v-uni-view",{staticClass:"timeline-timestamp"},[t._v(t._s(n.date))]),i("v-uni-view",{staticClass:"timeline-title"},[t._v(t._s(n.title)+"：¥"+t._s(t.formatNumber(n.amount))+" ("+t._s(n.percentage)+"%)")]),i("v-uni-view",{staticClass:"timeline-desc"},[t._v(t._s(n.description))])],1)})),1)],1),i("v-uni-view",{staticClass:"section-card"},[i("v-uni-view",{staticClass:"section-title"},[t._v("合同条款")]),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-view",{staticClass:"info-label"},[t._v("项目周期")]),i("v-uni-view",{staticClass:"info-value"},[t._v(t._s(t.contract.terms.projectDuration))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-view",{staticClass:"info-label"},[t._v("质保期")]),i("v-uni-view",{staticClass:"info-value"},[t._v(t._s(t.contract.terms.warrantyPeriod))])],1),i("v-uni-view",{staticClass:"collapse-toggle",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.navigateToTerms.apply(void 0,arguments)}}},[i("v-uni-text",[t._v("查看详细条款")]),i("v-uni-text",{staticClass:"ri-arrow-right-s-line"})],1)],1),i("v-uni-view",{staticClass:"section-card"},[i("v-uni-view",{staticClass:"section-title"},[t._v("相关文档")]),i("v-uni-view",{staticClass:"file-list"},t._l(t.contract.documents,(function(n,a){return i("v-uni-view",{key:a,staticClass:"file-item"},[i("v-uni-text",{staticClass:"ri-file-pdf-line"}),i("v-uni-text",[t._v(t._s(n.name))]),i("v-uni-button",{staticClass:"download-button",attrs:{type:"button"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.downloadFile(n)}}},[i("v-uni-text",{staticClass:"ri-download-line"})],1)],1)})),1)],1),i("v-uni-view",{staticClass:"section-card"},[i("v-uni-view",{staticClass:"section-title"},[t._v("历史记录")]),i("v-uni-view",{staticClass:"history-summary"},t._l(t.contract.history.slice(0,3),(function(n,a){return i("v-uni-view",{key:a,staticClass:"history-item"},[i("v-uni-view",{staticClass:"history-date"},[t._v(t._s(n.date))]),i("v-uni-view",{staticClass:"history-content"},[i("v-uni-view",{staticClass:"history-title"},[t._v(t._s(n.title))]),i("v-uni-view",{staticClass:"history-user"},[t._v(t._s(n.user))])],1)],1)})),1),i("v-uni-view",{staticClass:"collapse-toggle",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.navigateToHistory.apply(void 0,arguments)}}},[i("v-uni-text",[t._v("查看完整历史")]),i("v-uni-text",{staticClass:"ri-arrow-right-s-line"})],1)],1)],1),i("v-uni-view",{staticClass:"float-actions"},[i("v-uni-button",{staticClass:"action-btn secondary-action",attrs:{type:"button"},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.downloadContract.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"ri-download-line"}),i("v-uni-text",[t._v("下载合同")])],1),i("v-uni-button",{staticClass:"action-btn primary-action",attrs:{type:"button"},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.navigateToEdit.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"ri-edit-line"}),i("v-uni-text",[t._v("编辑合同")])],1)],1)],1)},e=[]},e770:function(t,n,i){"use strict";i("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,i("5c47"),i("a1c1"),i("c9b5"),i("bf0f"),i("ab80");var a={data:function(){return{contract:{title:"企业系统集成项目合同",code:"CT-2023-09-001",quoteCode:"QT-2023-09-005",signDate:"2023-10-25",expiryDate:"2024-10-24",type:"系统集成",owner:"王销售",status:{code:"signed",name:"已签署"},customer:{name:"上海智能科技",contact:"张总监",title:"技术总监",phone:"13812345678",email:"<EMAIL>",address:"上海市浦东新区张江高科技园区科苑路88号"},items:[{name:"系统集成",amount:32e4},{name:"定制开发",amount:15e4},{name:"培训与支持",amount:45e3}],taxRate:13,tax:66950,totalAmount:581950,paymentPlan:[{title:"首付款",amount:290975,percentage:50,date:"2023-10-25",description:"合同签署后7天内支付",status:"done"},{title:"中期款",amount:174585,percentage:30,date:"预计：2023-12-25",description:"项目交付后7天内支付",status:"pending"},{title:"尾款",amount:116390,percentage:20,date:"预计：2024-01-25",description:"项目验收通过后7天内支付",status:""}],terms:{projectDuration:"90天",warrantyPeriod:"12个月"},documents:[{name:"企业系统集成项目合同.pdf",url:""},{name:"技术方案附件.pdf",url:""}],history:[{date:"2023-10-25",title:"合同签署完成",user:"张总监（客户）"},{date:"2023-10-25",title:"合同签署",user:"王经理（我方）"},{date:"2023-10-20",title:"合同审批通过",user:"刘总监（法务部）"}]}}},methods:{navigateBack:function(){uni.navigateBack()},showActionMenu:function(){var t=this;uni.showActionSheet({itemList:["查看历史记录","复制合同","标记为已完成","导出为PDF","删除合同"],success:function(n){switch(n.tapIndex){case 0:t.navigateToHistory();break;default:uni.showToast({title:"该功能开发中...",icon:"none"})}}})},navigateToEdit:function(){uni.navigateTo({url:"/pages/contracts/contract-edit?id=".concat(this.contract.code)})},navigateToHistory:function(){uni.navigateTo({url:"/pages/contracts/contract-history?id=".concat(this.contract.code)})},navigateToTerms:function(){uni.navigateTo({url:"/pages/contracts/contract-terms?id=".concat(this.contract.code)})},downloadContract:function(){uni.showToast({title:"下载合同功能开发中...",icon:"none"})},downloadFile:function(t){uni.showToast({title:"下载功能开发中...",icon:"none"})},formatNumber:function(t){return t.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}}};n.default=a}}]);
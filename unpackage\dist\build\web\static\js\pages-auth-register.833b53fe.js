(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-auth-register"],{"301e":function(t,e,i){"use strict";i.r(e);var s=i("d1f7"),o=i("cf0b");for(var r in o)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(r);i("e242");var n=i("828b"),a=Object(n["a"])(o["default"],s["b"],s["c"],!1,null,"41b0659c",null,!1,s["a"],void 0);e["default"]=a.exports},"46b0":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var s={data:function(){return{registerForm:{phone:"",verifyCode:"",password:"",confirmPassword:""},passwordVisible:!1,confirmPasswordVisible:!1,termsAgreed:!1,countdown:0,timer:null,isLoading:!1}},computed:{canRegister:function(){return this.registerForm.phone&&11===this.registerForm.phone.length&&this.registerForm.verifyCode&&6===this.registerForm.verifyCode.length&&this.registerForm.password&&this.registerForm.password.length>=6&&this.registerForm.confirmPassword===this.registerForm.password&&this.termsAgreed}},onUnload:function(){this.timer&&(clearInterval(this.timer),this.timer=null)},methods:{togglePasswordVisibility:function(){this.passwordVisible=!this.passwordVisible},toggleConfirmPasswordVisibility:function(){this.confirmPasswordVisible=!this.confirmPasswordVisible},toggleTermsAgreement:function(){this.termsAgreed=!this.termsAgreed},getVerifyCode:function(){var t=this;this.countdown>0||(this.registerForm.phone&&11===this.registerForm.phone.length?(uni.showLoading({title:"发送中..."}),setTimeout((function(){uni.hideLoading(),t.countdown=60,t.timer=setInterval((function(){t.countdown--,t.countdown<=0&&(clearInterval(t.timer),t.timer=null)}),1e3),uni.showToast({title:"验证码已发送",icon:"success"})}),1e3)):uni.showToast({title:"请输入有效的手机号",icon:"none"}))},handleRegister:function(){var t=this;this.canRegister&&(11===this.registerForm.phone.length?6===this.registerForm.verifyCode.length?this.registerForm.password.length<6?uni.showToast({title:"密码至少6位",icon:"none"}):this.registerForm.password===this.registerForm.confirmPassword?this.termsAgreed?(this.isLoading=!0,uni.showLoading({title:"注册中..."}),setTimeout((function(){uni.hideLoading(),t.isLoading=!1,uni.showToast({title:"注册成功",icon:"success",duration:2e3,success:function(){setTimeout((function(){uni.redirectTo({url:"/pages/auth/login"})}),2e3)}})}),1500)):uni.showToast({title:"请同意用户协议和隐私政策",icon:"none"}):uni.showToast({title:"两次密码不一致",icon:"none"}):uni.showToast({title:"请输入6位验证码",icon:"none"}):uni.showToast({title:"请输入有效的手机号",icon:"none"}))},showTerms:function(){uni.showToast({title:"用户协议功能开发中",icon:"none"})},showPrivacyPolicy:function(){uni.showToast({title:"隐私政策功能开发中",icon:"none"})}}};e.default=s},4926:function(t,e,i){var s=i("c86c");e=s(!1),e.push([t.i,".auth-container[data-v-41b0659c]{min-height:100vh;display:flex;flex-direction:column;padding:%?30?%;background-color:#fff}.auth-header[data-v-41b0659c]{text-align:center;margin:%?60?% 0}.logo[data-v-41b0659c]{font-size:%?80?%;font-weight:700;color:#3a86ff;margin-bottom:%?20?%}.welcome-text[data-v-41b0659c]{font-size:%?48?%;color:#333;margin-bottom:%?10?%;display:block}.sub-text[data-v-41b0659c]{font-size:%?32?%;color:#666;display:block}.auth-form[data-v-41b0659c]{margin-top:%?20?%}.form-group[data-v-41b0659c]{margin-bottom:%?30?%}.form-label[data-v-41b0659c]{display:block;margin-bottom:%?10?%;color:#333;font-weight:500}.form-input[data-v-41b0659c]{width:100%;height:%?90?%;padding:0 %?30?%;border:1px solid #ddd;border-radius:%?12?%;font-size:%?32?%;box-sizing:border-box}.verify-code-group[data-v-41b0659c]{display:flex;align-items:center}.verify-input[data-v-41b0659c]{flex:1}.verify-btn[data-v-41b0659c]{width:%?220?%;height:%?90?%;margin-left:%?20?%;background-color:#f0f7ff;color:#3a86ff;font-size:%?28?%;display:flex;align-items:center;justify-content:center;border-radius:%?12?%;border:1px solid #3a86ff}.verify-btn[disabled][data-v-41b0659c]{background-color:#f5f5f5;color:#999;border-color:#ddd}.password-input-group[data-v-41b0659c]{position:relative}.password-toggle[data-v-41b0659c]{position:absolute;right:%?30?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#666}.terms-agreement[data-v-41b0659c]{display:flex;align-items:flex-start;margin-bottom:%?30?%}.terms-text[data-v-41b0659c]{margin-left:%?10?%;font-size:%?28?%;color:#666;line-height:1.4;flex:1}.terms-link[data-v-41b0659c]{color:#3a86ff;display:inline}.btn[data-v-41b0659c]{height:%?90?%;display:flex;align-items:center;justify-content:center;border-radius:%?12?%;font-size:%?32?%;font-weight:500}.btn-primary[data-v-41b0659c]{background-color:#3a86ff;color:#fff}.btn-primary[disabled][data-v-41b0659c]{background-color:#a6c8ff}.btn-full[data-v-41b0659c]{width:100%}.auth-footer[data-v-41b0659c]{text-align:center;margin-top:auto;padding:%?40?% 0;display:flex;justify-content:center}.auth-footer-text[data-v-41b0659c]{color:#666;font-size:%?28?%}.auth-footer-link[data-v-41b0659c]{color:#3a86ff;font-size:%?28?%;font-weight:500;margin-left:%?10?%}",""]),t.exports=e},"59f3":function(t,e,i){var s=i("4926");s.__esModule&&(s=s.default),"string"===typeof s&&(s=[[t.i,s,""]]),s.locals&&(t.exports=s.locals);var o=i("967d").default;o("382c420d",s,!0,{sourceMap:!1,shadowMode:!1})},cf0b:function(t,e,i){"use strict";i.r(e);var s=i("46b0"),o=i.n(s);for(var r in s)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return s[t]}))}(r);e["default"]=o.a},d1f7:function(t,e,i){"use strict";i.d(e,"b",(function(){return s})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"auth-container"},[i("v-uni-view",{staticClass:"auth-header"},[i("v-uni-view",{staticClass:"logo"},[t._v("CRM")]),i("v-uni-text",{staticClass:"welcome-text"},[t._v("创建新账号")]),i("v-uni-text",{staticClass:"sub-text"},[t._v("请填写以下信息完成注册")])],1),i("v-uni-view",{staticClass:"auth-form"},[i("v-uni-view",{staticClass:"form-group"},[i("v-uni-text",{staticClass:"form-label"},[t._v("手机号")]),i("v-uni-input",{staticClass:"form-input",attrs:{type:"number",placeholder:"请输入手机号",maxlength:"11"},model:{value:t.registerForm.phone,callback:function(e){t.$set(t.registerForm,"phone",e)},expression:"registerForm.phone"}})],1),i("v-uni-view",{staticClass:"form-group"},[i("v-uni-text",{staticClass:"form-label"},[t._v("验证码")]),i("v-uni-view",{staticClass:"verify-code-group"},[i("v-uni-input",{staticClass:"form-input verify-input",attrs:{type:"number",placeholder:"请输入验证码",maxlength:"6"},model:{value:t.registerForm.verifyCode,callback:function(e){t.$set(t.registerForm,"verifyCode",e)},expression:"registerForm.verifyCode"}}),i("v-uni-button",{staticClass:"verify-btn",attrs:{disabled:t.countdown>0},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.getVerifyCode.apply(void 0,arguments)}}},[t._v(t._s(t.countdown>0?t.countdown+"秒后重试":"获取验证码"))])],1)],1),i("v-uni-view",{staticClass:"form-group"},[i("v-uni-text",{staticClass:"form-label"},[t._v("设置密码")]),i("v-uni-view",{staticClass:"password-input-group"},["checkbox"===(t.passwordVisible?"text":"password")?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请设置密码 (至少6位)",type:"checkbox"},model:{value:t.registerForm.password,callback:function(e){t.$set(t.registerForm,"password",e)},expression:"registerForm.password"}}):"radio"===(t.passwordVisible?"text":"password")?i("input",{directives:[{name:"model",rawName:"v-model",value:t.registerForm.password,expression:"registerForm.password"}],staticClass:"form-input",attrs:{placeholder:"请设置密码 (至少6位)",type:"radio"},domProps:{checked:t._q(t.registerForm.password,null)},on:{change:function(e){return t.$set(t.registerForm,"password",null)}}}):i("input",{directives:[{name:"model",rawName:"v-model",value:t.registerForm.password,expression:"registerForm.password"}],staticClass:"form-input",attrs:{placeholder:"请设置密码 (至少6位)",type:t.passwordVisible?"text":"password"},domProps:{value:t.registerForm.password},on:{input:function(e){e.target.composing||t.$set(t.registerForm,"password",e.target.value)}}}),i("v-uni-text",{staticClass:"password-toggle",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.togglePasswordVisibility.apply(void 0,arguments)}}},[t.passwordVisible?i("v-uni-text",{staticClass:"ri-eye-off-line"}):i("v-uni-text",{staticClass:"ri-eye-line"})],1)],1)],1),i("v-uni-view",{staticClass:"form-group"},[i("v-uni-text",{staticClass:"form-label"},[t._v("确认密码")]),i("v-uni-view",{staticClass:"password-input-group"},["checkbox"===(t.confirmPasswordVisible?"text":"password")?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请再次输入密码",type:"checkbox"},model:{value:t.registerForm.confirmPassword,callback:function(e){t.$set(t.registerForm,"confirmPassword",e)},expression:"registerForm.confirmPassword"}}):"radio"===(t.confirmPasswordVisible?"text":"password")?i("input",{directives:[{name:"model",rawName:"v-model",value:t.registerForm.confirmPassword,expression:"registerForm.confirmPassword"}],staticClass:"form-input",attrs:{placeholder:"请再次输入密码",type:"radio"},domProps:{checked:t._q(t.registerForm.confirmPassword,null)},on:{change:function(e){return t.$set(t.registerForm,"confirmPassword",null)}}}):i("input",{directives:[{name:"model",rawName:"v-model",value:t.registerForm.confirmPassword,expression:"registerForm.confirmPassword"}],staticClass:"form-input",attrs:{placeholder:"请再次输入密码",type:t.confirmPasswordVisible?"text":"password"},domProps:{value:t.registerForm.confirmPassword},on:{input:function(e){e.target.composing||t.$set(t.registerForm,"confirmPassword",e.target.value)}}}),i("v-uni-text",{staticClass:"password-toggle",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toggleConfirmPasswordVisibility.apply(void 0,arguments)}}},[t.confirmPasswordVisible?i("v-uni-text",{staticClass:"ri-eye-off-line"}):i("v-uni-text",{staticClass:"ri-eye-line"})],1)],1)],1),i("v-uni-view",{staticClass:"terms-agreement"},[i("v-uni-checkbox",{attrs:{checked:t.termsAgreed,color:"#3a86ff"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toggleTermsAgreement.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"terms-text"},[i("v-uni-text",[t._v("我已阅读并同意")]),i("v-uni-text",{staticClass:"terms-link",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showTerms.apply(void 0,arguments)}}},[t._v("用户协议")]),i("v-uni-text",[t._v("和")]),i("v-uni-text",{staticClass:"terms-link",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showPrivacyPolicy.apply(void 0,arguments)}}},[t._v("隐私政策")])],1)],1),i("v-uni-button",{staticClass:"btn btn-primary btn-full",attrs:{disabled:!t.canRegister},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleRegister.apply(void 0,arguments)}}},[t._v("注册")])],1),i("v-uni-view",{staticClass:"auth-footer"},[i("v-uni-text",{staticClass:"auth-footer-text"},[t._v("已有账号？")]),i("v-uni-navigator",{staticClass:"auth-footer-link",attrs:{url:"/pages/auth/login"}},[t._v("立即登录")])],1)],1)},o=[]},e242:function(t,e,i){"use strict";var s=i("59f3"),o=i.n(s);o.a}}]);
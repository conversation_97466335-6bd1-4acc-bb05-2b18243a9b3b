<template>
  <view class="outer-container">
    <view class="container">
      <!-- 页面头部 -->
      <view class="page-header">
        <view class="back-button" @click="goBack">
          <svg-icon name="arrow-left" type="svg" size="24"></svg-icon>
        </view>
        <text class="page-title">编辑报价单</text>
        <view class="header-actions">
          <button class="save-button" @click="saveQuotation">保存</button>
        </view>
      </view>

      <scroll-view scroll-y class="form-scroll" :scroll-top="scrollTop" @scroll="onScroll">
        <!-- 包装内容的居中容器 -->
        <view class="form-content-wrapper">
          <!-- 基本信息 -->
          <view class="form-section">
            <text class="section-title">基本信息</text>
            
            <view class="form-group">
              <text class="form-label">报价单名称 <text class="required">*</text></text>
              <input 
                type="text" 
                class="form-input" 
                placeholder="请输入报价单名称" 
                v-model="quotation.title"
              />
            </view>
            
            <view class="form-group">
              <text class="form-label">报价单编号</text>
              <input 
                type="text" 
                class="form-input" 
                placeholder="自动生成" 
                disabled
                v-model="quotation.code"
              />
            </view>
            
            <view class="form-group">
              <text class="form-label">有效期至 <text class="required">*</text></text>
              <view class="date-picker">
                <picker 
                  mode="date" 
                  :value="quotation.validUntil" 
                  @change="onValidUntilChange"
                  class="form-picker"
                >
                  <view class="picker-value">
                    {{ quotation.validUntil || '请选择日期' }}
                  </view>
                </picker>
                <svg-icon name="calendar" type="svg" size="20"></svg-icon>
              </view>
            </view>
            
            <view class="form-group">
              <text class="form-label">负责人 <text class="required">*</text></text>
              <picker 
                mode="selector" 
                :range="owners.map(item => item.name)" 
                @change="onOwnerChange"
                class="form-picker"
              >
                <view class="picker-value">
                  {{ selectedOwner ? selectedOwner.name : '请选择负责人' }}
                </view>
              </picker>
            </view>
            
            <view class="form-group">
              <text class="form-label">状态</text>
              <picker 
                mode="selector" 
                :range="statuses.map(item => item.name)" 
                @change="onStatusChange"
                class="form-picker"
              >
                <view class="picker-value">
                  {{ selectedStatus ? selectedStatus.name : '请选择状态' }}
                </view>
              </picker>
            </view>
          </view>
          
          <!-- 客户信息 -->
          <view class="form-section">
            <text class="section-title">客户信息</text>
            
            <view class="form-group">
              <text class="form-label">客户 <text class="required">*</text></text>
              <view class="customer-select" @click="selectCustomer">
                <text class="customer-name">{{ selectedCustomer && selectedCustomer.name || '请选择客户' }}</text>
                <svg-icon name="arrow-right" type="svg" size="18"></svg-icon>
              </view>
            </view>
            
            <view class="customer-details" v-if="selectedCustomer">
              <view class="customer-info-item">
                <text class="info-label">联系人</text>
                <text class="info-value">{{ selectedCustomer.contact || '未指定' }}</text>
              </view>
              <view class="customer-info-item">
                <text class="info-label">电话</text>
                <text class="info-value">{{ selectedCustomer.phone || '未提供' }}</text>
              </view>
              <view class="customer-info-item">
                <text class="info-label">邮箱</text>
                <text class="info-value">{{ selectedCustomer.email || '未提供' }}</text>
              </view>
            </view>
            
            <view class="form-group">
              <text class="form-label">关联商机</text>
              <view class="opportunity-select" @click="selectOpportunity">
                <text class="opportunity-name">{{ selectedOpportunity && selectedOpportunity.name || '选择关联商机（可选）' }}</text>
                <svg-icon name="arrow-right" type="svg" size="18"></svg-icon>
              </view>
            </view>
          </view>
          
          <!-- 产品与服务 -->
          <view class="form-section">
            <view class="section-header">
              <text class="section-title">产品与服务</text>
              <button class="add-button" @click="addProduct">
                <svg-icon name="add" type="svg" size="18"></svg-icon>
                <text>添加产品</text>
              </button>
            </view>
            
            <view class="empty-products" v-if="quotation.items.length === 0">
              <svg-icon name="shopping" type="svg" size="36"></svg-icon>
              <text class="empty-text">暂无产品</text>
              <text class="empty-desc">点击"添加产品"按钮添加产品或服务项目</text>
            </view>
            
            <view class="product-list" v-else>
              <view 
                class="product-item" 
                v-for="(item, index) in quotation.items" 
                :key="index"
              >
                <view class="product-header">
                  <text class="product-name">{{ item.name }}</text>
                  <view class="product-actions">
                    <view class="action-icon" @click="editProduct(index)">
                      <svg-icon name="edit" type="svg" size="18"></svg-icon>
                    </view>
                    <view class="action-icon" @click="removeProduct(index)">
                      <svg-icon name="delete-bin" type="svg" size="18"></svg-icon>
                    </view>
                  </view>
                </view>
                
                <view class="product-details">
                  <view class="product-price">¥{{ formatPrice(item.amount) }}</view>
                  <view class="product-description" v-if="item.description">{{ item.description }}</view>
                </view>
              </view>
              
              <view class="product-total">
                <text class="total-label">总计</text>
                <text class="total-amount">¥{{ formatPrice(calculateTotal()) }}</text>
              </view>
            </view>
          </view>
          
          <!-- 备注信息 -->
          <view class="form-section">
            <text class="section-title">备注说明</text>
            <view class="form-group">
              <textarea 
                class="form-textarea" 
                placeholder="请输入备注信息" 
                v-model="quotation.remark"
              ></textarea>
            </view>
          </view>
          
          <!-- 附加信息 -->
          <view class="form-section">
            <text class="section-title">附加信息</text>
            <view class="info-content">
              <view class="info-row">
                <text class="info-label">创建人</text>
                <text class="info-value">{{quotation.creator || '系统'}}</text>
              </view>
              <view class="info-row">
                <text class="info-label">创建时间</text>
                <text class="info-value">{{quotation.createTime || quotation.createDate}}</text>
              </view>
              <view class="info-row">
                <text class="info-label">最后更新</text>
                <text class="info-value">{{quotation.updateTime || '无更新记录'}}</text>
              </view>
            </view>
          </view>
          
          <!-- 底部占位区域，确保内容不被TabBar遮挡 -->
          <view class="bottom-space"></view>
        </view>
      </scroll-view>
      
      <!-- 自定义TabBar组件 -->
      <custom-tab-bar ref="customTabBar"></custom-tab-bar>
    </view>
  </view>
</template>

<script>
import CustomTabBar from '@/components/CustomTabBar.vue';
import SvgIcon from '@/components/svg-icon.vue';

export default {
  components: {
    CustomTabBar,
    SvgIcon
  },
  data() {
    return {
      id: '',
      scrollTop: 0,
      quotation: {
        id: '',
        title: '',
        code: '',
        validUntil: '',
        owner: '',
        status: '',
        customer: '',
        opportunity: '',
        items: [],
        remark: '',
        creator: '',
        createDate: '',
        createTime: '',
        updateTime: ''
      },
      selectedCustomer: null,
      selectedOpportunity: null,
      selectedOwner: null,
      selectedStatus: null,
      
      owners: [
        { id: '1', name: '李销售' },
        { id: '2', name: '王经理' },
        { id: '3', name: '张总监' }
      ],
      statuses: [
        { code: 'draft', name: '草稿' },
        { code: 'sent', name: '已发送' },
        { code: 'accepted', name: '已接受' },
        { code: 'rejected', name: '已拒绝' }
      ]
    }
  },
  onLoad(options) {
    if (options.id) {
      this.id = options.id;
      this.loadQuotationData();
    }
  },
  methods: {
    onScroll(e) {
      this.scrollTop = e.detail.scrollTop;
    },
    loadQuotationData() {
      // 实际项目中应该通过API获取报价单详情
      // 这里模拟加载数据
      const mockData = {
        id: '1',
        title: '云数据分析平台解决方案',
        code: 'QT-2023-10-001',
        customer: '北京科技有限公司',
        customerId: '1',
        opportunity: '云平台建设项目',
        opportunityId: '2',
        contact: '张经理',
        phone: '13800138000',
        email: '<EMAIL>',
        createDate: '2023-10-10',
        createTime: '2023-10-10 14:30',
        validUntil: '2023-11-10',
        owner: '1',
        creator: '李销售',
        updateTime: '2023-10-12 09:45',
        remark: '1. 本报价单有效期为30天；\n2. 不包含硬件设备费用；\n3. 可根据客户需求调整配置和价格。',
        items: [
          { 
            id: '101',
            name: '产品费用', 
            amount: 280000,
            description: '包含基础模块、数据分析模块、报表模块的授权费用'
          },
          { 
            id: '102',
            name: '实施费用', 
            amount: 50000,
            description: '包含需求调研、系统部署、数据迁移和系统测试'
          },
          { 
            id: '103',
            name: '年度维护', 
            amount: 20000,
            description: '包含系统升级、故障处理和技术支持'
          }
        ],
        status: 'sent'
      };
      
      // 填充数据
      this.quotation = { ...mockData };
      
      // 设置关联的选择器数据
      this.selectedCustomer = {
        id: mockData.customerId,
        name: mockData.customer,
        contact: mockData.contact,
        phone: mockData.phone,
        email: mockData.email
      };
      
      if (mockData.opportunityId) {
        this.selectedOpportunity = {
          id: mockData.opportunityId,
          name: mockData.opportunity
        };
      }
      
      // 设置负责人
      const ownerIndex = this.owners.findIndex(o => o.id === mockData.owner);
      if (ownerIndex !== -1) {
        this.selectedOwner = this.owners[ownerIndex];
      }
      
      // 设置状态
      const statusIndex = this.statuses.findIndex(s => s.code === mockData.status);
      if (statusIndex !== -1) {
        this.selectedStatus = this.statuses[statusIndex];
      }
    },
    formatPrice(price) {
      return price.toLocaleString('zh-CN');
    },
    calculateTotal() {
      return this.quotation.items.reduce((total, item) => total + item.amount, 0);
    },
    goBack() {
      uni.navigateBack();
    },
    onValidUntilChange(e) {
      this.quotation.validUntil = e.detail.value;
    },
    onOwnerChange(e) {
      const index = e.detail.value;
      this.selectedOwner = this.owners[index];
      this.quotation.owner = this.owners[index].id;
    },
    onStatusChange(e) {
      const index = e.detail.value;
      this.selectedStatus = this.statuses[index];
      this.quotation.status = this.statuses[index].code;
    },
    selectCustomer() {
      uni.navigateTo({
        url: '/pages/sales/customer-select?mode=select',
        events: {
          selectCustomer: (customer) => {
            this.selectedCustomer = customer;
            this.quotation.customer = customer.id;
          }
        }
      });
    },
    selectOpportunity() {
      if (!this.selectedCustomer) {
        uni.showToast({
          title: '请先选择客户',
          icon: 'none'
        });
        return;
      }
      
      uni.navigateTo({
        url: `/pages/sales/opportunity-select?customerId=${this.selectedCustomer.id}`,
        events: {
          selectOpportunity: (opportunity) => {
            this.selectedOpportunity = opportunity;
            this.quotation.opportunity = opportunity.id;
          }
        }
      });
    },
    addProduct() {
      uni.navigateTo({
        url: '/pages/sales/product-select?mode=select',
        events: {
          selectProduct: (product) => {
            this.quotation.items.push({
              id: product.id,
              name: product.name,
              amount: product.price,
              description: product.description
            });
          }
        }
      });
    },
    editProduct(index) {
      const product = this.quotation.items[index];
      uni.navigateTo({
        url: `/pages/sales/product-edit?mode=edit&index=${index}`,
        events: {
          updateProduct: (updatedProduct, index) => {
            this.quotation.items[index] = updatedProduct;
          }
        },
        success: (res) => {
          res.eventChannel.emit('productData', { product, index });
        }
      });
    },
    removeProduct(index) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除该产品吗？',
        success: (res) => {
          if (res.confirm) {
            this.quotation.items.splice(index, 1);
          }
        }
      });
    },
    validateForm() {
      if (!this.quotation.title.trim()) {
        uni.showToast({
          title: '请输入报价单名称',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.quotation.validUntil) {
        uni.showToast({
          title: '请选择有效期',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.selectedCustomer) {
        uni.showToast({
          title: '请选择客户',
          icon: 'none'
        });
        return false;
      }
      
      if (this.quotation.items.length === 0) {
        uni.showToast({
          title: '请至少添加一个产品或服务',
          icon: 'none'
        });
        return false;
      }
      
      return true;
    },
    saveQuotation() {
      if (!this.validateForm()) return;
      
      // 设置更新时间
      this.quotation.updateTime = new Date().toISOString().replace('T', ' ').substring(0, 19);
      
      // 实际项目中应该调用API保存报价单
      uni.showLoading({
        title: '保存中...'
      });
      
      // 模拟保存延迟
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '保存成功',
          icon: 'success',
          success: () => {
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          }
        });
      }, 1000);
    }
  },
  onShow() {
    // 设置TabBar当前选中项
    if (typeof this.$refs.customTabBar !== 'undefined') {
      this.$refs.customTabBar.current = 2; // 对应"销售"菜单
    }
  }
}
</script>

<style>
/* 重置所有样式 */
.outer-container {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center; /* 水平居中 */
  background-color: #f5f5f5;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  max-width: 600px; /* 限制最大宽度 */
  background-color: #f5f5f5;
  position: relative;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  height: 44px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  width: 100%;
  box-sizing: border-box;
  position: sticky;
  top: 0;
  z-index: 10;
}

.page-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.back-button {
  color: #666;
  display: flex;
  align-items: center;
}

.save-button {
  background-color: #3a86ff;
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  border: none;
}

.form-scroll {
  flex: 1;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 0; /* 移除内边距，让内部容器处理 */
}

.form-content-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center; /* 水平居中内容 */
  padding: 16px 0 150px 0; /* 添加顶部和底部内边距 */
}

.form-section {
  background-color: white;
  margin-bottom: 16px;
  padding: 16px;
  border-radius: 12px;
  border: 1px solid #eee;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  width: calc(100% - 32px); /* 减去左右padding */
  box-sizing: border-box;
  max-width: 540px; /* 限制最大宽度 */
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.add-button {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  color: #333;
}

.form-group {
  width: 100%;
  margin-bottom: 16px;
}

.form-label {
  font-size: 14px;
  margin-bottom: 8px;
  display: block;
  color: #333;
  width: 100%;
}

.required {
  color: #dc2626;
}

.form-input, .form-textarea, .form-picker, .customer-select, .opportunity-select {
  width: 100%;
  box-sizing: border-box;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  color: #333;
  background-color: #fff;
}

/* 文本区域特殊样式 */
.form-textarea {
  height: 120px;
}

.date-picker {
  position: relative;
  display: flex;
  align-items: center;
}

.picker-value {
  padding-right: 24px;
  color: #333;
}

.date-picker svg-icon {
  position: absolute;
  right: 12px;
  color: #666;
  pointer-events: none;
}

.customer-name, .opportunity-name {
  color: #333;
}

.customer-details {
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 8px;
  margin: 8px 0 16px;
  border: 1px solid #eee;
}

.customer-info-item {
  margin-bottom: 12px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.customer-info-item:last-child {
  margin-bottom: 0;
}

.customer-info-item .info-label {
  font-size: 13px;
  color: #666;
  width: 60px;
}

.customer-info-item .info-value {
  font-size: 14px;
  color: #333;
  flex: 1;
  text-align: right;
  word-break: break-all;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-row {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.info-value {
  font-size: 14px;
  color: #333;
}

.empty-products {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 0;
  color: #999;
}

.empty-text {
  font-size: 16px;
  margin: 12px 0 8px;
}

.empty-desc {
  font-size: 14px;
  color: #999;
}

.product-list {
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
}

.product-item {
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #eee;
  margin-bottom: 10px;
}

.product-item:last-child {
  margin-bottom: 0;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.product-name {
  font-size: 15px;
  font-weight: 500;
  color: #333;
}

.product-actions {
  display: flex;
  gap: 8px;
}

.action-icon {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.product-details {
  display: flex;
  flex-direction: column;
}

.product-price {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.product-description {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}

.product-total {
  display: flex;
  justify-content: space-between;
  padding: 12px;
  margin-top: 8px;
  background-color: #f5f5f5;
  border-radius: 8px;
  border: 1px solid #eee;
}

.total-label {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.total-amount {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 底部占位区域，防止内容被TabBar遮挡 */
.bottom-space {
  height: 150rpx;
  width: 100%;
}
</style> 